// Check if we're running in Tauri or web mode
const isTauri = typeof window !== 'undefined' && window.__TAURI_IPC__;

let invoke;
if (isTauri) {
    const tauriModule = await import('@tauri-apps/api/tauri');
    invoke = tauriModule.invoke;
} else {
    // Use the global mock invoke function
    invoke = window.mockInvoke || (async (command, args) => {
        console.log(`Mock invoke in file-explorer: ${command}`, args);
        // Return mock data for directory tree
        if (command === 'get_directory_tree') {
            return {
                name: 'example',
                path: args.path,
                is_directory: true,
                children: [
                    {
                        name: 'hello.rs',
                        path: args.path + '/hello.rs',
                        is_directory: false,
                        size: 1024
                    },
                    {
                        name: 'hello.js',
                        path: args.path + '/hello.js',
                        is_directory: false,
                        size: 2048
                    },
                    {
                        name: 'README.md',
                        path: args.path + '/README.md',
                        is_directory: false,
                        size: 512
                    }
                ]
            };
        }
        return null;
    });
}

export class FileExplorer {
    constructor(container, onFileSelect, onFileAction) {
        this.container = container;
        this.onFileSelect = onFileSelect;
        this.onFileAction = onFileAction;
        this.expandedFolders = new Set();

        this.init();
    }

    init() {
        this.container.innerHTML = '<div class="file-tree-loading">No folder opened</div>';
    }

    async loadDirectory(path) {
        try {
            this.container.innerHTML = '<div class="file-tree-loading">Loading...</div>';

            const tree = await invoke('get_directory_tree', {
                path: path,
                maxDepth: 3
            });

            this.renderTree(tree);
        } catch (error) {
            console.error('Failed to load directory:', error);
            this.container.innerHTML = `<div class="file-tree-empty">Failed to load directory: ${error}</div>`;
        }
    }

    renderTree(node, level = 0) {
        if (level === 0) {
            this.container.innerHTML = '';
            const ul = document.createElement('ul');
            ul.className = 'file-tree';
            this.container.appendChild(ul);
            this.renderNode(node, ul, level);
        } else {
            const ul = document.createElement('ul');
            ul.className = 'file-tree-children';
            return ul;
        }
    }

    renderNode(node, parentElement, level) {
        const li = document.createElement('li');
        li.className = 'file-tree-item';
        li.dataset.path = node.path;
        li.dataset.isDirectory = node.is_directory;

        // Create indentation
        for (let i = 0; i < level; i++) {
            const indent = document.createElement('span');
            indent.className = 'file-tree-indent';
            li.appendChild(indent);
        }

        // Create arrow for directories
        if (node.is_directory) {
            const arrow = document.createElement('span');
            arrow.className = 'file-tree-arrow';
            arrow.textContent = '▶';

            if (this.expandedFolders.has(node.path)) {
                arrow.classList.add('expanded');
            }

            arrow.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleFolder(node, li, arrow);
            });

            li.appendChild(arrow);
        } else {
            const spacer = document.createElement('span');
            spacer.className = 'file-tree-indent';
            li.appendChild(spacer);
        }

        // Create icon
        const icon = document.createElement('span');
        icon.className = `file-tree-icon ${node.is_directory ? 'folder' : 'file'}`;

        if (node.is_directory) {
            icon.textContent = this.expandedFolders.has(node.path) ? '📂' : '📁';
            if (this.expandedFolders.has(node.path)) {
                icon.classList.add('open');
            }
        } else {
            const ext = this.getFileExtension(node.name);
            icon.dataset.ext = ext;
            icon.textContent = this.getFileIcon(ext);
        }

        li.appendChild(icon);

        // Create label
        const label = document.createElement('span');
        label.className = 'file-tree-label';
        label.textContent = node.name;
        li.appendChild(label);

        // Add click handler
        li.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectItem(li);

            if (!node.is_directory) {
                this.onFileSelect(node.path);
            } else {
                this.toggleFolder(node, li, arrow);
            }
        });

        // Add context menu handler
        li.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.showContextMenu(e, node);
        });

        parentElement.appendChild(li);

        // Add children if directory is expanded
        if (node.is_directory && node.children && this.expandedFolders.has(node.path)) {
            const childrenContainer = document.createElement('ul');
            childrenContainer.className = 'file-tree-children';

            node.children.forEach(child => {
                this.renderNode(child, childrenContainer, level + 1);
            });

            parentElement.appendChild(childrenContainer);
        }
    }

    async toggleFolder(node, li, arrow) {
        if (this.expandedFolders.has(node.path)) {
            // Collapse
            this.expandedFolders.delete(node.path);
            arrow.classList.remove('expanded');

            const icon = li.querySelector('.file-tree-icon');
            icon.textContent = '📁';
            icon.classList.remove('open');

            // Remove children
            const children = li.parentElement.querySelector('.file-tree-children');
            if (children) {
                children.remove();
            }
        } else {
            // Expand
            this.expandedFolders.add(node.path);
            arrow.classList.add('expanded');

            const icon = li.querySelector('.file-tree-icon');
            icon.textContent = '📂';
            icon.classList.add('open');

            // Load and add children if not already loaded
            try {
                const tree = await invoke('get_directory_tree', {
                    path: node.path,
                    maxDepth: 1
                });

                if (tree.children && tree.children.length > 0) {
                    const childrenContainer = document.createElement('ul');
                    childrenContainer.className = 'file-tree-children';

                    tree.children.forEach(child => {
                        this.renderNode(child, childrenContainer, this.getNodeLevel(li) + 1);
                    });

                    li.parentElement.appendChild(childrenContainer);
                }
            } catch (error) {
                console.error('Failed to load folder contents:', error);
            }
        }
    }

    selectItem(item) {
        // Remove previous selection
        const previousSelected = this.container.querySelector('.file-tree-item.selected');
        if (previousSelected) {
            previousSelected.classList.remove('selected');
        }

        // Add selection to current item
        item.classList.add('selected');
    }

    showContextMenu(event, node) {
        const contextMenu = document.getElementById('context-menu');

        // Position the context menu
        contextMenu.style.left = event.pageX + 'px';
        contextMenu.style.top = event.pageY + 'px';
        contextMenu.classList.remove('hidden');

        // Set up context menu handlers
        const items = contextMenu.querySelectorAll('.context-menu-item');
        items.forEach(item => {
            item.onclick = () => {
                const action = item.dataset.action;
                this.onFileAction(action, node.path);
                contextMenu.classList.add('hidden');
            };
        });
    }

    getNodeLevel(element) {
        let level = 0;
        let current = element;

        while (current && current !== this.container) {
            if (current.classList.contains('file-tree-children')) {
                level++;
            }
            current = current.parentElement;
        }

        return level;
    }

    getFileExtension(filename) {
        const parts = filename.split('.');
        return parts.length > 1 ? parts.pop().toLowerCase() : '';
    }

    getFileIcon(extension) {
        const iconMap = {
            'rs': '🦀',
            'js': '📜',
            'mjs': '📜',
            'ts': '📘',
            'py': '🐍',
            'html': '🌐',
            'htm': '🌐',
            'css': '🎨',
            'json': '📋',
            'xml': '📄',
            'md': '📝',
            'txt': '📄',
            'yaml': '⚙️',
            'yml': '⚙️',
            'toml': '⚙️',
            'sh': '💻',
            'bash': '💻',
            'c': '📄',
            'cpp': '📄',
            'h': '📄',
            'hpp': '📄',
            'java': '☕',
            'go': '🐹',
            'php': '🐘',
            'rb': '💎',
            'swift': '🦉',
            'kt': '📱',
            'scala': '📄',
            'sql': '🗃️',
            'dockerfile': '🐳',
            'gitignore': '📄',
            'license': '📄',
            'readme': '📖'
        };

        return iconMap[extension] || '📄';
    }

    refresh() {
        // TODO: Implement refresh functionality
        console.log('Refresh file explorer');
    }
}

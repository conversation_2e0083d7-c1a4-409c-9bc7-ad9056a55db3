export class CommandPalette {
    constructor(container, onCommandExecute) {
        this.container = container;
        this.onCommandExecute = onCommandExecute;
        this.input = null;
        this.results = null;
        this.commands = [];
        this.filteredCommands = [];
        this.selectedIndex = 0;
        this.isVisible = false;
        
        this.init();
        this.loadCommands();
    }

    init() {
        this.input = this.container.querySelector('#command-input');
        this.results = this.container.querySelector('#command-results');
        
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Input events
        this.input.addEventListener('input', (e) => {
            this.filterCommands(e.target.value);
        });

        this.input.addEventListener('keydown', (e) => {
            this.handleKeyDown(e);
        });

        // Click outside to close
        this.container.addEventListener('click', (e) => {
            if (e.target === this.container) {
                this.hide();
            }
        });

        // Escape key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isVisible) {
                this.hide();
            }
        });
    }

    loadCommands() {
        this.commands = [
            // File commands
            {
                id: 'file.new',
                title: 'File: New File',
                description: 'Create a new file',
                category: 'File',
                keybinding: 'Ctrl+N',
                icon: '📄'
            },
            {
                id: 'file.open',
                title: 'File: Open Folder',
                description: 'Open a folder',
                category: 'File',
                keybinding: 'Ctrl+Shift+O',
                icon: '📁'
            },
            {
                id: 'file.save',
                title: 'File: Save',
                description: 'Save the current file',
                category: 'File',
                keybinding: 'Ctrl+S',
                icon: '💾'
            },
            {
                id: 'file.saveAs',
                title: 'File: Save As',
                description: 'Save the current file with a new name',
                category: 'File',
                keybinding: 'Ctrl+Shift+S',
                icon: '💾'
            },
            {
                id: 'file.saveAll',
                title: 'File: Save All',
                description: 'Save all open files',
                category: 'File',
                keybinding: 'Ctrl+K S',
                icon: '💾'
            },

            // Edit commands
            {
                id: 'editor.action.selectAll',
                title: 'Select All',
                description: 'Select all text in the editor',
                category: 'Edit',
                keybinding: 'Ctrl+A',
                icon: '📝'
            },
            {
                id: 'editor.find',
                title: 'Find',
                description: 'Find text in the current file',
                category: 'Edit',
                keybinding: 'Ctrl+F',
                icon: '🔍'
            },
            {
                id: 'editor.action.startFindReplaceAction',
                title: 'Replace',
                description: 'Find and replace text',
                category: 'Edit',
                keybinding: 'Ctrl+H',
                icon: '🔄'
            },
            {
                id: 'workbench.action.findInFiles',
                title: 'Find in Files',
                description: 'Search across all files',
                category: 'Edit',
                keybinding: 'Ctrl+Shift+F',
                icon: '🔍'
            },

            // View commands
            {
                id: 'workbench.action.toggleSidebarVisibility',
                title: 'View: Toggle Sidebar Visibility',
                description: 'Show or hide the sidebar',
                category: 'View',
                keybinding: 'Ctrl+B',
                icon: '📋'
            },
            {
                id: 'workbench.action.togglePanel',
                title: 'View: Toggle Panel',
                description: 'Show or hide the bottom panel',
                category: 'View',
                keybinding: 'Ctrl+J',
                icon: '📊'
            },
            {
                id: 'workbench.action.zoomIn',
                title: 'View: Zoom In',
                description: 'Increase editor font size',
                category: 'View',
                keybinding: 'Ctrl+=',
                icon: '🔍'
            },
            {
                id: 'workbench.action.zoomOut',
                title: 'View: Zoom Out',
                description: 'Decrease editor font size',
                category: 'View',
                keybinding: 'Ctrl+-',
                icon: '🔍'
            },

            // Navigation commands
            {
                id: 'workbench.action.quickOpen',
                title: 'Go to File',
                description: 'Quickly open a file by name',
                category: 'Navigation',
                keybinding: 'Ctrl+P',
                icon: '📄'
            },
            {
                id: 'workbench.action.gotoLine',
                title: 'Go to Line',
                description: 'Jump to a specific line number',
                category: 'Navigation',
                keybinding: 'Ctrl+G',
                icon: '📍'
            },
            {
                id: 'workbench.action.navigateBack',
                title: 'Go Back',
                description: 'Navigate to previous location',
                category: 'Navigation',
                keybinding: 'Alt+Left',
                icon: '⬅️'
            },
            {
                id: 'workbench.action.navigateForward',
                title: 'Go Forward',
                description: 'Navigate to next location',
                category: 'Navigation',
                keybinding: 'Alt+Right',
                icon: '➡️'
            },

            // Terminal commands
            {
                id: 'workbench.action.terminal.new',
                title: 'Terminal: Create New Terminal',
                description: 'Open a new terminal',
                category: 'Terminal',
                keybinding: 'Ctrl+Shift+`',
                icon: '💻'
            },

            // Help commands
            {
                id: 'workbench.action.showCommands',
                title: 'Show All Commands',
                description: 'Show the command palette',
                category: 'Help',
                keybinding: 'Ctrl+Shift+P',
                icon: '⌘'
            },
            {
                id: 'workbench.action.openKeyboardShortcuts',
                title: 'Keyboard Shortcuts',
                description: 'Show keyboard shortcuts',
                category: 'Help',
                keybinding: 'Ctrl+K Ctrl+S',
                icon: '⌨️'
            }
        ];
    }

    show(mode = 'commands') {
        this.isVisible = true;
        this.container.classList.remove('hidden');
        
        // Clear and focus input
        this.input.value = '';
        this.input.focus();
        
        // Set placeholder based on mode
        if (mode === 'files') {
            this.input.placeholder = 'Type a file name...';
            // TODO: Load file list for quick open
        } else {
            this.input.placeholder = 'Type a command...';
        }
        
        // Show all commands initially
        this.filterCommands('');
    }

    hide() {
        this.isVisible = false;
        this.container.classList.add('hidden');
        this.input.blur();
    }

    filterCommands(query) {
        const lowerQuery = query.toLowerCase();
        
        if (!query.trim()) {
            this.filteredCommands = [...this.commands];
        } else {
            this.filteredCommands = this.commands.filter(command => {
                return command.title.toLowerCase().includes(lowerQuery) ||
                       command.description.toLowerCase().includes(lowerQuery) ||
                       command.category.toLowerCase().includes(lowerQuery);
            });
        }
        
        this.selectedIndex = 0;
        this.renderResults(query);
    }

    renderResults(query = '') {
        this.results.innerHTML = '';
        
        if (this.filteredCommands.length === 0) {
            const noResults = document.createElement('div');
            noResults.className = 'command-no-results';
            noResults.textContent = 'No commands found';
            this.results.appendChild(noResults);
            return;
        }

        // Group commands by category
        const categories = {};
        this.filteredCommands.forEach(command => {
            if (!categories[command.category]) {
                categories[command.category] = [];
            }
            categories[command.category].push(command);
        });

        // Render categories and commands
        Object.keys(categories).forEach(categoryName => {
            // Add category header
            const categoryHeader = document.createElement('div');
            categoryHeader.className = 'command-category';
            categoryHeader.textContent = categoryName;
            this.results.appendChild(categoryHeader);

            // Add commands in category
            categories[categoryName].forEach((command, index) => {
                const commandElement = this.createCommandElement(command, query);
                
                // Calculate global index for selection
                const globalIndex = this.filteredCommands.indexOf(command);
                if (globalIndex === this.selectedIndex) {
                    commandElement.classList.add('selected');
                }
                
                this.results.appendChild(commandElement);
            });
        });
    }

    createCommandElement(command, query) {
        const element = document.createElement('div');
        element.className = 'command-result-item';
        element.dataset.commandId = command.id;

        // Icon
        const icon = document.createElement('span');
        icon.className = 'command-result-icon';
        icon.textContent = command.icon;
        element.appendChild(icon);

        // Content
        const content = document.createElement('div');
        content.className = 'command-result-content';

        const title = document.createElement('div');
        title.className = 'command-result-title';
        title.innerHTML = this.highlightMatch(command.title, query);

        const description = document.createElement('div');
        description.className = 'command-result-description';
        description.innerHTML = this.highlightMatch(command.description, query);

        content.appendChild(title);
        content.appendChild(description);
        element.appendChild(content);

        // Keybinding
        if (command.keybinding) {
            const keybinding = document.createElement('span');
            keybinding.className = 'command-result-keybinding';
            keybinding.textContent = command.keybinding;
            element.appendChild(keybinding);
        }

        // Click handler
        element.addEventListener('click', () => {
            this.executeCommand(command);
        });

        return element;
    }

    highlightMatch(text, query) {
        if (!query.trim()) {
            return text;
        }

        const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        return text.replace(regex, '<span class="command-highlight">$1</span>');
    }

    handleKeyDown(event) {
        switch (event.key) {
            case 'ArrowDown':
                event.preventDefault();
                this.selectedIndex = Math.min(this.selectedIndex + 1, this.filteredCommands.length - 1);
                this.updateSelection();
                break;

            case 'ArrowUp':
                event.preventDefault();
                this.selectedIndex = Math.max(this.selectedIndex - 1, 0);
                this.updateSelection();
                break;

            case 'Enter':
                event.preventDefault();
                if (this.filteredCommands[this.selectedIndex]) {
                    this.executeCommand(this.filteredCommands[this.selectedIndex]);
                }
                break;

            case 'Escape':
                event.preventDefault();
                this.hide();
                break;
        }
    }

    updateSelection() {
        // Remove previous selection
        const previousSelected = this.results.querySelector('.selected');
        if (previousSelected) {
            previousSelected.classList.remove('selected');
        }

        // Add selection to current item
        const items = this.results.querySelectorAll('.command-result-item');
        if (items[this.selectedIndex]) {
            items[this.selectedIndex].classList.add('selected');
            items[this.selectedIndex].scrollIntoView({ block: 'nearest' });
        }
    }

    executeCommand(command) {
        this.hide();
        
        if (this.onCommandExecute) {
            this.onCommandExecute(command);
        }
    }
}

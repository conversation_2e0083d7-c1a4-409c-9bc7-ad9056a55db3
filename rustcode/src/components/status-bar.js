export class StatusBar {
    constructor(container) {
        this.container = container;
        this.elements = {};
        
        this.init();
        this.setupEventListeners();
    }

    init() {
        // Get references to status bar elements
        this.elements = {
            fileInfo: document.getElementById('status-file-info'),
            cursorPosition: document.getElementById('status-cursor-position'),
            language: document.getElementById('status-language'),
            encoding: document.getElementById('status-encoding'),
            lineEnding: document.getElementById('status-line-ending')
        };

        // Set initial state
        this.updateFileInfo('', '');
        this.updateCursorPosition(1, 1);
        this.updateLanguage('');
    }

    setupEventListeners() {
        // Listen for cursor position changes from editor
        document.addEventListener('cursorPositionChange', (e) => {
            this.updateCursorPosition(e.detail.line, e.detail.column);
        });

        // Listen for selection changes from editor
        document.addEventListener('selectionChange', (e) => {
            this.updateSelection(e.detail);
        });

        // Add click handlers for status bar items
        if (this.elements.language) {
            this.elements.language.addEventListener('click', () => {
                this.showLanguageSelector();
            });
        }

        if (this.elements.encoding) {
            this.elements.encoding.addEventListener('click', () => {
                this.showEncodingSelector();
            });
        }

        if (this.elements.lineEnding) {
            this.elements.lineEnding.addEventListener('click', () => {
                this.showLineEndingSelector();
            });
        }
    }

    updateFileInfo(fileName, language) {
        if (this.elements.fileInfo) {
            if (fileName) {
                this.elements.fileInfo.textContent = fileName;
                this.elements.fileInfo.title = fileName;
                this.container.classList.remove('no-file');
            } else {
                this.elements.fileInfo.textContent = 'No file open';
                this.elements.fileInfo.title = '';
                this.container.classList.add('no-file');
            }
        }
        
        this.updateLanguage(language);
    }

    updateCursorPosition(line, column) {
        if (this.elements.cursorPosition) {
            this.elements.cursorPosition.textContent = `Ln ${line}, Col ${column}`;
        }
    }

    updateSelection(selection) {
        if (this.elements.cursorPosition) {
            if (selection.selectedText && selection.selectedText.length > 0) {
                const lines = selection.selectedText.split('\n').length;
                const chars = selection.selectedText.length;
                
                if (lines > 1) {
                    this.elements.cursorPosition.textContent = 
                        `Ln ${selection.startLine}, Col ${selection.startColumn} (${lines} lines, ${chars} chars selected)`;
                } else {
                    this.elements.cursorPosition.textContent = 
                        `Ln ${selection.startLine}, Col ${selection.startColumn} (${chars} chars selected)`;
                }
            } else {
                this.updateCursorPosition(selection.startLine, selection.startColumn);
            }
        }
    }

    updateLanguage(language) {
        if (this.elements.language) {
            if (language) {
                this.elements.language.textContent = this.getLanguageDisplayName(language);
                this.elements.language.title = `Language: ${language}`;
            } else {
                this.elements.language.textContent = 'Plain Text';
                this.elements.language.title = 'Language: Plain Text';
            }
        }
    }

    updateEncoding(encoding = 'UTF-8') {
        if (this.elements.encoding) {
            this.elements.encoding.textContent = encoding;
            this.elements.encoding.title = `Encoding: ${encoding}`;
        }
    }

    updateLineEnding(lineEnding = 'LF') {
        if (this.elements.lineEnding) {
            this.elements.lineEnding.textContent = lineEnding;
            this.elements.lineEnding.title = `Line Ending: ${lineEnding}`;
        }
    }

    updateWorkspace(workspacePath) {
        // Update the title or add workspace indicator
        const workspaceName = workspacePath.split('/').pop() || workspacePath.split('\\').pop();
        document.title = `${workspaceName} - RustCode`;
    }

    showNotification(message, type = 'info', duration = 3000) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `status-notification ${type}`;
        notification.textContent = message;

        // Add to status bar
        this.container.appendChild(notification);

        // Remove after duration
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, duration);
    }

    showProgress(percentage) {
        // Remove existing progress bar
        const existingProgress = this.container.querySelector('.status-progress');
        if (existingProgress) {
            existingProgress.remove();
        }

        if (percentage >= 0 && percentage <= 100) {
            const progress = document.createElement('div');
            progress.className = 'status-progress';
            progress.style.width = `${percentage}%`;
            this.container.appendChild(progress);

            // Remove when complete
            if (percentage >= 100) {
                setTimeout(() => {
                    if (progress.parentNode) {
                        progress.parentNode.removeChild(progress);
                    }
                }, 500);
            }
        }
    }

    hideProgress() {
        const progress = this.container.querySelector('.status-progress');
        if (progress) {
            progress.remove();
        }
    }

    showProblems(errors = 0, warnings = 0, infos = 0) {
        // Remove existing problems indicator
        const existingProblems = this.container.querySelector('.status-problems');
        if (existingProblems) {
            existingProblems.remove();
        }

        if (errors > 0 || warnings > 0 || infos > 0) {
            const problems = document.createElement('div');
            problems.className = 'status-problems';

            if (errors > 0) {
                const errorItem = this.createProblemItem('error', errors);
                problems.appendChild(errorItem);
            }

            if (warnings > 0) {
                const warningItem = this.createProblemItem('warning', warnings);
                problems.appendChild(warningItem);
            }

            if (infos > 0) {
                const infoItem = this.createProblemItem('info', infos);
                problems.appendChild(infoItem);
            }

            // Insert before the right section
            const statusRight = this.container.querySelector('.status-right');
            this.container.insertBefore(problems, statusRight);
        }
    }

    createProblemItem(type, count) {
        const item = document.createElement('div');
        item.className = 'status-problems-item';
        item.title = `${count} ${type}${count !== 1 ? 's' : ''}`;

        const icon = document.createElement('span');
        icon.className = `status-problems-icon ${type}`;
        icon.textContent = type === 'error' ? '✕' : type === 'warning' ? '⚠' : 'ℹ';

        const countSpan = document.createElement('span');
        countSpan.className = 'status-problems-count';
        countSpan.textContent = count;

        item.appendChild(icon);
        item.appendChild(countSpan);

        return item;
    }

    showGitBranch(branchName) {
        // Remove existing git indicator
        const existingGit = this.container.querySelector('.status-git');
        if (existingGit) {
            existingGit.remove();
        }

        if (branchName) {
            const git = document.createElement('div');
            git.className = 'status-git';
            git.title = `Git branch: ${branchName}`;

            const icon = document.createElement('span');
            icon.className = 'status-git-icon';
            icon.textContent = '⎇';

            const branch = document.createElement('span');
            branch.className = 'status-git-branch';
            branch.textContent = branchName;

            git.appendChild(icon);
            git.appendChild(branch);

            // Insert before the right section
            const statusRight = this.container.querySelector('.status-right');
            this.container.insertBefore(git, statusRight);
        }
    }

    showLanguageSelector() {
        // TODO: Implement language selector
        console.log('Show language selector');
    }

    showEncodingSelector() {
        // TODO: Implement encoding selector
        console.log('Show encoding selector');
    }

    showLineEndingSelector() {
        // TODO: Implement line ending selector
        console.log('Show line ending selector');
    }

    getLanguageDisplayName(language) {
        const displayNames = {
            'rust': 'Rust',
            'javascript': 'JavaScript',
            'typescript': 'TypeScript',
            'python': 'Python',
            'html': 'HTML',
            'css': 'CSS',
            'json': 'JSON',
            'xml': 'XML',
            'markdown': 'Markdown',
            'yaml': 'YAML',
            'toml': 'TOML',
            'shell': 'Shell Script',
            'c': 'C',
            'cpp': 'C++',
            'java': 'Java',
            'go': 'Go',
            'php': 'PHP',
            'ruby': 'Ruby',
            'swift': 'Swift',
            'kotlin': 'Kotlin',
            'scala': 'Scala',
            'sql': 'SQL',
            'dockerfile': 'Dockerfile'
        };

        return displayNames[language] || language || 'Plain Text';
    }
}

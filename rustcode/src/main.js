import { invoke } from '@tauri-apps/api/tauri';
import { appWindow } from '@tauri-apps/api/window';
import { dialog } from '@tauri-apps/api';
import * as monaco from 'monaco-editor';

import { FileExplorer } from './components/file-explorer.js';
import { EditorManager } from './components/editor.js';
import { TabManager } from './components/tabs.js';
import { StatusBar } from './components/status-bar.js';
import { CommandPalette } from './components/command-palette.js';

class RustCodeApp {
    constructor() {
        this.fileExplorer = null;
        this.editorManager = null;
        this.tabManager = null;
        this.statusBar = null;
        this.commandPalette = null;
        this.currentWorkspace = null;
        
        this.init();
    }

    async init() {
        try {
            // Initialize Monaco Editor
            await this.initializeMonaco();
            
            // Initialize components
            this.initializeComponents();
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Set up keyboard shortcuts
            this.setupKeyboardShortcuts();
            
            console.log('RustCode initialized successfully');
        } catch (error) {
            console.error('Failed to initialize RustCode:', error);
            this.showError('Failed to initialize application', error.message);
        }
    }

    async initializeMonaco() {
        // Configure Monaco Editor
        monaco.editor.defineTheme('rustcode-dark', {
            base: 'vs-dark',
            inherit: true,
            rules: [
                { token: 'comment', foreground: '6A9955' },
                { token: 'keyword', foreground: '569CD6' },
                { token: 'string', foreground: 'CE9178' },
                { token: 'number', foreground: 'B5CEA8' },
                { token: 'type', foreground: '4EC9B0' },
                { token: 'function', foreground: 'DCDCAA' },
                { token: 'variable', foreground: '9CDCFE' },
            ],
            colors: {
                'editor.background': '#1e1e1e',
                'editor.foreground': '#cccccc',
                'editor.lineHighlightBackground': '#2a2a2a',
                'editor.selectionBackground': '#264f78',
                'editor.inactiveSelectionBackground': '#3a3d41',
                'editorLineNumber.foreground': '#858585',
                'editorLineNumber.activeForeground': '#c6c6c6',
                'editorCursor.foreground': '#ffffff',
                'editor.findMatchBackground': '#515c6a',
                'editor.findMatchHighlightBackground': '#ea5c004d',
                'editor.wordHighlightBackground': '#575757b8',
                'editor.wordHighlightStrongBackground': '#004972b8',
                'editorBracketMatch.background': '#0064001a',
                'editorBracketMatch.border': '#888888',
            }
        });

        monaco.editor.setTheme('rustcode-dark');
    }

    initializeComponents() {
        // Initialize file explorer
        this.fileExplorer = new FileExplorer(
            document.getElementById('file-explorer'),
            this.onFileSelect.bind(this),
            this.onFileAction.bind(this)
        );

        // Initialize tab manager
        this.tabManager = new TabManager(
            document.getElementById('tab-bar'),
            this.onTabSelect.bind(this),
            this.onTabClose.bind(this)
        );

        // Initialize editor manager
        this.editorManager = new EditorManager(
            document.getElementById('monaco-editor'),
            this.onEditorChange.bind(this)
        );

        // Initialize status bar
        this.statusBar = new StatusBar(document.getElementById('status-bar'));

        // Initialize command palette
        this.commandPalette = new CommandPalette(
            document.getElementById('command-palette'),
            this.onCommandExecute.bind(this)
        );
    }

    setupEventListeners() {
        // Title bar controls
        document.getElementById('minimize-btn').addEventListener('click', () => {
            appWindow.minimize();
        });

        document.getElementById('maximize-btn').addEventListener('click', () => {
            appWindow.toggleMaximize();
        });

        document.getElementById('close-btn').addEventListener('click', () => {
            this.handleAppClose();
        });

        // Sidebar actions
        document.getElementById('new-file-btn').addEventListener('click', () => {
            this.createNewFile();
        });

        document.getElementById('new-folder-btn').addEventListener('click', () => {
            this.createNewFolder();
        });

        document.getElementById('refresh-btn').addEventListener('click', () => {
            this.refreshFileExplorer();
        });

        // Welcome screen actions
        document.getElementById('open-folder-btn').addEventListener('click', () => {
            this.openFolder();
        });

        document.getElementById('new-file-welcome-btn').addEventListener('click', () => {
            this.createNewFile();
        });

        // Resize handle
        this.setupResizeHandle();

        // Context menu
        this.setupContextMenu();
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Command palette
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'P') {
                e.preventDefault();
                this.commandPalette.show();
            }

            // Quick open
            if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                e.preventDefault();
                this.commandPalette.show('files');
            }

            // Save file
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                this.saveCurrentFile();
            }

            // New file
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                this.createNewFile();
            }

            // Open folder
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'O') {
                e.preventDefault();
                this.openFolder();
            }

            // Close tab
            if ((e.ctrlKey || e.metaKey) && e.key === 'w') {
                e.preventDefault();
                this.closeCurrentTab();
            }

            // Find
            if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                e.preventDefault();
                this.editorManager.showFind();
            }

            // Find in files
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'F') {
                e.preventDefault();
                this.showSearchPanel();
            }
        });
    }

    setupResizeHandle() {
        const resizeHandle = document.getElementById('resize-handle');
        const sidebar = document.getElementById('sidebar');
        let isResizing = false;

        resizeHandle.addEventListener('mousedown', (e) => {
            isResizing = true;
            document.addEventListener('mousemove', handleResize);
            document.addEventListener('mouseup', stopResize);
        });

        function handleResize(e) {
            if (!isResizing) return;
            const newWidth = e.clientX;
            if (newWidth >= 200 && newWidth <= 400) {
                sidebar.style.width = newWidth + 'px';
            }
        }

        function stopResize() {
            isResizing = false;
            document.removeEventListener('mousemove', handleResize);
            document.removeEventListener('mouseup', stopResize);
        }
    }

    setupContextMenu() {
        const contextMenu = document.getElementById('context-menu');
        
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.hideContextMenu();
        });

        document.addEventListener('click', () => {
            this.hideContextMenu();
        });
    }

    // Event handlers
    async onFileSelect(filePath) {
        try {
            const tabId = await invoke('open_file_in_editor', { path: filePath });
            const editorState = await invoke('get_editor_state');
            
            this.tabManager.updateTabs(editorState.tabs, editorState.tab_order, editorState.active_tab_id);
            
            if (editorState.active_tab_id) {
                const activeTab = editorState.tabs[editorState.active_tab_id];
                this.editorManager.openFile(activeTab.content, activeTab.language);
                this.statusBar.updateFileInfo(activeTab.file_name, activeTab.language);
                this.showEditor();
            }
        } catch (error) {
            console.error('Failed to open file:', error);
            this.showError('Failed to open file', error);
        }
    }

    async onFileAction(action, filePath) {
        try {
            switch (action) {
                case 'delete':
                    await invoke('delete_file', { path: filePath });
                    this.refreshFileExplorer();
                    break;
                case 'rename':
                    // TODO: Implement rename dialog
                    break;
                case 'new-file':
                    // TODO: Implement new file dialog
                    break;
                case 'new-folder':
                    // TODO: Implement new folder dialog
                    break;
            }
        } catch (error) {
            console.error('File action failed:', error);
            this.showError('File operation failed', error);
        }
    }

    async onTabSelect(tabId) {
        try {
            await invoke('set_active_editor_tab', { tabId });
            const editorState = await invoke('get_editor_state');
            
            if (editorState.active_tab_id) {
                const activeTab = editorState.tabs[editorState.active_tab_id];
                this.editorManager.openFile(activeTab.content, activeTab.language);
                this.statusBar.updateFileInfo(activeTab.file_name, activeTab.language);
            }
        } catch (error) {
            console.error('Failed to switch tab:', error);
        }
    }

    async onTabClose(tabId) {
        try {
            await invoke('close_editor_tab', { tabId });
            const editorState = await invoke('get_editor_state');
            
            this.tabManager.updateTabs(editorState.tabs, editorState.tab_order, editorState.active_tab_id);
            
            if (editorState.active_tab_id) {
                const activeTab = editorState.tabs[editorState.active_tab_id];
                this.editorManager.openFile(activeTab.content, activeTab.language);
                this.statusBar.updateFileInfo(activeTab.file_name, activeTab.language);
            } else {
                this.showWelcomeScreen();
            }
        } catch (error) {
            console.error('Failed to close tab:', error);
        }
    }

    async onEditorChange(content) {
        try {
            const editorState = await invoke('get_editor_state');
            if (editorState.active_tab_id) {
                await invoke('update_editor_content', {
                    tabId: editorState.active_tab_id,
                    content
                });
                
                // Update tab to show modified state
                const updatedState = await invoke('get_editor_state');
                this.tabManager.updateTabs(updatedState.tabs, updatedState.tab_order, updatedState.active_tab_id);
            }
        } catch (error) {
            console.error('Failed to update editor content:', error);
        }
    }

    async onCommandExecute(command) {
        try {
            switch (command.id) {
                case 'file.open':
                    await this.openFolder();
                    break;
                case 'file.new':
                    await this.createNewFile();
                    break;
                case 'file.save':
                    await this.saveCurrentFile();
                    break;
                case 'editor.find':
                    this.editorManager.showFind();
                    break;
                case 'workbench.action.findInFiles':
                    this.showSearchPanel();
                    break;
                default:
                    console.log('Unknown command:', command);
            }
        } catch (error) {
            console.error('Command execution failed:', error);
            this.showError('Command failed', error);
        }
    }

    // Utility methods
    async openFolder() {
        try {
            const selected = await dialog.open({
                directory: true,
                multiple: false,
                title: 'Open Folder'
            });

            if (selected) {
                this.currentWorkspace = selected;
                await this.fileExplorer.loadDirectory(selected);
                this.statusBar.updateWorkspace(selected);
            }
        } catch (error) {
            console.error('Failed to open folder:', error);
            this.showError('Failed to open folder', error);
        }
    }

    async createNewFile() {
        // TODO: Implement new file creation
        console.log('Create new file');
    }

    async createNewFolder() {
        // TODO: Implement new folder creation
        console.log('Create new folder');
    }

    async saveCurrentFile() {
        try {
            const editorState = await invoke('get_editor_state');
            if (editorState.active_tab_id) {
                await invoke('save_editor_tab', { tabId: editorState.active_tab_id });
                
                // Update tab to remove modified state
                const updatedState = await invoke('get_editor_state');
                this.tabManager.updateTabs(updatedState.tabs, updatedState.tab_order, updatedState.active_tab_id);
                
                this.statusBar.showNotification('File saved', 'success');
            }
        } catch (error) {
            console.error('Failed to save file:', error);
            this.showError('Failed to save file', error);
        }
    }

    async closeCurrentTab() {
        const editorState = await invoke('get_editor_state');
        if (editorState.active_tab_id) {
            await this.onTabClose(editorState.active_tab_id);
        }
    }

    async refreshFileExplorer() {
        if (this.currentWorkspace) {
            await this.fileExplorer.loadDirectory(this.currentWorkspace);
        }
    }

    showEditor() {
        document.getElementById('welcome-screen').style.display = 'none';
        document.getElementById('monaco-editor').style.display = 'block';
    }

    showWelcomeScreen() {
        document.getElementById('welcome-screen').style.display = 'flex';
        document.getElementById('monaco-editor').style.display = 'none';
    }

    showSearchPanel() {
        // TODO: Implement search panel
        console.log('Show search panel');
    }

    hideContextMenu() {
        document.getElementById('context-menu').classList.add('hidden');
    }

    showError(title, message) {
        console.error(`${title}: ${message}`);
        this.statusBar.showNotification(`${title}: ${message}`, 'error');
    }

    async handleAppClose() {
        try {
            const editorState = await invoke('get_editor_state');
            if (editorState && Object.values(editorState.tabs).some(tab => tab.is_modified)) {
                // TODO: Show unsaved changes dialog
                console.log('There are unsaved changes');
            }
            appWindow.close();
        } catch (error) {
            console.error('Error during app close:', error);
            appWindow.close();
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new RustCodeApp();
});

/* Tab Bar Styles */
#tab-bar {
    display: flex;
    background-color: #2d2d30;
    border-bottom: 1px solid #3e3e42;
    overflow-x: auto;
    overflow-y: hidden;
    height: 35px;
    align-items: center;
}

#tab-bar::-webkit-scrollbar {
    height: 0;
}

.tab {
    display: flex;
    align-items: center;
    padding: 0 12px;
    height: 35px;
    background-color: #2d2d30;
    border-right: 1px solid #3e3e42;
    cursor: pointer;
    user-select: none;
    min-width: 120px;
    max-width: 200px;
    position: relative;
    white-space: nowrap;
    overflow: hidden;
}

.tab:hover {
    background-color: #37373d;
}

.tab.active {
    background-color: #1e1e1e;
    border-bottom: 2px solid #007acc;
}

.tab-icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    flex-shrink: 0;
}

/* File type specific tab icons */
.tab-icon[data-ext="rs"] {
    color: #dea584;
}

.tab-icon[data-ext="js"],
.tab-icon[data-ext="mjs"] {
    color: #f7df1e;
}

.tab-icon[data-ext="ts"] {
    color: #3178c6;
}

.tab-icon[data-ext="py"] {
    color: #3776ab;
}

.tab-icon[data-ext="html"],
.tab-icon[data-ext="htm"] {
    color: #e34c26;
}

.tab-icon[data-ext="css"] {
    color: #1572b6;
}

.tab-icon[data-ext="json"] {
    color: #cbcb41;
}

.tab-icon[data-ext="md"] {
    color: #083fa1;
}

.tab-icon[data-ext="xml"] {
    color: #e37933;
}

.tab-icon[data-ext="yaml"],
.tab-icon[data-ext="yml"] {
    color: #cb171e;
}

.tab-icon[data-ext="toml"] {
    color: #9c4221;
}

.tab-icon[data-ext="sh"],
.tab-icon[data-ext="bash"] {
    color: #89e051;
}

.tab-label {
    flex: 1;
    font-size: 13px;
    color: #cccccc;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.tab.active .tab-label {
    color: #ffffff;
}

.tab-modified {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #f0f0f0;
    margin-left: 6px;
    flex-shrink: 0;
}

.tab-close {
    width: 16px;
    height: 16px;
    margin-left: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    font-size: 12px;
    color: #cccccc;
    cursor: pointer;
    flex-shrink: 0;
    opacity: 0;
    transition: opacity 0.1s ease;
}

.tab:hover .tab-close {
    opacity: 1;
}

.tab-close:hover {
    background-color: #3e3e42;
    color: #ffffff;
}

.tab.modified .tab-close {
    opacity: 1;
    background-color: #f0f0f0;
    color: #2d2d30;
    border-radius: 50%;
    width: 8px;
    height: 8px;
    margin-left: 6px;
}

.tab.modified:hover .tab-close {
    background-color: #e74c3c;
    color: #ffffff;
    border-radius: 2px;
    width: 16px;
    height: 16px;
}

/* Tab drag and drop */
.tab.dragging {
    opacity: 0.5;
}

.tab.drag-over {
    border-left: 2px solid #007acc;
}

/* Tab overflow indicator */
.tab-overflow {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 35px;
    background-color: #2d2d30;
    border-left: 1px solid #3e3e42;
    cursor: pointer;
    color: #cccccc;
    font-size: 12px;
}

.tab-overflow:hover {
    background-color: #37373d;
}

/* Empty tab bar */
.tab-bar-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 35px;
    color: #888;
    font-style: italic;
    font-size: 12px;
}

/* Tab context menu */
.tab-context-menu {
    position: absolute;
    background-color: #383838;
    border: 1px solid #5a5a5a;
    border-radius: 3px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    min-width: 150px;
}

.tab-context-menu-item {
    padding: 8px 12px;
    cursor: pointer;
    font-size: 12px;
    color: #cccccc;
}

.tab-context-menu-item:hover {
    background-color: #094771;
}

.tab-context-menu-separator {
    height: 1px;
    background-color: #5a5a5a;
    margin: 4px 0;
}

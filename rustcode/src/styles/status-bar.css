/* Status Bar Styles */
#status-bar {
    height: 22px;
    background-color: #007acc;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    font-size: 12px;
    border-top: 1px solid #005a9e;
}

.status-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.status-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.status-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 2px 6px;
    border-radius: 2px;
    white-space: nowrap;
}

.status-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

#status-file-info {
    font-weight: 500;
}

#status-cursor-position {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

#status-language {
    text-transform: capitalize;
    font-weight: 500;
}

#status-encoding,
#status-line-ending {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 11px;
}

/* Status bar icons */
.status-icon {
    margin-right: 4px;
    font-size: 11px;
}

/* Status bar notifications */
.status-notification {
    background-color: #f39c12;
    color: #ffffff;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 11px;
    animation: fadeIn 0.3s ease;
}

.status-notification.error {
    background-color: #e74c3c;
}

.status-notification.success {
    background-color: #27ae60;
}

.status-notification.info {
    background-color: #3498db;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(5px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Status bar progress */
.status-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 2px;
    background-color: #ffffff;
    transition: width 0.3s ease;
    opacity: 0.8;
}

/* Status bar when no file is open */
#status-bar.no-file {
    background-color: #2d2d30;
    color: #cccccc;
    border-top: 1px solid #3e3e42;
}

#status-bar.no-file .status-item:hover {
    background-color: #3e3e42;
}

/* Status bar problems indicator */
.status-problems {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-problems-item {
    display: flex;
    align-items: center;
    gap: 3px;
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 2px;
}

.status-problems-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.status-problems-icon {
    font-size: 10px;
}

.status-problems-icon.error {
    color: #f14c4c;
}

.status-problems-icon.warning {
    color: #ffcc02;
}

.status-problems-icon.info {
    color: #75beff;
}

.status-problems-count {
    font-size: 11px;
    font-weight: 500;
}

/* Status bar git branch */
.status-git {
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    padding: 2px 6px;
    border-radius: 2px;
}

.status-git:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.status-git-icon {
    font-size: 11px;
}

.status-git-branch {
    font-size: 11px;
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Status bar selection info */
.status-selection {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 11px;
}

/* Status bar indentation */
.status-indentation {
    cursor: pointer;
    padding: 2px 6px;
    border-radius: 2px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 11px;
}

.status-indentation:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Command Palette Styles */
#command-palette {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding-top: 100px;
    z-index: 2000;
}

.command-palette-content {
    background-color: #252526;
    border: 1px solid #3e3e42;
    border-radius: 6px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    width: 600px;
    max-width: 90vw;
    max-height: 70vh;
    overflow: hidden;
    animation: commandPaletteSlideIn 0.2s ease-out;
}

@keyframes commandPaletteSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

#command-input {
    width: 100%;
    padding: 15px 20px;
    background-color: #3c3c3c;
    border: none;
    border-bottom: 1px solid #3e3e42;
    color: #cccccc;
    font-size: 14px;
    outline: none;
}

#command-input::placeholder {
    color: #888;
}

#command-results {
    max-height: 400px;
    overflow-y: auto;
}

.command-result-item {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    cursor: pointer;
    border-bottom: 1px solid #2d2d30;
}

.command-result-item:hover,
.command-result-item.selected {
    background-color: #094771;
}

.command-result-item:last-child {
    border-bottom: none;
}

.command-result-icon {
    width: 16px;
    height: 16px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #cccccc;
    flex-shrink: 0;
}

.command-result-content {
    flex: 1;
    min-width: 0;
}

.command-result-title {
    font-size: 13px;
    color: #cccccc;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.command-result-description {
    font-size: 11px;
    color: #888;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.command-result-keybinding {
    font-size: 11px;
    color: #888;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    margin-left: 10px;
    flex-shrink: 0;
}

/* Command categories */
.command-category {
    padding: 8px 20px;
    background-color: #2d2d30;
    border-bottom: 1px solid #3e3e42;
    font-size: 11px;
    font-weight: bold;
    color: #888;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* No results */
.command-no-results {
    padding: 20px;
    text-align: center;
    color: #888;
    font-style: italic;
}

/* Recent commands */
.command-recent {
    border-left: 3px solid #007acc;
}

/* Command palette overlay */
.command-palette-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 1999;
}

/* Search highlighting */
.command-highlight {
    background-color: #264f78;
    color: #ffffff;
    padding: 1px 2px;
    border-radius: 2px;
}

/* Command palette loading */
.command-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #888;
}

.command-loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #3e3e42;
    border-top: 2px solid #007acc;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

/* Command palette shortcuts */
.command-shortcuts {
    padding: 10px 20px;
    background-color: #2d2d30;
    border-top: 1px solid #3e3e42;
    font-size: 11px;
    color: #888;
    display: flex;
    justify-content: space-between;
}

.command-shortcut {
    display: flex;
    align-items: center;
    gap: 5px;
}

.command-shortcut-key {
    background-color: #3e3e42;
    color: #cccccc;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 10px;
}

/* Responsive design */
@media (max-width: 768px) {
    .command-palette-content {
        width: 95vw;
        margin: 0 10px;
    }
    
    #command-input {
        padding: 12px 15px;
        font-size: 16px; /* Prevent zoom on iOS */
    }
    
    .command-result-item {
        padding: 12px 15px;
    }
    
    .command-result-keybinding {
        display: none;
    }
}

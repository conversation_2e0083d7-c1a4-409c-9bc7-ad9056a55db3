<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RustCode</title>
    <link rel="stylesheet" href="./styles/main.css">
    <link rel="stylesheet" href="./styles/file-explorer.css">
    <link rel="stylesheet" href="./styles/editor.css">
    <link rel="stylesheet" href="./styles/tabs.css">
    <link rel="stylesheet" href="./styles/status-bar.css">
    <link rel="stylesheet" href="./styles/command-palette.css">
</head>
<body>
    <div id="app">
        <!-- Title Bar -->
        <div id="title-bar" data-tauri-drag-region>
            <div class="title-bar-content">
                <div class="title-bar-title">RustCode</div>
                <div class="title-bar-controls">
                    <button id="minimize-btn" class="title-bar-button">−</button>
                    <button id="maximize-btn" class="title-bar-button">□</button>
                    <button id="close-btn" class="title-bar-button">×</button>
                </div>
            </div>
        </div>

        <!-- Menu Bar -->
        <div id="menu-bar">
            <div class="menu-item" data-menu="file">File</div>
            <div class="menu-item" data-menu="edit">Edit</div>
            <div class="menu-item" data-menu="view">View</div>
            <div class="menu-item" data-menu="search">Search</div>
            <div class="menu-item" data-menu="help">Help</div>
        </div>

        <!-- Main Content -->
        <div id="main-content">
            <!-- Sidebar -->
            <div id="sidebar" class="panel">
                <div class="sidebar-header">
                    <div class="sidebar-title">Explorer</div>
                    <div class="sidebar-actions">
                        <button id="new-file-btn" title="New File">📄</button>
                        <button id="new-folder-btn" title="New Folder">📁</button>
                        <button id="refresh-btn" title="Refresh">🔄</button>
                    </div>
                </div>
                <div id="file-explorer"></div>
            </div>

            <!-- Resize Handle -->
            <div id="resize-handle"></div>

            <!-- Editor Area -->
            <div id="editor-area" class="panel">
                <!-- Tab Bar -->
                <div id="tab-bar"></div>
                
                <!-- Editor Container -->
                <div id="editor-container">
                    <div id="welcome-screen">
                        <div class="welcome-content">
                            <h1>Welcome to RustCode</h1>
                            <p>A minimalistic code editor written in Rust</p>
                            <div class="welcome-actions">
                                <button id="open-folder-btn" class="welcome-button">Open Folder</button>
                                <button id="new-file-welcome-btn" class="welcome-button">New File</button>
                            </div>
                        </div>
                    </div>
                    <div id="monaco-editor" style="display: none;"></div>
                </div>
            </div>
        </div>

        <!-- Status Bar -->
        <div id="status-bar">
            <div class="status-left">
                <span id="status-file-info"></span>
                <span id="status-cursor-position"></span>
            </div>
            <div class="status-right">
                <span id="status-language"></span>
                <span id="status-encoding">UTF-8</span>
                <span id="status-line-ending">LF</span>
            </div>
        </div>

        <!-- Command Palette -->
        <div id="command-palette" class="hidden">
            <div class="command-palette-content">
                <input type="text" id="command-input" placeholder="Type a command...">
                <div id="command-results"></div>
            </div>
        </div>

        <!-- Context Menu -->
        <div id="context-menu" class="hidden">
            <div class="context-menu-item" data-action="open">Open</div>
            <div class="context-menu-item" data-action="rename">Rename</div>
            <div class="context-menu-item" data-action="delete">Delete</div>
            <div class="context-menu-separator"></div>
            <div class="context-menu-item" data-action="new-file">New File</div>
            <div class="context-menu-item" data-action="new-folder">New Folder</div>
        </div>

        <!-- Search Panel -->
        <div id="search-panel" class="hidden">
            <div class="search-header">
                <input type="text" id="search-input" placeholder="Search in files...">
                <button id="search-btn">🔍</button>
                <button id="close-search-btn">×</button>
            </div>
            <div id="search-results"></div>
        </div>
    </div>

    <script type="module" src="./main.js"></script>
</body>
</html>

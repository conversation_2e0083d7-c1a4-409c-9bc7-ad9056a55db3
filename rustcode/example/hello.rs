// Example Rust file for testing RustCode

use std::io;

fn main() {
    println!("Hello, RustCode!");
    
    // Get user input
    println!("What's your name?");
    let mut name = String::new();
    
    io::stdin()
        .read_line(&mut name)
        .expect("Failed to read line");
    
    let name = name.trim();
    
    if !name.is_empty() {
        println!("Hello, {}! Welcome to RustCode.", name);
        greet_user(name);
    } else {
        println!("Hello, anonymous user!");
    }
}

fn greet_user(name: &str) {
    match name.to_lowercase().as_str() {
        "rust" => println!("🦀 You have excellent taste in programming languages!"),
        "rustcode" => println!("🎉 You're using the best editor!"),
        _ => println!("Nice to meet you, {}!", name),
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_greet_user() {
        // This is just a simple test
        greet_user("test");
    }
}

# RustCode Example Files

This directory contains example files to test RustCode's features.

## Files

- **hello.rs** - Example Rust file with syntax highlighting
- **hello.js** - Example JavaScript file with modern features
- **README.md** - This file (Markdown example)

## Testing Features

### Syntax Highlighting
Open any of the example files to see syntax highlighting in action.

### File Explorer
Use the file explorer to navigate between files in this directory.

### Search
Try searching for specific terms across all files using `Ctrl+Shift+F`.

### Command Palette
Press `Ctrl+Shift+P` to open the command palette and explore available commands.

## Code Examples

### Rust Features Demonstrated
- Functions and modules
- Pattern matching
- Error handling
- Unit tests
- Comments and documentation

### JavaScript Features Demonstrated
- Modern ES6+ syntax
- Arrow functions
- Async/await
- Classes
- Array methods
- Template literals
- Destructuring

## Getting Started

1. Open RustCode
2. Use "Open Folder" to select this example directory
3. Click on files in the explorer to open them
4. Try editing the files to see real-time syntax highlighting
5. Use keyboard shortcuts to navigate and search

Enjoy exploring RustCode! 🦀

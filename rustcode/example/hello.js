// Example JavaScript file for testing RustCode

console.log("Hello, RustCode!");

// Simple function to demonstrate syntax highlighting
function greetUser(name) {
    if (!name || name.trim() === '') {
        return "Hello, anonymous user!";
    }
    
    const greeting = `Hello, ${name}! Welcome to RustCode.`;
    console.log(greeting);
    
    // Check for special names
    switch (name.toLowerCase()) {
        case 'javascript':
        case 'js':
            console.log("📜 JavaScript is awesome!");
            break;
        case 'rustcode':
            console.log("🎉 You're using the best editor!");
            break;
        default:
            console.log(`Nice to meet you, ${name}!`);
    }
    
    return greeting;
}

// Example of modern JavaScript features
const users = [
    { name: '<PERSON>', age: 30 },
    { name: '<PERSON>', age: 25 },
    { name: '<PERSON>', age: 35 }
];

// Arrow functions and array methods
const adultUsers = users
    .filter(user => user.age >= 18)
    .map(user => ({ ...user, isAdult: true }));

console.log('Adult users:', adultUsers);

// Async/await example
async function fetchData() {
    try {
        // Simulated API call
        const data = await new Promise(resolve => {
            setTimeout(() => {
                resolve({ message: "Data fetched successfully!" });
            }, 1000);
        });
        
        console.log(data.message);
        return data;
    } catch (error) {
        console.error('Error fetching data:', error);
    }
}

// Class example
class CodeEditor {
    constructor(name) {
        this.name = name;
        this.files = [];
    }
    
    openFile(filename) {
        this.files.push(filename);
        console.log(`Opened ${filename} in ${this.name}`);
    }
    
    closeFile(filename) {
        const index = this.files.indexOf(filename);
        if (index > -1) {
            this.files.splice(index, 1);
            console.log(`Closed ${filename}`);
        }
    }
    
    get openFileCount() {
        return this.files.length;
    }
}

// Usage
const editor = new CodeEditor('RustCode');
editor.openFile('hello.js');
editor.openFile('hello.rs');

console.log(`Files open: ${editor.openFileCount}`);

// Call the async function
fetchData();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { greetUser, CodeEditor };
}

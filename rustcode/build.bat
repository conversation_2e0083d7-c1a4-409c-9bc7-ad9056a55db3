@echo off

echo 🦀 Building RustCode...

REM Check if Node.js is installed
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js first.
    exit /b 1
)

REM Check if Rust is installed
where cargo >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Rust is not installed. Please install Rust first.
    exit /b 1
)

REM Check if Tauri CLI is installed
cargo tauri --version >nul 2>nul
if %errorlevel% neq 0 (
    echo 📦 Installing Tauri CLI...
    cargo install tauri-cli
)

REM Install Node.js dependencies
echo 📦 Installing Node.js dependencies...
npm install

REM Build the application
echo 🔨 Building RustCode...
npm run tauri build

echo ✅ Build complete! Check the src-tauri/target/release/bundle directory for the executable.

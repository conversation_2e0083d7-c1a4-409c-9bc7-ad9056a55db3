{"version": 3, "sources": ["out-editor/vs/editor/editor.main.nls.de.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\ndefine(\"vs/editor/editor.main.nls.de\", {\n\t\"vs/base/browser/ui/actionbar/actionViewItems\": [\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/base/browser/ui/findinput/findInput\": [\n\t\t\"Eingabe\",\n\t],\n\t\"vs/base/browser/ui/findinput/findInputToggles\": [\n\t\t\"Groß-/Kleinschreibung beachten\",\n\t\t\"Nur ganzes Wort suchen\",\n\t\t\"Regulären Ausdruck verwenden\",\n\t],\n\t\"vs/base/browser/ui/findinput/replaceInput\": [\n\t\t\"Eingabe\",\n\t\t\"Groß-/Kleinschreibung beibehalten\",\n\t],\n\t\"vs/base/browser/ui/hover/hoverWidget\": [\n\t\t\"Überprüfen Sie dies in der barrierefreien Ansicht mit \\\"{0}\\\".\",\n\t\t\"Überprüfen Sie dies in der barrierefreien Ansicht über den Befehl \\\"Barrierefreie Ansicht öffnen\\\", der zurzeit nicht über eine Tastenzuordnung ausgelöst werden kann.\",\n\t],\n\t\"vs/base/browser/ui/iconLabel/iconLabelHover\": [\n\t\t\"Wird geladen...\",\n\t],\n\t\"vs/base/browser/ui/inputbox/inputBox\": [\n\t\t\"Fehler: {0}\",\n\t\t\"Warnung: {0}\",\n\t\t\"Info: {0}\",\n\t\t\"für Verlauf\",\n\t\t\"Gelöschte Eingabe\",\n\t],\n\t\"vs/base/browser/ui/keybindingLabel/keybindingLabel\": [\n\t\t\"Ungebunden\",\n\t],\n\t\"vs/base/browser/ui/selectBox/selectBoxCustom\": [\n\t\t\"Auswahlfeld\",\n\t],\n\t\"vs/base/browser/ui/toolbar/toolbar\": [\n\t\t\"Weitere Aktionen...\",\n\t],\n\t\"vs/base/browser/ui/tree/abstractTree\": [\n\t\t\"Filter\",\n\t\t\"Fuzzyübereinstimmung\",\n\t\t\"Zum Filtern Text eingeben\",\n\t\t\"Zum Suchen eingeben\",\n\t\t\"Zum Suchen eingeben\",\n\t\t\"Schließen\",\n\t\t\"Kein Element gefunden.\",\n\t],\n\t\"vs/base/common/actions\": [\n\t\t\"(leer)\",\n\t],\n\t\"vs/base/common/errorMessage\": [\n\t\t\"{0}: {1}\",\n\t\t\"Ein Systemfehler ist aufgetreten ({0}).\",\n\t\t\"Ein unbekannter Fehler ist aufgetreten. Weitere Details dazu finden Sie im Protokoll.\",\n\t\t\"Ein unbekannter Fehler ist aufgetreten. Weitere Details dazu finden Sie im Protokoll.\",\n\t\t\"{0} ({1} Fehler gesamt)\",\n\t\t\"Ein unbekannter Fehler ist aufgetreten. Weitere Details dazu finden Sie im Protokoll.\",\n\t],\n\t\"vs/base/common/keybindingLabels\": [\n\t\t\"STRG\",\n\t\t\"UMSCHALTTASTE\",\n\t\t\"ALT\",\n\t\t\"Windows\",\n\t\t\"STRG\",\n\t\t\"UMSCHALTTASTE\",\n\t\t\"ALT\",\n\t\t\"Super\",\n\t\t\"Steuern\",\n\t\t\"UMSCHALTTASTE\",\n\t\t\"Option\",\n\t\t\"Befehl\",\n\t\t\"Steuern\",\n\t\t\"UMSCHALTTASTE\",\n\t\t\"ALT\",\n\t\t\"Windows\",\n\t\t\"Steuern\",\n\t\t\"UMSCHALTTASTE\",\n\t\t\"ALT\",\n\t\t\"Super\",\n\t],\n\t\"vs/base/common/platform\": [\n\t\t\"_\",\n\t],\n\t\"vs/editor/browser/controller/textAreaHandler\": [\n\t\t\"Editor\",\n\t\t\"Auf den Editor kann zurzeit nicht zugegriffen werden.\",\n\t\t\"{0} Um den für die Sprachausgabe optimierten Modus zu aktivieren, verwenden Sie {1}\",\n\t\t\"{0} Um den für die Sprachausgabe optimierten Modus zu aktivieren, öffnen Sie die Schnellauswahl mit {1}, und führen Sie den Befehl \\\"Barrierefreiheitsmodus der Bildschirmsprachausgabe umschalten\\\" aus, der derzeit nicht über die Tastatur ausgelöst werden kann.\",\n\t\t\"{0} Weisen Sie eine Tastenzuordnung für den Befehl \\\"Barrierefreiheitsmodus der Sprachausgabe umschalten\\\" zu, indem Sie mit auf den Editor für Tastenzuordnungen zugreifen {1} und ihn ausführen.\",\n\t],\n\t\"vs/editor/browser/coreCommands\": [\n\t\t\"Auch bei längeren Zeilen am Ende bleiben\",\n\t\t\"Auch bei längeren Zeilen am Ende bleiben\",\n\t\t\"Sekundäre Cursor entfernt\",\n\t],\n\t\"vs/editor/browser/editorExtensions\": [\n\t\t\"&&Rückgängig\",\n\t\t\"Rückgängig\",\n\t\t\"&&Wiederholen\",\n\t\t\"Wiederholen\",\n\t\t\"&&Alles auswählen\",\n\t\t\"Alle auswählen\",\n\t],\n\t\"vs/editor/browser/widget/codeEditorWidget\": [\n\t\t\"Die Anzahl der Cursor wurde auf {0} beschränkt. Erwägen Sie die Verwendung von [Suchen und Ersetzen](https://code.visualstudio.com/docs/editor/codebasics#_find-und-ersetzen) für größere Änderungen, oder erhöhen Sie die Multicursorbegrenzungseinstellung des Editors.\",\n\t\t\"Erhöhen des Grenzwerts für mehrere Cursor\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/accessibleDiffViewer\": [\n\t\t\"Symbol für \\\"Einfügen\\\" im barrierefreien Diff-Viewer.\",\n\t\t\"Symbol für \\\"Entfernen\\\" im barrierefreien Diff-Viewer.\",\n\t\t\"Symbol für \\\"Schließen\\\" im barrierefreien Diff-Viewer.\",\n\t\t\"Schließen\",\n\t\t\"Barrierefreier Diff-Viewer. Verwenden Sie den Pfeil nach oben und unten, um zu navigieren.\",\n\t\t\"keine geänderten Zeilen\",\n\t\t\"1 Zeile geändert\",\n\t\t\"{0} Zeilen geändert\",\n\t\t\"Unterschied {0} von {1}: ursprüngliche Zeile {2}, {3}, geänderte Zeile {4}, {5}\",\n\t\t\"leer\",\n\t\t\"{0}: unveränderte Zeile {1}\",\n\t\t\"{0} ursprüngliche Zeile {1} geänderte Zeile {2}\",\n\t\t\"+ {0} geänderte Zeile(n) {1}\",\n\t\t\"– {0} Originalzeile {1}\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/colors\": [\n\t\t\"Die Rahmenfarbe für Text, der im Diff-Editor verschoben wurde.\",\n\t\t\"Die aktive Rahmenfarbe für Text, der im Diff-Editor verschoben wurde.\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/decorations\": [\n\t\t\"Zeilenformatierung für Einfügungen im Diff-Editor\",\n\t\t\"Zeilenformatierung für Entfernungen im Diff-Editor\",\n\t\t\"Klicken Sie, um die Änderung rückgängig zu machen\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/diffEditor.contribution\": [\n\t\t\"\\\"Unveränderte Bereiche reduzieren\\\" umschalten\",\n\t\t\"\\\"Verschobene Codeblöcke anzeigen\\\" umschalten\",\n\t\t\"\\\"Bei eingeschränktem Speicherplatz Inlineansicht verwenden\\\" umschalten\",\n\t\t\"Bei eingeschränktem Speicherplatz Inlineansicht verwenden\",\n\t\t\"Verschobene Codeblöcke anzeigen\",\n\t\t\"Diff-Editor\",\n\t\t\"Seite wechseln\",\n\t\t\"Vergleichsmodus beenden\",\n\t\t\"Alle unveränderten Regionen reduzieren\",\n\t\t\"Alle unveränderten Regionen anzeigen\",\n\t\t\"Barrierefreier Diff-Viewer\",\n\t\t\"Zum nächsten Unterschied wechseln\",\n\t\t\"Barrierefreien Diff-Viewer öffnen\",\n\t\t\"Zum vorherigen Unterschied wechseln\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/diffEditorEditors\": [\n\t\t\" verwenden Sie {0}, um die Hilfe zur Barrierefreiheit zu öffnen.\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/hideUnchangedRegionsFeature\": [\n\t\t\"Unveränderten Bereich falten\",\n\t\t\"Klicken oder ziehen Sie, um oben mehr anzuzeigen.\",\n\t\t\"Alle anzeigen\",\n\t\t\"Klicken oder ziehen Sie, um unten mehr anzuzeigen.\",\n\t\t\"{0} ausgeblendete Linien\",\n\t\t\"Zum Auffalten doppelklicken\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/inlineDiffDeletedCodeMargin\": [\n\t\t\"Gelöschte Zeilen kopieren\",\n\t\t\"Gelöschte Zeile kopieren\",\n\t\t\"Geänderte Zeilen kopieren\",\n\t\t\"Geänderte Zeile kopieren\",\n\t\t\"Gelöschte Zeile kopieren ({0})\",\n\t\t\"Geänderte Zeile ({0}) kopieren\",\n\t\t\"Diese Änderung rückgängig machen\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/movedBlocksLines\": [\n\t\t\"Code mit Änderungen in Zeile {0}-{1} verschoben\",\n\t\t\"Code mit Änderungen aus Zeile {0}-{1} verschoben\",\n\t\t\"Code in Zeile {0}-{1} verschoben\",\n\t\t\"Code aus Zeile {0}-{1} verschoben\",\n\t],\n\t\"vs/editor/common/config/editorConfigurationSchema\": [\n\t\t\"Editor\",\n\t\t\"Die Anzahl der Leerzeichen, denen ein Tabstopp entspricht. Diese Einstellung wird basierend auf dem Inhalt der Datei überschrieben, wenn {0} aktiviert ist.\",\n\t\t\"Die Anzahl von Leerzeichen, die für den Einzug oder „tabSize“ verwendet werden, um den Wert aus „#editor.tabSize#“ zu verwenden. Diese Einstellung wird basierend auf dem Dateiinhalt überschrieben, wenn „#editor.detectIndentation#“ aktiviert ist.\",\n\t\t\"Fügt beim Drücken der TAB-Taste Leerzeichen ein. Diese Einstellung wird basierend auf dem Inhalt der Datei überschrieben, wenn {0} aktiviert ist.\",\n\t\t\"Steuert, ob {0} und {1} automatisch erkannt werden, wenn eine Datei basierend auf dem Dateiinhalt geöffnet wird.\",\n\t\t\"Nachfolgende automatisch eingefügte Leerzeichen entfernen\",\n\t\t\"Spezielle Behandlung für große Dateien zum Deaktivieren bestimmter speicherintensiver Funktionen.\",\n\t\t\"Steuert, ob Vervollständigungen auf Grundlage der Wörter im Dokument berechnet werden sollen.\",\n\t\t\"Nur Wörter aus dem aktiven Dokument vorschlagen\",\n\t\t\"Wörter aus allen geöffneten Dokumenten derselben Sprache vorschlagen\",\n\t\t\"Wörter aus allen geöffneten Dokumenten vorschlagen\",\n\t\t\"Steuert, aus welchen Dokumenten wortbasierte Vervollständigungen berechnet werden.\",\n\t\t\"Die semantische Hervorhebung ist für alle Farbdesigns aktiviert.\",\n\t\t\"Die semantische Hervorhebung ist für alle Farbdesigns deaktiviert.\",\n\t\t\"Die semantische Hervorhebung wird durch die Einstellung \\\"semanticHighlighting\\\" des aktuellen Farbdesigns konfiguriert.\",\n\t\t\"Steuert, ob die semantische Hervorhebung für die Sprachen angezeigt wird, die sie unterstützen.\",\n\t\t\"Lassen Sie Peek-Editoren geöffnet, auch wenn Sie auf ihren Inhalt doppelklicken oder auf die ESCAPETASTE klicken.\",\n\t\t\"Zeilen, die diese Länge überschreiten, werden aus Leistungsgründen nicht tokenisiert\",\n\t\t\"Steuert, ob die Tokenisierung asynchron auf einem Webworker erfolgen soll.\",\n\t\t\"Steuert, ob die asynchrone Tokenisierung protokolliert werden soll. Nur zum Debuggen.\",\n\t\t\"Steuert, ob die asynchrone Tokenisierung anhand der Legacy-Hintergrundtokenisierung überprüft werden soll. Die Tokenisierung kann verlangsamt werden. Nur zum Debuggen.\",\n\t\t\"Definiert die Klammersymbole, die den Einzug vergrößern oder verkleinern.\",\n\t\t\"Das öffnende Klammerzeichen oder die Zeichenfolgensequenz.\",\n\t\t\"Das schließende Klammerzeichen oder die Zeichenfolgensequenz.\",\n\t\t\"Definiert die Klammerpaare, die durch ihre Schachtelungsebene farbig formatiert werden, wenn die Farbgebung für das Klammerpaar aktiviert ist.\",\n\t\t\"Das öffnende Klammerzeichen oder die Zeichenfolgensequenz.\",\n\t\t\"Das schließende Klammerzeichen oder die Zeichenfolgensequenz.\",\n\t\t\"Timeout in Millisekunden, nach dem die Diff-Berechnung abgebrochen wird. Bei 0 wird kein Timeout verwendet.\",\n\t\t\"Maximale Dateigröße in MB, für die Diffs berechnet werden sollen. Verwenden Sie 0, um keinen Grenzwert zu setzen.\",\n\t\t\"Steuert, ob der Diff-Editor die Unterschiede nebeneinander oder im Text anzeigt.\",\n\t\t\"Wenn die Breite des Diff-Editors unter diesem Wert liegt, wird die Inlineansicht verwendet.\",\n\t\t\"Wenn diese Option aktiviert ist und die Breite des Editors nicht ausreicht, wird die Inlineansicht verwendet.\",\n\t\t\"Wenn diese Option aktiviert ist, zeigt der Diff-Editor Pfeile in seinem Glyphenrand an, um Änderungen rückgängig zu machen.\",\n\t\t\"Wenn aktiviert, ignoriert der Diff-Editor Änderungen an voran- oder nachgestellten Leerzeichen.\",\n\t\t\"Steuert, ob der Diff-Editor die Indikatoren \\\"+\\\" und \\\"-\\\" für hinzugefügte/entfernte Änderungen anzeigt.\",\n\t\t\"Steuert, ob der Editor CodeLens anzeigt.\",\n\t\t\"Zeilenumbrüche erfolgen nie.\",\n\t\t\"Der Zeilenumbruch erfolgt an der Breite des Anzeigebereichs.\",\n\t\t\"Zeilen werden gemäß der Einstellung „{0}“ umbrochen.\",\n\t\t\"Verwendet den Legacyvergleichsalgorithmus.\",\n\t\t\"Verwendet den erweiterten Vergleichsalgorithmus.\",\n\t\t\"Steuert, ob der Diff-Editor unveränderte Regionen anzeigt.\",\n\t\t\"Steuert, wie viele Zeilen für unveränderte Regionen verwendet werden.\",\n\t\t\"Steuert, wie viele Zeilen als Mindestwert für unveränderte Regionen verwendet werden.\",\n\t\t\"Steuert, wie viele Zeilen beim Vergleich unveränderter Regionen als Kontext verwendet werden.\",\n\t\t\"Steuert, ob der Diff-Editor erkannte Codeverschiebevorgänge anzeigen soll.\",\n\t\t\"Steuert, ob der diff-Editor leere Dekorationen anzeigt, um anzuzeigen, wo Zeichen eingefügt oder gelöscht wurden.\",\n\t],\n\t\"vs/editor/common/config/editorOptions\": [\n\t\t\"Plattform-APIs verwenden, um zu erkennen, wenn eine Sprachausgabe angefügt ist\",\n\t\t\"Für die Verwendung mit einer Sprachausgabe optimieren\",\n\t\t\"Annehmen, dass keine Sprachausgabe angefügt ist\",\n\t\t\"Steuert, ob die Benutzeroberfläche in einem Modus ausgeführt werden soll, in dem sie für Sprachausgaben optimiert ist.\",\n\t\t\"Steuert, ob beim Kommentieren ein Leerzeichen eingefügt wird.\",\n\t\t\"Steuert, ob leere Zeilen bei Umschalt-, Hinzufügungs- oder Entfernungsaktionen für Zeilenkommentare ignoriert werden sollen.\",\n\t\t\"Steuert, ob ein Kopiervorgang ohne Auswahl die aktuelle Zeile kopiert.\",\n\t\t\"Steuert, ob der Cursor bei der Suche nach Übereinstimmungen während der Eingabe springt.\",\n\t\t\"Suchzeichenfolge niemals aus der Editorauswahl seeden.\",\n\t\t\"Suchzeichenfolge immer aus der Editorauswahl seeden, einschließlich Wort an Cursorposition.\",\n\t\t\"Suchzeichenfolge nur aus der Editorauswahl seeden.\",\n\t\t\"Steuert, ob für die Suchzeichenfolge im Widget \\\"Suche\\\" ein Seeding aus der Auswahl des Editors ausgeführt wird.\",\n\t\t\"\\\"In Auswahl suchen\\\" niemals automatisch aktivieren (Standard).\",\n\t\t\"\\\"In Auswahl suchen\\\" immer automatisch aktivieren.\",\n\t\t\"\\\"In Auswahl suchen\\\" automatisch aktivieren, wenn mehrere Inhaltszeilen ausgewählt sind.\",\n\t\t\"Steuert die Bedingung zum automatischen Aktivieren von \\\"In Auswahl suchen\\\".\",\n\t\t\"Steuert, ob das Widget \\\"Suche\\\" die freigegebene Suchzwischenablage unter macOS lesen oder bearbeiten soll.\",\n\t\t\"Steuert, ob das Suchwidget zusätzliche Zeilen im oberen Bereich des Editors hinzufügen soll. Wenn die Option auf \\\"true\\\" festgelegt ist, können Sie über die erste Zeile hinaus scrollen, wenn das Suchwidget angezeigt wird.\",\n\t\t\"Steuert, ob die Suche automatisch am Anfang (oder am Ende) neu gestartet wird, wenn keine weiteren Übereinstimmungen gefunden werden.\",\n\t\t\"Hiermit werden Schriftligaturen (Schriftartfeatures \\\"calt\\\" und \\\"liga\\\") aktiviert/deaktiviert. Ändern Sie diesen Wert in eine Zeichenfolge, um die CSS-Eigenschaft \\\"font-feature-settings\\\" detailliert zu steuern.\",\n\t\t\"Explizite CSS-Eigenschaft \\\"font-feature-settings\\\". Stattdessen kann ein boolescher Wert übergeben werden, wenn nur Ligaturen aktiviert/deaktiviert werden müssen.\",\n\t\t\"Hiermit werden Schriftligaturen oder Schriftartfeatures konfiguriert. Hierbei kann es sich entweder um einen booleschen Wert zum Aktivieren oder Deaktivieren von Ligaturen oder um eine Zeichenfolge für den Wert der CSS-Eigenschaft \\\"font-feature-settings\\\" handeln.\",\n\t\t\"Aktiviert/deaktiviert die Übersetzung von „font-weight“ in „font-variation-settings“. Ändern Sie dies in eine Zeichenfolge für eine differenzierte Steuerung der CSS-Eigenschaft „font-variation-settings“.\",\n\t\t\"Explizite CSS-Eigenschaft „font-variation-settings“. Stattdessen kann ein boolescher Wert eingeben werden, wenn nur „font-weight“ in „font-variation-settings“ übersetzt werden muss.\",\n\t\t\"Konfiguriert Variationen der Schriftart. Kann entweder ein boolescher Wert zum Aktivieren/Deaktivieren der Übersetzung von „font-weight“ in „font-variation-settings“ oder eine Zeichenfolge für den Wert der CSS-Eigenschaft „font-variation-settings“ sein.\",\n\t\t\"Legt die Schriftgröße in Pixeln fest.\",\n\t\t\"Es sind nur die Schlüsselwörter \\\"normal\\\" und \\\"bold\\\" sowie Zahlen zwischen 1 und 1000 zulässig.\",\n\t\t\"Steuert die Schriftbreite. Akzeptiert die Schlüsselwörter \\\"normal\\\" und \\\"bold\\\" sowie Zahlen zwischen 1 und 1000.\",\n\t\t\"Vorschauansicht der Ergebnisse anzeigen (Standardeinstellung)\",\n\t\t\"Zum Hauptergebnis gehen und Vorschauansicht anzeigen\",\n\t\t\"Wechseln Sie zum primären Ergebnis, und aktivieren Sie die Navigation ohne Vorschau zu anderen Ergebnissen.\",\n\t\t\"Diese Einstellung ist veraltet. Verwenden Sie stattdessen separate Einstellungen wie \\\"editor.editor.gotoLocation.multipleDefinitions\\\" oder \\\"editor.editor.gotoLocation.multipleImplementations\\\".\",\n\t\t\"Legt das Verhalten des Befehls \\\"Gehe zu Definition\\\" fest, wenn mehrere Zielpositionen vorhanden sind\",\n\t\t\"Legt das Verhalten des Befehls \\\"Gehe zur Typdefinition\\\" fest, wenn mehrere Zielpositionen vorhanden sind.\",\n\t\t\"Legt das Verhalten des Befehls \\\"Gehe zu Deklaration\\\" fest, wenn mehrere Zielpositionen vorhanden sind.\",\n\t\t\"Legt das Verhalten des Befehls \\\"Gehe zu Implementierungen\\\", wenn mehrere Zielspeicherorte vorhanden sind\",\n\t\t\"Legt das Verhalten des Befehls \\\"Gehe zu Verweisen\\\" fest, wenn mehrere Zielpositionen vorhanden sind\",\n\t\t\"Die alternative Befehls-ID, die ausgeführt wird, wenn das Ergebnis von \\\"Gehe zu Definition\\\" die aktuelle Position ist.\",\n\t\t\"Die alternative Befehls-ID, die ausgeführt wird, wenn das Ergebnis von \\\"Gehe zu Typdefinition\\\" die aktuelle Position ist.\",\n\t\t\"Die alternative Befehls-ID, die ausgeführt wird, wenn das Ergebnis von \\\"Gehe zu Deklaration\\\" der aktuelle Speicherort ist.\",\n\t\t\"Die alternative Befehls-ID, die ausgeführt wird, wenn das Ergebnis von \\\"Gehe zu Implementatierung\\\" der aktuelle Speicherort ist.\",\n\t\t\"Die alternative Befehls-ID, die ausgeführt wird, wenn das Ergebnis von \\\"Gehe zu Verweis\\\" die aktuelle Position ist.\",\n\t\t\"Steuert, ob die Hovermarkierung angezeigt wird.\",\n\t\t\"Steuert die Verzögerung in Millisekunden, nach der die Hovermarkierung angezeigt wird.\",\n\t\t\"Steuert, ob die Hovermarkierung sichtbar bleiben soll, wenn der Mauszeiger darüber bewegt wird.\",\n\t\t\"Steuert die Verzögerung in Millisekunden, nach der die Hovermarkierung ausgeblendet wird. Erfordert die Aktivierung von \\\"editor.hover.sticky\\\".\",\n\t\t\"Zeigen Sie den Mauszeiger lieber über der Linie an, wenn Platz vorhanden ist.\",\n\t\t\"Es wird angenommen, dass alle Zeichen gleich breit sind. Dies ist ein schneller Algorithmus, der für Festbreitenschriftarten und bestimmte Alphabete (wie dem lateinischen), bei denen die Glyphen gleich breit sind, korrekt funktioniert.\",\n\t\t\"Delegiert die Berechnung von Umbruchpunkten an den Browser. Dies ist ein langsamer Algorithmus, der bei großen Dateien Code Freezes verursachen kann, aber in allen Fällen korrekt funktioniert.\",\n\t\t\"Steuert den Algorithmus, der Umbruchpunkte berechnet. Beachten Sie, dass \\\"advanced\\\" im Barrierefreiheitsmodus für eine optimale Benutzererfahrung verwendet wird.\",\n\t\t\"Aktiviert das Glühlampensymbol für Codeaktionen im Editor.\",\n\t\t\"Zeigt die geschachtelten aktuellen Bereiche während des Bildlaufs am oberen Rand des Editors an.\",\n\t\t\"Definiert die maximale Anzahl fixierter Zeilen, die angezeigt werden sollen.\",\n\t\t\"Legt das Modell fest, das zur Bestimmung der zu fixierenden Zeilen verwendet wird. Existiert das Gliederungsmodell nicht, wird auf das Modell des Folding Providers zurückgegriffen, der wiederum auf das Einrückungsmodell zurückgreift. Diese Reihenfolge wird in allen drei Fällen beachtet.\",\n\t\t\"Aktiviert das Scrollen des Widgets für den fixierten Bildlauf mit der horizontalen Scrollleiste des Editors.\",\n\t\t\"Aktiviert die Inlay-Hinweise im Editor.\",\n\t\t\"Inlay-Hinweise sind aktiviert\",\n\t\t\"Inlay-Hinweise werden standardmäßig angezeigt und ausgeblendet, wenn Sie {0} gedrückt halten\",\n\t\t\"Inlayhinweise sind standardmäßig ausgeblendet. Sie werden angezeigt, wenn {0} gedrückt gehalten wird.\",\n\t\t\"Inlay-Hinweise sind deaktiviert\",\n\t\t\"Steuert den Schriftgrad von Einlapphinweisen im Editor. Standardmäßig wird die {0} verwendet, wenn der konfigurierte Wert kleiner als {1} oder größer als der Schriftgrad des Editors ist.\",\n\t\t\"Steuert die Schriftartfamilie von Einlapphinweisen im Editor. Bei Festlegung auf \\\"leer\\\" wird die {0} verwendet.\",\n\t\t\"Aktiviert den Abstand um die Inlay-Hinweise im Editor.\",\n\t\t\"Steuert die Zeilenhöhe. \\r\\n – Verwenden Sie 0, um die Zeilenhöhe automatisch anhand des Schriftgrads zu berechnen.\\r\\n – Werte zwischen 0 und 8 werden als Multiplikator mit dem Schriftgrad verwendet.\\r\\n – Werte größer oder gleich 8 werden als effektive Werte verwendet.\",\n\t\t\"Steuert, ob die Minimap angezeigt wird.\",\n\t\t\"Steuert, ob die Minimap automatisch ausgeblendet wird.\",\n\t\t\"Die Minimap hat die gleiche Größe wie der Editor-Inhalt (und kann scrollen).\",\n\t\t\"Die Minimap wird bei Bedarf vergrößert oder verkleinert, um die Höhe des Editors zu füllen (kein Scrollen).\",\n\t\t\"Die Minimap wird bei Bedarf verkleinert, damit sie nicht größer als der Editor ist (kein Scrollen).\",\n\t\t\"Legt die Größe der Minimap fest.\",\n\t\t\"Steuert die Seite, wo die Minimap gerendert wird.\",\n\t\t\"Steuert, wann der Schieberegler für die Minimap angezeigt wird.\",\n\t\t\"Maßstab des in der Minimap gezeichneten Inhalts: 1, 2 oder 3.\",\n\t\t\"Die tatsächlichen Zeichen in einer Zeile rendern im Gegensatz zu Farbblöcken.\",\n\t\t\"Begrenzen Sie die Breite der Minimap, um nur eine bestimmte Anzahl von Spalten zu rendern.\",\n\t\t\"Steuert den Abstand zwischen dem oberen Rand des Editors und der ersten Zeile.\",\n\t\t\"Steuert den Abstand zwischen dem unteren Rand des Editors und der letzten Zeile.\",\n\t\t\"Aktiviert ein Pop-up, das Dokumentation und Typ eines Parameters anzeigt während Sie tippen.\",\n\t\t\"Steuert, ob das Menü mit Parameterhinweisen zyklisch ist oder sich am Ende der Liste schließt.\",\n\t\t\"Schnelle Vorschläge werden im Vorschlagswidget angezeigt\",\n\t\t\"Schnelle Vorschläge werden als inaktiver Text angezeigt\",\n\t\t\"Schnelle Vorschläge sind deaktiviert\",\n\t\t\"Schnellvorschläge innerhalb von Zeichenfolgen aktivieren.\",\n\t\t\"Schnellvorschläge innerhalb von Kommentaren aktivieren.\",\n\t\t\"Schnellvorschläge außerhalb von Zeichenfolgen und Kommentaren aktivieren.\",\n\t\t\"Steuert, ob Vorschläge während des Tippens automatisch angezeigt werden sollen. Dies kann bei der Eingabe von Kommentaren, Zeichenketten und anderem Code kontrolliert werden. Schnellvorschläge können so konfiguriert werden, dass sie als Geistertext oder mit dem Vorschlags-Widget angezeigt werden. Beachten Sie auch die \\'{0}\\'-Einstellung, die steuert, ob Vorschläge durch Sonderzeichen ausgelöst werden.\",\n\t\t\"Zeilennummern werden nicht dargestellt.\",\n\t\t\"Zeilennummern werden als absolute Zahl dargestellt.\",\n\t\t\"Zeilennummern werden als Abstand in Zeilen an Cursorposition dargestellt.\",\n\t\t\"Zeilennummern werden alle 10 Zeilen dargestellt.\",\n\t\t\"Steuert die Anzeige von Zeilennummern.\",\n\t\t\"Anzahl der Zeichen aus Festbreitenschriftarten, ab der dieses Editor-Lineal gerendert wird.\",\n\t\t\"Farbe dieses Editor-Lineals.\",\n\t\t\"Vertikale Linien nach einer bestimmten Anzahl von Monospacezeichen rendern. Verwenden Sie mehrere Werte für mehrere Linien. Wenn das Array leer ist, werden keine Linien gerendert.\",\n\t\t\"Die vertikale Bildlaufleiste wird nur bei Bedarf angezeigt.\",\n\t\t\"Die vertikale Bildlaufleiste ist immer sichtbar.\",\n\t\t\"Die vertikale Bildlaufleiste wird immer ausgeblendet.\",\n\t\t\"Steuert die Sichtbarkeit der vertikalen Bildlaufleiste.\",\n\t\t\"Die horizontale Bildlaufleiste wird nur bei Bedarf angezeigt.\",\n\t\t\"Die horizontale Bildlaufleiste ist immer sichtbar.\",\n\t\t\"Die horizontale Bildlaufleiste wird immer ausgeblendet.\",\n\t\t\"Steuert die Sichtbarkeit der horizontalen Bildlaufleiste.\",\n\t\t\"Die Breite der vertikalen Bildlaufleiste.\",\n\t\t\"Die Höhe der horizontalen Bildlaufleiste.\",\n\t\t\"Steuert, ob Klicks nach Seite scrollen oder zur Klickposition springen.\",\n\t\t\"Legt fest, ob alle nicht einfachen ASCII-Zeichen hervorgehoben werden. Nur Zeichen zwischen U+0020 und U+007E, Tabulator, Zeilenvorschub und Wagenrücklauf gelten als einfache ASCII-Zeichen.\",\n\t\t\"Legt fest, ob Zeichen, die nur als Platzhalter dienen oder überhaupt keine Breite haben, hervorgehoben werden.\",\n\t\t\"Legt fest, ob Zeichen hervorgehoben werden, die mit einfachen ASCII-Zeichen verwechselt werden können, mit Ausnahme derjenigen, die im aktuellen Gebietsschema des Benutzers üblich sind.\",\n\t\t\"Steuert, ob Zeichen in Kommentaren auch mit Unicode-Hervorhebung versehen werden sollen.\",\n\t\t\"Steuert, ob Zeichen in Zeichenfolgen auch mit Unicode-Hervorhebung versehen werden sollen.\",\n\t\t\"Definiert zulässige Zeichen, die nicht hervorgehoben werden.\",\n\t\t\"Unicodezeichen, die in zulässigen Gebietsschemas üblich sind, werden nicht hervorgehoben.\",\n\t\t\"Steuert, ob Inline-Vorschläge automatisch im Editor angezeigt werden.\",\n\t\t\"Die Symbolleiste „Inline-Vorschlag“ anzeigen, wenn ein Inline-Vorschlag angezeigt wird.\",\n\t\t\"Die Symbolleiste „Inline-Vorschlag“ anzeigen, wenn Sie mit dem Mauszeiger auf einen Inline-Vorschlag zeigen.\",\n\t\t\"Steuert, wann die Inlinevorschlagssymbolleiste angezeigt werden soll.\",\n\t\t\"Steuert, wie Inlinevorschläge mit dem Vorschlagswidget interagieren. Wenn diese Option aktiviert ist, wird das Vorschlagswidget nicht automatisch angezeigt, wenn Inlinevorschläge verfügbar sind.\",\n\t\t\"Steuert, ob die Klammerpaar-Farbgebung aktiviert ist oder nicht. Verwenden Sie {0}, um die Hervorhebungsfarben der Klammer zu überschreiben.\",\n\t\t\"Steuert, ob jeder Klammertyp über einen eigenen unabhängigen Farbpool verfügt.\",\n\t\t\"Aktiviert Klammernpaarführungslinien.\",\n\t\t\"Aktiviert Klammernpaarführungslinien nur für das aktive Klammerpaar.\",\n\t\t\"Deaktiviert Klammernpaarführungslinien.\",\n\t\t\"Steuert, ob Führungslinien für Klammerpaare aktiviert sind oder nicht.\",\n\t\t\"Aktiviert horizontale Führungslinien als Ergänzung zu vertikalen Klammernpaarführungslinien.\",\n\t\t\"Aktiviert horizontale Führungslinien nur für das aktive Klammerpaar.\",\n\t\t\"Deaktiviert horizontale Führungslinien für Klammernpaare.\",\n\t\t\"Steuert, ob horizontale Führungslinien für Klammernpaare aktiviert sind oder nicht.\",\n\t\t\"Steuert, ob der Editor das aktive Klammerpaar hervorheben soll.\",\n\t\t\"Steuert, ob der Editor Einzugsführungslinien rendern soll.\",\n\t\t\"Hebt die aktive Einzugsführung hervor.\",\n\t\t\"Hebt die aktive Einzugshilfslinie hervor, selbst wenn Klammerhilfslinien hervorgehoben sind.\",\n\t\t\"Heben Sie die aktive Einzugshilfslinie nicht hervor.\",\n\t\t\"Steuert, ob der Editor die aktive Einzugsführungslinie hevorheben soll.\",\n\t\t\"Vorschlag einfügen, ohne den Text auf der rechten Seite des Cursors zu überschreiben\",\n\t\t\"Vorschlag einfügen und Text auf der rechten Seite des Cursors überschreiben\",\n\t\t\"Legt fest, ob Wörter beim Akzeptieren von Vervollständigungen überschrieben werden. Beachten Sie, dass dies von Erweiterungen abhängt, die für dieses Features aktiviert sind.\",\n\t\t\"Steuert, ob Filter- und Suchvorschläge geringfügige Tippfehler berücksichtigen.\",\n\t\t\"Steuert, ob bei der Sortierung Wörter priorisiert werden, die in der Nähe des Cursors stehen.\",\n\t\t\"Steuert, ob gespeicherte Vorschlagauswahlen in verschiedenen Arbeitsbereichen und Fenstern gemeinsam verwendet werden (dafür ist \\\"#editor.suggestSelection#\\\" erforderlich).\",\n\t\t\"Wählen Sie immer einen Vorschlag aus, wenn IntelliSense automatisch ausgelöst wird.\",\n\t\t\"Wählen Sie niemals einen Vorschlag aus, wenn IntelliSense automatisch ausgelöst wird.\",\n\t\t\"Wählen Sie einen Vorschlag nur aus, wenn IntelliSense aus einem Triggerzeichen ausgelöst wird.\",\n\t\t\"Wählen Sie einen Vorschlag nur aus, wenn Sie IntelliSense während der Eingabe auslösen.\",\n\t\t\"Steuert, ob ein Vorschlag ausgewählt wird, wenn das Widget angezeigt wird. Beachten Sie, dass dies nur für automatisch ausgelöste Vorschläge gilt (\\\"#editor.quickSuggestions#\\\" und \\\"#editor.suggestOnTriggerCharacters#\\\"), und dass ein Vorschlag immer ausgewählt wird, wenn er explizit aufgerufen wird, z. B. über STRG+LEERTASTE.\",\n\t\t\"Steuert, ob ein aktiver Schnipsel verhindert, dass der Bereich \\\"Schnelle Vorschläge\\\" angezeigt wird.\",\n\t\t\"Steuert, ob Symbole in Vorschlägen ein- oder ausgeblendet werden.\",\n\t\t\"Steuert die Sichtbarkeit der Statusleiste unten im Vorschlagswidget.\",\n\t\t\"Steuert, ob das Ergebnis des Vorschlags im Editor in der Vorschau angezeigt werden soll.\",\n\t\t\"Steuert, ob Vorschlagsdetails inline mit der Bezeichnung oder nur im Detailwidget angezeigt werden.\",\n\t\t\"Diese Einstellung ist veraltet. Die Größe des Vorschlagswidgets kann jetzt geändert werden.\",\n\t\t\"Diese Einstellung ist veraltet. Verwenden Sie stattdessen separate Einstellungen wie \\\"editor.suggest.showKeywords\\\" oder \\\"editor.suggest.showSnippets\\\".\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"method\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"funktions\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"constructor\\\"-Vorschläge an.\",\n\t\t\"Wenn IntelliSense aktiviert ist, werden „veraltete“ Vorschläge angezeigt.\",\n\t\t\"Wenn dies aktiviert ist, erfordert die IntelliSense-Filterung, dass das erste Zeichen mit einem Wortanfang übereinstimmt, z. B. „c“ in „Console“ oder „WebContext“, aber _nicht_ bei „description“. Wenn diese Option deaktiviert ist, zeigt IntelliSense mehr Ergebnisse an, sortiert sie aber weiterhin nach der Übereinstimmungsqualität.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"field\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"variable\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"class\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"struct\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"interface\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"module\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"property\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"event\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"operator\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"unit\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"value\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"constant\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"enum\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"enumMember\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"keyword\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"text\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"color\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"file\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"reference\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"customcolor\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"folder\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"typeParameter\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense \\\"snippet\\\"-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense user-Vorschläge an.\",\n\t\t\"Wenn aktiviert, zeigt IntelliSense issues-Vorschläge an.\",\n\t\t\"Gibt an, ob führende und nachstehende Leerzeichen immer ausgewählt werden sollen.\",\n\t\t\"Gibt an, ob Unterwörter (z. B. \\\"foo\\\" in \\\"fooBar\\\" oder \\\"foo_bar\\\") ausgewählt werden sollen.\",\n\t\t\"Kein Einzug. Umbrochene Zeilen beginnen bei Spalte 1.\",\n\t\t\"Umbrochene Zeilen erhalten den gleichen Einzug wie das übergeordnete Element.\",\n\t\t\"Umbrochene Zeilen erhalten + 1 Einzug auf das übergeordnete Element.\",\n\t\t\"Umgebrochene Zeilen werden im Vergleich zum übergeordneten Element +2 eingerückt.\",\n\t\t\"Steuert die Einrückung der umbrochenen Zeilen.\",\n\t\t\"Steuert, ob Sie eine Datei in einen Editor ziehen und ablegen können, indem Sie die UMSCHALTTASTE gedrückt halten (anstatt die Datei in einem Editor zu öffnen).\",\n\t\t\"Steuert, ob beim Ablegen von Dateien im Editor ein Widget angezeigt wird. Mit diesem Widget können Sie steuern, wie die Datei ablegt wird.\",\n\t\t\"Zeigt das Widget für die Dropdownauswahl an, nachdem eine Datei im Editor abgelegt wurde.\",\n\t\t\"Das Widget für die Ablageauswahl wird nie angezeigt. Stattdessen wird immer der Standardablageanbieter verwendet.\",\n\t\t\"Steuert, ob Sie Inhalte auf unterschiedliche Weise einfügen können.\",\n\t\t\"Steuert, ob beim Einfügen von Inhalt im Editor ein Widget angezeigt wird. Mit diesem Widget können Sie steuern, wie die Datei eingefügt wird.\",\n\t\t\"Das Widget für die Einfügeauswahl anzeigen, nachdem der Inhalt in den Editor eingefügt wurde.\",\n\t\t\"Das Widget für die Einfügeauswahl wird nie angezeigt. Stattdessen wird immer das Standardeinfügeverhalten verwendet.\",\n\t\t\"Steuert, ob Vorschläge über Commitzeichen angenommen werden sollen. In JavaScript kann ein Semikolon (\\\";\\\") beispielsweise ein Commitzeichen sein, das einen Vorschlag annimmt und dieses Zeichen eingibt.\",\n\t\t\"Einen Vorschlag nur mit der EINGABETASTE akzeptieren, wenn dieser eine Änderung am Text vornimmt.\",\n\t\t\"Steuert, ob Vorschläge mit der EINGABETASTE (zusätzlich zur TAB-Taste) akzeptiert werden sollen. Vermeidet Mehrdeutigkeit zwischen dem Einfügen neuer Zeilen oder dem Annehmen von Vorschlägen.\",\n\t\t\"Steuert die Anzahl von Zeilen im Editor, die von einer Sprachausgabe in einem Arbeitsschritt gelesen werden können. Wenn eine Sprachausgabe erkannt wird, wird der Standardwert automatisch auf 500 festgelegt. Warnung: Ein Wert höher als der Standardwert, kann sich auf die Leistung auswirken.\",\n\t\t\"Editor-Inhalt\",\n\t\t\"Steuern Sie, ob Inlinevorschläge von einer Sprachausgabe angekündigt werden.\",\n\t\t\"Verwenden Sie Sprachkonfigurationen, um zu bestimmen, wann Klammern automatisch geschlossen werden sollen.\",\n\t\t\"Schließe Klammern nur automatisch, wenn der Cursor sich links von einem Leerzeichen befindet.\",\n\t\t\"Steuert, ob der Editor automatisch Klammern schließen soll, nachdem der Benutzer eine öffnende Klammer hinzugefügt hat.\",\n\t\t\"Verwenden Sie Sprachkonfigurationen, um festzulegen, wann Kommentare automatisch geschlossen werden sollen.\",\n\t\t\"Kommentare werden nur dann automatisch geschlossen, wenn sich der Cursor links von einem Leerraum befindet.\",\n\t\t\"Steuert, ob der Editor Kommentare automatisch schließen soll, nachdem die Benutzer*innen einen ersten Kommentar hinzugefügt haben.\",\n\t\t\"Angrenzende schließende Anführungszeichen oder Klammern werden nur überschrieben, wenn sie automatisch eingefügt wurden.\",\n\t\t\"Steuert, ob der Editor angrenzende schließende Anführungszeichen oder Klammern beim Löschen entfernen soll.\",\n\t\t\"Schließende Anführungszeichen oder Klammern werden nur überschrieben, wenn sie automatisch eingefügt wurden.\",\n\t\t\"Steuert, ob der Editor schließende Anführungszeichen oder Klammern überschreiben soll.\",\n\t\t\"Verwende die Sprachkonfiguration, um zu ermitteln, wann Anführungsstriche automatisch geschlossen werden.\",\n\t\t\"Schließende Anführungszeichen nur dann automatisch ergänzen, wenn der Cursor sich links von einem Leerzeichen befindet.\",\n\t\t\"Steuert, ob der Editor Anführungszeichen automatisch schließen soll, nachdem der Benutzer ein öffnendes Anführungszeichen hinzugefügt hat.\",\n\t\t\"Der Editor fügt den Einzug nicht automatisch ein.\",\n\t\t\"Der Editor behält den Einzug der aktuellen Zeile bei.\",\n\t\t\"Der Editor behält den in der aktuellen Zeile definierten Einzug bei und beachtet für Sprachen definierte Klammern.\",\n\t\t\"Der Editor behält den Einzug der aktuellen Zeile bei, beachtet von Sprachen definierte Klammern und ruft spezielle onEnterRules-Regeln auf, die von Sprachen definiert wurden.\",\n\t\t\"Der Editor behält den Einzug der aktuellen Zeile bei, beachtet die von Sprachen definierten Klammern, ruft von Sprachen definierte spezielle onEnterRules-Regeln auf und beachtet von Sprachen definierte indentationRules-Regeln.\",\n\t\t\"Legt fest, ob der Editor den Einzug automatisch anpassen soll, wenn Benutzer Zeilen eingeben, einfügen, verschieben oder einrücken\",\n\t\t\"Sprachkonfigurationen verwenden, um zu bestimmen, wann eine Auswahl automatisch umschlossen werden soll.\",\n\t\t\"Mit Anführungszeichen, nicht mit Klammern umschließen.\",\n\t\t\"Mit Klammern, nicht mit Anführungszeichen umschließen.\",\n\t\t\"Steuert, ob der Editor die Auswahl beim Eingeben von Anführungszeichen oder Klammern automatisch umschließt.\",\n\t\t\"Emuliert das Auswahlverhalten von Tabstoppzeichen, wenn Leerzeichen für den Einzug verwendet werden. Die Auswahl wird an Tabstopps ausgerichtet.\",\n\t\t\"Steuert, ob der Editor CodeLens anzeigt.\",\n\t\t\"Steuert die Schriftfamilie für CodeLens.\",\n\t\t\"Steuert den Schriftgrad in Pixeln für CodeLens. Bei Festlegung auf „0, 90 % von „#editor.fontSize#“ verwendet.\",\n\t\t\"Steuert, ob der Editor die Inline-Farbdecorators und die Farbauswahl rendern soll.\",\n\t\t\"Farbauswahl sowohl beim Klicken als auch beim Daraufzeigen des Farbdekorators anzeigen\",\n\t\t\"Farbauswahl beim Draufzeigen auf den Farbdekorator anzeigen\",\n\t\t\"Farbauswahl beim Klicken auf den Farbdekorator anzeigen\",\n\t\t\"Steuert die Bedingung, damit eine Farbauswahl aus einem Farbdekorator angezeigt wird.\",\n\t\t\"Steuert die maximale Anzahl von Farb-Decorators, die in einem Editor gleichzeitig gerendert werden können.\",\n\t\t\"Zulassen, dass die Auswahl per Maus und Tasten die Spaltenauswahl durchführt.\",\n\t\t\"Steuert, ob Syntax-Highlighting in die Zwischenablage kopiert wird.\",\n\t\t\"Steuert den Cursoranimationsstil.\",\n\t\t\"Die Smooth Caret-Animation ist deaktiviert.\",\n\t\t\"Die Smooth Caret-Animation ist nur aktiviert, wenn der Benutzer den Cursor mit einer expliziten Geste bewegt.\",\n\t\t\"Die Smooth Caret-Animation ist immer aktiviert.\",\n\t\t\"Steuert, ob die weiche Cursoranimation aktiviert werden soll.\",\n\t\t\"Steuert den Cursor-Stil.\",\n\t\t\"Steuert die Mindestanzahl sichtbarer führender Zeilen (mindestens 0) und nachfolgender Zeilen (mindestens 1) um den Cursor. Dies wird in einigen anderen Editoren als „scrollOff“ oder „scrollOffset“ bezeichnet.\",\n\t\t\"\\\"cursorSurroundingLines\\\" wird nur erzwungen, wenn die Auslösung über die Tastatur oder API erfolgt.\",\n\t\t\"\\\"cursorSurroundingLines\\\" wird immer erzwungen.\",\n\t\t\"Steuert, wann \\\"#cursorSurroundingLines#\\\" erzwungen werden soll.\",\n\t\t\"Steuert die Breite des Cursors, wenn `#editor.cursorStyle#` auf `line` festgelegt ist.\",\n\t\t\"Steuert, ob der Editor das Verschieben einer Auswahl per Drag and Drop zulässt.\",\n\t\t\"Verwenden Sie eine neue Rendering-Methode mit SVGs.\",\n\t\t\"Verwenden Sie eine neue Rendering-Methode mit Schriftartzeichen.\",\n\t\t\"Verwenden Sie die stabile Rendering-Methode.\",\n\t\t\"Steuert, ob Leerzeichen mit einer neuen experimentellen Methode gerendert werden.\",\n\t\t\"Multiplikator für Scrollgeschwindigkeit bei Drücken von ALT.\",\n\t\t\"Steuert, ob Codefaltung im Editor aktiviert ist.\",\n\t\t\"Verwenden Sie eine sprachspezifische Faltstrategie, falls verfügbar. Andernfalls wird eine einzugsbasierte verwendet.\",\n\t\t\"Einzugsbasierte Faltstrategie verwenden.\",\n\t\t\"Steuert die Strategie für die Berechnung von Faltbereichen.\",\n\t\t\"Steuert, ob der Editor eingefaltete Bereiche hervorheben soll.\",\n\t\t\"Steuert, ob der Editor Importbereiche automatisch reduziert.\",\n\t\t\"Die maximale Anzahl von faltbaren Regionen. Eine Erhöhung dieses Werts kann dazu führen, dass der Editor weniger reaktionsfähig wird, wenn die aktuelle Quelle eine große Anzahl von faltbaren Regionen aufweist.\",\n\t\t\"Steuert, ob eine Zeile aufgefaltet wird, wenn nach einer gefalteten Zeile auf den leeren Inhalt geklickt wird.\",\n\t\t\"Steuert die Schriftfamilie.\",\n\t\t\"Steuert, ob der Editor den eingefügten Inhalt automatisch formatieren soll. Es muss ein Formatierer vorhanden sein, der in der Lage ist, auch Dokumentbereiche zu formatieren.\",\n\t\t\"Steuert, ob der Editor die Zeile nach der Eingabe automatisch formatieren soll.\",\n\t\t\"Steuert, ob der Editor den vertikalen Glyphenrand rendert. Der Glyphenrand wird hauptsächlich zum Debuggen verwendet.\",\n\t\t\"Steuert, ob der Cursor im Übersichtslineal ausgeblendet werden soll.\",\n\t\t\"Legt den Abstand der Buchstaben in Pixeln fest.\",\n\t\t\"Steuert, ob die verknüpfte Bearbeitung im Editor aktiviert ist. Abhängig von der Sprache werden zugehörige Symbole, z. B. HTML-Tags, während der Bearbeitung aktualisiert.\",\n\t\t\"Steuert, ob der Editor Links erkennen und anklickbar machen soll.\",\n\t\t\"Passende Klammern hervorheben\",\n\t\t\"Ein Multiplikator, der für die Mausrad-Bildlaufereignisse \\\"deltaX\\\" und \\\"deltaY\\\" verwendet werden soll.\",\n\t\t\"Schriftart des Editors vergrößern, wenn das Mausrad verwendet und die STRG-TASTE gedrückt wird.\",\n\t\t\"Mehrere Cursor zusammenführen, wenn sie sich überlappen.\",\n\t\t\"Ist unter Windows und Linux der STRG-Taste und unter macOS der Befehlstaste zugeordnet.\",\n\t\t\"Ist unter Windows und Linux der ALT-Taste und unter macOS der Wahltaste zugeordnet.\",\n\t\t\"Der Modifizierer, der zum Hinzufügen mehrerer Cursor mit der Maus verwendet werden soll. Die Mausgesten \\\"Gehe zu Definition\\\" und \\\"Link öffnen\\\" werden so angepasst, dass sie nicht mit dem [Multicursormodifizierer](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-Modifizierer) in Konflikt stehen.\",\n\t\t\"Jeder Cursor fügt eine Textzeile ein.\",\n\t\t\"Jeder Cursor fügt den vollständigen Text ein.\",\n\t\t\"Steuert das Einfügen, wenn die Zeilenanzahl des Einfügetexts der Cursor-Anzahl entspricht.\",\n\t\t\"Steuert die maximale Anzahl von Cursorn, die sich gleichzeitig in einem aktiven Editor befindet.\",\n\t\t\"Steuert, ob der Editor das Vorkommen semantischer Symbole hervorheben soll.\",\n\t\t\"Steuert, ob um das Übersichtslineal ein Rahmen gezeichnet werden soll.\",\n\t\t\"Struktur beim Öffnen des Peek-Editors fokussieren\",\n\t\t\"Editor fokussieren, wenn Sie den Peek-Editor öffnen\",\n\t\t\"Steuert, ob der Inline-Editor oder die Struktur im Peek-Widget fokussiert werden soll.\",\n\t\t\"Steuert, ob die Mausgeste \\\"Gehe zu Definition\\\" immer das Vorschauwidget öffnet.\",\n\t\t\"Steuert die Verzögerung in Millisekunden nach der Schnellvorschläge angezeigt werden.\",\n\t\t\"Steuert, ob der Editor bei Eingabe automatisch eine Umbenennung vornimmt.\",\n\t\t\"Veraltet. Verwenden Sie stattdessen \\\"editor.linkedEditing\\\".\",\n\t\t\"Steuert, ob der Editor Steuerzeichen rendern soll.\",\n\t\t\"Letzte Zeilennummer rendern, wenn die Datei mit einem Zeilenumbruch endet.\",\n\t\t\"Hebt den Bundsteg und die aktuelle Zeile hervor.\",\n\t\t\"Steuert, wie der Editor die aktuelle Zeilenhervorhebung rendern soll.\",\n\t\t\"Steuert, ob der Editor die aktuelle Zeilenhervorhebung nur dann rendern soll, wenn der Fokus auf dem Editor liegt.\",\n\t\t\"Leerraumzeichen werden gerendert mit Ausnahme der einzelnen Leerzeichen zwischen Wörtern.\",\n\t\t\"Hiermit werden Leerraumzeichen nur für ausgewählten Text gerendert.\",\n\t\t\"Nur nachstehende Leerzeichen rendern\",\n\t\t\"Steuert, wie der Editor Leerzeichen rendern soll.\",\n\t\t\"Steuert, ob eine Auswahl abgerundete Ecken aufweisen soll.\",\n\t\t\"Steuert die Anzahl der zusätzlichen Zeichen, nach denen der Editor horizontal scrollt.\",\n\t\t\"Steuert, ob der Editor jenseits der letzten Zeile scrollen wird.\",\n\t\t\"Nur entlang der vorherrschenden Achse scrollen, wenn gleichzeitig vertikal und horizontal gescrollt wird. Dadurch wird ein horizontaler Versatz beim vertikalen Scrollen auf einem Trackpad verhindert.\",\n\t\t\"Steuert, ob die primäre Linux-Zwischenablage unterstützt werden soll.\",\n\t\t\"Steuert, ob der Editor Übereinstimmungen hervorheben soll, die der Auswahl ähneln.\",\n\t\t\"Steuerelemente für die Codefaltung immer anzeigen.\",\n\t\t\"Zeigen Sie niemals die Faltungssteuerelemente an, und verringern Sie die Größe des Bundstegs.\",\n\t\t\"Steuerelemente für die Codefaltung nur anzeigen, wenn sich die Maus über dem Bundsteg befindet.\",\n\t\t\"Steuert, wann die Steuerungselemente für die Codefaltung am Bundsteg angezeigt werden.\",\n\t\t\"Steuert das Ausblenden von nicht verwendetem Code.\",\n\t\t\"Steuert durchgestrichene veraltete Variablen.\",\n\t\t\"Zeige Schnipselvorschläge über den anderen Vorschlägen.\",\n\t\t\"Schnipselvorschläge unter anderen Vorschlägen anzeigen.\",\n\t\t\"Zeige Schnipselvorschläge mit anderen Vorschlägen.\",\n\t\t\"Keine Schnipselvorschläge anzeigen.\",\n\t\t\"Steuert, ob Codeschnipsel mit anderen Vorschlägen angezeigt und wie diese sortiert werden.\",\n\t\t\"Legt fest, ob der Editor Bildläufe animiert ausführt.\",\n\t\t\"Steuert, ob für Benutzer*innen, die eine Sprachausgabe nutzen, bei Anzeige einer Inlinevervollständigung ein Hinweis zur Barrierefreiheit angezeigt werden soll.\",\n\t\t\"Schriftgrad für das Vorschlagswidget. Bei Festlegung auf {0} wird der Wert von {1} verwendet.\",\n\t\t\"Zeilenhöhe für das Vorschlagswidget. Bei Festlegung auf {0} wird der Wert von {1} verwendet. Der Mindestwert ist 8.\",\n\t\t\"Steuert, ob Vorschläge automatisch angezeigt werden sollen, wenn Triggerzeichen eingegeben werden.\",\n\t\t\"Immer den ersten Vorschlag auswählen.\",\n\t\t\"Wählen Sie die aktuellsten Vorschläge aus, es sei denn, es wird ein Vorschlag durch eine weitere Eingabe ausgewählt, z.B. \\\"console.| -> console.log\\\", weil \\\"log\\\" vor Kurzem abgeschlossen wurde.\",\n\t\t\"Wählen Sie Vorschläge basierend auf früheren Präfixen aus, die diese Vorschläge abgeschlossen haben, z.B. \\\"co -> console\\\" und \\\"con ->\\\" const\\\".\",\n\t\t\"Steuert, wie Vorschläge bei Anzeige der Vorschlagsliste vorab ausgewählt werden.\",\n\t\t\"Die Tab-Vervollständigung fügt den passendsten Vorschlag ein, wenn auf Tab gedrückt wird.\",\n\t\t\"Tab-Vervollständigungen deaktivieren.\",\n\t\t\"Codeschnipsel per Tab vervollständigen, wenn die Präfixe übereinstimmen. Funktioniert am besten, wenn \\\"quickSuggestions\\\" deaktiviert sind.\",\n\t\t\"Tab-Vervollständigungen aktivieren.\",\n\t\t\"Ungewöhnliche Zeilenabschlusszeichen werden automatisch entfernt.\",\n\t\t\"Ungewöhnliche Zeilenabschlusszeichen werden ignoriert.\",\n\t\t\"Zum Entfernen ungewöhnlicher Zeilenabschlusszeichen wird eine Eingabeaufforderung angezeigt.\",\n\t\t\"Entfernen Sie unübliche Zeilenabschlusszeichen, die Probleme verursachen können.\",\n\t\t\"Das Einfügen und Löschen von Leerzeichen erfolgt nach Tabstopps.\",\n\t\t\"Verwenden Sie die Standardregel für Zeilenumbrüche.\",\n\t\t\"Trennstellen dürfen nicht für Texte in Chinesisch/Japanisch/Koreanisch (CJK) verwendet werden. Das Verhalten von Nicht-CJK-Texten ist mit dem für normales Verhalten identisch.\",\n\t\t\"Steuert die Regeln für Trennstellen, die für Texte in Chinesisch/Japanisch/Koreanisch (CJK) verwendet werden.\",\n\t\t\"Zeichen, die als Worttrennzeichen verwendet werden, wenn wortbezogene Navigationen oder Vorgänge ausgeführt werden.\",\n\t\t\"Zeilenumbrüche erfolgen nie.\",\n\t\t\"Der Zeilenumbruch erfolgt an der Breite des Anzeigebereichs.\",\n\t\t\"Der Zeilenumbruch erfolgt bei \\\"#editor.wordWrapColumn#\\\".\",\n\t\t\"Der Zeilenumbruch erfolgt beim Mindestanzeigebereich und \\\"#editor.wordWrapColumn\\\".\",\n\t\t\"Steuert, wie der Zeilenumbruch durchgeführt werden soll.\",\n\t\t\"Steuert die umschließende Spalte des Editors, wenn \\\"#editor.wordWrap#\\\" den Wert \\\"wordWrapColumn\\\" oder \\\"bounded\\\" aufweist.\",\n\t\t\"Steuert, ob Inlinefarbdekorationen mithilfe des Standard-Dokumentfarbanbieters angezeigt werden sollen.\",\n\t\t\"Steuert, ob der Editor Registerkarten empfängt oder zur Navigation zur Workbench zurückgibt.\",\n\t],\n\t\"vs/editor/common/core/editorColorRegistry\": [\n\t\t\"Hintergrundfarbe zur Hervorhebung der Zeile an der Cursorposition.\",\n\t\t\"Hintergrundfarbe für den Rahmen um die Zeile an der Cursorposition.\",\n\t\t\"Hintergrundfarbe der markierten Bereiche, wie z.B. Quick Open oder die Suche. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Hintergrundfarbe für den Rahmen um hervorgehobene Bereiche.\",\n\t\t\"Hintergrundfarbe des hervorgehobenen Symbols, z. B. \\\"Gehe zu Definition\\\" oder \\\"Gehe zu nächster/vorheriger\\\". Die Farbe darf nicht undurchsichtig sein, um zugrunde liegende Dekorationen nicht zu verbergen.\",\n\t\t\"Hintergrundfarbe des Rahmens um hervorgehobene Symbole\",\n\t\t\"Farbe des Cursors im Editor.\",\n\t\t\"Hintergrundfarbe vom Editor-Cursor. Erlaubt die Anpassung der Farbe von einem Zeichen, welches von einem Block-Cursor überdeckt wird.\",\n\t\t\"Farbe der Leerzeichen im Editor.\",\n\t\t\"Zeilennummernfarbe im Editor.\",\n\t\t\"Farbe der Führungslinien für Einzüge im Editor.\",\n\t\t\"\\\"editorIndentGuide.background\\\" ist veraltet. Verwenden Sie stattdessen \\\"editorIndentGuide.background1\\\".\",\n\t\t\"Farbe der Führungslinien für Einzüge im aktiven Editor.\",\n\t\t\"\\\"editorIndentGuide.activeBackground\\\" ist veraltet. Verwenden Sie stattdessen \\\"editorIndentGuide.activeBackground1\\\".\",\n\t\t\"Farbe der Führungslinien für Einzüge im Editor (1).\",\n\t\t\"Farbe der Führungslinien für Einzüge im Editor (2).\",\n\t\t\"Farbe der Führungslinien für Einzüge im Editor (3).\",\n\t\t\"Farbe der Führungslinien für Einzüge im Editor (4).\",\n\t\t\"Farbe der Führungslinien für Einzüge im Editor (5).\",\n\t\t\"Farbe der Führungslinien für Einzüge im Editor (6).\",\n\t\t\"Farbe der Führungslinien für Einzüge im aktiven Editor (1).\",\n\t\t\"Farbe der Führungslinien für Einzüge im aktiven Editor (2).\",\n\t\t\"Farbe der Führungslinien für Einzüge im aktiven Editor (3).\",\n\t\t\"Farbe der Führungslinien für Einzüge im aktiven Editor (4).\",\n\t\t\"Farbe der Führungslinien für Einzüge im aktiven Editor (5).\",\n\t\t\"Farbe der Führungslinien für Einzüge im aktiven Editor (6).\",\n\t\t\"Zeilennummernfarbe der aktiven Editorzeile.\",\n\t\t\"Die ID ist veraltet. Verwenden Sie stattdessen \\\"editorLineNumber.activeForeground\\\".\",\n\t\t\"Zeilennummernfarbe der aktiven Editorzeile.\",\n\t\t\"Die Farbe der letzten Editor-Zeile, wenn „editor.renderFinalNewline“ auf „abgeblendet“ festgelegt ist.\",\n\t\t\"Farbe des Editor-Lineals.\",\n\t\t\"Vordergrundfarbe der CodeLens-Links im Editor\",\n\t\t\"Hintergrundfarbe für zusammengehörige Klammern\",\n\t\t\"Farbe für zusammengehörige Klammern\",\n\t\t\"Farbe des Rahmens für das Übersicht-Lineal.\",\n\t\t\"Hintergrundfarbe des Editor-Übersichtslineals.\",\n\t\t\"Hintergrundfarbe der Editorleiste. Die Leiste enthält die Glyphenränder und die Zeilennummern.\",\n\t\t\"Rahmenfarbe unnötigen (nicht genutzten) Quellcodes im Editor.\",\n\t\t\"Deckkraft des unnötigen (nicht genutzten) Quellcodes im Editor. \\\"#000000c0\\\" rendert z.B. den Code mit einer Deckkraft von 75%. Verwenden Sie für Designs mit hohem Kontrast das Farbdesign \\\"editorUnnecessaryCode.border\\\", um unnötigen Code zu unterstreichen statt ihn abzublenden.\",\n\t\t\"Rahmenfarbe des Ghost-Texts im Editor.\",\n\t\t\"Vordergrundfarbe des Ghost-Texts im Editor.\",\n\t\t\"Hintergrundfarbe des Ghost-Texts im Editor.\",\n\t\t\"Übersichtslinealmarkerfarbe für das Hervorheben von Bereichen. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Übersichtslineal-Markierungsfarbe für Fehler.\",\n\t\t\"Übersichtslineal-Markierungsfarbe für Warnungen.\",\n\t\t\"Übersichtslineal-Markierungsfarbe für Informationen.\",\n\t\t\"Vordergrundfarbe der Klammern (1). Erfordert die Aktivierung der Farbgebung des Klammerpaars.\",\n\t\t\"Vordergrundfarbe der Klammern (2). Erfordert die Aktivierung der Farbgebung des Klammerpaars.\",\n\t\t\"Vordergrundfarbe der Klammern (3). Erfordert die Aktivierung der Farbgebung des Klammerpaars.\",\n\t\t\"Vordergrundfarbe der Klammern (4). Erfordert die Aktivierung der Farbgebung des Klammerpaars.\",\n\t\t\"Vordergrundfarbe der Klammern (5). Erfordert die Aktivierung der Farbgebung des Klammerpaars.\",\n\t\t\"Vordergrundfarbe der Klammern (6). Erfordert die Aktivierung der Farbgebung des Klammerpaars.\",\n\t\t\"Vordergrundfarbe der unerwarteten Klammern.\",\n\t\t\"Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (1). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\n\t\t\"Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (2). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\n\t\t\"Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (3). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\n\t\t\"Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (4). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\n\t\t\"Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (5). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\n\t\t\"Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (6). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\n\t\t\"Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (1). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\n\t\t\"Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (2). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\n\t\t\"Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (3). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\n\t\t\"Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (4). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\n\t\t\"Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (5). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\n\t\t\"Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (6). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.\",\n\t\t\"Rahmenfarbe, die zum Hervorheben von Unicode-Zeichen verwendet wird.\",\n\t\t\"Hintergrundfarbe, die zum Hervorheben von Unicode-Zeichen verwendet wird.\",\n\t],\n\t\"vs/editor/common/editorContextKeys\": [\n\t\t\"Gibt an, ob der Editor-Text den Fokus besitzt (Cursor blinkt).\",\n\t\t\"Gibt an, ob der Editor oder ein Editor-Widget den Fokus besitzt (z. B. ob der Fokus sich im Suchwidget befindet).\",\n\t\t\"Gibt an, ob ein Editor oder eine Rich-Text-Eingabe den Fokus besitzt (Cursor blinkt).\",\n\t\t\"Gibt an, ob der Editor schreibgeschützt ist\",\n\t\t\"Gibt an, ob der Kontext ein Diff-Editor ist.\",\n\t\t\"Gibt an, ob der Kontext ein eingebetteter Diff-Editor ist.\",\n\t\t\"Gibt an, ob ein verschobener Codeblock für den Vergleich ausgewählt wird.\",\n\t\t\"Gibt an, ob der barrierefreie Diff-Viewer sichtbar ist.\",\n\t\t\"Gibt an, ob für den Diff-Editor der Breakpoint für das Rendern im Modus \\\"Parallel\\\" oder \\\"Inline\\\" erreicht wurde.\",\n\t\t\"Gibt an, ob \\\"editor.columnSelection\\\" aktiviert ist.\",\n\t\t\"Gibt an, ob im Editor Text ausgewählt ist.\",\n\t\t\"Gibt an, ob der Editor über Mehrfachauswahl verfügt.\",\n\t\t\"Gibt an, ob die TAB-TASTE den Fokus aus dem Editor verschiebt.\",\n\t\t\"Gibt an, ob Hover im Editor sichtbar ist.\",\n\t\t\"Gibt an, ob Daraufzeigen im Editor fokussiert ist.\",\n\t\t\"Gibt an, ob der Fokus auf dem Fixierten Bildlauf liegt.\",\n\t\t\"Gibt an, ob der Fixierte Bildlauf sichtbar ist.\",\n\t\t\"Gibt an, ob der eigenständige Farbwähler sichtbar ist.\",\n\t\t\"Gibt an, ob der eigenständige Farbwähler fokussiert ist.\",\n\t\t\"Gibt an, ob der Editor Bestandteil eines größeren Editors ist (z. B. Notebooks).\",\n\t\t\"Der Sprachbezeichner des Editors.\",\n\t\t\"Gibt an, ob der Editor über einen Vervollständigungselementanbieter verfügt.\",\n\t\t\"Gibt an, ob der Editor über einen Codeaktionsanbieter verfügt.\",\n\t\t\"Gibt an, ob der Editor über einen CodeLens-Anbieter verfügt.\",\n\t\t\"Gibt an, ob der Editor über einen Definitionsanbieter verfügt.\",\n\t\t\"Gibt an, ob der Editor über einen Deklarationsanbieter verfügt.\",\n\t\t\"Gibt an, ob der Editor über einen Implementierungsanbieter verfügt.\",\n\t\t\"Gibt an, ob der Editor über einen Typdefinitionsanbieter verfügt.\",\n\t\t\"Gibt an, ob der Editor über einen Hoveranbieter verfügt.\",\n\t\t\"Gibt an, ob der Editor über einen Dokumenthervorhebungsanbieter verfügt.\",\n\t\t\"Gibt an, ob der Editor über einen Dokumentsymbolanbieter verfügt.\",\n\t\t\"Gibt an, ob der Editor über einen Verweisanbieter verfügt.\",\n\t\t\"Gibt an, ob der Editor über einen Umbenennungsanbieter verfügt.\",\n\t\t\"Gibt an, ob der Editor über einen Signaturhilfeanbieter verfügt.\",\n\t\t\"Gibt an, ob der Editor über einen Inlinehinweisanbieter verfügt.\",\n\t\t\"Gibt an, ob der Editor über einen Dokumentformatierungsanbieter verfügt.\",\n\t\t\"Gibt an, ob der Editor über einen Anbieter für Dokumentauswahlformatierung verfügt.\",\n\t\t\"Gibt an, ob der Editor über mehrere Dokumentformatierungsanbieter verfügt.\",\n\t\t\"Gibt an, ob der Editor über mehrere Anbieter für Dokumentauswahlformatierung verfügt.\",\n\t],\n\t\"vs/editor/common/languages\": [\n\t\t\"Array\",\n\t\t\"Boolescher Wert\",\n\t\t\"Klasse\",\n\t\t\"Konstante\",\n\t\t\"Konstruktor\",\n\t\t\"Enumeration\",\n\t\t\"Enumerationsmember\",\n\t\t\"Ereignis\",\n\t\t\"Feld\",\n\t\t\"Datei\",\n\t\t\"Funktion\",\n\t\t\"Schnittstelle\",\n\t\t\"Schlüssel\",\n\t\t\"Methode\",\n\t\t\"Modul\",\n\t\t\"Namespace\",\n\t\t\"NULL\",\n\t\t\"Zahl\",\n\t\t\"Objekt\",\n\t\t\"Operator\",\n\t\t\"Paket\",\n\t\t\"Eigenschaft\",\n\t\t\"Zeichenfolge\",\n\t\t\"Struktur\",\n\t\t\"Typparameter\",\n\t\t\"Variable\",\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/editor/common/languages/modesRegistry\": [\n\t\t\"Nur-Text\",\n\t],\n\t\"vs/editor/common/model/editStack\": [\n\t\t\"Eingabe\",\n\t],\n\t\"vs/editor/common/standaloneStrings\": [\n\t\t\"Entwickler: Token überprüfen\",\n\t\t\"Gehe zu Zeile/Spalte...\",\n\t\t\"Alle Anbieter für den Schnellzugriff anzeigen\",\n\t\t\"Befehlspalette\",\n\t\t\"Befehle anzeigen und ausführen\",\n\t\t\"Gehe zu Symbol...\",\n\t\t\"Gehe zu Symbol nach Kategorie...\",\n\t\t\"Editor-Inhalt\",\n\t\t\"Drücken Sie ALT + F1, um die Barrierefreiheitsoptionen aufzurufen.\",\n\t\t\"Zu Design mit hohem Kontrast umschalten\",\n\t\t\"{0} Bearbeitungen in {1} Dateien durchgeführt\",\n\t],\n\t\"vs/editor/common/viewLayout/viewLineRenderer\": [\n\t\t\"Mehr anzeigen ({0})\",\n\t\t\"{0} Zeichen\",\n\t],\n\t\"vs/editor/contrib/anchorSelect/browser/anchorSelect\": [\n\t\t\"Auswahlanker\",\n\t\t\"Anker festgelegt bei \\\"{0}:{1}\\\"\",\n\t\t\"Auswahlanker festlegen\",\n\t\t\"Zu Auswahlanker wechseln\",\n\t\t\"Auswahl von Anker zu Cursor\",\n\t\t\"Auswahlanker abbrechen\",\n\t],\n\t\"vs/editor/contrib/bracketMatching/browser/bracketMatching\": [\n\t\t\"Übersichtslineal-Markierungsfarbe für zusammengehörige Klammern.\",\n\t\t\"Gehe zu Klammer\",\n\t\t\"Auswählen bis Klammer\",\n\t\t\"Klammern entfernen\",\n\t\t\"Gehe zu &&Klammer\",\n\t],\n\t\"vs/editor/contrib/caretOperations/browser/caretOperations\": [\n\t\t\"Ausgewählten Text nach links verschieben\",\n\t\t\"Ausgewählten Text nach rechts verschieben\",\n\t],\n\t\"vs/editor/contrib/caretOperations/browser/transpose\": [\n\t\t\"Buchstaben austauschen\",\n\t],\n\t\"vs/editor/contrib/clipboard/browser/clipboard\": [\n\t\t\"&&Ausschneiden\",\n\t\t\"Ausschneiden\",\n\t\t\"Ausschneiden\",\n\t\t\"Ausschneiden\",\n\t\t\"&&Kopieren\",\n\t\t\"Kopieren\",\n\t\t\"Kopieren\",\n\t\t\"Kopieren\",\n\t\t\"Kopieren als\",\n\t\t\"Kopieren als\",\n\t\t\"Freigeben\",\n\t\t\"Freigeben\",\n\t\t\"Freigeben\",\n\t\t\"&&Einfügen\",\n\t\t\"Einfügen\",\n\t\t\"Einfügen\",\n\t\t\"Einfügen\",\n\t\t\"Mit Syntaxhervorhebung kopieren\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeAction\": [\n\t\t\"Beim Anwenden der Code-Aktion ist ein unbekannter Fehler aufgetreten\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionCommands\": [\n\t\t\"Art der auszuführenden Codeaktion\",\n\t\t\"Legt fest, wann die zurückgegebenen Aktionen angewendet werden\",\n\t\t\"Die erste zurückgegebene Codeaktion immer anwenden\",\n\t\t\"Die erste zurückgegebene Codeaktion anwenden, wenn nur eine vorhanden ist\",\n\t\t\"Zurückgegebene Codeaktionen nicht anwenden\",\n\t\t\"Legt fest, ob nur bevorzugte Codeaktionen zurückgegeben werden sollen\",\n\t\t\"Schnelle Problembehebung ...\",\n\t\t\"Keine Codeaktionen verfügbar\",\n\t\t\"Keine bevorzugten Codeaktionen für \\\"{0}\\\" verfügbar\",\n\t\t\"Keine Codeaktionen für \\\"{0}\\\" verfügbar\",\n\t\t\"Keine bevorzugten Codeaktionen verfügbar\",\n\t\t\"Keine Codeaktionen verfügbar\",\n\t\t\"Refactoring durchführen...\",\n\t\t\"Keine bevorzugten Refactorings für \\\"{0}\\\" verfügbar\",\n\t\t\"Keine Refactorings für \\\"{0}\\\" verfügbar\",\n\t\t\"Keine bevorzugten Refactorings verfügbar\",\n\t\t\"Keine Refactorings verfügbar\",\n\t\t\"Quellaktion...\",\n\t\t\"Keine bevorzugten Quellaktionen für \\\"{0}\\\" verfügbar\",\n\t\t\"Keine Quellaktionen für \\\"{0}\\\" verfügbar\",\n\t\t\"Keine bevorzugten Quellaktionen verfügbar\",\n\t\t\"Keine Quellaktionen verfügbar\",\n\t\t\"Importe organisieren\",\n\t\t\"Keine Aktion zum Organisieren von Importen verfügbar\",\n\t\t\"Alle korrigieren\",\n\t\t\"Aktion \\\"Alle korrigieren\\\" nicht verfügbar\",\n\t\t\"Automatisch korrigieren...\",\n\t\t\"Keine automatischen Korrekturen verfügbar\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionContributions\": [\n\t\t\"Aktivieren/Deaktivieren Sie die Anzeige von Gruppenheadern im Codeaktionsmenü.\",\n\t\t\"Aktivieren/deaktivieren Sie die Anzeige der nächstgelegenen schnellen Problembehebung innerhalb einer Zeile, wenn derzeit keine Diagnose durchgeführt wird.\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionController\": [\n\t\t\"Kontext: {0} in Zeile {1} und Spalte {2}.\",\n\t\t\"Deaktivierte Elemente ausblenden\",\n\t\t\"Deaktivierte Elemente anzeigen\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionMenu\": [\n\t\t\"Weitere Aktionen...\",\n\t\t\"Schnelle Problembehebung\",\n\t\t\"Extrahieren\",\n\t\t\"Inline\",\n\t\t\"Erneut generieren\",\n\t\t\"Verschieben\",\n\t\t\"Umgeben mit\",\n\t\t\"Quellaktion\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/lightBulbWidget\": [\n\t\t\"Zeigt Codeaktionen an. Bevorzugte Schnellkorrektur verfügbar ({0})\",\n\t\t\"Codeaktionen anzeigen ({0})\",\n\t\t\"Codeaktionen anzeigen\",\n\t],\n\t\"vs/editor/contrib/codelens/browser/codelensController\": [\n\t\t\"CodeLens-Befehle für aktuelle Zeile anzeigen\",\n\t\t\"Befehl auswählen\",\n\t],\n\t\"vs/editor/contrib/colorPicker/browser/colorPickerWidget\": [\n\t\t\"Zum Umschalten zwischen Farboptionen (rgb/hsl/hex) klicken\",\n\t\t\"Symbol zum Schließen des Farbwählers\",\n\t],\n\t\"vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions\": [\n\t\t\"Eigenständige Farbwähler anzeigen oder konzentrieren\",\n\t\t\"&&Eigenständige Farbwähler anzeigen oder fokussieren\",\n\t\t\"Farbwähler ausblenden\",\n\t\t\"Farbe mit eigenständigem Farbwähler einfügen\",\n\t],\n\t\"vs/editor/contrib/comment/browser/comment\": [\n\t\t\"Zeilenkommentar umschalten\",\n\t\t\"Zeilenkommen&&tar umschalten\",\n\t\t\"Zeilenkommentar hinzufügen\",\n\t\t\"Zeilenkommentar entfernen\",\n\t\t\"Blockkommentar umschalten\",\n\t\t\"&&Blockkommentar umschalten\",\n\t],\n\t\"vs/editor/contrib/contextmenu/browser/contextmenu\": [\n\t\t\"Minimap\",\n\t\t\"Zeichen rendern\",\n\t\t\"Vertikale Größe\",\n\t\t\"Proportional\",\n\t\t\"Ausfüllen\",\n\t\t\"Anpassen\",\n\t\t\"Schieberegler\",\n\t\t\"Maus über\",\n\t\t\"Immer\",\n\t\t\"Editor-Kontextmenü anzeigen\",\n\t],\n\t\"vs/editor/contrib/cursorUndo/browser/cursorUndo\": [\n\t\t\"Mit Cursor rückgängig machen\",\n\t\t\"Wiederholen mit Cursor\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution\": [\n\t\t\"Einfügen als...\",\n\t\t\"Die ID der Einfügebearbeitung, die angewendet werden soll. Wenn keine Angabe erfolgt, zeigt der Editor eine Auswahl an.\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/copyPasteController\": [\n\t\t\"Gibt an, ob das Einfügewidget angezeigt wird.\",\n\t\t\"Einfügeoptionen anzeigen...\",\n\t\t\"Einfügehandler werden ausgeführt. Klicken Sie hier, um den Vorgang abzubrechen.\",\n\t\t\"Einfügeaktion auswählen\",\n\t\t\"Einfügehandler werden ausgeführt\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/defaultProviders\": [\n\t\t\"Integriert\",\n\t\t\"Nur-Text einfügen\",\n\t\t\"URI einfügen\",\n\t\t\"URI einfügen\",\n\t\t\"Pfade einfügen\",\n\t\t\"Pfad einfügen\",\n\t\t\"Relative Pfade einfügen\",\n\t\t\"Relativen Pfad einfügen\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution\": [\n\t\t\"Konfiguriert den Standardablageanbieter für den Inhalt eines vorgegebenen MIME-Typs.\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController\": [\n\t\t\"Gibt an, ob das Ablagewidget angezeigt wird.\",\n\t\t\"Ablageoptionen anzeigen...\",\n\t\t\"Drophandler werden ausgeführt. Klicken Sie hier, um den Vorgang abzubrechen.\",\n\t],\n\t\"vs/editor/contrib/editorState/browser/keybindingCancellation\": [\n\t\t\"Gibt an, ob der Editor einen abbrechbaren Vorgang ausführt, z. B. \\\"Verweisvorschau\\\".\",\n\t],\n\t\"vs/editor/contrib/find/browser/findController\": [\n\t\t\"Die Datei ist zu groß, um einen Vorgang zum Ersetzen aller Elemente auszuführen.\",\n\t\t\"Suchen\",\n\t\t\"&&Suchen\",\n\t\t\"Überschreibt das Flag „Use Regular Expression“.\\r\\nDas Flag wird für die Zukunft nicht gespeichert.\\r\\n0: Nichts unternehmen\\r\\n1: TRUE\\r\\n2: FALSE\",\n\t\t\"Überschreibt das Flag „Match Whole Word“.\\r\\nDas Flag wird für die Zukunft nicht gespeichert.\\r\\n0: Nichts unternehmen\\r\\n1: TRUE\\r\\n2: FALSE\",\n\t\t\"Überschreibt das Flag „Math Case“.\\r\\nDas Flag wird für die Zukunft nicht gespeichert.\\r\\n0: Nichts unternehmen\\r\\n1: TRUE\\r\\n2: FALSE\",\n\t\t\"Überschreibt das Flag „Preserve Case“.\\r\\nDas Flag wird für die Zukunft nicht gespeichert.\\r\\n0: Nichts unternehmen\\r\\n1: TRUE\\r\\n2: FALSE\",\n\t\t\"Mit Argumenten suchen\",\n\t\t\"Mit Auswahl suchen\",\n\t\t\"Weitersuchen\",\n\t\t\"Vorheriges Element suchen\",\n\t\t\"Zu Übereinstimmung wechseln ...\",\n\t\t\"Keine Übereinstimmungen. Versuchen Sie, nach etwas anderem zu suchen.\",\n\t\t\"Geben Sie eine Zahl ein, um zu einer bestimmten Übereinstimmung zu wechseln (zwischen 1 und {0}).\",\n\t\t\"Zahl zwischen 1 und {0} eingeben\",\n\t\t\"Zahl zwischen 1 und {0} eingeben\",\n\t\t\"Nächste Auswahl suchen\",\n\t\t\"Vorherige Auswahl suchen\",\n\t\t\"Ersetzen\",\n\t\t\"&&Ersetzen\",\n\t],\n\t\"vs/editor/contrib/find/browser/findWidget\": [\n\t\t\"Symbol für \\\"In Auswahl suchen\\\" im Editor-Such-Widget.\",\n\t\t\"Symbol für die Anzeige, dass das Editor-Such-Widget zugeklappt wurde.\",\n\t\t\"Symbol für die Anzeige, dass das Editor-Such-Widget aufgeklappt wurde.\",\n\t\t\"Symbol für \\\"Ersetzen\\\" im Editor-Such-Widget.\",\n\t\t\"Symbol für \\\"Alle ersetzen\\\" im Editor-Such-Widget.\",\n\t\t\"Symbol für \\\"Vorheriges Element suchen\\\" im Editor-Such-Widget.\",\n\t\t\"Symbol für \\\"Nächstes Element suchen\\\" im Editor-Such-Widget.\",\n\t\t\"Suchen/Ersetzen\",\n\t\t\"Suchen\",\n\t\t\"Suchen\",\n\t\t\"Vorherige Übereinstimmung\",\n\t\t\"Nächste Übereinstimmung\",\n\t\t\"In Auswahl suchen\",\n\t\t\"Schließen\",\n\t\t\"Ersetzen\",\n\t\t\"Ersetzen\",\n\t\t\"Ersetzen\",\n\t\t\"Alle ersetzen\",\n\t\t\"Ersetzen umschalten\",\n\t\t\"Nur die ersten {0} Ergebnisse wurden hervorgehoben, aber alle Suchoperationen werden auf dem gesamten Text durchgeführt.\",\n\t\t\"{0} von {1}\",\n\t\t\"Keine Ergebnisse\",\n\t\t\"{0} gefunden\",\n\t\t\"{0} für \\\"{1}\\\" gefunden\",\n\t\t\"{0} für \\\"{1}\\\" gefunden, bei {2}\",\n\t\t\"{0} für \\\"{1}\\\" gefunden\",\n\t\t\"STRG+EINGABE fügt jetzt einen Zeilenumbruch ein, statt alles zu ersetzen. Sie können die Tastenzuordnung für \\\"editor.action.replaceAll\\\" ändern, um dieses Verhalten außer Kraft zu setzen.\",\n\t],\n\t\"vs/editor/contrib/folding/browser/folding\": [\n\t\t\"Auffalten\",\n\t\t\"Faltung rekursiv aufheben\",\n\t\t\"Falten\",\n\t\t\"Einklappung umschalten\",\n\t\t\"Rekursiv falten\",\n\t\t\"Alle Blockkommentare falten\",\n\t\t\"Alle Regionen falten\",\n\t\t\"Alle Regionen auffalten\",\n\t\t\"Alle bis auf ausgewählte falten\",\n\t\t\"Alle bis auf ausgewählte auffalten\",\n\t\t\"Alle falten\",\n\t\t\"Alle auffalten\",\n\t\t\"Zur übergeordneten Reduzierung wechseln\",\n\t\t\"Zum vorherigen Faltbereich wechseln\",\n\t\t\"Zum nächsten Faltbereich wechseln\",\n\t\t\"Faltungsbereich aus Auswahl erstellen\",\n\t\t\"Manuelle Faltbereiche entfernen\",\n\t\t\"Faltebene {0}\",\n\t],\n\t\"vs/editor/contrib/folding/browser/foldingDecorations\": [\n\t\t\"Hintergrundfarbe hinter gefalteten Bereichen. Die Farbe darf nicht deckend sein, sodass zugrunde liegende Dekorationen nicht ausgeblendet werden.\",\n\t\t\"Farbe des Faltsteuerelements im Editor-Bundsteg.\",\n\t\t\"Symbol für aufgeklappte Bereiche im Editor-Glyphenrand.\",\n\t\t\"Symbol für zugeklappte Bereiche im Editor-Glyphenrand.\",\n\t\t\"Symbol für manuell reduzierte Bereiche im Glyphenrand des Editors.\",\n\t\t\"Symbol für manuell erweiterte Bereiche im Glyphenrand des Editors.\",\n\t],\n\t\"vs/editor/contrib/fontZoom/browser/fontZoom\": [\n\t\t\"Editorschriftart vergrößern\",\n\t\t\"Editorschriftart verkleinern\",\n\t\t\"Editor Schriftart Vergrößerung zurücksetzen\",\n\t],\n\t\"vs/editor/contrib/format/browser/format\": [\n\t\t\"1 Formatierung in Zeile {0} vorgenommen\",\n\t\t\"{0} Formatierungen in Zeile {1} vorgenommen\",\n\t\t\"1 Formatierung zwischen Zeilen {0} und {1} vorgenommen\",\n\t\t\"{0} Formatierungen zwischen Zeilen {1} und {2} vorgenommen\",\n\t],\n\t\"vs/editor/contrib/format/browser/formatActions\": [\n\t\t\"Dokument formatieren\",\n\t\t\"Auswahl formatieren\",\n\t],\n\t\"vs/editor/contrib/gotoError/browser/gotoError\": [\n\t\t\"Gehe zu nächstem Problem (Fehler, Warnung, Information)\",\n\t\t\"Symbol für den Marker zum Wechseln zum nächsten Element.\",\n\t\t\"Gehe zu vorigem Problem (Fehler, Warnung, Information)\",\n\t\t\"Symbol für den Marker zum Wechseln zum vorherigen Element.\",\n\t\t\"Gehe zu dem nächsten Problem in den Dateien (Fehler, Warnung, Info)\",\n\t\t\"Nächstes &&Problem\",\n\t\t\"Gehe zu dem vorherigen Problem in den Dateien (Fehler, Warnung, Info)\",\n\t\t\"Vorheriges &&Problem\",\n\t],\n\t\"vs/editor/contrib/gotoError/browser/gotoErrorWidget\": [\n\t\t\"Fehler\",\n\t\t\"Warnung\",\n\t\t\"Info\",\n\t\t\"Hinweis\",\n\t\t\"{0} bei {1}. \",\n\t\t\"{0} von {1} Problemen\",\n\t\t\"{0} von {1} Problemen\",\n\t\t\"Editormarkierung: Farbe bei Fehler des Navigationswidgets.\",\n\t\t\"Hintergrund der Fehlerüberschrift des Markernavigationswidgets im Editor.\",\n\t\t\"Editormarkierung: Farbe bei Warnung des Navigationswidgets.\",\n\t\t\"Hintergrund der Warnungsüberschrift des Markernavigationswidgets im Editor.\",\n\t\t\"Editormarkierung: Farbe bei Information des Navigationswidgets.\",\n\t\t\"Hintergrund der Informationsüberschrift des Markernavigationswidgets im Editor.\",\n\t\t\"Editormarkierung: Hintergrund des Navigationswidgets.\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/goToCommands\": [\n\t\t\"Vorschau\",\n\t\t\"Definitionen\",\n\t\t\"Keine Definition gefunden für \\\"{0}\\\".\",\n\t\t\"Keine Definition gefunden\",\n\t\t\"Gehe zu Definition\",\n\t\t\"Gehe &&zu Definition\",\n\t\t\"Definition an der Seite öffnen\",\n\t\t\"Definition einsehen\",\n\t\t\"Deklarationen\",\n\t\t\"Keine Deklaration für \\\"{0}\\\" gefunden.\",\n\t\t\"Keine Deklaration gefunden.\",\n\t\t\"Zur Deklaration wechseln\",\n\t\t\"Gehe zu &&Deklaration\",\n\t\t\"Keine Deklaration für \\\"{0}\\\" gefunden.\",\n\t\t\"Keine Deklaration gefunden.\",\n\t\t\"Vorschau für Deklaration anzeigen\",\n\t\t\"Typdefinitionen\",\n\t\t\"Keine Typendefinition gefunden für \\\"{0}\\\"\",\n\t\t\"Keine Typendefinition gefunden\",\n\t\t\"Zur Typdefinition wechseln\",\n\t\t\"Zur &&Typdefinition wechseln\",\n\t\t\"Vorschau der Typdefinition anzeigen\",\n\t\t\"Implementierungen\",\n\t\t\"Keine Implementierung gefunden für \\\"{0}\\\"\",\n\t\t\"Keine Implementierung gefunden\",\n\t\t\"Gehe zu Implementierungen\",\n\t\t\"Gehe zu &&Implementierungen\",\n\t\t\"Vorschau für Implementierungen anzeigen\",\n\t\t\"Für \\\"{0}\\\" wurden keine Verweise gefunden.\",\n\t\t\"Keine Referenzen gefunden\",\n\t\t\"Gehe zu Verweisen\",\n\t\t\"Gehe zu &&Verweisen\",\n\t\t\"Verweise\",\n\t\t\"Vorschau für Verweise anzeigen\",\n\t\t\"Verweise\",\n\t\t\"Zum beliebigem Symbol wechseln\",\n\t\t\"Speicherorte\",\n\t\t\"Keine Ergebnisse für \\\"{0}\\\"\",\n\t\t\"Verweise\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition\": [\n\t\t\"Klicken Sie, um {0} Definitionen anzuzeigen.\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesController\": [\n\t\t\"Gibt an, ob die Verweisvorschau sichtbar ist, z. B. \\\"Verweisvorschau\\\" oder \\\"Definition einsehen\\\".\",\n\t\t\"Wird geladen...\",\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesTree\": [\n\t\t\"{0} Verweise\",\n\t\t\"{0} Verweis\",\n\t\t\"Verweise\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget\": [\n\t\t\"Keine Vorschau verfügbar.\",\n\t\t\"Keine Ergebnisse\",\n\t\t\"Verweise\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/referencesModel\": [\n\t\t\"in {0} in Zeile {1} in Spalte {2}\",\n\t\t\"{0} in {1} in Zeile {2} in Spalte {3}\",\n\t\t\"1 Symbol in {0}, vollständiger Pfad {1}\",\n\t\t\"{0} Symbole in {1}, vollständiger Pfad {2}\",\n\t\t\"Es wurden keine Ergebnisse gefunden.\",\n\t\t\"1 Symbol in {0} gefunden\",\n\t\t\"{0} Symbole in {1} gefunden\",\n\t\t\"{0} Symbole in {1} Dateien gefunden\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/symbolNavigation\": [\n\t\t\"Gibt an, ob Symbolpositionen vorliegen, bei denen die Navigation nur über die Tastatur möglich ist.\",\n\t\t\"Symbol {0} von {1}, {2} für nächstes\",\n\t\t\"Symbol {0} von {1}\",\n\t],\n\t\"vs/editor/contrib/hover/browser/hover\": [\n\t\t\"Anzeigen oder Fokus beim Daraufzeigen\",\n\t\t\"Definitionsvorschauhover anzeigen\",\n\t\t\"Bildlauf nach oben beim Daraufzeigen\",\n\t\t\"Bildlauf nach unten beim Daraufzeigen\",\n\t\t\"Bildlauf nach links beim Daraufzeigen\",\n\t\t\"Bildlauf nach rechts beim Daraufzeigen\",\n\t\t\"Eine Seite nach oben beim Daraufzeigen\",\n\t\t\"Eine Seite nach unten beim Daraufzeigen\",\n\t\t\"Gehe nach oben beim Daraufzeigen\",\n\t\t\"Gehe nach unten beim Daraufzeigen\",\n\t],\n\t\"vs/editor/contrib/hover/browser/markdownHoverParticipant\": [\n\t\t\"Wird geladen...\",\n\t\t\"Das Rendering langer Zeilen wurde aus Leistungsgründen angehalten. Dies kann über „editor.stopRenderingLineAfter“ konfiguriert werden.\",\n\t\t\"Die Tokenisierung wird bei langen Zeilen aus Leistungsgründen übersprungen. Dies kann über „editor.maxTokenizationLineLength“ konfiguriert werden.\",\n\t],\n\t\"vs/editor/contrib/hover/browser/markerHoverParticipant\": [\n\t\t\"Problem anzeigen\",\n\t\t\"Keine Schnellkorrekturen verfügbar\",\n\t\t\"Es wird nach Schnellkorrekturen gesucht...\",\n\t\t\"Keine Schnellkorrekturen verfügbar\",\n\t\t\"Schnelle Problembehebung ...\",\n\t],\n\t\"vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace\": [\n\t\t\"Durch vorherigen Wert ersetzen\",\n\t\t\"Durch nächsten Wert ersetzen\",\n\t],\n\t\"vs/editor/contrib/indentation/browser/indentation\": [\n\t\t\"Einzug in Leerzeichen konvertieren\",\n\t\t\"Einzug in Tabstopps konvertieren\",\n\t\t\"Konfigurierte Tabulatorgröße\",\n\t\t\"Standardregisterkartengröße\",\n\t\t\"Aktuelle Registerkartengröße\",\n\t\t\"Tabulatorgröße für aktuelle Datei auswählen\",\n\t\t\"Einzug mithilfe von Tabstopps\",\n\t\t\"Einzug mithilfe von Leerzeichen\",\n\t\t\"Anzeigegröße der Registerkarte ändern\",\n\t\t\"Einzug aus Inhalt erkennen\",\n\t\t\"Neuen Einzug für Zeilen festlegen\",\n\t\t\"Gewählte Zeilen zurückziehen\",\n\t],\n\t\"vs/editor/contrib/inlayHints/browser/inlayHintsHover\": [\n\t\t\"Zum Einfügen doppelklicken\",\n\t\t\"BEFEHL + Klicken\",\n\t\t\"STRG + Klicken\",\n\t\t\"OPTION + Klicken\",\n\t\t\"ALT + Klicken\",\n\t\t\"Wechseln Sie zu Definition ({0}), klicken Sie mit der rechten Maustaste, um weitere Informationen zu finden.\",\n\t\t\"Gehe zu Definition ({0})\",\n\t\t\"Befehl ausführen\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/commands\": [\n\t\t\"Nächsten Inline-Vorschlag anzeigen\",\n\t\t\"Vorherigen Inline-Vorschlag anzeigen\",\n\t\t\"Inline-Vorschlag auslösen\",\n\t\t\"Nächstes Wort des Inline-Vorschlags annehmen\",\n\t\t\"Wort annehmen\",\n\t\t\"Nächste Zeile des Inlinevorschlags akzeptieren\",\n\t\t\"Zeile annehmen\",\n\t\t\"Inline-Vorschlag annehmen\",\n\t\t\"Annehmen\",\n\t\t\"Inlinevorschlag ausblenden\",\n\t\t\"Symbolleiste immer anzeigen\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/hoverParticipant\": [\n\t\t\"Vorschlag:\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys\": [\n\t\t\"Gibt an, ob ein Inline-Vorschlag sichtbar ist.\",\n\t\t\"Gibt an, ob der Inline-Vorschlag mit Leerzeichen beginnt.\",\n\t\t\"Ob der Inline-Vorschlag mit Leerzeichen beginnt, das kleiner ist als das, was durch die Tabulatortaste eingefügt werden würde\",\n\t\t\"Gibt an, ob Vorschläge für den aktuellen Vorschlag unterdrückt werden sollen\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsController\": [\n\t\t\"Überprüfen Sie dies in der barrierefreien Ansicht ({0}).\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget\": [\n\t\t\"Symbol für die Anzeige des nächsten Parameterhinweises.\",\n\t\t\"Symbol für die Anzeige des vorherigen Parameterhinweises.\",\n\t\t\"{0} ({1})\",\n\t\t\"Zurück\",\n\t\t\"Weiter\",\n\t],\n\t\"vs/editor/contrib/lineSelection/browser/lineSelection\": [\n\t\t\"Zeilenauswahl erweitern\",\n\t],\n\t\"vs/editor/contrib/linesOperations/browser/linesOperations\": [\n\t\t\"Zeile nach oben kopieren\",\n\t\t\"Zeile nach oben &&kopieren\",\n\t\t\"Zeile nach unten kopieren\",\n\t\t\"Zeile nach unten ko&&pieren\",\n\t\t\"Auswahl duplizieren\",\n\t\t\"&&Auswahl duplizieren\",\n\t\t\"Zeile nach oben verschieben\",\n\t\t\"Zeile nach oben &&verschieben\",\n\t\t\"Zeile nach unten verschieben\",\n\t\t\"Zeile nach &&unten verschieben\",\n\t\t\"Zeilen aufsteigend sortieren\",\n\t\t\"Zeilen absteigend sortieren\",\n\t\t\"Doppelte Zeilen löschen\",\n\t\t\"Nachgestelltes Leerzeichen kürzen\",\n\t\t\"Zeile löschen\",\n\t\t\"Zeileneinzug\",\n\t\t\"Zeile ausrücken\",\n\t\t\"Zeile oben einfügen\",\n\t\t\"Zeile unten einfügen\",\n\t\t\"Alle übrigen löschen\",\n\t\t\"Alle rechts löschen\",\n\t\t\"Zeilen verknüpfen\",\n\t\t\"Zeichen um den Cursor herum transponieren\",\n\t\t\"In Großbuchstaben umwandeln\",\n\t\t\"In Kleinbuchstaben umwandeln\",\n\t\t\"In große Anfangsbuchstaben umwandeln\",\n\t\t\"In Snake Case umwandeln\",\n\t\t\"In Camel-Fall transformieren\",\n\t\t\"Verwandle dich in eine Kebab-Hülle\",\n\t],\n\t\"vs/editor/contrib/linkedEditing/browser/linkedEditing\": [\n\t\t\"Verknüpfte Bearbeitung starten\",\n\t\t\"Hintergrundfarbe, wenn der Editor automatisch nach Typ umbenennt.\",\n\t],\n\t\"vs/editor/contrib/links/browser/links\": [\n\t\t\"Fehler beim Öffnen dieses Links, weil er nicht wohlgeformt ist: {0}\",\n\t\t\"Fehler beim Öffnen dieses Links, weil das Ziel fehlt.\",\n\t\t\"Befehl ausführen\",\n\t\t\"Link folgen\",\n\t\t\"BEFEHL + Klicken\",\n\t\t\"STRG + Klicken\",\n\t\t\"OPTION + Klicken\",\n\t\t\"alt + klicken\",\n\t\t\"Führen Sie den Befehl \\\"{0}\\\" aus.\",\n\t\t\"Link öffnen\",\n\t],\n\t\"vs/editor/contrib/message/browser/messageController\": [\n\t\t\"Gibt an, ob der Editor zurzeit eine Inlinenachricht anzeigt.\",\n\t],\n\t\"vs/editor/contrib/multicursor/browser/multicursor\": [\n\t\t\"Hinzugefügter Cursor: {0}\",\n\t\t\"Hinzugefügte Cursor: {0}\",\n\t\t\"Cursor oberhalb hinzufügen\",\n\t\t\"Cursor oberh&&alb hinzufügen\",\n\t\t\"Cursor unterhalb hinzufügen\",\n\t\t\"Cursor unterhal&&b hinzufügen\",\n\t\t\"Cursor an Zeilenenden hinzufügen\",\n\t\t\"C&&ursor an Zeilenenden hinzufügen\",\n\t\t\"Cursor am Ende hinzufügen\",\n\t\t\"Cursor am Anfang hinzufügen\",\n\t\t\"Auswahl zur nächsten Übereinstimmungssuche hinzufügen\",\n\t\t\"&&Nächstes Vorkommen hinzufügen\",\n\t\t\"Letzte Auswahl zu vorheriger Übereinstimmungssuche hinzufügen\",\n\t\t\"Vo&&rheriges Vorkommen hinzufügen\",\n\t\t\"Letzte Auswahl in nächste Übereinstimmungssuche verschieben\",\n\t\t\"Letzte Auswahl in vorherige Übereinstimmungssuche verschieben\",\n\t\t\"Alle Vorkommen auswählen und Übereinstimmung suchen\",\n\t\t\"Alle V&&orkommen auswählen\",\n\t\t\"Alle Vorkommen ändern\",\n\t\t\"Fokus auf nächsten Cursor\",\n\t\t\"Fokussiert den nächsten Cursor\",\n\t\t\"Fokus auf vorherigen Cursor\",\n\t\t\"Fokussiert den vorherigen Cursor\",\n\t],\n\t\"vs/editor/contrib/parameterHints/browser/parameterHints\": [\n\t\t\"Parameterhinweise auslösen\",\n\t],\n\t\"vs/editor/contrib/parameterHints/browser/parameterHintsWidget\": [\n\t\t\"Symbol für die Anzeige des nächsten Parameterhinweises.\",\n\t\t\"Symbol für die Anzeige des vorherigen Parameterhinweises.\",\n\t\t\"{0}, Hinweis\",\n\t\t\"Vordergrundfarbe des aktiven Elements im Parameterhinweis.\",\n\t],\n\t\"vs/editor/contrib/peekView/browser/peekView\": [\n\t\t\"Gibt an, ob der aktuelle Code-Editor in der Vorschau eingebettet ist.\",\n\t\t\"Schließen\",\n\t\t\"Hintergrundfarbe des Titelbereichs der Peek-Ansicht.\",\n\t\t\"Farbe des Titels in der Peek-Ansicht.\",\n\t\t\"Farbe der Titelinformationen in der Peek-Ansicht.\",\n\t\t\"Farbe der Peek-Ansichtsränder und des Pfeils.\",\n\t\t\"Hintergrundfarbe der Ergebnisliste in der Peek-Ansicht.\",\n\t\t\"Vordergrundfarbe für Zeilenknoten in der Ergebnisliste der Peek-Ansicht.\",\n\t\t\"Vordergrundfarbe für Dateiknoten in der Ergebnisliste der Peek-Ansicht.\",\n\t\t\"Hintergrundfarbe des ausgewählten Eintrags in der Ergebnisliste der Peek-Ansicht.\",\n\t\t\"Vordergrundfarbe des ausgewählten Eintrags in der Ergebnisliste der Peek-Ansicht.\",\n\t\t\"Hintergrundfarbe des Peek-Editors.\",\n\t\t\"Hintergrundfarbe der Leiste im Peek-Editor.\",\n\t\t\"Die Hintergrundfarbe für den „Sticky“-Bildlaufeffekt im Editor für die „Peek“-Ansicht.\",\n\t\t\"Farbe für Übereinstimmungsmarkierungen in der Ergebnisliste der Peek-Ansicht.\",\n\t\t\"Farbe für Übereinstimmungsmarkierungen im Peek-Editor.\",\n\t\t\"Rahmen für Übereinstimmungsmarkierungen im Peek-Editor.\",\n\t],\n\t\"vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess\": [\n\t\t\"Öffnen Sie zuerst einen Text-Editor, um zu einer Zeile zu wechseln.\",\n\t\t\"Wechseln Sie zu Zeile {0} und Zeichen {1}.\",\n\t\t\"Zu Zeile {0} wechseln.\",\n\t\t\"Aktuelle Zeile: {0}, Zeichen: {1}. Geben Sie eine Zeilennummer zwischen 1 und {2} ein, zu der Sie navigieren möchten.\",\n\t\t\"Aktuelle Zeile: {0}, Zeichen: {1}. Geben Sie eine Zeilennummer ein, zu der Sie navigieren möchten.\",\n\t],\n\t\"vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess\": [\n\t\t\"Öffnen Sie zunächst einen Text-Editor mit Symbolinformationen, um zu einem Symbol zu navigieren.\",\n\t\t\"Der aktive Text-Editor stellt keine Symbolinformationen bereit.\",\n\t\t\"Keine übereinstimmenden Editorsymbole.\",\n\t\t\"Keine Editorsymbole.\",\n\t\t\"An der Seite öffnen\",\n\t\t\"Unten öffnen\",\n\t\t\"Symbole ({0})\",\n\t\t\"Eigenschaften ({0})\",\n\t\t\"Methoden ({0})\",\n\t\t\"Funktionen ({0})\",\n\t\t\"Konstruktoren ({0})\",\n\t\t\"Variablen ({0})\",\n\t\t\"Klassen ({0})\",\n\t\t\"Strukturen ({0})\",\n\t\t\"Ereignisse ({0})\",\n\t\t\"Operatoren ({0})\",\n\t\t\"Schnittstellen ({0})\",\n\t\t\"Namespaces ({0})\",\n\t\t\"Pakete ({0})\",\n\t\t\"Typparameter ({0})\",\n\t\t\"Module ({0})\",\n\t\t\"Eigenschaften ({0})\",\n\t\t\"Enumerationen ({0})\",\n\t\t\"Enumerationsmember ({0})\",\n\t\t\"Zeichenfolgen ({0})\",\n\t\t\"Dateien ({0})\",\n\t\t\"Arrays ({0})\",\n\t\t\"Zahlen ({0})\",\n\t\t\"Boolesche Werte ({0})\",\n\t\t\"Objekte ({0})\",\n\t\t\"Schlüssel ({0})\",\n\t\t\"Felder ({0})\",\n\t\t\"Konstanten ({0})\",\n\t],\n\t\"vs/editor/contrib/readOnlyMessage/browser/contribution\": [\n\t\t\"Bearbeitung von schreibgeschützter Eingabe nicht möglich\",\n\t\t\"Ein Bearbeiten ist im schreibgeschützten Editor nicht möglich\",\n\t],\n\t\"vs/editor/contrib/rename/browser/rename\": [\n\t\t\"Kein Ergebnis.\",\n\t\t\"Ein unbekannter Fehler ist beim Auflösen der Umbenennung eines Ortes aufgetreten.\",\n\t\t\"\\'{0}\\' wird in \\'{1}\\' umbenannt\",\n\t\t\"{0} wird in {1} umbenannt.\",\n\t\t\"\\\"{0}\\\" erfolgreich in \\\"{1}\\\" umbenannt. Zusammenfassung: {2}\",\n\t\t\"Die rename-Funktion konnte die Änderungen nicht anwenden.\",\n\t\t\"Die rename-Funktion konnte die Änderungen nicht berechnen.\",\n\t\t\"Symbol umbenennen\",\n\t\t\"Möglichkeit aktivieren/deaktivieren, Änderungen vor dem Umbenennen als Vorschau anzeigen zu lassen\",\n\t],\n\t\"vs/editor/contrib/rename/browser/renameInputField\": [\n\t\t\"Gibt an, ob das Widget zum Umbenennen der Eingabe sichtbar ist.\",\n\t\t\"Benennen Sie die Eingabe um. Geben Sie einen neuen Namen ein, und drücken Sie die EINGABETASTE, um den Commit auszuführen.\",\n\t\t\"{0} zur Umbenennung, {1} zur Vorschau\",\n\t],\n\t\"vs/editor/contrib/smartSelect/browser/smartSelect\": [\n\t\t\"Auswahl aufklappen\",\n\t\t\"Auswahl &&erweitern\",\n\t\t\"Markierung verkleinern\",\n\t\t\"Au&&swahl verkleinern\",\n\t],\n\t\"vs/editor/contrib/snippet/browser/snippetController2\": [\n\t\t\"Gibt an, ob der Editor sich zurzeit im Schnipselmodus befindet.\",\n\t\t\"Gibt an, ob ein nächster Tabstopp im Schnipselmodus vorhanden ist.\",\n\t\t\"Gibt an, ob ein vorheriger Tabstopp im Schnipselmodus vorhanden ist.\",\n\t\t\"Zum nächsten Platzhalter wechseln...\",\n\t],\n\t\"vs/editor/contrib/snippet/browser/snippetVariables\": [\n\t\t\"Sonntag\",\n\t\t\"Montag\",\n\t\t\"Dienstag\",\n\t\t\"Mittwoch\",\n\t\t\"Donnerstag\",\n\t\t\"Freitag\",\n\t\t\"Samstag\",\n\t\t\"So\",\n\t\t\"Mo\",\n\t\t\"Di\",\n\t\t\"Mi\",\n\t\t\"Do\",\n\t\t\"Fr\",\n\t\t\"Sa\",\n\t\t\"Januar\",\n\t\t\"Februar\",\n\t\t\"März\",\n\t\t\"April\",\n\t\t\"Mai\",\n\t\t\"Juni\",\n\t\t\"Juli\",\n\t\t\"August\",\n\t\t\"September\",\n\t\t\"Oktober\",\n\t\t\"November\",\n\t\t\"Dezember\",\n\t\t\"Jan\",\n\t\t\"Feb\",\n\t\t\"Mär\",\n\t\t\"Apr\",\n\t\t\"Mai\",\n\t\t\"Jun\",\n\t\t\"Jul\",\n\t\t\"Aug\",\n\t\t\"Sep\",\n\t\t\"Okt\",\n\t\t\"Nov\",\n\t\t\"Dez\",\n\t],\n\t\"vs/editor/contrib/stickyScroll/browser/stickyScrollActions\": [\n\t\t\"Fixierten Bildlauf umschalten\",\n\t\t\"Fixierten Bildlauf &&umschalten\",\n\t\t\"Fixierter Bildlauf\",\n\t\t\"&&Fixierter Bildlauf\",\n\t\t\"Fokus auf Fixierten Bildlauf\",\n\t\t\"&&Fokus fixierter Bildlauf\",\n\t\t\"Nächste fixierte Zeile auswählen\",\n\t\t\"Zuletzt gewählte fixierte Zeile auswählen\",\n\t\t\"Gehe zur fokussierten fixierten Zeile\",\n\t\t\"Editor auswählen\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggest\": [\n\t\t\"Gibt an, ob ein Vorschlag fokussiert ist\",\n\t\t\"Gibt an, ob Vorschlagsdetails sichtbar sind.\",\n\t\t\"Gibt an, ob mehrere Vorschläge zur Auswahl stehen.\",\n\t\t\"Gibt an, ob das Einfügen des aktuellen Vorschlags zu einer Änderung führt oder ob bereits alles eingegeben wurde.\",\n\t\t\"Gibt an, ob Vorschläge durch Drücken der EINGABETASTE eingefügt werden.\",\n\t\t\"Gibt an, ob der aktuelle Vorschlag Verhalten zum Einfügen und Ersetzen aufweist.\",\n\t\t\"Gibt an, ob Einfügen oder Ersetzen als Standardverhalten verwendet wird.\",\n\t\t\"Gibt an, ob der aktuelle Vorschlag die Auflösung weiterer Details unterstützt.\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestController\": [\n\t\t\"Das Akzeptieren von \\\"{0}\\\" ergab {1} zusätzliche Bearbeitungen.\",\n\t\t\"Vorschlag auslösen\",\n\t\t\"Einfügen\",\n\t\t\"Einfügen\",\n\t\t\"Ersetzen\",\n\t\t\"Ersetzen\",\n\t\t\"Einfügen\",\n\t\t\"weniger anzeigen\",\n\t\t\"mehr anzeigen\",\n\t\t\"Größe des Vorschlagswidgets zurücksetzen\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidget\": [\n\t\t\"Hintergrundfarbe des Vorschlagswidgets.\",\n\t\t\"Rahmenfarbe des Vorschlagswidgets.\",\n\t\t\"Vordergrundfarbe des Vorschlagswidgets.\",\n\t\t\"Die Vordergrundfarbe des ausgewählten Eintrags im Vorschlagswidget.\",\n\t\t\"Die Vordergrundfarbe des Symbols des ausgewählten Eintrags im Vorschlagswidget.\",\n\t\t\"Hintergrundfarbe des ausgewählten Eintrags im Vorschlagswidget.\",\n\t\t\"Farbe der Trefferhervorhebung im Vorschlagswidget.\",\n\t\t\"Die Farbe des Treffers wird im Vorschlagswidget hervorgehoben, wenn ein Element fokussiert wird.\",\n\t\t\"Vordergrundfarbe des Status des Vorschlagswidgets.\",\n\t\t\"Wird geladen...\",\n\t\t\"Keine Vorschläge.\",\n\t\t\"Vorschlagen\",\n\t\t\"{0} {1}, {2}\",\n\t\t\"{0} {1}\",\n\t\t\"{0}, {1}\",\n\t\t\"{0}, Dokumente: {1}\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetDetails\": [\n\t\t\"Schließen\",\n\t\t\"Wird geladen...\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetRenderer\": [\n\t\t\"Symbol für weitere Informationen im Vorschlags-Widget.\",\n\t\t\"Weitere Informationen\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetStatus\": [\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/editor/contrib/symbolIcons/browser/symbolIcons\": [\n\t\t\"Die Vordergrundfarbe für Arraysymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für boolesche Symbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Klassensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Farbsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für konstante Symbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Konstruktorsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Enumeratorsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Enumeratormembersymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Ereignissymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Feldsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Dateisymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Ordnersymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Funktionssymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Schnittstellensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Schlüsselsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Schlüsselwortsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Methodensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Modulsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Namespacesymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für NULL-Symbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Zahlensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Objektsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Operatorsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Paketsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Eigenschaftensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Referenzsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Codeschnipselsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Zeichenfolgensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Struktursymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Textsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Typparametersymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für Einheitensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t\t\"Die Vordergrundfarbe für variable Symbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.\",\n\t],\n\t\"vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode\": [\n\t\t\"TAB-Umschalttaste verschiebt Fokus\",\n\t\t\"Beim Drücken auf Tab wird der Fokus jetzt auf das nächste fokussierbare Element verschoben\",\n\t\t\"Beim Drücken von Tab wird jetzt das Tabulator-Zeichen eingefügt\",\n\t],\n\t\"vs/editor/contrib/tokenization/browser/tokenization\": [\n\t\t\"Entwickler: Force Retokenize\",\n\t],\n\t\"vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter\": [\n\t\t\"Symbol, das mit einer Warnmeldung im Erweiterungs-Editor angezeigt wird.\",\n\t\t\"Dieses Dokument enthält viele nicht einfache ASCII-Unicode-Zeichen.\",\n\t\t\"Dieses Dokument enthält viele mehrdeutige Unicode-Zeichen.\",\n\t\t\"Dieses Dokument enthält viele unsichtbare Unicode-Zeichen.\",\n\t\t\"Das Zeichen {0} kann mit dem Zeichen {1} verwechselt werden, was im Quellcode häufiger vorkommt.\",\n\t\t\"Das Zeichen {0} kann mit dem Zeichen {1} verwechselt werden, was im Quellcode häufiger vorkommt.\",\n\t\t\"Das Zeichen {0} ist nicht sichtbar.\",\n\t\t\"Das Zeichen {0} ist kein einfaches ASCII-Zeichen.\",\n\t\t\"Einstellungen anpassen\",\n\t\t\"Hervorhebung in Kommentaren deaktivieren\",\n\t\t\"Deaktivieren der Hervorhebung von Zeichen in Kommentaren\",\n\t\t\"Hervorhebung in Zeichenfolgen deaktivieren\",\n\t\t\"Deaktivieren der Hervorhebung von Zeichen in Zeichenfolgen\",\n\t\t\"Mehrdeutige Hervorhebung deaktivieren\",\n\t\t\"Deaktivieren der Hervorhebung von mehrdeutigen Zeichen\",\n\t\t\"Unsichtbare Hervorhebung deaktivieren\",\n\t\t\"Deaktivieren der Hervorhebung unsichtbarer Zeichen\",\n\t\t\"Nicht-ASCII-Hervorhebung deaktivieren\",\n\t\t\"Deaktivieren der Hervorhebung von nicht einfachen ASCII-Zeichen\",\n\t\t\"Ausschlussoptionen anzeigen\",\n\t\t\"{0} (unsichtbares Zeichen) von der Hervorhebung ausschließen\",\n\t\t\"{0} nicht hervorheben\",\n\t\t\"Unicodezeichen zulassen, die in der Sprache „{0}“ häufiger vorkommen.\",\n\t\t\"Konfigurieren der Optionen für die Unicode-Hervorhebung\",\n\t],\n\t\"vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators\": [\n\t\t\"Ungewöhnliche Zeilentrennzeichen\",\n\t\t\"Ungewöhnliche Zeilentrennzeichen erkannt\",\n\t\t\"Die Datei \\\"{0}\\\" enthält mindestens ein ungewöhnliches Zeilenabschlusszeichen, z. B. Zeilentrennzeichen (LS) oder Absatztrennzeichen (PS).\\r\\n\\r\\nEs wird empfohlen, sie aus der Datei zu entfernen. Dies kann über \\\"editor.unusualLineTerminators\\\" konfiguriert werden.\",\n\t\t\"&&Ungewöhnliche Zeilenabschlusszeichen entfernen\",\n\t\t\"Ignorieren\",\n\t],\n\t\"vs/editor/contrib/wordHighlighter/browser/highlightDecorations\": [\n\t\t\"Hintergrundfarbe eines Symbols beim Lesezugriff, z.B. beim Lesen einer Variablen. Die Farbe darf nicht deckend sein, damit sie nicht die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Hintergrundfarbe eines Symbols bei Schreibzugriff, z.B. beim Schreiben in eine Variable. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Die Hintergrundfarbe eines Textteils für ein Symbol. Die Farbe darf nicht deckend sein, um zugrunde liegende Dekorationen nicht auszublenden.\",\n\t\t\"Randfarbe eines Symbols beim Lesezugriff, wie etwa beim Lesen einer Variablen.\",\n\t\t\"Randfarbe eines Symbols beim Schreibzugriff, wie etwa beim Schreiben einer Variablen.\",\n\t\t\"Die Rahmenfarbe eines Textteils für ein Symbol.\",\n\t\t\"Übersichtslinealmarkerfarbd für das Hervorheben von Symbolen. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Übersichtslinealmarkerfarbe für Symbolhervorhebungen bei Schreibzugriff. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Die Markierungsfarbe des Übersichtslineals eines Textteils für ein Symbol. Die Farbe darf nicht deckend sein, um zugrunde liegende Dekorationen nicht auszublenden.\",\n\t],\n\t\"vs/editor/contrib/wordHighlighter/browser/wordHighlighter\": [\n\t\t\"Gehe zur nächsten Symbolhervorhebungen\",\n\t\t\"Gehe zur vorherigen Symbolhervorhebungen\",\n\t\t\"Symbol-Hervorhebung ein-/ausschalten\",\n\t],\n\t\"vs/editor/contrib/wordOperations/browser/wordOperations\": [\n\t\t\"Wort löschen\",\n\t],\n\t\"vs/platform/action/common/actionCommonCategories\": [\n\t\t\"Ansehen\",\n\t\t\"Hilfe\",\n\t\t\"Test\",\n\t\t\"Datei\",\n\t\t\"Einstellungen\",\n\t\t\"Entwickler\",\n\t],\n\t\"vs/platform/actionWidget/browser/actionList\": [\n\t\t\"{0} zum Anwenden, {1} für die Vorschau\",\n\t\t\"{0} zum Anwenden\",\n\t\t\"{0} deaktiviert, Grund: {1}\",\n\t\t\"Aktionswidget\",\n\t],\n\t\"vs/platform/actionWidget/browser/actionWidget\": [\n\t\t\"Hintergrundfarbe für umgeschaltete Aktionselemente in der Aktionsleiste.\",\n\t\t\"Gibt an, ob die Aktionswidgetliste sichtbar ist.\",\n\t\t\"Codeaktionswidget ausblenden\",\n\t\t\"Vorherige Aktion auswählen\",\n\t\t\"Nächste Aktion auswählen\",\n\t\t\"Ausgewählte Aktion akzeptieren\",\n\t\t\"Vorschau für ausgewählte Elemente anzeigen\",\n\t],\n\t\"vs/platform/actions/browser/menuEntryActionViewItem\": [\n\t\t\"{0} ({1})\",\n\t\t\"{0} ({1})\",\n\t\t\"{0}\\r\\n[{1}] {2}\",\n\t],\n\t\"vs/platform/actions/browser/toolbar\": [\n\t\t\"Ausblenden\",\n\t\t\"Menü zurücksetzen\",\n\t],\n\t\"vs/platform/actions/common/menuService\": [\n\t\t\"\\\"{0}\\\" ausblenden\",\n\t],\n\t\"vs/platform/audioCues/browser/audioCueService\": [\n\t\t\"Fehler in der Zeile\",\n\t\t\"Warnung in der Zeile\",\n\t\t\"Gefalteter Bereich in der Zeile\",\n\t\t\"Haltepunkt in der Zeile\",\n\t\t\"Inlinevorschlag in der Zeile\",\n\t\t\"Terminale schnelle Problembehebung\",\n\t\t\"Debugger auf Haltepunkt beendet\",\n\t\t\"Keine Inlay-Hinweise in der Zeile\",\n\t\t\"Aufgabe abgeschlossen\",\n\t\t\"Aufgabe fehlgeschlagen\",\n\t\t\"Terminalbefehl fehlgeschlagen\",\n\t\t\"Terminalglocke\",\n\t\t\"Notebookzelle abgeschlossen\",\n\t\t\"Notebookzelle fehlgeschlagen\",\n\t\t\"Vergleichslinie eingefügt\",\n\t\t\"Vergleichslinie gelöscht\",\n\t\t\"Vergleichslinie geändert\",\n\t\t\"Chatanfrage gesendet\",\n\t\t\"Chatantwort empfangen\",\n\t\t\"Chatantwort ausstehend\",\n\t],\n\t\"vs/platform/configuration/common/configurationRegistry\": [\n\t\t\"Außerkraftsetzungen für die Standardsprachkonfiguration\",\n\t\t\"Konfigurieren Sie Einstellungen, die für die Sprache {0} überschrieben werden sollen.\",\n\t\t\"Zu überschreibende Editor-Einstellungen für eine Sprache konfigurieren.\",\n\t\t\"Diese Einstellung unterstützt keine sprachspezifische Konfiguration.\",\n\t\t\"Zu überschreibende Editor-Einstellungen für eine Sprache konfigurieren.\",\n\t\t\"Diese Einstellung unterstützt keine sprachspezifische Konfiguration.\",\n\t\t\"Eine leere Eigenschaft kann nicht registriert werden.\",\n\t\t\"\\\"{0}\\\" kann nicht registriert werden. Stimmt mit dem Eigenschaftsmuster \\\"\\\\\\\\[.*\\\\\\\\]$\\\" zum Beschreiben sprachspezifischer Editor-Einstellungen überein. Verwenden Sie den Beitrag \\\"configurationDefaults\\\".\",\n\t\t\"{0}\\\" kann nicht registriert werden. Diese Eigenschaft ist bereits registriert.\",\n\t\t\"\\\"{0}\\\" kann nicht registriert werden. Die zugeordnete Richtlinie {1} ist bereits bei {2} registriert.\",\n\t],\n\t\"vs/platform/contextkey/browser/contextKeyService\": [\n\t\t\"Ein Befehl, der Informationen zu Kontextschlüsseln zurückgibt\",\n\t],\n\t\"vs/platform/contextkey/common/contextkey\": [\n\t\t\"Leerer Kontextschlüsselausdruck\",\n\t\t\"Haben Sie vergessen, einen Ausdruck zu schreiben? Sie können auch „false“ oder „true“ festlegen, um immer auf „false“ oder „true“ auszuwerten.\",\n\t\t\"„in“ nach „not“.\",\n\t\t\"schließende Klammer „)“\",\n\t\t\"Unerwartetes Token\",\n\t\t\"Haben Sie vergessen, && oder || vor dem Token einzufügen?\",\n\t\t\"Unerwartetes Ende des Ausdrucks.\",\n\t\t\"Haben Sie vergessen, einen Kontextschlüssel zu setzen?\",\n\t\t\"Erwartet: {0}\\r\\nEmpfangen: „{1}“.\",\n\t],\n\t\"vs/platform/contextkey/common/contextkeys\": [\n\t\t\"Gibt an, ob macOS als Betriebssystem verwendet wird.\",\n\t\t\"Gibt an, ob Linux als Betriebssystem verwendet wird.\",\n\t\t\"Gibt an, ob Windows als Betriebssystem verwendet wird.\",\n\t\t\"Gibt an, ob es sich bei der Plattform um einen Webbrowser handelt.\",\n\t\t\"Gibt an, ob macOS auf einer Nicht-Browser-Plattform als Betriebssystem verwendet wird.\",\n\t\t\"Gibt an, ob iOS als Betriebssystem verwendet wird.\",\n\t\t\"Gibt an, ob es sich bei der Plattform um einen mobilen Webbrowser handelt.\",\n\t\t\"Qualitätstyp des VS Codes\",\n\t\t\"Gibt an, ob sich der Tastaturfokus in einem Eingabefeld befindet.\",\n\t],\n\t\"vs/platform/contextkey/common/scanner\": [\n\t\t\"Meinten Sie {0}?\",\n\t\t\"Meinten Sie {0} oder {1}?\",\n\t\t\"Meinten Sie {0}, {1} oder {2}?\",\n\t\t\"Haben Sie vergessen, das Anführungszeichen zu öffnen oder zu schließen?\",\n\t\t\"Haben Sie vergessen, das Zeichen „/“ (Schrägstrich) zu escapen? Setzen Sie zwei Backslashes davor, um es zu escapen, z. B. „\\\\\\\\/“.\",\n\t],\n\t\"vs/platform/history/browser/contextScopedHistoryWidget\": [\n\t\t\"Gibt an, ob Vorschläge sichtbar sind.\",\n\t],\n\t\"vs/platform/keybinding/common/abstractKeybindingService\": [\n\t\t\"({0}) wurde gedrückt. Es wird auf die zweite Taste in der Kombination gewartet...\",\n\t\t\"({0}) wurde gedrückt. Es wird auf die zweite Taste in der Kombination gewartet...\",\n\t\t\"Die Tastenkombination ({0}, {1}) ist kein Befehl.\",\n\t\t\"Die Tastenkombination ({0}, {1}) ist kein Befehl.\",\n\t],\n\t\"vs/platform/list/browser/listService\": [\n\t\t\"Workbench\",\n\t\t\"Ist unter Windows und Linux der STRG-Taste und unter macOS der Befehlstaste zugeordnet.\",\n\t\t\"Ist unter Windows und Linux der ALT-Taste und unter macOS der Wahltaste zugeordnet.\",\n\t\t\"Der Modifizierer zum Hinzufügen eines Elements in Bäumen und Listen zu einer Mehrfachauswahl mit der Maus (zum Beispiel im Explorer, in geöffneten Editoren und in der SCM-Ansicht). Die Mausbewegung \\\"Seitlich öffnen\\\" wird – sofern unterstützt – so angepasst, dass kein Konflikt mit dem Modifizierer für Mehrfachauswahl entsteht.\",\n\t\t\"Steuert, wie Elemente in Strukturen und Listen mithilfe der Maus geöffnet werden (sofern unterstützt). Bei übergeordneten Elementen, deren untergeordnete Elemente sich in Strukturen befinden, steuert diese Einstellung, ob ein Einfachklick oder ein Doppelklick das übergeordnete Elemente erweitert. Beachten Sie, dass einige Strukturen und Listen diese Einstellung ggf. ignorieren, wenn sie nicht zutrifft.\",\n\t\t\"Steuert, ob Listen und Strukturen ein horizontales Scrollen in der Workbench unterstützen. Warnung: Das Aktivieren dieser Einstellung kann sich auf die Leistung auswirken.\",\n\t\t\"Steuert, ob Klicks in der Bildlaufleiste Seite für Seite scrollen.\",\n\t\t\"Steuert den Struktureinzug in Pixeln.\",\n\t\t\"Steuert, ob die Struktur Einzugsführungslinien rendern soll.\",\n\t\t\"Steuert, ob Listen und Strukturen einen optimierten Bildlauf verwenden.\",\n\t\t\"Ein Multiplikator, der für die Mausrad-Bildlaufereignisse \\\"deltaX\\\" und \\\"deltaY\\\" verwendet werden soll.\",\n\t\t\"Multiplikator für Scrollgeschwindigkeit bei Drücken von ALT.\",\n\t\t\"Elemente beim Suchen hervorheben. Die Navigation nach oben und unten durchläuft dann nur die markierten Elemente.\",\n\t\t\"Filterelemente bei der Suche.\",\n\t\t\"Steuert den Standardsuchmodus für Listen und Strukturen in der Workbench.\",\n\t\t\"Bei der einfachen Tastaturnavigation werden Elemente in den Fokus genommen, die mit der Tastatureingabe übereinstimmen. Die Übereinstimmungen gelten nur für Präfixe.\",\n\t\t\"Hervorheben von Tastaturnavigationshervorgebungselemente, die mit der Tastatureingabe übereinstimmen. Beim nach oben und nach unten Navigieren werden nur die hervorgehobenen Elemente durchlaufen.\",\n\t\t\"Durch das Filtern der Tastaturnavigation werden alle Elemente herausgefiltert und ausgeblendet, die nicht mit der Tastatureingabe übereinstimmen.\",\n\t\t\"Steuert die Tastaturnavigation in Listen und Strukturen in der Workbench. Kann \\\"simple\\\" (einfach), \\\"highlight\\\" (hervorheben) und \\\"filter\\\" (filtern) sein.\",\n\t\t\"Bitte verwenden Sie stattdessen „workbench.list.defaultFindMode“ und „workbench.list.typeNavigationMode“.\",\n\t\t\"Verwenden Sie bei der Suche eine Fuzzyübereinstimmung.\",\n\t\t\"Verwenden Sie bei der Suche eine zusammenhängende Übereinstimmung.\",\n\t\t\"Steuert den Typ der Übereinstimmung, der beim Durchsuchen von Listen und Strukturen in der Workbench verwendet wird.\",\n\t\t\"Steuert, wie Strukturordner beim Klicken auf die Ordnernamen erweitert werden. Beachten Sie, dass einige Strukturen und Listen diese Einstellung ggf. ignorieren, wenn sie nicht zutrifft.\",\n\t\t\"Steuert die Funktionsweise der Typnavigation in Listen und Strukturen in der Workbench. Bei einer Festlegung auf \\\"trigger\\\" beginnt die Typnavigation, sobald der Befehl \\\"list.triggerTypeNavigation\\\" ausgeführt wird.\",\n\t],\n\t\"vs/platform/markers/common/markers\": [\n\t\t\"Fehler\",\n\t\t\"Warnung\",\n\t\t\"Info\",\n\t],\n\t\"vs/platform/quickinput/browser/commandsQuickAccess\": [\n\t\t\"zuletzt verwendet\",\n\t\t\"ähnliche Befehle\",\n\t\t\"häufig verwendet\",\n\t\t\"andere Befehle\",\n\t\t\"ähnliche Befehle\",\n\t\t\"{0}, {1}\",\n\t\t\"Der Befehl \\\"{0}\\\" hat zu einem Fehler geführt.\",\n\t],\n\t\"vs/platform/quickinput/browser/helpQuickAccess\": [\n\t\t\"{0}, {1}\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInput\": [\n\t\t\"Zurück\",\n\t\t\"Drücken Sie die EINGABETASTE, um Ihre Eingabe zu bestätigen, oder ESC, um den Vorgang abzubrechen.\",\n\t\t\"{0}/{1}\",\n\t\t\"Nehmen Sie eine Eingabe vor, um die Ergebnisse einzugrenzen.\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInputController\": [\n\t\t\"Aktivieren Sie alle Kontrollkästchen\",\n\t\t\"{0} Ergebnisse\",\n\t\t\"{0} ausgewählt\",\n\t\t\"OK\",\n\t\t\"Benutzerdefiniert\",\n\t\t\"Zurück ({0})\",\n\t\t\"Zurück\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInputList\": [\n\t\t\"Schnelleingabe\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInputUtils\": [\n\t\t\"Klicken, um den Befehl \\\"{0}\\\" auszuführen\",\n\t],\n\t\"vs/platform/theme/common/colorRegistry\": [\n\t\t\"Allgemeine Vordergrundfarbe. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente überschrieben wird.\",\n\t\t\"Allgemeine Vordergrundfarbe. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente überschrieben wird.\",\n\t\t\"Allgemeine Vordergrundfarbe für Fehlermeldungen. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente überschrieben wird.\",\n\t\t\"Vordergrundfarbe für Beschreibungstexte, die weitere Informationen anzeigen, z.B. für eine Beschriftung.\",\n\t\t\"Die für Symbole in der Workbench verwendete Standardfarbe.\",\n\t\t\"Allgemeine Rahmenfarbe für fokussierte Elemente. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente überschrieben wird.\",\n\t\t\"Ein zusätzlicher Rahmen um Elemente, mit dem diese von anderen getrennt werden, um einen größeren Kontrast zu erreichen.\",\n\t\t\"Ein zusätzlicher Rahmen um aktive Elemente, mit dem diese von anderen getrennt werden, um einen größeren Kontrast zu erreichen.\",\n\t\t\"Hintergrundfarbe der Textauswahl in der Workbench (z.B. für Eingabefelder oder Textbereiche). Diese Farbe gilt nicht für die Auswahl im Editor.\",\n\t\t\"Farbe für Text-Trennzeichen.\",\n\t\t\"Vordergrundfarbe für Links im Text.\",\n\t\t\"Vordergrundfarbe für angeklickte Links im Text und beim Zeigen darauf mit der Maus.\",\n\t\t\"Vordergrundfarbe für vorformatierte Textsegmente.\",\n\t\t\"Hintergrundfarbe für Blockzitate im Text.\",\n\t\t\"Rahmenfarbe für blockquote-Elemente im Text.\",\n\t\t\"Hintergrundfarbe für Codeblöcke im Text.\",\n\t\t\"Schattenfarbe von Widgets wie zum Beispiel Suchen/Ersetzen innerhalb des Editors.\",\n\t\t\"Die Rahmenfarbe von Widgets, z. B. Suchen/Ersetzen im Editor.\",\n\t\t\"Hintergrund für Eingabefeld.\",\n\t\t\"Vordergrund für Eingabefeld.\",\n\t\t\"Rahmen für Eingabefeld.\",\n\t\t\"Rahmenfarbe für aktivierte Optionen in Eingabefeldern.\",\n\t\t\"Hintergrundfarbe für aktivierte Optionen in Eingabefeldern.\",\n\t\t\"Hintergrundfarbe beim Daraufzeigen für Optionen in Eingabefeldern.\",\n\t\t\"Vordergrundfarbe für aktivierte Optionen in Eingabefeldern.\",\n\t\t\"Eingabefeld-Vordergrundfarbe für Platzhaltertext.\",\n\t\t\"Hintergrundfarbe bei der Eingabevalidierung für den Schweregrad der Information.\",\n\t\t\"Vordergrundfarbe bei der Eingabevalidierung für den Schweregrad der Information.\",\n\t\t\"Rahmenfarbe bei der Eingabevalidierung für den Schweregrad der Information.\",\n\t\t\"Hintergrundfarbe bei der Eingabevalidierung für den Schweregrad der Warnung.\",\n\t\t\"Vordergrundfarbe bei der Eingabevalidierung für den Schweregrad der Warnung.\",\n\t\t\"Rahmenfarbe bei der Eingabevalidierung für den Schweregrad der Warnung.\",\n\t\t\"Hintergrundfarbe bei der Eingabevalidierung für den Schweregrad des Fehlers.\",\n\t\t\"Vordergrundfarbe bei der Eingabevalidierung für den Schweregrad des Fehlers.\",\n\t\t\"Rahmenfarbe bei der Eingabevalidierung für den Schweregrad des Fehlers.\",\n\t\t\"Hintergrund für Dropdown.\",\n\t\t\"Hintergrund für Dropdownliste.\",\n\t\t\"Vordergrund für Dropdown.\",\n\t\t\"Rahmen für Dropdown.\",\n\t\t\"Vordergrundfarbe der Schaltfläche.\",\n\t\t\"Farbe des Schaltflächentrennzeichens.\",\n\t\t\"Hintergrundfarbe der Schaltfläche.\",\n\t\t\"Hintergrundfarbe der Schaltfläche, wenn darauf gezeigt wird.\",\n\t\t\"Rahmenfarbe der Schaltfläche.\",\n\t\t\"Sekundäre Vordergrundfarbe der Schaltfläche.\",\n\t\t\"Hintergrundfarbe der sekundären Schaltfläche.\",\n\t\t\"Hintergrundfarbe der sekundären Schaltfläche beim Daraufzeigen.\",\n\t\t\"Hintergrundfarbe für Badge. Badges sind kurze Info-Texte, z.B. für Anzahl Suchergebnisse.\",\n\t\t\"Vordergrundfarbe für Badge. Badges sind kurze Info-Texte, z.B. für Anzahl Suchergebnisse.\",\n\t\t\"Schatten der Scrollleiste, um anzuzeigen, dass die Ansicht gescrollt wird.\",\n\t\t\"Hintergrundfarbe vom Scrollbar-Schieber\",\n\t\t\"Hintergrundfarbe des Schiebereglers, wenn darauf gezeigt wird.\",\n\t\t\"Hintergrundfarbe des Schiebereglers, wenn darauf geklickt wird.\",\n\t\t\"Hintergrundfarbe des Fortschrittbalkens, der für zeitintensive Vorgänge angezeigt werden kann.\",\n\t\t\"Hintergrundfarbe für Fehlertext im Editor. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Vordergrundfarbe von Fehlerunterstreichungen im Editor.\",\n\t\t\"Wenn festgelegt, wird die Farbe doppelter Unterstreichungen für Fehler im Editor angezeigt.\",\n\t\t\"Hintergrundfarbe für Warnungstext im Editor. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Vordergrundfarbe von Warnungsunterstreichungen im Editor.\",\n\t\t\"Wenn festgelegt, wird die Farbe doppelter Unterstreichungen für Warnungen im Editor angezeigt.\",\n\t\t\"Hintergrundfarbe für Infotext im Editor. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Vordergrundfarbe von Informationsunterstreichungen im Editor.\",\n\t\t\"Wenn festgelegt, wird die Farbe doppelter Unterstreichungen für Infos im Editor angezeigt.\",\n\t\t\"Vordergrundfarbe der Hinweisunterstreichungen im Editor.\",\n\t\t\"Wenn festgelegt, wird die Farbe doppelter Unterstreichungen für Hinweise im Editor angezeigt.\",\n\t\t\"Rahmenfarbe aktiver Trennleisten.\",\n\t\t\"Hintergrundfarbe des Editors.\",\n\t\t\"Standardvordergrundfarbe des Editors.\",\n\t\t\"Einrastfunktion der Hintergrundfarbe für den Editor\",\n\t\t\"Einrastfunktion beim Daraufzeigen der Hintergrundfarbe für den Editor\",\n\t\t\"Hintergrundfarbe von Editor-Widgets wie zum Beispiel Suchen/Ersetzen.\",\n\t\t\"Vordergrundfarbe für Editorwidgets wie Suchen/Ersetzen.\",\n\t\t\"Rahmenfarbe von Editorwigdets. Die Farbe wird nur verwendet, wenn für das Widget ein Rahmen verwendet wird und die Farbe nicht von einem Widget überschrieben wird.\",\n\t\t\"Rahmenfarbe der Größenanpassungsleiste von Editorwigdets. Die Farbe wird nur verwendet, wenn für das Widget ein Größenanpassungsrahmen verwendet wird und die Farbe nicht von einem Widget außer Kraft gesetzt wird.\",\n\t\t\"Schnellauswahl der Hintergrundfarbe. Im Widget für die Schnellauswahl sind Auswahlelemente wie die Befehlspalette enthalten.\",\n\t\t\"Vordergrundfarbe der Schnellauswahl. Im Widget für die Schnellauswahl sind Auswahlelemente wie die Befehlspalette enthalten.\",\n\t\t\"Hintergrundfarbe für den Titel der Schnellauswahl. Im Widget für die Schnellauswahl sind Auswahlelemente wie die Befehlspalette enthalten.\",\n\t\t\"Schnellauswahlfarbe für das Gruppieren von Bezeichnungen.\",\n\t\t\"Schnellauswahlfarbe für das Gruppieren von Rahmen.\",\n\t\t\"Die Hintergrundfarbe der Tastenbindungsbeschriftung. Die Tastenbindungsbeschriftung wird verwendet, um eine Tastenkombination darzustellen.\",\n\t\t\"Die Vordergrundfarbe der Tastenbindungsbeschriftung. Die Tastenbindungsbeschriftung wird verwendet, um eine Tastenkombination darzustellen.\",\n\t\t\"Die Rahmenfarbe der Tastenbindungsbeschriftung. Die Tastenbindungsbeschriftung wird verwendet, um eine Tastenkombination darzustellen.\",\n\t\t\"Die Rahmenfarbe der Schaltfläche der Tastenbindungsbeschriftung. Die Tastenbindungsbeschriftung wird verwendet, um eine Tastenkombination darzustellen.\",\n\t\t\"Farbe der Editor-Auswahl.\",\n\t\t\"Farbe des gewählten Text für einen hohen Kontrast\",\n\t\t\"Die Farbe der Auswahl befindet sich in einem inaktiven Editor. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegende Dekorationen verdeckt.\",\n\t\t\"Farbe für Bereiche mit dem gleichen Inhalt wie die Auswahl. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Randfarbe für Bereiche, deren Inhalt der Auswahl entspricht.\",\n\t\t\"Farbe des aktuellen Suchergebnisses.\",\n\t\t\"Farbe der anderen Suchergebnisse. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Farbe des Bereichs, der die Suche eingrenzt. Die Farbe darf nicht deckend sein, damit sie nicht die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Randfarbe des aktuellen Suchergebnisses.\",\n\t\t\"Randfarbe der anderen Suchtreffer.\",\n\t\t\"Rahmenfarbe des Bereichs, der die Suche eingrenzt. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Farbe der Abfrageübereinstimmungen des Such-Editors\",\n\t\t\"Rahmenfarbe der Abfrageübereinstimmungen des Such-Editors\",\n\t\t\"Farbe des Texts in der Abschlussmeldung des Such-Viewlets.\",\n\t\t\"Hervorhebung unterhalb des Worts, für das ein Hoverelement angezeigt wird. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Hintergrundfarbe des Editor-Mauszeigers.\",\n\t\t\"Vordergrundfarbe des Editor-Mauszeigers\",\n\t\t\"Rahmenfarbe des Editor-Mauszeigers.\",\n\t\t\"Hintergrundfarbe der Hoverstatusleiste des Editors.\",\n\t\t\"Farbe der aktiven Links.\",\n\t\t\"Vordergrundfarbe für Inlinehinweise\",\n\t\t\"Hintergrundfarbe für Inlinehinweise\",\n\t\t\"Vordergrundfarbe von Inlinehinweisen für Typen\",\n\t\t\"Hintergrundfarbe von Inlinehinweisen für Typen\",\n\t\t\"Vordergrundfarbe von Inlinehinweisen für Parameter\",\n\t\t\"Hintergrundfarbe von Inlinehinweisen für Parameter\",\n\t\t\"Die für das Aktionssymbol \\\"Glühbirne\\\" verwendete Farbe.\",\n\t\t\"Die für das Aktionssymbol \\\"Automatische Glühbirnenkorrektur\\\" verwendete Farbe.\",\n\t\t\"Hintergrundfarbe für eingefügten Text. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Hintergrundfarbe für Text, der entfernt wurde. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Hintergrundfarbe für eingefügte Zeilen. Die Farbe darf nicht deckend sein, um zugrunde liegende Dekorationen nicht auszublenden.\",\n\t\t\"Hintergrundfarbe für Zeilen, die entfernt wurden. Die Farbe darf nicht deckend sein, um zugrunde liegende Dekorationen nicht auszublenden.\",\n\t\t\"Hintergrundfarbe für den Rand, an dem Zeilen eingefügt wurden.\",\n\t\t\"Hintergrundfarbe für den Rand, an dem die Zeilen entfernt wurden.\",\n\t\t\"Vordergrund des Diff-Übersichtslineals für eingefügten Inhalt.\",\n\t\t\"Vordergrund des Diff-Übersichtslineals für entfernten Inhalt.\",\n\t\t\"Konturfarbe für eingefügten Text.\",\n\t\t\"Konturfarbe für entfernten Text.\",\n\t\t\"Die Rahmenfarbe zwischen zwei Text-Editoren.\",\n\t\t\"Farbe der diagonalen Füllung des Vergleichs-Editors. Die diagonale Füllung wird in Ansichten mit parallelem Vergleich verwendet.\",\n\t\t\"Die Hintergrundfarbe von unveränderten Blöcken im Diff-Editor.\",\n\t\t\"Die Vordergrundfarbe von unveränderten Blöcken im Diff-Editor.\",\n\t\t\"Die Hintergrundfarbe des unveränderten Codes im Diff-Editor.\",\n\t\t\"Hintergrundfarbe der Liste/Struktur für das fokussierte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.\",\n\t\t\"Vordergrundfarbe der Liste/Struktur für das fokussierte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.\",\n\t\t\"Konturfarbe der Liste/Struktur für das fokussierte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.\",\n\t\t\"Umrissfarbe der Liste/des Baums für das fokussierte Element, wenn die Liste/der Baum aktiv und ausgewählt ist. Eine aktive Liste/Baum hat Tastaturfokus, eine inaktive nicht.\",\n\t\t\"Hintergrundfarbe der Liste/Struktur für das ausgewählte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.\",\n\t\t\"Vordergrundfarbe der Liste/Struktur für das ausgewählte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.\",\n\t\t\"Vordergrundfarbe des Symbols der Liste/Struktur für das ausgewählte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.\",\n\t\t\"Hintergrundfarbe der Liste/Struktur für das ausgewählte Element, wenn die Liste/Struktur inaktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.\",\n\t\t\"Vordergrundfarbe der Liste/Struktur für das ausgewählte Element, wenn die Liste/Baumstruktur inaktiv ist. Eine aktive Liste/Baumstruktur hat Tastaturfokus, eine inaktive hingegen nicht.\",\n\t\t\"Vordergrundfarbe des Symbols der Liste/Struktur für das ausgewählte Element, wenn die Liste/Struktur inaktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.\",\n\t\t\"Hintergrundfarbe der Liste/Struktur für das fokussierte Element, wenn die Liste/Struktur inaktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.\",\n\t\t\"Konturfarbe der Liste/Struktur für das fokussierte Element, wenn die Liste/Struktur inaktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.\",\n\t\t\"Hintergrund der Liste/Struktur, wenn mit der Maus auf Elemente gezeigt wird.\",\n\t\t\"Vordergrund der Liste/Struktur, wenn mit der Maus auf Elemente gezeigt wird.\",\n\t\t\"Drag & Drop-Hintergrund der Liste/Struktur, wenn Elemente mithilfe der Maus verschoben werden.\",\n\t\t\"Vordergrundfarbe der Liste/Struktur zur Trefferhervorhebung beim Suchen innerhalb der Liste/Struktur.\",\n\t\t\"Die Vordergrundfarbe der Liste/Struktur des Treffers hebt aktiv fokussierte Elemente hervor, wenn innerhalb der Liste / der Struktur gesucht wird.\",\n\t\t\"Vordergrundfarbe einer Liste/Struktur für ungültige Elemente, z.B. ein nicht ausgelöster Stamm im Explorer.\",\n\t\t\"Vordergrundfarbe für Listenelemente, die Fehler enthalten.\",\n\t\t\"Vordergrundfarbe für Listenelemente, die Warnungen enthalten.\",\n\t\t\"Hintergrundfarbe des Typfilterwidgets in Listen und Strukturen.\",\n\t\t\"Konturfarbe des Typfilterwidgets in Listen und Strukturen.\",\n\t\t\"Konturfarbe des Typfilterwidgets in Listen und Strukturen, wenn es keine Übereinstimmungen gibt.\",\n\t\t\"Schattenfarbe des Typfilterwidgets in Listen und Strukturen.\",\n\t\t\"Hintergrundfarbe der gefilterten Übereinstimmung\",\n\t\t\"Rahmenfarbe der gefilterten Übereinstimmung\",\n\t\t\"Strukturstrichfarbe für die Einzugsführungslinien.\",\n\t\t\"Strukturstrichfarbe für die Einzugslinien, die nicht aktiv sind.\",\n\t\t\"Tabellenrahmenfarbe zwischen Spalten.\",\n\t\t\"Hintergrundfarbe für ungerade Tabellenzeilen.\",\n\t\t\"Hintergrundfarbe für nicht hervorgehobene Listen-/Strukturelemente.\",\n\t\t\"Hintergrundfarbe von Kontrollkästchenwidget.\",\n\t\t\"Hintergrundfarbe des Kontrollkästchenwidgets, wenn das Element ausgewählt ist, in dem es sich befindet.\",\n\t\t\"Vordergrundfarbe von Kontrollkästchenwidget.\",\n\t\t\"Rahmenfarbe von Kontrollkästchenwidget.\",\n\t\t\"Rahmenfarbe des Kontrollkästchenwidgets, wenn das Element ausgewählt ist, in dem es sich befindet.\",\n\t\t\"Verwenden Sie stattdessen \\\"quickInputList.focusBackground\\\".\",\n\t\t\"Die Hintergrundfarbe der Schnellauswahl für das fokussierte Element.\",\n\t\t\"Die Vordergrundfarbe des Symbols der Schnellauswahl für das fokussierte Element.\",\n\t\t\"Die Hintergrundfarbe der Schnellauswahl für das fokussierte Element.\",\n\t\t\"Rahmenfarbe von Menüs.\",\n\t\t\"Vordergrundfarbe von Menüelementen.\",\n\t\t\"Hintergrundfarbe von Menüelementen.\",\n\t\t\"Vordergrundfarbe des ausgewählten Menüelements im Menü.\",\n\t\t\"Hintergrundfarbe des ausgewählten Menüelements im Menü.\",\n\t\t\"Rahmenfarbe des ausgewählten Menüelements im Menü.\",\n\t\t\"Farbe eines Trenner-Menüelements in Menüs.\",\n\t\t\"Symbolleistenhintergrund beim Bewegen der Maus über Aktionen\",\n\t\t\"Symbolleistengliederung beim Bewegen der Maus über Aktionen\",\n\t\t\"Symbolleistenhintergrund beim Halten der Maus über Aktionen\",\n\t\t\"Hervorhebungs-Hintergrundfarbe eines Codeschnipsel-Tabstopps.\",\n\t\t\"Hervorhebungs-Rahmenfarbe eines Codeschnipsel-Tabstopps.\",\n\t\t\"Hervorhebungs-Hintergrundfarbe des letzten Tabstopps eines Codeschnipsels.\",\n\t\t\"Rahmenfarbe zur Hervorhebung des letzten Tabstopps eines Codeschnipsels.\",\n\t\t\"Farbe der Breadcrumb-Elemente, die den Fokus haben.\",\n\t\t\"Hintergrundfarbe der Breadcrumb-Elemente.\",\n\t\t\"Farbe der Breadcrumb-Elemente, die den Fokus haben.\",\n\t\t\"Die Farbe der ausgewählten Breadcrumb-Elemente.\",\n\t\t\"Hintergrundfarbe des Breadcrumb-Auswahltools.\",\n\t\t\"Hintergrund des aktuellen Headers in Inlinezusammenführungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Hintergrund für den aktuellen Inhalt in Inlinezusammenführungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Hintergrund für eingehende Header in Inlinezusammenführungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Hintergrund für eingehenden Inhalt in Inlinezusammenführungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Headerhintergrund für gemeinsame Vorgängerelemente in Inlinezusammenführungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Hintergrund des Inhalts gemeinsamer Vorgängerelemente in Inlinezusammenführungskonflikt. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Rahmenfarbe für Kopfzeilen und die Aufteilung in Inline-Mergingkonflikten.\",\n\t\t\"Aktueller Übersichtslineal-Vordergrund für Inline-Mergingkonflikte.\",\n\t\t\"Eingehender Übersichtslineal-Vordergrund für Inline-Mergingkonflikte.\",\n\t\t\"Hintergrund des Übersichtslineals des gemeinsamen übergeordneten Elements bei Inlinezusammenführungskonflikten.\",\n\t\t\"Übersichtslinealmarkerfarbe für das Suchen von Übereinstimmungen. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Übersichtslinealmarkerfarbe für das Hervorheben der Auswahl. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.\",\n\t\t\"Minimap-Markerfarbe für gefundene Übereinstimmungen.\",\n\t\t\"Minimap-Markerfarbe für wiederholte Editorauswahlen.\",\n\t\t\"Minimap-Markerfarbe für die Editorauswahl.\",\n\t\t\"Minimapmarkerfarbe für Informationen.\",\n\t\t\"Minimapmarkerfarbe für Warnungen\",\n\t\t\"Minimapmarkerfarbe für Fehler\",\n\t\t\"Hintergrundfarbe der Minimap.\",\n\t\t\"Deckkraft von Vordergrundelementen, die in der Minimap gerendert werden. Beispiel: „#000000c0“ wird die Elemente mit einer Deckkraft von 75 % rendern.\",\n\t\t\"Hintergrundfarbe des Minimap-Schiebereglers.\",\n\t\t\"Hintergrundfarbe des Minimap-Schiebereglers beim Daraufzeigen.\",\n\t\t\"Hintergrundfarbe des Minimap-Schiebereglers, wenn darauf geklickt wird.\",\n\t\t\"Die Farbe, die für das Problemfehlersymbol verwendet wird.\",\n\t\t\"Die Farbe, die für das Problemwarnsymbol verwendet wird.\",\n\t\t\"Die Farbe, die für das Probleminfosymbol verwendet wird.\",\n\t\t\"Die in Diagrammen verwendete Vordergrundfarbe.\",\n\t\t\"Die für horizontale Linien in Diagrammen verwendete Farbe.\",\n\t\t\"Die in Diagrammvisualisierungen verwendete Farbe Rot.\",\n\t\t\"Die in Diagrammvisualisierungen verwendete Farbe Blau.\",\n\t\t\"Die in Diagrammvisualisierungen verwendete Farbe Gelb.\",\n\t\t\"Die in Diagrammvisualisierungen verwendete Farbe Orange.\",\n\t\t\"Die in Diagrammvisualisierungen verwendete Farbe Grün.\",\n\t\t\"Die in Diagrammvisualisierungen verwendete Farbe Violett.\",\n\t],\n\t\"vs/platform/theme/common/iconRegistry\": [\n\t\t\"Die ID der zu verwendenden Schriftart. Sofern nicht festgelegt, wird die zuerst definierte Schriftart verwendet.\",\n\t\t\"Das der Symboldefinition zugeordnete Schriftzeichen.\",\n\t\t\"Symbol für Aktion zum Schließen in Widgets\",\n\t\t\"Symbol für den Wechsel zur vorherigen Editor-Position.\",\n\t\t\"Symbol für den Wechsel zur nächsten Editor-Position.\",\n\t],\n\t\"vs/platform/undoRedo/common/undoRedoService\": [\n\t\t\"Die folgenden Dateien wurden geschlossen und auf dem Datenträger geändert: {0}.\",\n\t\t\"Die folgenden Dateien wurden auf inkompatible Weise geändert: {0}.\",\n\t\t\"\\\"{0}\\\" konnte nicht für alle Dateien rückgängig gemacht werden. {1}\",\n\t\t\"\\\"{0}\\\" konnte nicht für alle Dateien rückgängig gemacht werden. {1}\",\n\t\t\"\\\"{0}\\\" konnte nicht für alle Dateien rückgängig gemacht werden, da Änderungen an {1} vorgenommen wurden.\",\n\t\t\"\\\"{0}\\\" konnte nicht für alle Dateien rückgängig gemacht werden, weil bereits ein Vorgang zum Rückgängigmachen oder Wiederholen für \\\"{1}\\\" durchgeführt wird.\",\n\t\t\"\\\"{0}\\\" konnte nicht für alle Dateien rückgängig gemacht werden, weil in der Zwischenzeit bereits ein Vorgang zum Rückgängigmachen oder Wiederholen durchgeführt wurde.\",\n\t\t\"Möchten Sie \\\"{0}\\\" für alle Dateien rückgängig machen?\",\n\t\t\"&&In {0} Dateien rückgängig machen\",\n\t\t\"&&Datei rückgängig machen\",\n\t\t\"\\\"{0}\\\" konnte nicht rückgängig gemacht werden, weil bereits ein Vorgang zum Rückgängigmachen oder Wiederholen durchgeführt wird.\",\n\t\t\"Möchten Sie \\\"{0}\\\" rückgängig machen?\",\n\t\t\"&&Ja\",\n\t\t\"Nein\",\n\t\t\"\\\"{0}\\\" konnte nicht in allen Dateien wiederholt werden. {1}\",\n\t\t\"\\\"{0}\\\" konnte nicht in allen Dateien wiederholt werden. {1}\",\n\t\t\"\\\"{0}\\\" konnte nicht in allen Dateien wiederholt werden, da Änderungen an {1} vorgenommen wurden.\",\n\t\t\"\\\"{0}\\\" konnte nicht für alle Dateien wiederholt werden, weil bereits ein Vorgang zum Rückgängigmachen oder Wiederholen für \\\"{1}\\\" durchgeführt wird.\",\n\t\t\"\\\"{0}\\\" konnte nicht für alle Dateien wiederholt werden, weil in der Zwischenzeit bereits ein Vorgang zum Rückgängigmachen oder Wiederholen durchgeführt wurde.\",\n\t\t\"\\\"{0}\\\" konnte nicht wiederholt werden, weil bereits ein Vorgang zum Rückgängigmachen oder Wiederholen durchgeführt wird.\",\n\t],\n\t\"vs/platform/workspace/common/workspace\": [\n\t\t\"Codearbeitsbereich\",\n\t]\n});"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DA<PERSON>,OAAO,+BAAgC,CACtC,+CAAgD,CAC/C,WACD,EACA,yCAA0C,CACzC,SACD,EACA,gDAAiD,CAChD,oCACA,yBACA,iCACD,EACA,4CAA6C,CAC5C,UACA,sCACD,EACA,uCAAwC,CACvC,qEACA,wLACD,EACA,8CAA+C,CAC9C,iBACD,EACA,uCAAwC,CACvC,cACA,eACA,YACA,iBACA,sBACD,EACA,qDAAsD,CACrD,YACD,EACA,+CAAgD,CAC/C,aACD,EACA,qCAAsC,CACrC,qBACD,EACA,uCAAwC,CACvC,SACA,0BACA,4BACA,sBACA,sBACA,eACA,wBACD,EACA,yBAA0B,CACzB,QACD,EACA,8BAA+B,CAC9B,WACA,0CACA,wFACA,wFACA,0BACA,uFACD,EACA,kCAAmC,CAClC,OACA,gBACA,MACA,UACA,OACA,gBACA,MACA,QACA,UACA,gBACA,SACA,SACA,UACA,gBACA,MACA,UACA,UACA,gBACA,MACA,OACD,EACA,0BAA2B,CAC1B,GACD,EACA,+CAAgD,CAC/C,SACA,wDACA,yFACA,oRACA,2MACD,EACA,iCAAkC,CACjC,8CACA,8CACA,8BACD,EACA,qCAAsC,CACrC,qBACA,mBACA,gBACA,cACA,uBACA,mBACD,EACA,4CAA6C,CAC5C,iSACA,iDACD,EACA,2DAA4D,CAC3D,6DACA,2DACA,8DACA,eACA,6FACA,6BACA,sBACA,yBACA,wFACA,OACA,iCACA,wDACA,kCACA,8BACD,EACA,6CAA8C,CAC7C,oEACA,0EACD,EACA,kDAAmD,CAClD,0DACA,wDACA,4DACD,EACA,8DAA+D,CAC9D,mDACA,kDACA,4EACA,+DACA,qCACA,cACA,iBACA,0BACA,4CACA,0CACA,6BACA,uCACA,uCACA,qCACD,EACA,wDAAyD,CACxD,qEACD,EACA,kEAAmE,CAClE,kCACA,oDACA,gBACA,qDACA,2BACA,6BACD,EACA,kEAAmE,CAClE,+BACA,8BACA,+BACA,8BACA,oCACA,oCACA,2CACD,EACA,uDAAwD,CACvD,qDACA,sDACA,mCACA,mCACD,EACA,oDAAqD,CACpD,SACA,iKACA,4RACA,6JACA,sHACA,+DACA,0GACA,sGACA,qDACA,6EACA,2DACA,wFACA,sEACA,wEACA,yHACA,wGACA,uHACA,gGACA,6EACA,wFACA,gLACA,kFACA,gEACA,mEACA,oJACA,gEACA,mEACA,8GACA,6HACA,mFACA,8FACA,gHACA,uIACA,qGACA,kHACA,2CACA,kCACA,+DACA,uEACA,6CACA,mDACA,gEACA,8EACA,8FACA,mGACA,gFACA,yHACD,EACA,wCAAyC,CACxC,oFACA,2DACA,qDACA,kIACA,mEACA,qIACA,yEACA,iGACA,yDACA,iGACA,qDACA,wHACA,iEACA,oDACA,6FACA,8EACA,6GACA,2OACA,2IACA,uNACA,0KACA,6QACA,qPACA,yNACA,oSACA,8CACA,0GACA,wHACA,gEACA,uDACA,iHACA,mMACA,uGACA,4GACA,yGACA,2GACA,sGACA,4HACA,+HACA,gIACA,sIACA,yHACA,kDACA,4FACA,qGACA,oJACA,mFACA,iPACA,yMACA,uKACA,mEACA,sGACA,+EACA,8SACA,kHACA,0CACA,gCACA,wGACA,iHACA,kCACA,yMACA,kHACA,yDACA;AAAA;AAAA;AAAA,gFACA,0CACA,yDACA,qFACA,0HACA,4GACA,yCACA,oDACA,qEACA,mEACA,sFACA,6FACA,iFACA,mFACA,kGACA,uGACA,8DACA,6DACA,0CACA,+DACA,6DACA,kFACA,waACA,0CACA,sDACA,4EACA,mDACA,yCACA,8FACA,+BACA,yLACA,8DACA,mDACA,wDACA,0DACA,gEACA,qDACA,0DACA,4DACA,4CACA,+CACA,0EACA,mMACA,oHACA,kMACA,2FACA,6FACA,kEACA,kGACA,2EACA,oGACA,yHACA,wEACA,8MACA,kJACA,0FACA,2CACA,6EACA,6CACA,+EACA,wGACA,6EACA,kEACA,4FACA,kEACA,gEACA,4CACA,+FACA,uDACA,6EACA,6FACA,oFACA,gMACA,2FACA,sGACA,iLACA,4FACA,8FACA,uGACA,mGACA,0VACA,0GACA,uEACA,uEACA,2FACA,sGACA,uGACA,yJACA,gEACA,mEACA,qEACA,yFACA,mYACA,+DACA,kEACA,+DACA,gEACA,mEACA,gEACA,kEACA,+DACA,kEACA,8DACA,+DACA,kEACA,8DACA,oEACA,iEACA,8DACA,+DACA,8DACA,mEACA,qEACA,gEACA,uEACA,iEACA,4DACA,8DACA,0FACA,sGACA,wDACA,mFACA,0EACA,0FACA,oDACA,4KACA,gJACA,+FACA,uHACA,4EACA,yJACA,yGACA,gIACA,kNACA,uGACA,8MACA,4SACA,gBACA,qFACA,6GACA,mGACA,mIACA,8GACA,8GACA,2IACA,uIACA,uHACA,2HACA,kGACA,+GACA,mIACA,4JACA,uDACA,2DACA,2HACA,oLACA,wOACA,2IACA,2GACA,+DACA,+DACA,qHACA,sJACA,2CACA,8CACA,sIACA,qFACA,yFACA,8DACA,0DACA,wFACA,gHACA,mFACA,sEACA,oCACA,8CACA,gHACA,kDACA,gEACA,2BACA,uPACA,4GACA,iDACA,kEACA,yFACA,qFACA,sDACA,mEACA,+CACA,oFACA,qEACA,mDACA,2HACA,2CACA,iEACA,iEACA,+DACA,gOACA,iHACA,8BACA,oLACA,kFACA,2HACA,0EACA,kDACA,4LACA,oEACA,gCACA,4GACA,2GACA,iEACA,0FACA,sFACA,iUACA,2CACA,sDACA,mGACA,mGACA,8EACA,4EACA,uDACA,yDACA,yFACA,qFACA,8FACA,4EACA,8DACA,qDACA,6EACA,mDACA,wEACA,qHACA,+FACA,4EACA,uCACA,oDACA,6DACA,4FACA,mEACA,0MACA,8EACA,2FACA,wDACA,sGACA,wGACA,4FACA,qDACA,gDACA,mEACA,gEACA,2DACA,yCACA,gGACA,8DACA,yKACA,mGACA,4HACA,wGACA,2CACA,4MACA,gKACA,yFACA,qGACA,2CACA,sJACA,yCACA,uEACA,4DACA,kGACA,yFACA,yEACA,4DACA,2LACA,sHACA,4HACA,kCACA,+DACA,2DACA,qFACA,8DACA,+HACA,0GACA,oGACD,EACA,4CAA6C,CAC5C,qEACA,yEACA,gLACA,iEACA,kNACA,yDACA,+BACA,2IACA,mCACA,gCACA,2DACA,0GACA,mEACA,sHACA,+DACA,+DACA,+DACA,+DACA,+DACA,+DACA,uEACA,uEACA,uEACA,uEACA,uEACA,uEACA,8CACA,sFACA,8CACA,6HACA,4BACA,gDACA,uDACA,4CACA,oDACA,oDACA,uGACA,mEACA,iSACA,yCACA,8CACA,8CACA,uKACA,sDACA,yDACA,6DACA,gGACA,gGACA,gGACA,gGACA,gGACA,gGACA,8CACA,oHACA,oHACA,oHACA,oHACA,oHACA,oHACA,kHACA,kHACA,kHACA,kHACA,kHACA,kHACA,uEACA,2EACD,EACA,qCAAsC,CACrC,iEACA,uHACA,wFACA,iDACA,+CACA,6DACA,kFACA,0DACA,yHACA,sDACA,gDACA,6DACA,iEACA,4CACA,qDACA,0DACA,kDACA,+DACA,iEACA,4FACA,oCACA,wFACA,uEACA,qEACA,uEACA,wEACA,4EACA,0EACA,iEACA,iFACA,0EACA,mEACA,wEACA,yEACA,yEACA,iFACA,+FACA,mFACA,gGACD,EACA,6BAA8B,CAC7B,QACA,kBACA,SACA,YACA,cACA,cACA,qBACA,WACA,OACA,QACA,WACA,gBACA,eACA,UACA,QACA,YACA,OACA,OACA,SACA,WACA,QACA,cACA,eACA,WACA,eACA,WACA,WACD,EACA,2CAA4C,CAC3C,UACD,EACA,mCAAoC,CACnC,SACD,EACA,qCAAsC,CACrC,qCACA,0BACA,mDACA,iBACA,oCACA,oBACA,mCACA,gBACA,wEACA,0CACA,kDACD,EACA,+CAAgD,CAC/C,sBACA,aACD,EACA,sDAAuD,CACtD,eACA,iCACA,yBACA,2BACA,8BACA,wBACD,EACA,4DAA6D,CAC5D,4EACA,kBACA,2BACA,qBACA,mBACD,EACA,4DAA6D,CAC5D,8CACA,8CACD,EACA,sDAAuD,CACtD,wBACD,EACA,gDAAiD,CAChD,iBACA,eACA,eACA,eACA,aACA,WACA,WACA,WACA,eACA,eACA,YACA,YACA,YACA,gBACA,cACA,cACA,cACA,iCACD,EACA,kDAAmD,CAClD,sEACD,EACA,0DAA2D,CAC1D,uCACA,oEACA,wDACA,+EACA,gDACA,2EACA,+BACA,kCACA,2DACA,+CACA,8CACA,kCACA,gCACA,2DACA,+CACA,8CACA,kCACA,iBACA,4DACA,gDACA,+CACA,mCACA,uBACA,0DACA,mBACA,+CACA,6BACA,8CACD,EACA,+DAAgE,CAC/D,oFACA,mKACD,EACA,4DAA6D,CAC5D,4CACA,mCACA,gCACD,EACA,sDAAuD,CACtD,sBACA,2BACA,cACA,SACA,oBACA,cACA,cACA,aACD,EACA,uDAAwD,CACvD,wEACA,8BACA,uBACD,EACA,wDAAyD,CACxD,kDACA,qBACD,EACA,0DAA2D,CAC1D,6DACA,4CACD,EACA,qEAAsE,CACrE,6DACA,6DACA,2BACA,uDACD,EACA,4CAA6C,CAC5C,6BACA,+BACA,gCACA,4BACA,4BACA,6BACD,EACA,oDAAqD,CACpD,UACA,kBACA,wBACA,eACA,eACA,WACA,gBACA,eACA,QACA,gCACD,EACA,kDAAmD,CAClD,qCACA,wBACD,EACA,kEAAmE,CAClE,qBACA,4HACD,EACA,gEAAiE,CAChE,mDACA,iCACA,wFACA,gCACA,wCACD,EACA,6DAA8D,CAC7D,aACA,uBACA,kBACA,kBACA,oBACA,mBACA,6BACA,4BACD,EACA,uEAAwE,CACvE,yFACD,EACA,qEAAsE,CACrE,+CACA,6BACA,iFACD,EACA,+DAAgE,CAC/D,4FACD,EACA,gDAAiD,CAChD,yFACA,SACA,WACA;AAAA;AAAA;AAAA;AAAA,aACA;AAAA;AAAA;AAAA;AAAA,aACA;AAAA;AAAA;AAAA;AAAA,aACA;AAAA;AAAA;AAAA;AAAA,aACA,wBACA,qBACA,eACA,4BACA,wCACA,2EACA,0GACA,sCACA,sCACA,4BACA,2BACA,WACA,YACD,EACA,4CAA6C,CAC5C,2DACA,2EACA,4EACA,kDACA,uDACA,mEACA,oEACA,kBACA,SACA,SACA,+BACA,gCACA,oBACA,eACA,WACA,WACA,WACA,gBACA,sBACA,8HACA,cACA,mBACA,eACA,4BACA,qCACA,4BACA,2MACD,EACA,4CAA6C,CAC5C,YACA,4BACA,SACA,yBACA,kBACA,8BACA,uBACA,0BACA,qCACA,wCACA,cACA,iBACA,6CACA,sCACA,uCACA,wCACA,kCACA,eACD,EACA,uDAAwD,CACvD,oJACA,mDACA,6DACA,4DACA,wEACA,uEACD,EACA,8CAA+C,CAC9C,oCACA,+BACA,sDACD,EACA,0CAA2C,CAC1C,0CACA,8CACA,yDACA,4DACD,EACA,iDAAkD,CACjD,uBACA,qBACD,EACA,gDAAiD,CAChD,6DACA,iEACA,yDACA,gEACA,yEACA,wBACA,wEACA,sBACD,EACA,sDAAuD,CACtD,SACA,UACA,OACA,UACA,gBACA,wBACA,wBACA,6DACA,+EACA,8DACA,iFACA,kEACA,qFACA,uDACD,EACA,oDAAqD,CACpD,WACA,eACA,0CACA,4BACA,qBACA,uBACA,oCACA,sBACA,gBACA,2CACA,8BACA,2BACA,wBACA,2CACA,8BACA,uCACA,kBACA,8CACA,iCACA,6BACA,+BACA,sCACA,oBACA,8CACA,iCACA,4BACA,8BACA,6CACA,+CACA,4BACA,oBACA,sBACA,WACA,oCACA,WACA,iCACA,eACA,gCACA,UACD,EACA,qEAAsE,CACrE,8CACD,EACA,iEAAkE,CACjE,uGACA,kBACA,WACD,EACA,2DAA4D,CAC3D,eACA,cACA,UACD,EACA,6DAA8D,CAC7D,+BACA,mBACA,UACD,EACA,uDAAwD,CACvD,oCACA,wCACA,6CACA,gDACA,uCACA,2BACA,8BACA,qCACD,EACA,wDAAyD,CACxD,4GACA,6CACA,oBACD,EACA,wCAAyC,CACxC,wCACA,oCACA,uCACA,wCACA,wCACA,yCACA,yCACA,0CACA,mCACA,mCACD,EACA,2DAA4D,CAC3D,kBACA,yJACA,uKACD,EACA,yDAA0D,CACzD,mBACA,wCACA,6CACA,wCACA,8BACD,EACA,0DAA2D,CAC1D,iCACA,iCACD,EACA,oDAAqD,CACpD,qCACA,mCACA,qCACA,oCACA,qCACA,0DACA,gCACA,kCACA,iDACA,6BACA,uCACA,oCACD,EACA,uDAAwD,CACvD,gCACA,mBACA,iBACA,mBACA,gBACA,+GACA,2BACA,qBACD,EACA,uDAAwD,CACvD,wCACA,uCACA,+BACA,kDACA,gBACA,oDACA,iBACA,4BACA,WACA,6BACA,6BACD,EACA,+DAAgE,CAC/D,YACD,EACA,0EAA2E,CAC1E,iDACA,4DACA,sIACA,uFACD,EACA,0EAA2E,CAC1E,gEACD,EACA,2EAA4E,CAC3E,gEACA,+DACA,YACA,YACA,QACD,EACA,wDAAyD,CACxD,yBACD,EACA,4DAA6D,CAC5D,2BACA,6BACA,4BACA,8BACA,sBACA,wBACA,8BACA,gCACA,+BACA,iCACA,+BACA,8BACA,6BACA,uCACA,mBACA,eACA,qBACA,yBACA,0BACA,6BACA,yBACA,uBACA,4CACA,iCACA,+BACA,0CACA,0BACA,+BACA,uCACD,EACA,wDAAyD,CACxD,oCACA,mEACD,EACA,wCAAyC,CACxC,yEACA,2DACA,sBACA,cACA,mBACA,iBACA,mBACA,gBACA,sCACA,gBACD,EACA,sDAAuD,CACtD,8DACD,EACA,oDAAqD,CACpD,+BACA,8BACA,gCACA,kCACA,iCACA,mCACA,sCACA,wCACA,+BACA,iCACA,iEACA,wCACA,sEACA,uCACA,oEACA,mEACA,4DACA,gCACA,2BACA,+BACA,oCACA,8BACA,kCACD,EACA,0DAA2D,CAC1D,+BACD,EACA,gEAAiE,CAChE,gEACA,+DACA,eACA,4DACD,EACA,8CAA+C,CAC9C,wEACA,eACA,uDACA,wCACA,oDACA,mDACA,0DACA,8EACA,6EACA,uFACA,uFACA,qCACA,8CACA,mHACA,sFACA,+DACA,+DACD,EACA,4DAA6D,CAC5D,yEACA,6CACA,yBACA,2HACA,uGACD,EACA,8DAA+D,CAC9D,yGACA,kEACA,4CACA,uBACA,yBACA,kBACA,gBACA,sBACA,iBACA,mBACA,sBACA,kBACA,gBACA,mBACA,mBACA,mBACA,uBACA,mBACA,eACA,qBACA,eACA,sBACA,sBACA,2BACA,sBACA,gBACA,eACA,eACA,wBACA,gBACA,qBACA,eACA,kBACD,EACA,yDAA0D,CACzD,iEACA,qEACD,EACA,0CAA2C,CAC1C,iBACA,uFACA,gCACA,6BACA,6DACA,+DACA,gEACA,oBACA,0GACD,EACA,oDAAqD,CACpD,kEACA,mIACA,uCACD,EACA,oDAAqD,CACpD,qBACA,sBACA,yBACA,uBACD,EACA,uDAAwD,CACvD,kEACA,wEACA,uEACA,yCACD,EACA,qDAAsD,CACrD,UACA,SACA,WACA,WACA,aACA,UACA,UACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,SACA,UACA,UACA,QACA,MACA,OACA,OACA,SACA,YACA,UACA,WACA,WACA,MACA,MACA,SACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,KACD,EACA,6DAA8D,CAC7D,gCACA,kCACA,qBACA,uBACA,+BACA,6BACA,yCACA,kDACA,wCACA,qBACD,EACA,4CAA6C,CAC5C,2CACA,+CACA,wDACA,6HACA,mFACA,sFACA,8EACA,sFACD,EACA,sDAAuD,CACtD,oEACA,wBACA,cACA,cACA,WACA,WACA,cACA,mBACA,gBACA,mDACD,EACA,kDAAmD,CAClD,0CACA,qCACA,0CACA,yEACA,qFACA,qEACA,qDACA,mGACA,qDACA,kBACA,uBACA,cACA,eACA,UACA,WACA,qBACD,EACA,yDAA0D,CACzD,eACA,iBACD,EACA,0DAA2D,CAC1D,4DACA,uBACD,EACA,wDAAyD,CACxD,WACD,EACA,oDAAqD,CACpD,4IACA,iJACA,8IACA,2IACA,iJACA,kJACA,iJACA,uJACA,+IACA,2IACA,4IACA,6IACA,gJACA,qJACA,mJACA,uJACA,+IACA,4IACA,gJACA,4IACA,6IACA,6IACA,+IACA,4IACA,oJACA,+IACA,oJACA,oJACA,+IACA,2IACA,mJACA,gJACA,+IACD,EACA,kEAAmE,CAClE,qCACA,mGACA,uEACD,EACA,sDAAuD,CACtD,8BACD,EACA,kEAAmE,CAClE,2EACA,yEACA,gEACA,gEACA,sGACA,sGACA,sCACA,oDACA,yBACA,2CACA,2DACA,6CACA,6DACA,wCACA,yDACA,wCACA,qDACA,wCACA,kEACA,8BACA,kEACA,wBACA,qFACA,4DACD,EACA,0EAA2E,CAC1E,sCACA,8CACA;AAAA;AAAA,2HACA,sDACA,YACD,EACA,iEAAkE,CACjE,qLACA,2LACA,mJACA,iFACA,wFACA,qDACA,sKACA,iLACA,2KACD,EACA,4DAA6D,CAC5D,4CACA,2CACA,sCACD,EACA,0DAA2D,CAC1D,iBACD,EACA,mDAAoD,CACnD,UACA,QACA,OACA,QACA,gBACA,YACD,EACA,8CAA+C,CAC9C,4CACA,mBACA,8BACA,eACD,EACA,gDAAiD,CAChD,8EACA,mDACA,+BACA,gCACA,iCACA,oCACA,kDACD,EACA,sDAAuD,CACtD,YACA,YACA;AAAA,UACD,EACA,sCAAuC,CACtC,aACA,yBACD,EACA,yCAA0C,CACzC,kBACD,EACA,gDAAiD,CAChD,sBACA,uBACA,kCACA,0BACA,+BACA,qCACA,kCACA,oCACA,wBACA,yBACA,gCACA,iBACA,8BACA,+BACA,+BACA,8BACA,8BACA,uBACA,wBACA,wBACD,EACA,yDAA0D,CACzD,gEACA,8FACA,gFACA,0EACA,gFACA,0EACA,wDACA,gNACA,iFACA,sGACD,EACA,mDAAoD,CACnD,qEACD,EACA,2CAA4C,CAC3C,qCACA,4LACA,uCACA,uCACA,qBACA,+DACA,mCACA,4DACA;AAAA,4BACD,EACA,4CAA6C,CAC5C,uDACA,uDACA,yDACA,qEACA,yFACA,qDACA,6EACA,+BACA,mEACD,EACA,wCAAyC,CACxC,mBACA,4BACA,iCACA,mFACA,4JACD,EACA,yDAA0D,CACzD,0CACD,EACA,0DAA2D,CAC1D,uFACA,uFACA,oDACA,mDACD,EACA,uCAAwC,CACvC,YACA,0FACA,sFACA,sWACA,oaACA,iLACA,wEACA,wCACA,kEACA,0EACA,4GACA,qEACA,uHACA,gCACA,+EACA,oLACA,yMACA,uJACA,4JACA,gIACA,4DACA,2EACA,0HACA,6LACA,0NACD,EACA,qCAAsC,CACrC,SACA,UACA,MACD,EACA,qDAAsD,CACrD,oBACA,sBACA,sBACA,iBACA,sBACA,WACA,kDACD,EACA,iDAAkD,CACjD,UACD,EACA,4CAA6C,CAC5C,YACA,2GACA,UACA,8DACD,EACA,sDAAuD,CACtD,0CACA,iBACA,oBACA,KACA,oBACA,kBACA,WACD,EACA,gDAAiD,CAChD,gBACD,EACA,iDAAkD,CACjD,6CACD,EACA,yCAA0C,CACzC,2HACA,2HACA,kJACA,iHACA,gEACA,kJACA,oIACA,2IACA,wJACA,kCACA,yCACA,yFACA,uDACA,+CACA,kDACA,iDACA,oFACA,mEACA,kCACA,kCACA,6BACA,4DACA,iEACA,wEACA,iEACA,uDACA,sFACA,sFACA,iFACA,kFACA,kFACA,6EACA,kFACA,kFACA,6EACA,+BACA,oCACA,+BACA,0BACA,wCACA,2CACA,wCACA,kEACA,mCACA,qDACA,sDACA,wEACA,kGACA,kGACA,6EACA,0CACA,iEACA,kEACA,uGACA,gJACA,0DACA,iGACA,kJACA,4DACA,oGACA,8IACA,gEACA,gGACA,2DACA,mGACA,oCACA,gCACA,wCACA,yDACA,2EACA,wEACA,6DACA,4KACA,yOACA,kIACA,kIACA,mJACA,+DACA,wDACA,8IACA,8IACA,yIACA,6JACA,4BACA,0DACA,gKACA,iKACA,kEACA,uCACA,oIACA,gJACA,2CACA,qCACA,qJACA,yDACA,+DACA,6DACA,gLACA,2CACA,0CACA,sCACA,sDACA,2BACA,yCACA,yCACA,oDACA,oDACA,wDACA,wDACA,gEACA,uFACA,+IACA,oJACA,yIACA,gJACA,uEACA,uEACA,0EACA,sEACA,0CACA,sCACA,+CACA,yIACA,uEACA,uEACA,kEACA,qLACA,qLACA,gLACA,sLACA,wLACA,wLACA,oMACA,0LACA,kMACA,sMACA,uLACA,kLACA,+EACA,+EACA,iGACA,wGACA,qJACA,uHACA,gEACA,mEACA,kEACA,6DACA,sGACA,+DACA,sDACA,iDACA,2DACA,sEACA,wCACA,mDACA,yEACA,kDACA,gHACA,kDACA,6CACA,2GACA,8DACA,0EACA,sFACA,0EACA,4BACA,yCACA,yCACA,mEACA,mEACA,8DACA,mDACA,kEACA,iEACA,iEACA,gEACA,2DACA,6EACA,2EACA,sDACA,4CACA,sDACA,qDACA,gDACA,4KACA,kLACA,+KACA,gLACA,mMACA,iMACA,gFACA,4EACA,8EACA,2HACA,6KACA,qKACA,6DACA,0DACA,gDACA,2CACA,sCACA,mCACA,gCACA,mKACA,+CACA,iEACA,0EACA,gEACA,8DACA,8DACA,iDACA,gEACA,wDACA,yDACA,yDACA,2DACA,4DACA,2DACD,EACA,wCAAyC,CACxC,mHACA,uDACA,mDACA,4DACA,4DACD,EACA,8CAA+C,CAC9C,wFACA,wEACA,8EACA,8EACA,sHACA,kLACA,0LACA,oEACA,2CACA,kCACA,iJACA,gDACA,OACA,OACA,6DACA,6DACA,qGACA,oKACA,4KACA,kIACD,EACA,yCAA0C,CACzC,oBACD,CACD,CAAC", "names": [], "file": "editor.main.nls.de.js"}
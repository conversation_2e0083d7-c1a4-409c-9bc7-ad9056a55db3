{"version": 3, "sources": ["out-editor/vs/editor/editor.main.nls.fr.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\ndefine(\"vs/editor/editor.main.nls.fr\", {\n\t\"vs/base/browser/ui/actionbar/actionViewItems\": [\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/base/browser/ui/findinput/findInput\": [\n\t\t\"entrée\",\n\t],\n\t\"vs/base/browser/ui/findinput/findInputToggles\": [\n\t\t\"Respecter la casse\",\n\t\t\"Mo<PERSON> entier\",\n\t\t\"Utiliser une expression régulière\",\n\t],\n\t\"vs/base/browser/ui/findinput/replaceInput\": [\n\t\t\"entrée\",\n\t\t\"Préserver la casse\",\n\t],\n\t\"vs/base/browser/ui/hover/hoverWidget\": [\n\t\t\"Inspectez ceci dans l’affichage accessible avec {0}.\",\n\t\t\"Inspectez ceci dans l’affichage accessible via la commande Open Accessible View qui ne peut pas être déclenchée via une combinaison de touches pour l’instant.\",\n\t],\n\t\"vs/base/browser/ui/iconLabel/iconLabelHover\": [\n\t\t\"Chargement...\",\n\t],\n\t\"vs/base/browser/ui/inputbox/inputBox\": [\n\t\t\"Erreur : {0}\",\n\t\t\"Avertissement : {0}\",\n\t\t\"Info : {0}\",\n\t\t\"pour l’historique\",\n\t\t\"Entrée effacée\",\n\t],\n\t\"vs/base/browser/ui/keybindingLabel/keybindingLabel\": [\n\t\t\"Indépendant\",\n\t],\n\t\"vs/base/browser/ui/selectBox/selectBoxCustom\": [\n\t\t\"Zone de sélection\",\n\t],\n\t\"vs/base/browser/ui/toolbar/toolbar\": [\n\t\t\"Plus d\\'actions...\",\n\t],\n\t\"vs/base/browser/ui/tree/abstractTree\": [\n\t\t\"Filtrer\",\n\t\t\"Correspondance approximative\",\n\t\t\"Type à filtrer\",\n\t\t\"Entrer le texte à rechercher\",\n\t\t\"Entrer le texte à rechercher\",\n\t\t\"Fermer\",\n\t\t\"Aucun élément trouvé.\",\n\t],\n\t\"vs/base/common/actions\": [\n\t\t\"(vide)\",\n\t],\n\t\"vs/base/common/errorMessage\": [\n\t\t\"{0}: {1}\",\n\t\t\"Une erreur système s\\'est produite ({0})\",\n\t\t\"Une erreur inconnue s’est produite. Veuillez consulter le journal pour plus de détails.\",\n\t\t\"Une erreur inconnue s’est produite. Veuillez consulter le journal pour plus de détails.\",\n\t\t\"{0} ({1} erreurs au total)\",\n\t\t\"Une erreur inconnue s’est produite. Veuillez consulter le journal pour plus de détails.\",\n\t],\n\t\"vs/base/common/keybindingLabels\": [\n\t\t\"Ctrl\",\n\t\t\"Maj\",\n\t\t\"Alt\",\n\t\t\"Windows\",\n\t\t\"Ctrl\",\n\t\t\"Maj\",\n\t\t\"Alt\",\n\t\t\"Super\",\n\t\t\"Contrôle\",\n\t\t\"Maj\",\n\t\t\"Option\",\n\t\t\"Commande\",\n\t\t\"Contrôle\",\n\t\t\"Maj\",\n\t\t\"Alt\",\n\t\t\"Windows\",\n\t\t\"Contrôle\",\n\t\t\"Maj\",\n\t\t\"Alt\",\n\t\t\"Super\",\n\t],\n\t\"vs/base/common/platform\": [\n\t\t\"_\",\n\t],\n\t\"vs/editor/browser/controller/textAreaHandler\": [\n\t\t\"éditeur\",\n\t\t\"L’éditeur n’est pas accessible pour le moment.\",\n\t\t\"{0} Pour activer le mode optimisé du lecteur d’écran, utilisez {1}\",\n\t\t\"{0} Pour activer le mode optimisé du lecteur d’écran, ouvrez la sélection rapide avec {1} et exécutez la commande Activer/Désactiver le mode d’accessibilité du lecteur d’écran, qui n’est pas déclenchable via le clavier pour le moment.\",\n\t\t\"{0} Attribuez une combinaison de touches à la commande Activer/Désactiver le mode d’accessibilité du lecteur d’écran en accédant à l’éditeur de combinaisons de touches avec {1} et exécutez-la.\",\n\t],\n\t\"vs/editor/browser/coreCommands\": [\n\t\t\"Aligner par rapport à la fin même en cas de passage à des lignes plus longues\",\n\t\t\"Aligner par rapport à la fin même en cas de passage à des lignes plus longues\",\n\t\t\"Curseurs secondaires supprimés\",\n\t],\n\t\"vs/editor/browser/editorExtensions\": [\n\t\t\"Ann&&uler\",\n\t\t\"Annuler\",\n\t\t\"&&Rétablir\",\n\t\t\"Rétablir\",\n\t\t\"&&Sélectionner tout\",\n\t\t\"Tout sélectionner\",\n\t],\n\t\"vs/editor/browser/widget/codeEditorWidget\": [\n\t\t\"Le nombre de curseurs a été limité à {0}. Envisagez d’utiliser [rechercher et remplacer](https://code.visualstudio.com/docs/editor/codebasics#_find-and-replace) pour les modifications plus importantes ou augmentez la limite du nombre de curseurs multiples du paramètre.\",\n\t\t\"Augmenter la limite de curseurs multiples\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/accessibleDiffViewer\": [\n\t\t\"Icône « Insérer » dans la visionneuse diff accessible.\",\n\t\t\"Icône « Supprimer » dans la visionneuse diff accessible.\",\n\t\t\"Icône de « Fermer » dans la visionneuse diff accessible.\",\n\t\t\"Fermer\",\n\t\t\"Visionneuse diff accessible. Utilisez les flèches haut et bas pour naviguer.\",\n\t\t\"aucune ligne changée\",\n\t\t\"1 ligne changée\",\n\t\t\"{0} lignes changées\",\n\t\t\"Différence {0} sur {1} : ligne d\\'origine {2}, {3}, ligne modifiée {4}, {5}\",\n\t\t\"vide\",\n\t\t\"{0} ligne inchangée {1}\",\n\t\t\"{0} ligne d\\'origine {1} ligne modifiée {2}\",\n\t\t\"+ {0} ligne modifiée {1}\",\n\t\t\"- {0} ligne d\\'origine {1}\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/colors\": [\n\t\t\"Couleur de bordure du texte déplacé dans l’éditeur de diff.\",\n\t\t\"Couleur de bordure active du texte déplacé dans l’éditeur de différences.\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/decorations\": [\n\t\t\"Élément décoratif de ligne pour les insertions dans l\\'éditeur de différences.\",\n\t\t\"Élément décoratif de ligne pour les suppressions dans l\\'éditeur de différences.\",\n\t\t\"Cliquez pour rétablir la modification\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/diffEditor.contribution\": [\n\t\t\"Activer/désactiver réduire les régions inchangées\",\n\t\t\"Activer/désactiver l’affichage des blocs de code déplacés\",\n\t\t\"Activer/désactiver Utiliser la vue inline lorsque l\\'espace est limité\",\n\t\t\"Utiliser la vue inline lorsque l\\'espace est limité\",\n\t\t\"Afficher les blocs de code déplacés\",\n\t\t\"Éditeur de différences\",\n\t\t\"Changer de côté\",\n\t\t\"Quitter Comparer le déplacement\",\n\t\t\"Réduire toutes les régions inchangées\",\n\t\t\"Afficher toutes les régions inchangées\",\n\t\t\"Visionneuse Diff accessible\",\n\t\t\"Accéder à la différence suivante\",\n\t\t\"Ouvrir la visionneuse diff accessible\",\n\t\t\"Accéder la différence précédente\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/diffEditorEditors\": [\n\t\t\" utilisez {0} pour ouvrir l’aide sur l’accessibilité.\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/hideUnchangedRegionsFeature\": [\n\t\t\"Replier la région inchangée\",\n\t\t\"Cliquez ou faites glisser pour afficher plus d\\'éléments au-dessus\",\n\t\t\"Tout afficher\",\n\t\t\"Cliquez ou faites glisser pour afficher plus d\\'éléments en dessous\",\n\t\t\"{0} lignes masquées\",\n\t\t\"Double-cliquer pour déplier\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/inlineDiffDeletedCodeMargin\": [\n\t\t\"Copier les lignes supprimées\",\n\t\t\"Copier la ligne supprimée\",\n\t\t\"Copier les lignes modifiées\",\n\t\t\"Copier la ligne modifiée\",\n\t\t\"Copier la ligne supprimée ({0})\",\n\t\t\"Copier la ligne modifiée ({0})\",\n\t\t\"Annuler la modification\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/movedBlocksLines\": [\n\t\t\"Code déplacé avec des modifications vers la ligne {0}-{1}\",\n\t\t\"Code déplacé avec des modifications à partir de la ligne {0}-{1}\",\n\t\t\"Code déplacé vers la ligne {0}-{1}\",\n\t\t\"Code déplacé à partir de la ligne {0}-{1}\",\n\t],\n\t\"vs/editor/common/config/editorConfigurationSchema\": [\n\t\t\"Éditeur\",\n\t\t\"Le nombre d’espaces auxquels une tabulation est égale. Ce paramètre est substitué basé sur le contenu du fichier lorsque {0} est activé.\",\n\t\t\"Nombre d’espaces utilisés pour la mise en retrait ou `\\\"tabSize\\\"` pour utiliser la valeur de `#editor.tabSize#`. Ce paramètre est remplacé en fonction du contenu du fichier quand `#editor.detectIndentation#` est activé.\",\n\t\t\"Espaces insérés quand vous appuyez sur la touche Tab. Ce paramètre est remplacé en fonction du contenu du fichier quand {0} est activé.\",\n\t\t\"Contrôle si {0} et {1} sont automatiquement détectés lors de l’ouverture d’un fichier en fonction de son contenu.\",\n\t\t\"Supprimer l\\'espace blanc de fin inséré automatiquement.\",\n\t\t\"Traitement spécial des fichiers volumineux pour désactiver certaines fonctionnalités utilisant beaucoup de mémoire.\",\n\t\t\"Contrôle si la saisie semi-automatique doit être calculée en fonction des mots présents dans le document.\",\n\t\t\"Suggère uniquement des mots dans le document actif.\",\n\t\t\"Suggère des mots dans tous les documents ouverts du même langage.\",\n\t\t\"Suggère des mots dans tous les documents ouverts.\",\n\t\t\"Contrôle la façon dont sont calculées les complétions basées sur des mots dans les documents.\",\n\t\t\"Coloration sémantique activée pour tous les thèmes de couleur.\",\n\t\t\"Coloration sémantique désactivée pour tous les thèmes de couleur.\",\n\t\t\"La coloration sémantique est configurée par le paramètre \\'semanticHighlighting\\' du thème de couleur actuel.\",\n\t\t\"Contrôle si semanticHighlighting est affiché pour les langages qui le prennent en charge.\",\n\t\t\"Maintenir les éditeurs d\\'aperçu ouverts même si l\\'utilisateur double-clique sur son contenu ou appuie sur la touche Échap.\",\n\t\t\"Les lignes plus longues que cette valeur ne sont pas tokenisées pour des raisons de performances\",\n\t\t\"Contrôle si la création de jetons doit se produire de manière asynchrone sur un worker web.\",\n\t\t\"Contrôle si la création de jetons asynchrones doit être journalisée. Pour le débogage uniquement.\",\n\t\t\"Contrôle si la segmentation du texte en unités lexicales asynchrones doit être vérifiée par rapport à la segmentation du texte en unités lexicales en arrière-plan héritée. Peut ralentir la segmentation du texte en unités lexicales. Pour le débogage uniquement.\",\n\t\t\"Définit les symboles de type crochet qui augmentent ou diminuent le retrait.\",\n\t\t\"Séquence de chaînes ou de caractères de crochets ouvrants.\",\n\t\t\"Séquence de chaînes ou de caractères de crochets fermants.\",\n\t\t\"Définit les paires de crochets qui sont colorisées par leur niveau d’imbrication si la colorisation des paires de crochets est activée.\",\n\t\t\"Séquence de chaînes ou de caractères de crochets ouvrants.\",\n\t\t\"Séquence de chaînes ou de caractères de crochets fermants.\",\n\t\t\"Délai d\\'expiration en millisecondes avant annulation du calcul de diff. Utilisez 0 pour supprimer le délai d\\'expiration.\",\n\t\t\"Taille de fichier maximale en Mo pour laquelle calculer les différences. Utilisez 0 pour ne pas avoir de limite.\",\n\t\t\"Contrôle si l\\'éditeur de différences affiche les différences en mode côte à côte ou inline.\",\n\t\t\"Si l\\'éditeur de différences est moins large que cette valeur, la vue inline est utilisée.\",\n\t\t\"Si cette option est activée et que la largeur de l\\'éditeur est trop étroite, la vue inline est utilisée.\",\n\t\t\"Lorsqu’il est activé, l’éditeur de différences affiche des flèches dans sa marge de glyphe pour rétablir les modifications.\",\n\t\t\"Quand il est activé, l\\'éditeur de différences ignore les changements d\\'espace blanc de début ou de fin.\",\n\t\t\"Contrôle si l\\'éditeur de différences affiche les indicateurs +/- pour les changements ajoutés/supprimés .\",\n\t\t\"Contrôle si l\\'éditeur affiche CodeLens.\",\n\t\t\"Le retour automatique à la ligne n\\'est jamais effectué.\",\n\t\t\"Le retour automatique à la ligne s\\'effectue en fonction de la largeur de la fenêtre d\\'affichage.\",\n\t\t\"Le retour automatique à la ligne dépend du paramètre {0}.\",\n\t\t\"Utilise l’algorithme de comparaison hérité.\",\n\t\t\"Utilise l’algorithme de comparaison avancé.\",\n\t\t\"Contrôle si l\\'éditeur de différences affiche les régions inchangées.\",\n\t\t\"Contrôle le nombre de lignes utilisées pour les régions inchangées.\",\n\t\t\"Contrôle le nombre de lignes utilisées comme minimum pour les régions inchangées.\",\n\t\t\"Contrôle le nombre de lignes utilisées comme contexte lors de la comparaison des régions inchangées.\",\n\t\t\"Contrôle si l’éditeur de différences doit afficher les déplacements de code détectés.\",\n\t\t\"Contrôle si l’éditeur de différences affiche des décorations vides pour voir où les caractères ont été insérés ou supprimés.\",\n\t],\n\t\"vs/editor/common/config/editorOptions\": [\n\t\t\"Utiliser les API de la plateforme pour détecter si un lecteur d\\'écran est attaché\",\n\t\t\"Optimiser pour l’utilisation avec un lecteur d’écran\",\n\t\t\"Supposer qu’un lecteur d’écran n’est pas attaché\",\n\t\t\"Contrôle si l’interface utilisateur doit s’exécuter dans un mode où elle est optimisée pour les lecteurs d’écran.\",\n\t\t\"Contrôle si un espace est inséré pour les commentaires.\",\n\t\t\"Contrôle si les lignes vides doivent être ignorées avec des actions d\\'activation/de désactivation, d\\'ajout ou de suppression des commentaires de ligne.\",\n\t\t\"Contrôle si la copie sans sélection permet de copier la ligne actuelle.\",\n\t\t\"Contrôle si le curseur doit sauter pour rechercher les correspondances lors de la saisie.\",\n\t\t\"Ne lancez jamais la chaîne de recherche dans la sélection de l’éditeur.\",\n\t\t\"Toujours amorcer la chaîne de recherche à partir de la sélection de l’éditeur, y compris le mot à la position du curseur.\",\n\t\t\"Chaîne de recherche initiale uniquement dans la sélection de l’éditeur.\",\n\t\t\"Détermine si la chaîne de recherche dans le Widget Recherche est initialisée avec la sélection de l’éditeur.\",\n\t\t\"Ne jamais activer automatiquement la recherche dans la sélection (par défaut).\",\n\t\t\"Toujours activer automatiquement la recherche dans la sélection.\",\n\t\t\"Activez Rechercher automatiquement dans la sélection quand plusieurs lignes de contenu sont sélectionnées.\",\n\t\t\"Contrôle la condition d\\'activation automatique de la recherche dans la sélection.\",\n\t\t\"Détermine si le Widget Recherche devrait lire ou modifier le presse-papiers de recherche partagé sur macOS.\",\n\t\t\"Contrôle si le widget Recherche doit ajouter des lignes supplémentaires en haut de l\\'éditeur. Quand la valeur est true, vous pouvez faire défiler au-delà de la première ligne si le widget Recherche est visible.\",\n\t\t\"Contrôle si la recherche redémarre automatiquement depuis le début (ou la fin) quand il n\\'existe aucune autre correspondance.\",\n\t\t\"Active/désactive les ligatures de police (fonctionnalités de police \\'calt\\' et \\'liga\\'). Remplacez ceci par une chaîne pour contrôler de manière précise la propriété CSS \\'font-feature-settings\\'.\",\n\t\t\"Propriété CSS \\'font-feature-settings\\' explicite. Vous pouvez passer une valeur booléenne à la place si vous devez uniquement activer/désactiver les ligatures.\",\n\t\t\"Configure les ligatures de police ou les fonctionnalités de police. Il peut s\\'agir d\\'une valeur booléenne permettant d\\'activer/de désactiver les ligatures, ou d\\'une chaîne correspondant à la valeur de la propriété CSS \\'font-feature-settings\\'.\",\n\t\t\"Active/désactive la traduction de font-weight en font-variation-settings. Remplacez ce paramètre par une chaîne pour un contrôle affiné de la propriété CSS \\'font-variation-settings\\'.\",\n\t\t\"Propriété CSS \\'font-variation-settings\\' explicite. Une valeur booléenne peut être passée à la place si une seule valeur doit traduire font-weight en font-variation-settings.\",\n\t\t\"Configure les variations de la police. Il peut s’agir d’une valeur booléenne pour activer/désactiver la traduction de font-weight en font-variation-settings ou d’une chaîne pour la valeur de la propriété CSS \\'font-variation-settings\\'.\",\n\t\t\"Contrôle la taille de police en pixels.\",\n\t\t\"Seuls les mots clés \\\"normal\\\" et \\\"bold\\\", ou les nombres compris entre 1 et 1 000 sont autorisés.\",\n\t\t\"Contrôle l\\'épaisseur de police. Accepte les mots clés \\\"normal\\\" et \\\"bold\\\", ou les nombres compris entre 1 et 1 000.\",\n\t\t\"Montrer l’aperçu des résultats (par défaut)\",\n\t\t\"Accéder au résultat principal et montrer un aperçu\",\n\t\t\"Accéder au résultat principal et activer l’accès sans aperçu pour les autres\",\n\t\t\"Ce paramètre est déprécié, utilisez des paramètres distincts comme \\'editor.editor.gotoLocation.multipleDefinitions\\' ou \\'editor.editor.gotoLocation.multipleImplementations\\' à la place.\",\n\t\t\"Contrôle le comportement de la commande \\'Atteindre la définition\\' quand plusieurs emplacements cibles existent.\",\n\t\t\"Contrôle le comportement de la commande \\'Atteindre la définition de type\\' quand plusieurs emplacements cibles existent.\",\n\t\t\"Contrôle le comportement de la commande \\'Atteindre la déclaration\\' quand plusieurs emplacements cibles existent.\",\n\t\t\"Contrôle le comportement de la commande \\'Atteindre les implémentations\\' quand plusieurs emplacements cibles existent.\",\n\t\t\"Contrôle le comportement de la commande \\'Atteindre les références\\' quand plusieurs emplacements cibles existent.\",\n\t\t\"ID de commande alternatif exécuté quand le résultat de \\'Atteindre la définition\\' est l\\'emplacement actuel.\",\n\t\t\"ID de commande alternatif exécuté quand le résultat de \\'Atteindre la définition de type\\' est l\\'emplacement actuel.\",\n\t\t\"ID de commande alternatif exécuté quand le résultat de \\'Atteindre la déclaration\\' est l\\'emplacement actuel.\",\n\t\t\"ID de commande alternatif exécuté quand le résultat de \\'Atteindre l\\'implémentation\\' est l\\'emplacement actuel.\",\n\t\t\"ID de commande alternatif exécuté quand le résultat de \\'Atteindre la référence\\' est l\\'emplacement actuel.\",\n\t\t\"Contrôle si le pointage est affiché.\",\n\t\t\"Contrôle le délai en millisecondes, après lequel le survol est affiché.\",\n\t\t\"Contrôle si le pointage doit rester visible quand la souris est déplacée au-dessus.\",\n\t\t\"Contrôle le délai en millisecondes, après lequel le pointage est masqué. Demande d\\'activer \\'editor.hover.sticky\\'.\",\n\t\t\"Préférez afficher les points au-dessus de la ligne, s’il y a de l’espace.\",\n\t\t\"Suppose que tous les caractères ont la même largeur. Il s\\'agit d\\'un algorithme rapide qui fonctionne correctement pour les polices à espacement fixe et certains scripts (comme les caractères latins) où les glyphes ont la même largeur.\",\n\t\t\"Délègue le calcul des points de wrapping au navigateur. Il s\\'agit d\\'un algorithme lent qui peut provoquer le gel des grands fichiers, mais qui fonctionne correctement dans tous les cas.\",\n\t\t\"Contrôle l’algorithme qui calcule les points d’habillage. Notez qu’en mode d’accessibilité, les options avancées sont utilisées pour une expérience optimale.\",\n\t\t\"Active l’ampoule d’action de code dans l’éditeur.\",\n\t\t\"Affiche les étendues actives imbriqués pendant le défilement en haut de l’éditeur.\",\n\t\t\"Définit le nombre maximal de lignes rémanentes à afficher.\",\n\t\t\"Définit le modèle à utiliser pour déterminer les lignes à coller. Si le modèle hiérarchique n’existe pas, il revient au modèle de fournisseur de pliage qui revient au modèle de mise en retrait. Cette demande est respectée dans les trois cas.\",\n\t\t\"Activez le défilement du widget Sticky Scroll avec la barre de défilement horizontale de l\\'éditeur.\",\n\t\t\"Active les indicateurs inlay dans l’éditeur.\",\n\t\t\"Les indicateurs d’inlay sont activés.\",\n\t\t\"Les indicateurs d’inlay sont affichés par défaut et masqués lors de la conservation {0}\",\n\t\t\"Les indicateurs d’inlay sont masqués par défaut et s’affichent lorsque vous maintenez {0}\",\n\t\t\"Les indicateurs d’inlay sont désactivés.\",\n\t\t\"Contrôle la taille de police des indicateurs d’inlay dans l’éditeur. Par défaut, le {0} est utilisé lorsque la valeur configurée est inférieure à {1} ou supérieure à la taille de police de l’éditeur.\",\n\t\t\"Contrôle la famille de polices des indicateurs d’inlay dans l’éditeur. Lorsqu’il est défini sur vide, le {0} est utilisé.\",\n\t\t\"Active le remplissage autour des indicateurs d’inlay dans l’éditeur.\",\n\t\t\"Contrôle la hauteur de ligne. \\r\\n - Utilisez 0 pour calculer automatiquement la hauteur de ligne à partir de la taille de police.\\r\\n : les valeurs comprises entre 0 et 8 sont utilisées comme multiplicateur avec la taille de police.\\r\\n : les valeurs supérieures ou égales à 8 seront utilisées comme valeurs effectives.\",\n\t\t\"Contrôle si la minimap est affichée.\",\n\t\t\"Contrôle si la minimap est masquée automatiquement.\",\n\t\t\"Le minimap a la même taille que le contenu de l\\'éditeur (défilement possible).\",\n\t\t\"Le minimap s\\'agrandit ou se réduit selon les besoins pour remplir la hauteur de l\\'éditeur (pas de défilement).\",\n\t\t\"Le minimap est réduit si nécessaire pour ne jamais dépasser la taille de l\\'éditeur (pas de défilement).\",\n\t\t\"Contrôle la taille du minimap.\",\n\t\t\"Contrôle le côté où afficher la minimap.\",\n\t\t\"Contrôle quand afficher le curseur du minimap.\",\n\t\t\"Échelle du contenu dessiné dans le minimap : 1, 2 ou 3.\",\n\t\t\"Afficher les caractères réels sur une ligne par opposition aux blocs de couleur.\",\n\t\t\"Limiter la largeur de la minimap pour afficher au plus un certain nombre de colonnes.\",\n\t\t\"Contrôle la quantité d’espace entre le bord supérieur de l’éditeur et la première ligne.\",\n\t\t\"Contrôle la quantité d\\'espace entre le bord inférieur de l\\'éditeur et la dernière ligne.\",\n\t\t\"Active une fenêtre contextuelle qui affiche de la documentation sur les paramètres et des informations sur les types à mesure que vous tapez.\",\n\t\t\"Détermine si le menu de suggestions de paramètres se ferme ou reviens au début lorsque la fin de la liste est atteinte.\",\n\t\t\"Des suggestions rapides s’affichent dans le widget de suggestion\",\n\t\t\"Les suggestions rapides s’affichent sous forme de texte fantôme\",\n\t\t\"Les suggestions rapides sont désactivées\",\n\t\t\"Activez les suggestions rapides dans les chaînes.\",\n\t\t\"Activez les suggestions rapides dans les commentaires.\",\n\t\t\"Activez les suggestions rapides en dehors des chaînes et des commentaires.\",\n\t\t\"Contrôle si les suggestions doivent s’afficher automatiquement lors de la saisie. Cela peut être contrôlé pour la saisie dans des commentaires, des chaînes et d’autres codes. Vous pouvez configurer la suggestion rapide pour qu’elle s’affiche sous forme de texte fantôme ou avec le widget de suggestion. Tenez également compte du paramètre \\'{0}\\' qui contrôle si des suggestions sont déclenchées par des caractères spéciaux.\",\n\t\t\"Les numéros de ligne ne sont pas affichés.\",\n\t\t\"Les numéros de ligne sont affichés en nombre absolu.\",\n\t\t\"Les numéros de ligne sont affichés sous la forme de distance en lignes à la position du curseur.\",\n\t\t\"Les numéros de ligne sont affichés toutes les 10 lignes.\",\n\t\t\"Contrôle l\\'affichage des numéros de ligne.\",\n\t\t\"Nombre de caractères monospace auxquels cette règle d\\'éditeur effectue le rendu.\",\n\t\t\"Couleur de cette règle d\\'éditeur.\",\n\t\t\"Rendre les règles verticales après un certain nombre de caractères à espacement fixe. Utiliser plusieurs valeurs pour plusieurs règles. Aucune règle n\\'est dessinée si le tableau est vide.\",\n\t\t\"La barre de défilement verticale sera visible uniquement lorsque cela est nécessaire.\",\n\t\t\"La barre de défilement verticale est toujours visible.\",\n\t\t\"La barre de défilement verticale est toujours masquée.\",\n\t\t\"Contrôle la visibilité de la barre de défilement verticale.\",\n\t\t\"La barre de défilement horizontale sera visible uniquement lorsque cela est nécessaire.\",\n\t\t\"La barre de défilement horizontale est toujours visible.\",\n\t\t\"La barre de défilement horizontale est toujours masquée.\",\n\t\t\"Contrôle la visibilité de la barre de défilement horizontale.\",\n\t\t\"Largeur de la barre de défilement verticale.\",\n\t\t\"Hauteur de la barre de défilement horizontale.\",\n\t\t\"Contrôle si les clics permettent de faire défiler par page ou d’accéder à la position de clic.\",\n\t\t\"Contrôle si tous les caractères ASCII non basiques sont mis en surbrillance. Seuls les caractères compris entre U+0020 et U+007E, tabulation, saut de ligne et retour chariot sont considérés comme des ASCII de base.\",\n\t\t\"Contrôle si les caractères qui réservent de l’espace ou qui n’ont pas de largeur sont mis en surbrillance.\",\n\t\t\"Contrôle si les caractères mis en surbrillance peuvent être déconcertés avec des caractères ASCII de base, à l’exception de ceux qui sont courants dans les paramètres régionaux utilisateur actuels.\",\n\t\t\"Contrôle si les caractères des commentaires doivent également faire l’objet d’une mise en surbrillance Unicode.\",\n\t\t\"Contrôle si les caractères des chaînes de texte doivent également faire l’objet d’une mise en surbrillance Unicode.\",\n\t\t\"Définit les caractères autorisés qui ne sont pas mis en surbrillance.\",\n\t\t\"Les caractères Unicode communs aux paramètres régionaux autorisés ne sont pas mis en surbrillance.\",\n\t\t\"Contrôle si les suggestions en ligne doivent être affichées automatiquement dans l’éditeur.\",\n\t\t\"Afficher la barre d’outils de suggestion en ligne chaque fois qu’une suggestion inline est affichée.\",\n\t\t\"Afficher la barre d’outils de suggestion en ligne lorsque vous pointez sur une suggestion incluse.\",\n\t\t\"Contrôle quand afficher la barre d’outils de suggestion incluse.\",\n\t\t\"Contrôle la façon dont les suggestions inline interagissent avec le widget de suggestion. Si cette option est activée, le widget de suggestion n’est pas affiché automatiquement lorsque des suggestions inline sont disponibles.\",\n\t\t\"Contrôle si la colorisation des paires de crochets est activée ou non. Utilisez {0} pour remplacer les couleurs de surbrillance des crochets.\",\n\t\t\"Contrôle si chaque type de crochet possède son propre pool de couleurs indépendant.\",\n\t\t\"Désactive les repères de paire de crochets.\",\n\t\t\"Active les repères de paire de crochets uniquement pour la paire de crochets actifs.\",\n\t\t\"Désactive les repères de paire de crochets.\",\n\t\t\"Contrôle si les guides de la paire de crochets sont activés ou non.\",\n\t\t\"Active les repères horizontaux en plus des repères de paire de crochets verticaux.\",\n\t\t\"Active les repères horizontaux uniquement pour la paire de crochets actifs.\",\n\t\t\"Désactive les repères de paire de crochets horizontaux.\",\n\t\t\"Contrôle si les guides de la paire de crochets horizontaux sont activés ou non.\",\n\t\t\"Contrôle si l’éditeur doit mettre en surbrillance la paire de crochets actifs.\",\n\t\t\"Contrôle si l’éditeur doit afficher les guides de mise en retrait.\",\n\t\t\"Met en surbrillance le guide de retrait actif.\",\n\t\t\"Met en surbrillance le repère de retrait actif même si les repères de crochet sont mis en surbrillance.\",\n\t\t\"Ne mettez pas en surbrillance le repère de retrait actif.\",\n\t\t\"Contrôle si l’éditeur doit mettre en surbrillance le guide de mise en retrait actif.\",\n\t\t\"Insérez une suggestion sans remplacer le texte à droite du curseur.\",\n\t\t\"Insérez une suggestion et remplacez le texte à droite du curseur.\",\n\t\t\"Contrôle si les mots sont remplacés en cas d\\'acceptation de la saisie semi-automatique. Notez que cela dépend des extensions adhérant à cette fonctionnalité.\",\n\t\t\"Détermine si le filtre et le tri des suggestions doivent prendre en compte les fautes de frappes mineures.\",\n\t\t\"Contrôle si le tri favorise les mots qui apparaissent à proximité du curseur.\",\n\t\t\"Contrôle si les sélections de suggestion mémorisées sont partagées entre plusieurs espaces de travail et fenêtres (nécessite \\'#editor.suggestSelection#\\').\",\n\t\t\"Toujours sélectionner une suggestion lors du déclenchement automatique d’IntelliSense.\",\n\t\t\"Ne jamais sélectionner une suggestion lors du déclenchement automatique d’IntelliSense.\",\n\t\t\"Sélectionnez une suggestion uniquement lors du déclenchement d’IntelliSense à partir d’un caractère déclencheur.\",\n\t\t\"Sélectionnez une suggestion uniquement lors du déclenchement d’IntelliSense au cours de la frappe.\",\n\t\t\"Contrôle si une suggestion est sélectionnée lorsque le widget s’affiche. Notez que cela s’applique uniquement aux suggestions déclenchées automatiquement (\\'#editor.quickSuggestions#\\' et \\'#editor.suggestOnTriggerCharacters#\\') et qu’une suggestion est toujours sélectionnée lorsqu’elle est appelée explicitement, par exemple via \\'Ctrl+Espace\\'.\",\n\t\t\"Contrôle si un extrait de code actif empêche les suggestions rapides.\",\n\t\t\"Contrôle s\\'il faut montrer ou masquer les icônes dans les suggestions.\",\n\t\t\"Contrôle la visibilité de la barre d\\'état en bas du widget de suggestion.\",\n\t\t\"Contrôle si la sortie de la suggestion doit être affichée en aperçu dans l’éditeur.\",\n\t\t\"Détermine si les détails du widget de suggestion sont inclus dans l’étiquette ou uniquement dans le widget de détails.\",\n\t\t\"Ce paramètre est déprécié. Le widget de suggestion peut désormais être redimensionné.\",\n\t\t\"Ce paramètre est déprécié, veuillez utiliser des paramètres distincts comme \\'editor.suggest.showKeywords\\' ou \\'editor.suggest.showSnippets\\' à la place.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'method\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'function\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'constructor\\'.\",\n\t\t\"Si cette option est activée, IntelliSense montre des suggestions `dépréciées`.\",\n\t\t\"Quand le filtrage IntelliSense est activé, le premier caractère correspond à un début de mot, par exemple \\'c\\' sur \\'Console\\' ou \\'WebContext\\', mais _not_ sur \\'description\\'. Si désactivé, IntelliSense affiche plus de résultats, mais les trie toujours par qualité de correspondance.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'field\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'variable\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'class\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'struct\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'interface\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'module\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'property\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'event\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'operator\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'unit\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'value\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'constant\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'enum\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'enumMember\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'keyword\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'text\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'color\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'file\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'reference\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'customcolor\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'folder\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'typeParameter\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'snippet\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'utilisateur\\'.\",\n\t\t\"Si activé, IntelliSense montre des suggestions de type \\'problèmes\\'.\",\n\t\t\"Indique si les espaces blancs de début et de fin doivent toujours être sélectionnés.\",\n\t\t\"Indique si les sous-mots (tels que « foo » dans « fooBar » ou « foo_bar ») doivent être sélectionnés.\",\n\t\t\"Aucune mise en retrait. Les lignes enveloppées commencent à la colonne 1.\",\n\t\t\"Les lignes enveloppées obtiennent la même mise en retrait que le parent.\",\n\t\t\"Les lignes justifiées obtiennent une mise en retrait +1 vers le parent.\",\n\t\t\"Les lignes justifiées obtiennent une mise en retrait +2 vers le parent. \",\n\t\t\"Contrôle la mise en retrait des lignes justifiées.\",\n\t\t\"Contrôle si vous pouvez faire glisser et déposer un fichier dans un éditeur de texte en maintenant la touche Maj enfoncée (au lieu d’ouvrir le fichier dans un éditeur).\",\n\t\t\"Contrôle si un widget est affiché lors de l’annulation de fichiers dans l’éditeur. Ce widget vous permet de contrôler la façon dont le fichier est annulé.\",\n\t\t\"Afficher le widget du sélecteur de dépôt après la suppression d’un fichier dans l’éditeur.\",\n\t\t\"Ne jamais afficher le widget du sélecteur de dépôt. À la place, le fournisseur de dépôt par défaut est toujours utilisé.\",\n\t\t\"Contrôle si vous pouvez coller le contenu de différentes manières.\",\n\t\t\"Contrôle l’affichage d’un widget lors du collage de contenu dans l’éditeur. Ce widget vous permet de contrôler la manière dont le fichier est collé.\",\n\t\t\"Afficher le widget du sélecteur de collage une fois le contenu collé dans l’éditeur.\",\n\t\t\"Ne jamais afficher le widget de sélection de collage. Au lieu de cela, le comportement de collage par défaut est toujours utilisé.\",\n\t\t\"Contrôle si les suggestions doivent être acceptées sur les caractères de validation. Par exemple, en JavaScript, le point-virgule (`;`) peut être un caractère de validation qui accepte une suggestion et tape ce caractère.\",\n\t\t\"Accepter uniquement une suggestion avec \\'Entrée\\' quand elle effectue une modification textuelle.\",\n\t\t\"Contrôle si les suggestions sont acceptées après appui sur \\'Entrée\\', en plus de \\'Tab\\'. Permet d’éviter toute ambiguïté entre l’insertion de nouvelles lignes et l\\'acceptation de suggestions.\",\n\t\t\"Contrôle le nombre de lignes de l’éditeur qu’un lecteur d’écran peut lire en une seule fois. Quand nous détectons un lecteur d’écran, nous définissons automatiquement la valeur par défaut à 500. Attention : Les valeurs supérieures à la valeur par défaut peuvent avoir un impact important sur les performances.\",\n\t\t\"Contenu de l\\'éditeur\",\n\t\t\"Contrôlez si les suggestions incluses sont annoncées par un lecteur d’écran.\",\n\t\t\"Utilisez les configurations de langage pour déterminer quand fermer automatiquement les parenthèses.\",\n\t\t\"Fermer automatiquement les parenthèses uniquement lorsque le curseur est à gauche de l’espace.\",\n\t\t\"Contrôle si l’éditeur doit fermer automatiquement les parenthèses quand l’utilisateur ajoute une parenthèse ouvrante.\",\n\t\t\"Utilisez les configurations de langage pour déterminer quand fermer automatiquement les commentaires.\",\n\t\t\"Fermez automatiquement les commentaires seulement si le curseur est à gauche de l\\'espace.\",\n\t\t\"Contrôle si l\\'éditeur doit fermer automatiquement les commentaires quand l\\'utilisateur ajoute un commentaire ouvrant.\",\n\t\t\"Supprimez les guillemets ou crochets fermants adjacents uniquement s\\'ils ont été insérés automatiquement.\",\n\t\t\"Contrôle si l\\'éditeur doit supprimer les guillemets ou crochets fermants adjacents au moment de la suppression.\",\n\t\t\"Tapez avant les guillemets ou les crochets fermants uniquement s\\'ils sont automatiquement insérés.\",\n\t\t\"Contrôle si l\\'éditeur doit taper avant les guillemets ou crochets fermants.\",\n\t\t\"Utilisez les configurations de langage pour déterminer quand fermer automatiquement les guillemets.\",\n\t\t\"Fermer automatiquement les guillemets uniquement lorsque le curseur est à gauche de l’espace.\",\n\t\t\"Contrôle si l’éditeur doit fermer automatiquement les guillemets après que l’utilisateur ajoute un guillemet ouvrant.\",\n\t\t\"L\\'éditeur n\\'insère pas de retrait automatiquement.\",\n\t\t\"L\\'éditeur conserve le retrait de la ligne actuelle.\",\n\t\t\"L\\'éditeur conserve le retrait de la ligne actuelle et honore les crochets définis par le langage.\",\n\t\t\"L\\'éditeur conserve le retrait de la ligne actuelle, honore les crochets définis par le langage et appelle des objets onEnterRules spéciaux définis par les langages.\",\n\t\t\"L\\'éditeur conserve le retrait de la ligne actuelle, honore les crochets définis par le langage, appelle des objets onEnterRules spéciaux définis par les langages et honore les objets indentationRules définis par les langages.\",\n\t\t\"Contrôle si l\\'éditeur doit ajuster automatiquement le retrait quand les utilisateurs tapent, collent, déplacent ou mettent en retrait des lignes.\",\n\t\t\"Utilisez les configurations de langue pour déterminer quand entourer automatiquement les sélections.\",\n\t\t\"Entourez avec des guillemets et non des crochets.\",\n\t\t\"Entourez avec des crochets et non des guillemets.\",\n\t\t\"Contrôle si l\\'éditeur doit automatiquement entourer les sélections quand l\\'utilisateur tape des guillemets ou des crochets.\",\n\t\t\"Émule le comportement des tabulations pour la sélection quand des espaces sont utilisés à des fins de mise en retrait. La sélection respecte les taquets de tabulation.\",\n\t\t\"Contrôle si l\\'éditeur affiche CodeLens.\",\n\t\t\"Contrôle la famille de polices pour CodeLens.\",\n\t\t\"Contrôle la taille de police en pixels pour CodeLens. Quand la valeur est 0, 90 % de \\'#editor.fontSize#\\' est utilisé.\",\n\t\t\"Contrôle si l\\'éditeur doit afficher les éléments décoratifs de couleurs inline et le sélecteur de couleurs.\",\n\t\t\"Faire apparaître le sélecteur de couleurs au clic et au pointage de l’élément décoratif de couleurs\",\n\t\t\"Faire apparaître le sélecteur de couleurs en survolant l’élément décoratif de couleurs\",\n\t\t\"Faire apparaître le sélecteur de couleurs en cliquant sur l’élément décoratif de couleurs\",\n\t\t\"Contrôle la condition pour faire apparaître un sélecteur de couleurs à partir d’un élément décoratif de couleurs\",\n\t\t\"Contrôle le nombre maximal d’éléments décoratifs de couleur qui peuvent être rendus simultanément dans un éditeur.\",\n\t\t\"Autoriser l\\'utilisation de la souris et des touches pour sélectionner des colonnes.\",\n\t\t\"Contrôle si la coloration syntaxique doit être copiée dans le presse-papiers.\",\n\t\t\"Contrôler le style d’animation du curseur.\",\n\t\t\"L’animation de caret fluide est désactivée.\",\n\t\t\"L’animation de caret fluide est activée uniquement lorsque l’utilisateur déplace le curseur avec un mouvement explicite.\",\n\t\t\"L’animation de caret fluide est toujours activée.\",\n\t\t\"Contrôle si l\\'animation du point d\\'insertion doit être activée.\",\n\t\t\"Contrôle le style du curseur.\",\n\t\t\"Contrôle le nombre minimal de lignes de début (0 minimum) et de fin (1 minimum) visibles autour du curseur. Également appelé « scrollOff » ou « scrollOffset » dans d\\'autres éditeurs.\",\n\t\t\"\\'cursorSurroundingLines\\' est appliqué seulement s\\'il est déclenché via le clavier ou une API.\",\n\t\t\"\\'cursorSurroundingLines\\' est toujours appliqué.\",\n\t\t\"Contrôle le moment où #cursorSurroundingLines# doit être appliqué.\",\n\t\t\"Détermine la largeur du curseur lorsque `#editor.cursorStyle#` est à `line`.\",\n\t\t\"Contrôle si l’éditeur autorise le déplacement de sélections par glisser-déplacer.\",\n\t\t\"Utilisez une nouvelle méthode de rendu avec des SVG.\",\n\t\t\"Utilisez une nouvelle méthode de rendu avec des caractères de police.\",\n\t\t\"Utilisez la méthode de rendu stable.\",\n\t\t\"Contrôle si les espaces blancs sont rendus avec une nouvelle méthode expérimentale.\",\n\t\t\"Multiplicateur de vitesse de défilement quand vous appuyez sur \\'Alt\\'.\",\n\t\t\"Contrôle si l\\'éditeur a le pliage de code activé.\",\n\t\t\"Utilisez une stratégie de pliage propre à la langue, si disponible, sinon utilisez la stratégie basée sur le retrait.\",\n\t\t\"Utilisez la stratégie de pliage basée sur le retrait.\",\n\t\t\"Contrôle la stratégie de calcul des plages de pliage.\",\n\t\t\"Contrôle si l\\'éditeur doit mettre en évidence les plages pliées.\",\n\t\t\"Contrôle si l’éditeur réduit automatiquement les plages d’importation.\",\n\t\t\"Nombre maximal de régions pliables. L’augmentation de cette valeur peut réduire la réactivité de l’éditeur lorsque la source actuelle comprend un grand nombre de régions pliables.\",\n\t\t\"Contrôle si le fait de cliquer sur le contenu vide après une ligne pliée déplie la ligne.\",\n\t\t\"Contrôle la famille de polices.\",\n\t\t\"Détermine si l’éditeur doit automatiquement mettre en forme le contenu collé. Un formateur doit être disponible et être capable de mettre en forme une plage dans un document.\",\n\t\t\"Contrôle si l’éditeur doit mettre automatiquement en forme la ligne après la saisie.\",\n\t\t\"Contrôle si l\\'éditeur doit afficher la marge de glyphes verticale. La marge de glyphes sert principalement au débogage.\",\n\t\t\"Contrôle si le curseur doit être masqué dans la règle de la vue d’ensemble.\",\n\t\t\"Contrôle l\\'espacement des lettres en pixels.\",\n\t\t\"Contrôle si la modification liée est activée dans l’éditeur. En fonction du langage, les symboles associés, par exemple les balises HTML, sont mis à jour durant le processus de modification.\",\n\t\t\"Contrôle si l’éditeur doit détecter les liens et les rendre cliquables.\",\n\t\t\"Mettez en surbrillance les crochets correspondants.\",\n\t\t\"Un multiplicateur à utiliser sur les `deltaX` et `deltaY` des événements de défilement de roulette de souris.\",\n\t\t\"Faire un zoom sur la police de l\\'éditeur quand l\\'utilisateur fait tourner la roulette de la souris tout en maintenant la touche \\'Ctrl\\' enfoncée.\",\n\t\t\"Fusionnez plusieurs curseurs quand ils se chevauchent.\",\n\t\t\"Mappe vers \\'Contrôle\\' dans Windows et Linux, et vers \\'Commande\\' dans macOS.\",\n\t\t\"Mappe vers \\'Alt\\' dans Windows et Linux, et vers \\'Option\\' dans macOS.\",\n\t\t\"Modificateur à utiliser pour ajouter plusieurs curseurs avec la souris. Les mouvements de la souris Atteindre la définition et Ouvrir le lien s’adaptent afin qu’ils ne soient pas en conflit avec le [modificateur multicurseur](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modificateur).\",\n\t\t\"Chaque curseur colle une seule ligne de texte.\",\n\t\t\"Chaque curseur colle le texte en entier.\",\n\t\t\"Contrôle le collage quand le nombre de lignes du texte collé correspond au nombre de curseurs.\",\n\t\t\"Contrôle le nombre maximal de curseurs pouvant se trouver dans un éditeur actif à la fois.\",\n\t\t\"Contrôle si l\\'éditeur doit mettre en surbrillance les occurrences de symboles sémantiques.\",\n\t\t\"Contrôle si une bordure doit être dessinée autour de la règle de la vue d\\'ensemble.\",\n\t\t\"Focus sur l\\'arborescence à l\\'ouverture de l\\'aperçu\",\n\t\t\"Placer le focus sur l\\'éditeur à l\\'ouverture de l\\'aperçu\",\n\t\t\"Contrôle s\\'il faut mettre le focus sur l\\'éditeur inline ou sur l\\'arborescence dans le widget d\\'aperçu.\",\n\t\t\"Contrôle si le geste de souris Accéder à la définition ouvre toujours le widget d\\'aperçu.\",\n\t\t\"Contrôle le délai en millisecondes après lequel des suggestions rapides sont affichées.\",\n\t\t\"Contrôle si l\\'éditeur renomme automatiquement selon le type.\",\n\t\t\"Déprécié. Utilisez \\'editor.linkedEditing\\' à la place.\",\n\t\t\"Contrôle si l’éditeur doit afficher les caractères de contrôle.\",\n\t\t\"Affichez le dernier numéro de ligne quand le fichier se termine par un saut de ligne.\",\n\t\t\"Met en surbrillance la gouttière et la ligne actuelle.\",\n\t\t\"Contrôle la façon dont l’éditeur doit afficher la mise en surbrillance de la ligne actuelle.\",\n\t\t\"Contrôle si l\\'éditeur doit afficher la mise en surbrillance de la ligne actuelle uniquement quand il a le focus.\",\n\t\t\"Affiche les espaces blancs à l\\'exception des espaces uniques entre les mots.\",\n\t\t\"Afficher les espaces blancs uniquement sur le texte sélectionné.\",\n\t\t\"Affiche uniquement les caractères correspondant aux espaces blancs de fin.\",\n\t\t\"Contrôle la façon dont l’éditeur doit restituer les caractères espaces.\",\n\t\t\"Contrôle si les sélections doivent avoir des angles arrondis.\",\n\t\t\"Contrôle le nombre de caractères supplémentaires, au-delà duquel l’éditeur défile horizontalement.\",\n\t\t\"Contrôle si l’éditeur défile au-delà de la dernière ligne.\",\n\t\t\"Faites défiler uniquement le long de l\\'axe prédominant quand le défilement est à la fois vertical et horizontal. Empêche la dérive horizontale en cas de défilement vertical sur un pavé tactile.\",\n\t\t\"Contrôle si le presse-papiers principal Linux doit être pris en charge.\",\n\t\t\"Contrôle si l\\'éditeur doit mettre en surbrillance les correspondances similaires à la sélection.\",\n\t\t\"Affichez toujours les contrôles de pliage.\",\n\t\t\"N’affichez jamais les contrôles de pliage et réduisez la taille de la marge.\",\n\t\t\"Affichez uniquement les contrôles de pliage quand la souris est au-dessus de la reliure.\",\n\t\t\"Contrôle quand afficher les contrôles de pliage sur la reliure.\",\n\t\t\"Contrôle la disparition du code inutile.\",\n\t\t\"Contrôle les variables dépréciées barrées.\",\n\t\t\"Afficher des suggestions d’extraits au-dessus d’autres suggestions.\",\n\t\t\"Afficher des suggestions d’extraits en-dessous d’autres suggestions.\",\n\t\t\"Afficher des suggestions d’extraits avec d’autres suggestions.\",\n\t\t\"Ne pas afficher de suggestions d’extrait de code.\",\n\t\t\"Contrôle si les extraits de code s\\'affichent en même temps que d\\'autres suggestions, ainsi que leur mode de tri.\",\n\t\t\"Contrôle si l\\'éditeur défile en utilisant une animation.\",\n\t\t\"Contrôle si l\\'indicateur d\\'accessibilité doit être fourni aux utilisateurs du lecteur d\\'écran lorsqu\\'une complétion inline est affichée.\",\n\t\t\"Taille de police pour le widget suggest. Lorsqu’elle est définie sur {0}, la valeur de {1} est utilisée.\",\n\t\t\"Hauteur de ligne pour le widget suggest. Lorsqu’elle est définie sur {0}, la valeur de {1} est utilisée. La valeur minimale est 8.\",\n\t\t\"Contrôle si les suggestions devraient automatiquement s’afficher lorsque vous tapez les caractères de déclencheur.\",\n\t\t\"Sélectionnez toujours la première suggestion.\",\n\t\t\"Sélectionnez les suggestions récentes sauf si une entrée ultérieure en a sélectionné une, par ex., \\'console.| -> console.log\\', car \\'log\\' a été effectué récemment.\",\n\t\t\"Sélectionnez des suggestions en fonction des préfixes précédents qui ont complété ces suggestions, par ex., \\'co -> console\\' et \\'con -> const\\'.\",\n\t\t\"Contrôle comment les suggestions sont pré-sélectionnés lors de l’affichage de la liste de suggestion.\",\n\t\t\"La complétion par tabulation insérera la meilleure suggestion lorsque vous appuyez sur tab.\",\n\t\t\"Désactiver les complétions par tabulation.\",\n\t\t\"Compléter les extraits de code par tabulation lorsque leur préfixe correspond. Fonctionne mieux quand les \\'quickSuggestions\\' ne sont pas activées.\",\n\t\t\"Active les complétions par tabulation\",\n\t\t\"Les marques de fin de ligne inhabituelles sont automatiquement supprimées.\",\n\t\t\"Les marques de fin de ligne inhabituelles sont ignorées.\",\n\t\t\"Les marques de fin de ligne inhabituelles demandent à être supprimées.\",\n\t\t\"Supprimez les marques de fin de ligne inhabituelles susceptibles de causer des problèmes.\",\n\t\t\"L\\'insertion et la suppression des espaces blancs suit les taquets de tabulation.\",\n\t\t\"Utilisez la règle de saut de ligne par défaut.\",\n\t\t\"Les sauts de mots ne doivent pas être utilisés pour le texte chinois/japonais/coréen (CJC). Le comportement du texte non CJC est identique à celui du texte normal.\",\n\t\t\"Contrôle les règles de séparateur de mots utilisées pour le texte chinois/japonais/coréen (CJC).\",\n\t\t\"Caractères utilisés comme séparateurs de mots durant la navigation ou les opérations basées sur les mots\",\n\t\t\"Le retour automatique à la ligne n\\'est jamais effectué.\",\n\t\t\"Le retour automatique à la ligne s\\'effectue en fonction de la largeur de la fenêtre d\\'affichage.\",\n\t\t\"Les lignes seront terminées à `#editor.wordWrapColumn#`.\",\n\t\t\"Les lignes seront terminées au minimum du viewport et `#editor.wordWrapColumn#`.\",\n\t\t\"Contrôle comment les lignes doivent être limitées.\",\n\t\t\"Contrôle la colonne de terminaison de l’éditeur lorsque `#editor.wordWrap#` est à `wordWrapColumn` ou `bounded`.\",\n\t\t\"Contrôle si les décorations de couleur inline doivent être affichées à l’aide du fournisseur de couleurs de document par défaut\",\n\t\t\"Contrôle si l’éditeur reçoit des onglets ou les reporte au banc d’essai pour la navigation.\",\n\t],\n\t\"vs/editor/common/core/editorColorRegistry\": [\n\t\t\"Couleur d\\'arrière-plan de la mise en surbrillance de la ligne à la position du curseur.\",\n\t\t\"Couleur d\\'arrière-plan de la bordure autour de la ligne à la position du curseur.\",\n\t\t\"Couleur d\\'arrière-plan des plages mises en surbrillance, comme par les fonctionnalités de recherche et Quick Open. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Couleur d\\'arrière-plan de la bordure autour des plages mises en surbrillance.\",\n\t\t\"Couleur d\\'arrière-plan du symbole mis en surbrillance, comme le symbole Atteindre la définition ou Suivant/Précédent. La couleur ne doit pas être opaque pour ne pas masquer les décorations sous-jacentes.\",\n\t\t\"Couleur d\\'arrière-plan de la bordure autour des symboles mis en surbrillance.\",\n\t\t\"Couleur du curseur de l\\'éditeur.\",\n\t\t\"La couleur de fond du curseur de l\\'éditeur. Permet de personnaliser la couleur d\\'un caractère survolé par un curseur de bloc.\",\n\t\t\"Couleur des espaces blancs dans l\\'éditeur.\",\n\t\t\"Couleur des numéros de ligne de l\\'éditeur.\",\n\t\t\"Couleur des repères de retrait de l\\'éditeur.\",\n\t\t\"\\'editorIndentGuide.background\\' est déconseillé. Utilisez \\'editorIndentGuide.background1\\' à la place.\",\n\t\t\"Couleur des guides d\\'indentation de l\\'éditeur actif\",\n\t\t\"\\'editorIndentGuide.activeBackground\\' est déconseillé. Utilisez \\'editorIndentGuide.activeBackground1\\' à la place.\",\n\t\t\"Couleur des repères de retrait de l\\'éditeur (1).\",\n\t\t\"Couleur des repères de retrait de l\\'éditeur (2).\",\n\t\t\"Couleur des repères de retrait de l\\'éditeur (3).\",\n\t\t\"Couleur des repères de retrait de l\\'éditeur (4).\",\n\t\t\"Couleur des repères de retrait de l\\'éditeur (5).\",\n\t\t\"Couleur des repères de retrait de l\\'éditeur (6).\",\n\t\t\"Couleur des repaires de retrait de l\\'éditeur actifs (1).\",\n\t\t\"Couleur des repaires de retrait de l\\'éditeur actifs (2).\",\n\t\t\"Couleur des repaires de retrait de l\\'éditeur actifs (3).\",\n\t\t\"Couleur des repaires de retrait de l\\'éditeur actifs (4).\",\n\t\t\"Couleur des repaires de retrait de l\\'éditeur actifs (5).\",\n\t\t\"Couleur des repaires de retrait de l\\'éditeur actifs (6).\",\n\t\t\"Couleur des numéros de lignes actives de l\\'éditeur\",\n\t\t\"L’ID est déprécié. Utilisez à la place \\'editorLineNumber.activeForeground\\'.\",\n\t\t\"Couleur des numéros de lignes actives de l\\'éditeur\",\n\t\t\"Couleur de la ligne finale de l’éditeur lorsque editor.renderFinalNewline est défini sur grisé.\",\n\t\t\"Couleur des règles de l\\'éditeur\",\n\t\t\"Couleur pour les indicateurs CodeLens\",\n\t\t\"Couleur d\\'arrière-plan pour les accolades associées\",\n\t\t\"Couleur pour le contour des accolades associées\",\n\t\t\"Couleur de la bordure de la règle d\\'aperçu.\",\n\t\t\"Couleur d’arrière-plan de la règle de vue d’ensemble de l’éditeur.\",\n\t\t\"Couleur de fond pour la bordure de l\\'éditeur. La bordure contient les marges pour les symboles et les numéros de ligne.\",\n\t\t\"Couleur de bordure du code source inutile (non utilisé) dans l\\'éditeur.\",\n\t\t\"Opacité du code source inutile (non utilisé) dans l\\'éditeur. Par exemple, \\'#000000c0\\' affiche le code avec une opacité de 75 %. Pour les thèmes à fort contraste, utilisez la couleur de thème \\'editorUnnecessaryCode.border\\' pour souligner le code inutile au lieu d\\'utiliser la transparence.\",\n\t\t\"Couleur de bordure du texte fantôme dans l’éditeur.\",\n\t\t\"Couleur de premier plan du texte fantôme dans l’éditeur.\",\n\t\t\"Couleur de l’arrière-plan du texte fantôme dans l’éditeur\",\n\t\t\"Couleur de marqueur de la règle d\\'aperçu pour la mise en surbrillance des plages. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Couleur du marqueur de la règle d\\'aperçu pour les erreurs.\",\n\t\t\"Couleur du marqueur de la règle d\\'aperçu pour les avertissements.\",\n\t\t\"Couleur du marqueur de la règle d\\'aperçu pour les informations.\",\n\t\t\"Couleur de premier plan des crochets (1). Nécessite l’activation de la coloration de la paire de crochets.\",\n\t\t\"Couleur de premier plan des crochets (2). Nécessite l’activation de la coloration de la paire de crochets.\",\n\t\t\"Couleur de premier plan des crochets (3). Nécessite l’activation de la coloration de la paire de crochets.\",\n\t\t\"Couleur de premier plan des crochets (4). Nécessite l’activation de la coloration de la paire de crochets.\",\n\t\t\"Couleur de premier plan des crochets (5). Nécessite l’activation de la coloration de la paire de crochets.\",\n\t\t\"Couleur de premier plan des crochets (6). Nécessite l’activation de la coloration de la paire de crochets.\",\n\t\t\"Couleur de premier plan des parenthèses inattendues\",\n\t\t\"Couleur d’arrière-plan des repères de paire de crochets inactifs (1). Nécessite l’activation des repères de paire de crochets.\",\n\t\t\"Couleur d’arrière-plan des repères de paire de crochets inactifs (2). Nécessite l’activation des repères de paire de crochets.\",\n\t\t\"Couleur d’arrière-plan des repères de paire de crochets inactifs (3). Nécessite l’activation des repères de paire de crochets.\",\n\t\t\"Couleur d’arrière-plan des repères de paire de crochets inactifs (4). Nécessite l’activation des repères de paire de crochets.\",\n\t\t\"Couleur d’arrière-plan des repères de paire de crochets inactifs (5). Nécessite l’activation des repères de paire de crochets.\",\n\t\t\"Couleur d’arrière-plan des repères de paire de crochets inactifs (6). Nécessite l’activation des repères de paire de crochets.\",\n\t\t\"Couleur d’arrière-plan des repères de paire de crochets actifs (1). Nécessite l’activation des repères de paire de crochets.\",\n\t\t\"Couleur d’arrière-plan des repères de paire de crochets actifs (2). Nécessite l’activation des repères de paire de crochets.\",\n\t\t\"Couleur d’arrière-plan des repères de paire de crochets actifs (3). Nécessite l’activation des repères de paire de crochets.\",\n\t\t\"Couleur d’arrière-plan des repères de paire de crochets actifs (4). Nécessite l’activation des repères de paire de crochets.\",\n\t\t\"Couleur d’arrière-plan des repères de paire de crochets actifs (5). Nécessite l’activation des repères de paire de crochets.\",\n\t\t\"Couleur d’arrière-plan des repères de paire de crochets actifs (6). Nécessite l’activation des repères de paire de crochets.\",\n\t\t\"Couleur de bordure utilisée pour mettre en surbrillance les caractères Unicode\",\n\t\t\"Couleur de fond utilisée pour mettre en évidence les caractères unicode\",\n\t],\n\t\"vs/editor/common/editorContextKeys\": [\n\t\t\"Indique si le texte de l\\'éditeur a le focus (le curseur clignote)\",\n\t\t\"Indique si l\\'éditeur ou un widget de l\\'éditeur a le focus (par exemple, le focus se trouve sur le widget de recherche)\",\n\t\t\"Indique si un éditeur ou une entrée de texte mis en forme a le focus (le curseur clignote)\",\n\t\t\"Indique si l’éditeur est en lecture seule\",\n\t\t\"Indique si le contexte est celui d\\'un éditeur de différences\",\n\t\t\"Indique si le contexte est celui d’un éditeur de différences intégré\",\n\t\t\"Indique si un bloc de code déplacé est sélectionné pour être comparé\",\n\t\t\"Indique si la visionneuse diff accessible est visible\",\n\t\t\"Indique si le point d\\'arrêt Render Side by Side ou inline de l\\'éditeur de différences est atteint\",\n\t\t\"Indique si \\'editor.columnSelection\\' est activé\",\n\t\t\"Indique si du texte est sélectionné dans l\\'éditeur\",\n\t\t\"Indique si l\\'éditeur a plusieurs sélections\",\n\t\t\"Indique si la touche Tab permet de déplacer le focus hors de l\\'éditeur\",\n\t\t\"Indique si le pointage de l\\'éditeur est visible\",\n\t\t\"Indique si le pointage de l’éditeur est ciblé\",\n\t\t\"Indique si le défilement du pense-bête a le focus\",\n\t\t\"Indique si le défilement du pense-bête est visible\",\n\t\t\"Indique si le sélecteur de couleurs autonome est visible\",\n\t\t\"Indique si le sélecteur de couleurs autonome est prioritaire\",\n\t\t\"Indique si l\\'éditeur fait partie d\\'un éditeur plus important (par exemple Notebooks)\",\n\t\t\"Identificateur de langage de l\\'éditeur\",\n\t\t\"Indique si l\\'éditeur a un fournisseur d\\'éléments de complétion\",\n\t\t\"Indique si l\\'éditeur a un fournisseur d\\'actions de code\",\n\t\t\"Indique si l\\'éditeur a un fournisseur d\\'informations CodeLens\",\n\t\t\"Indique si l\\'éditeur a un fournisseur de définitions\",\n\t\t\"Indique si l\\'éditeur a un fournisseur de déclarations\",\n\t\t\"Indique si l\\'éditeur a un fournisseur d\\'implémentation\",\n\t\t\"Indique si l\\'éditeur a un fournisseur de définitions de type\",\n\t\t\"Indique si l\\'éditeur a un fournisseur de pointage\",\n\t\t\"Indique si l\\'éditeur a un fournisseur de mise en surbrillance pour les documents\",\n\t\t\"Indique si l\\'éditeur a un fournisseur de symboles pour les documents\",\n\t\t\"Indique si l\\'éditeur a un fournisseur de référence\",\n\t\t\"Indique si l\\'éditeur a un fournisseur de renommage\",\n\t\t\"Indique si l\\'éditeur a un fournisseur d\\'aide sur les signatures\",\n\t\t\"Indique si l\\'éditeur a un fournisseur d\\'indicateurs inline\",\n\t\t\"Indique si l\\'éditeur a un fournisseur de mise en forme pour les documents\",\n\t\t\"Indique si l\\'éditeur a un fournisseur de mise en forme de sélection pour les documents\",\n\t\t\"Indique si l\\'éditeur a plusieurs fournisseurs de mise en forme pour les documents\",\n\t\t\"Indique si l\\'éditeur a plusieurs fournisseurs de mise en forme de sélection pour les documents\",\n\t],\n\t\"vs/editor/common/languages\": [\n\t\t\"tableau\",\n\t\t\"booléen\",\n\t\t\"classe\",\n\t\t\"constante\",\n\t\t\"constructeur\",\n\t\t\"énumération\",\n\t\t\"membre d\\'énumération\",\n\t\t\"événement\",\n\t\t\"champ\",\n\t\t\"fichier\",\n\t\t\"fonction\",\n\t\t\"interface\",\n\t\t\"clé\",\n\t\t\"méthode\",\n\t\t\"module\",\n\t\t\"espace de noms\",\n\t\t\"NULL\",\n\t\t\"nombre\",\n\t\t\"objet\",\n\t\t\"opérateur\",\n\t\t\"package\",\n\t\t\"propriété\",\n\t\t\"chaîne\",\n\t\t\"struct\",\n\t\t\"paramètre de type\",\n\t\t\"variable\",\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/editor/common/languages/modesRegistry\": [\n\t\t\"Texte brut\",\n\t],\n\t\"vs/editor/common/model/editStack\": [\n\t\t\"Frappe en cours\",\n\t],\n\t\"vs/editor/common/standaloneStrings\": [\n\t\t\"Développeur : Inspecter les jetons\",\n\t\t\"Accéder à la ligne/colonne...\",\n\t\t\"Afficher tous les fournisseurs d\\'accès rapide\",\n\t\t\"Palette de commandes\",\n\t\t\"Commandes d\\'affichage et d\\'exécution\",\n\t\t\"Accéder au symbole...\",\n\t\t\"Accéder au symbole par catégorie...\",\n\t\t\"Contenu de l\\'éditeur\",\n\t\t\"Appuyez sur Alt+F1 pour voir les options d\\'accessibilité.\",\n\t\t\"Activer/désactiver le thème à contraste élevé\",\n\t\t\"{0} modifications dans {1} fichiers\",\n\t],\n\t\"vs/editor/common/viewLayout/viewLineRenderer\": [\n\t\t\"Afficher plus ({0})\",\n\t\t\"{0} caractères\",\n\t],\n\t\"vs/editor/contrib/anchorSelect/browser/anchorSelect\": [\n\t\t\"Ancre de sélection\",\n\t\t\"Ancre définie sur {0}:{1}\",\n\t\t\"Définir l\\'ancre de sélection\",\n\t\t\"Atteindre l\\'ancre de sélection\",\n\t\t\"Sélectionner de l\\'ancre au curseur\",\n\t\t\"Annuler l\\'ancre de sélection\",\n\t],\n\t\"vs/editor/contrib/bracketMatching/browser/bracketMatching\": [\n\t\t\"Couleur du marqueur de la règle d\\'aperçu pour rechercher des parenthèses.\",\n\t\t\"Atteindre le crochet\",\n\t\t\"Sélectionner jusqu\\'au crochet\",\n\t\t\"Supprimer les crochets\",\n\t\t\"Accéder au &&crochet\",\n\t],\n\t\"vs/editor/contrib/caretOperations/browser/caretOperations\": [\n\t\t\"Déplacer le texte sélectionné à gauche\",\n\t\t\"Déplacer le texte sélectionné à droite\",\n\t],\n\t\"vs/editor/contrib/caretOperations/browser/transpose\": [\n\t\t\"Transposer les lettres\",\n\t],\n\t\"vs/editor/contrib/clipboard/browser/clipboard\": [\n\t\t\"Co&&uper\",\n\t\t\"Couper\",\n\t\t\"Couper\",\n\t\t\"Couper\",\n\t\t\"&&Copier\",\n\t\t\"Copier\",\n\t\t\"Copier\",\n\t\t\"Copier\",\n\t\t\"Copier en tant que\",\n\t\t\"Copier en tant que\",\n\t\t\"Partager\",\n\t\t\"Partager\",\n\t\t\"Partager\",\n\t\t\"Co&&ller\",\n\t\t\"Coller\",\n\t\t\"Coller\",\n\t\t\"Coller\",\n\t\t\"Copier avec la coloration syntaxique\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeAction\": [\n\t\t\"Une erreur inconnue s\\'est produite à l\\'application de l\\'action du code\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionCommands\": [\n\t\t\"Type d\\'action de code à exécuter.\",\n\t\t\"Contrôle quand les actions retournées sont appliquées.\",\n\t\t\"Appliquez toujours la première action de code retournée.\",\n\t\t\"Appliquez la première action de code retournée si elle est la seule.\",\n\t\t\"N\\'appliquez pas les actions de code retournées.\",\n\t\t\"Contrôle si seules les actions de code par défaut doivent être retournées.\",\n\t\t\"Correction rapide...\",\n\t\t\"Aucune action de code disponible\",\n\t\t\"Aucune action de code préférée n\\'est disponible pour \\'{0}\\'\",\n\t\t\"Aucune action de code disponible pour \\'{0}\\'\",\n\t\t\"Aucune action de code par défaut disponible\",\n\t\t\"Aucune action de code disponible\",\n\t\t\"Remanier...\",\n\t\t\"Aucune refactorisation par défaut disponible pour \\'{0}\\'\",\n\t\t\"Aucune refactorisation disponible pour \\'{0}\\'\",\n\t\t\"Aucune refactorisation par défaut disponible\",\n\t\t\"Aucune refactorisation disponible\",\n\t\t\"Action de la source\",\n\t\t\"Aucune action source par défaut disponible pour \\'{0}\\'\",\n\t\t\"Aucune action source disponible pour \\'{0}\\'\",\n\t\t\"Aucune action source par défaut disponible\",\n\t\t\"Aucune action n\\'est disponible\",\n\t\t\"Organiser les importations\",\n\t\t\"Aucune action organiser les imports disponible\",\n\t\t\"Tout corriger\",\n\t\t\"Aucune action Tout corriger disponible\",\n\t\t\"Corriger automatiquement...\",\n\t\t\"Aucun correctif automatique disponible\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionContributions\": [\n\t\t\"Activez/désactivez l’affichage des en-têtes de groupe dans le menu d’action du code.\",\n\t\t\"Activez/désactivez l’affichage du correctif rapide le plus proche dans une ligne lorsqu’il n’est pas sur un diagnostic.\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionController\": [\n\t\t\"Contexte : {0} à la ligne {1} et à la colonne {2}.\",\n\t\t\"Masquer désactivé\",\n\t\t\"Afficher les éléments désactivés\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionMenu\": [\n\t\t\"Plus d’actions...\",\n\t\t\"Correctif rapide\",\n\t\t\"Extraire\",\n\t\t\"Inline\",\n\t\t\"Réécrire\",\n\t\t\"Déplacer\",\n\t\t\"Entourer de\",\n\t\t\"Action source\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/lightBulbWidget\": [\n\t\t\"Afficher les actions de code. Correctif rapide disponible par défaut ({0})\",\n\t\t\"Afficher les actions de code ({0})\",\n\t\t\"Afficher les actions de code\",\n\t],\n\t\"vs/editor/contrib/codelens/browser/codelensController\": [\n\t\t\"Afficher les commandes Code Lens de la ligne actuelle\",\n\t\t\"Sélectionner une commande\",\n\t],\n\t\"vs/editor/contrib/colorPicker/browser/colorPickerWidget\": [\n\t\t\"Cliquez pour activer/désactiver les options de couleur (rgb/hsl/hexadécimal).\",\n\t\t\"Icône pour fermer le sélecteur de couleurs\",\n\t],\n\t\"vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions\": [\n\t\t\"Afficher ou mettre le focus sur le sélecteur de couleurs autonome\",\n\t\t\"&&Afficher ou mettre le focus sur le sélecteur de couleurs autonome\",\n\t\t\"Masquer le sélecteur de couleurs\",\n\t\t\"Insérer une couleur avec un sélecteur de couleurs autonome\",\n\t],\n\t\"vs/editor/contrib/comment/browser/comment\": [\n\t\t\"Activer/désactiver le commentaire de ligne\",\n\t\t\"Afficher/masquer le commen&&taire de ligne\",\n\t\t\"Ajouter le commentaire de ligne\",\n\t\t\"Supprimer le commentaire de ligne\",\n\t\t\"Activer/désactiver le commentaire de bloc\",\n\t\t\"Afficher/masquer le commentaire de &&bloc\",\n\t],\n\t\"vs/editor/contrib/contextmenu/browser/contextmenu\": [\n\t\t\"Minimap\",\n\t\t\"Afficher les caractères\",\n\t\t\"Taille verticale\",\n\t\t\"Proportionnel\",\n\t\t\"Remplissage\",\n\t\t\"Ajuster\",\n\t\t\"Curseur\",\n\t\t\"Pointer la souris\",\n\t\t\"Toujours\",\n\t\t\"Afficher le menu contextuel de l\\'éditeur\",\n\t],\n\t\"vs/editor/contrib/cursorUndo/browser/cursorUndo\": [\n\t\t\"Annulation du curseur\",\n\t\t\"Restauration du curseur\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution\": [\n\t\t\"Coller en tant que...\",\n\t\t\"ID de la modification de collage à appliquer. S’il n’est pas fourni, l’éditeur affiche un sélecteur.\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/copyPasteController\": [\n\t\t\"Si le widget de collage est affiché\",\n\t\t\"Afficher les options de collage...\",\n\t\t\"Exécution des gestionnaires de collage. Cliquez pour annuler\",\n\t\t\"Sélectionner l’action Coller\",\n\t\t\"Exécution des gestionnaires de collage\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/defaultProviders\": [\n\t\t\"Intégré\",\n\t\t\"Insérer du texte brut\",\n\t\t\"Insérer des URI\",\n\t\t\"Insérer un URI\",\n\t\t\"Insérer des chemins d’accès\",\n\t\t\"Insérer un chemin d’accès\",\n\t\t\"Insérer des chemins d’accès relatifs\",\n\t\t\"Insérer un chemin d’accès relatif\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution\": [\n\t\t\"Configure le fournisseur de dépôt par défaut à utiliser pour le contenu d’un type MIME donné.\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController\": [\n\t\t\"Indique si le widget de suppression s’affiche\",\n\t\t\"Afficher les options de suppression...\",\n\t\t\"Exécution des gestionnaires de dépôt. Cliquez pour annuler\",\n\t],\n\t\"vs/editor/contrib/editorState/browser/keybindingCancellation\": [\n\t\t\"Indique si l\\'éditeur exécute une opération annulable, par exemple \\'Avoir un aperçu des références\\'\",\n\t],\n\t\"vs/editor/contrib/find/browser/findController\": [\n\t\t\"Le fichier est trop volumineux pour effectuer une opération Tout remplacer.\",\n\t\t\"Rechercher\",\n\t\t\"&&Rechercher\",\n\t\t\"Remplace l’indicateur « Utiliser une expression régulière ».\\r\\nL’indicateur ne sera pas enregistré à l’avenir.\\r\\n0 : Ne rien faire\\r\\n1 : Vrai\\r\\n2 : Faux\",\n\t\t\"Remplace l’indicateur « Match Whole Word ».\\r\\nL’indicateur ne sera pas enregistré à l’avenir.\\r\\n0 : Ne rien faire\\r\\n1 : Vrai\\r\\n2 : Faux\",\n\t\t\"Remplace l’indicateur « Cas mathématiques ».\\r\\nL’indicateur ne sera pas enregistré à l’avenir.\\r\\n0 : Ne rien faire\\r\\n1 : Vrai\\r\\n2 : Faux\",\n\t\t\"Remplace l’indicateur « Preserve Case ».\\r\\nL’indicateur ne sera pas enregistré à l’avenir.\\r\\n0 : Ne rien faire\\r\\n1 : Vrai\\r\\n2 : Faux\",\n\t\t\"Trouver avec des arguments\",\n\t\t\"Rechercher dans la sélection\",\n\t\t\"Rechercher suivant\",\n\t\t\"Rechercher précédent\",\n\t\t\"Accéder à la correspondance...\",\n\t\t\"Aucune correspondance. Essayez de rechercher autre chose.\",\n\t\t\"Tapez un nombre pour accéder à une correspondance spécifique (entre 1 et {0})\",\n\t\t\"Veuillez entrer un nombre compris entre 1 et {0}\",\n\t\t\"Veuillez entrer un nombre compris entre 1 et {0}\",\n\t\t\"Sélection suivante\",\n\t\t\"Sélection précédente\",\n\t\t\"Remplacer\",\n\t\t\"&&Remplacer\",\n\t],\n\t\"vs/editor/contrib/find/browser/findWidget\": [\n\t\t\"Icône de l\\'option Rechercher dans la sélection dans le widget de recherche de l\\'éditeur.\",\n\t\t\"Icône permettant d\\'indiquer que le widget de recherche de l\\'éditeur est réduit.\",\n\t\t\"Icône permettant d\\'indiquer que le widget de recherche de l\\'éditeur est développé.\",\n\t\t\"Icône de l\\'option Remplacer dans le widget de recherche de l\\'éditeur.\",\n\t\t\"Icône de l\\'option Tout remplacer dans le widget de recherche de l\\'éditeur.\",\n\t\t\"Icône de l\\'option Rechercher précédent dans le widget de recherche de l\\'éditeur.\",\n\t\t\"Icône de l\\'option Rechercher suivant dans le widget de recherche de l\\'éditeur.\",\n\t\t\"Rechercher/remplacer\",\n\t\t\"Rechercher\",\n\t\t\"Rechercher\",\n\t\t\"Correspondance précédente\",\n\t\t\"Correspondance suivante\",\n\t\t\"Rechercher dans la sélection\",\n\t\t\"Fermer\",\n\t\t\"Remplacer\",\n\t\t\"Remplacer\",\n\t\t\"Remplacer\",\n\t\t\"Tout remplacer\",\n\t\t\"Activer/désactiver le remplacement\",\n\t\t\"Seuls les {0} premiers résultats sont mis en évidence, mais toutes les opérations de recherche fonctionnent sur l’ensemble du texte.\",\n\t\t\"{0} sur {1}\",\n\t\t\"Aucun résultat\",\n\t\t\"{0} trouvé(s)\",\n\t\t\"{0} trouvé pour \\'{1}\\'\",\n\t\t\"{0} trouvé pour \\'{1}\\', sur {2}\",\n\t\t\"{0} trouvé pour \\'{1}\\'\",\n\t\t\"La combinaison Ctrl+Entrée permet désormais d\\'ajouter un saut de ligne au lieu de tout remplacer. Vous pouvez modifier le raccourci clavier de editor.action.replaceAll pour redéfinir le comportement.\",\n\t],\n\t\"vs/editor/contrib/folding/browser/folding\": [\n\t\t\"Déplier\",\n\t\t\"Déplier de manière récursive\",\n\t\t\"Plier\",\n\t\t\"Activer/désactiver le pliage\",\n\t\t\"Plier de manière récursive\",\n\t\t\"Replier tous les commentaires de bloc\",\n\t\t\"Replier toutes les régions\",\n\t\t\"Déplier toutes les régions\",\n\t\t\"Plier tout, sauf les éléments sélectionnés\",\n\t\t\"Déplier tout, sauf les éléments sélectionnés\",\n\t\t\"Plier tout\",\n\t\t\"Déplier tout\",\n\t\t\"Atteindre le pli parent\",\n\t\t\"Accéder à la plage de pliage précédente\",\n\t\t\"Accéder à la plage de pliage suivante\",\n\t\t\"Créer une plage de pliage à partir de la sélection\",\n\t\t\"Supprimer les plages de pliage manuelles\",\n\t\t\"Niveau de pliage {0}\",\n\t],\n\t\"vs/editor/contrib/folding/browser/foldingDecorations\": [\n\t\t\"Couleur d\\'arrière-plan des gammes pliées. La couleur ne doit pas être opaque pour ne pas cacher les décorations sous-jacentes.\",\n\t\t\"Couleur du contrôle de pliage dans la marge de l\\'éditeur.\",\n\t\t\"Icône des plages développées dans la marge de glyphes de l\\'éditeur.\",\n\t\t\"Icône des plages réduites dans la marge de glyphes de l\\'éditeur.\",\n\t\t\"Icône pour les plages réduites manuellement dans la marge de glyphe de l’éditeur.\",\n\t\t\"Icône pour les plages développées manuellement dans la marge de glyphe de l’éditeur.\",\n\t],\n\t\"vs/editor/contrib/fontZoom/browser/fontZoom\": [\n\t\t\"Agrandissement de l\\'éditeur de polices de caractères\",\n\t\t\"Rétrécissement de l\\'éditeur de polices de caractères\",\n\t\t\"Remise à niveau du zoom de l\\'éditeur de polices de caractères\",\n\t],\n\t\"vs/editor/contrib/format/browser/format\": [\n\t\t\"1 modification de format effectuée à la ligne {0}\",\n\t\t\"{0} modifications de format effectuées à la ligne {1}\",\n\t\t\"1 modification de format effectuée entre les lignes {0} et {1}\",\n\t\t\"{0} modifications de format effectuées entre les lignes {1} et {2}\",\n\t],\n\t\"vs/editor/contrib/format/browser/formatActions\": [\n\t\t\"Mettre le document en forme\",\n\t\t\"Mettre la sélection en forme\",\n\t],\n\t\"vs/editor/contrib/gotoError/browser/gotoError\": [\n\t\t\"Aller au problème suivant (Erreur, Avertissement, Info)\",\n\t\t\"Icône du prochain marqueur goto.\",\n\t\t\"Aller au problème précédent (Erreur, Avertissement, Info)\",\n\t\t\"Icône du précédent marqueur goto.\",\n\t\t\"Aller au problème suivant dans Fichiers (Erreur, Avertissement, Info)\",\n\t\t\"&&Problème suivant\",\n\t\t\"Aller au problème précédent dans Fichiers (Erreur, Avertissement, Info)\",\n\t\t\"&&Problème précédent\",\n\t],\n\t\"vs/editor/contrib/gotoError/browser/gotoErrorWidget\": [\n\t\t\"Erreur\",\n\t\t\"Avertissement\",\n\t\t\"Info\",\n\t\t\"Conseil\",\n\t\t\"{0} à {1}. \",\n\t\t\"{0} problèmes sur {1}\",\n\t\t\"{0} problème(s) sur {1}\",\n\t\t\"Couleur d\\'erreur du widget de navigation dans les marqueurs de l\\'éditeur.\",\n\t\t\"Arrière-plan du titre d’erreur du widget de navigation dans les marqueurs de l’éditeur.\",\n\t\t\"Couleur d\\'avertissement du widget de navigation dans les marqueurs de l\\'éditeur.\",\n\t\t\"Arrière-plan du titre d’erreur du widget de navigation dans les marqueurs de l’éditeur.\",\n\t\t\"Couleur d’information du widget de navigation du marqueur de l\\'éditeur.\",\n\t\t\"Arrière-plan du titre des informations du widget de navigation dans les marqueurs de l’éditeur.\",\n\t\t\"Arrière-plan du widget de navigation dans les marqueurs de l\\'éditeur.\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/goToCommands\": [\n\t\t\"Aperçu\",\n\t\t\"Définitions\",\n\t\t\"Définition introuvable pour \\'{0}\\'\",\n\t\t\"Définition introuvable\",\n\t\t\"Atteindre la définition\",\n\t\t\"Atteindre la &&définition\",\n\t\t\"Ouvrir la définition sur le côté\",\n\t\t\"Aperçu de la définition\",\n\t\t\"Déclarations\",\n\t\t\"Aucune déclaration pour \\'{0}\\'\",\n\t\t\"Aucune déclaration\",\n\t\t\"Accéder à la déclaration\",\n\t\t\"Atteindre la &&déclaration\",\n\t\t\"Aucune déclaration pour \\'{0}\\'\",\n\t\t\"Aucune déclaration\",\n\t\t\"Aperçu de la déclaration\",\n\t\t\"Définitions de type\",\n\t\t\"Définition de type introuvable pour \\'{0}\\'\",\n\t\t\"Définition de type introuvable\",\n\t\t\"Atteindre la définition du type\",\n\t\t\"Accéder à la définition de &&type\",\n\t\t\"Aperçu de la définition du type\",\n\t\t\"Implémentations\",\n\t\t\"Implémentation introuvable pour \\'{0}\\'\",\n\t\t\"Implémentation introuvable\",\n\t\t\"Atteindre les implémentations\",\n\t\t\"Atteindre les &&implémentations\",\n\t\t\"Aperçu des implémentations\",\n\t\t\"Aucune référence pour \\'{0}\\'\",\n\t\t\"Aucune référence\",\n\t\t\"Atteindre les références\",\n\t\t\"Atteindre les &&références\",\n\t\t\"Références\",\n\t\t\"Aperçu des références\",\n\t\t\"Références\",\n\t\t\"Atteindre un symbole\",\n\t\t\"Emplacements\",\n\t\t\"Aucun résultat pour « {0} »\",\n\t\t\"Références\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition\": [\n\t\t\"Cliquez pour afficher {0} définitions.\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesController\": [\n\t\t\"Indique si l\\'aperçu des références est visible, par exemple via \\'Avoir un aperçu des références\\' ou \\'Faire un peek de la définition\\'\",\n\t\t\"Chargement en cours...\",\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesTree\": [\n\t\t\"{0} références\",\n\t\t\"{0} référence\",\n\t\t\"Références\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget\": [\n\t\t\"aperçu non disponible\",\n\t\t\"Aucun résultat\",\n\t\t\"Références\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/referencesModel\": [\n\t\t\"dans {0} à la ligne {1} à la colonne {2}\",\n\t\t\"{0}dans {1} à la ligne {2} à la colonne {3}\",\n\t\t\"1 symbole dans {0}, chemin complet {1}\",\n\t\t\"{0} symboles dans {1}, chemin complet {2}\",\n\t\t\"Résultats introuvables\",\n\t\t\"1 symbole dans {0}\",\n\t\t\"{0} symboles dans {1}\",\n\t\t\"{0} symboles dans {1} fichiers\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/symbolNavigation\": [\n\t\t\"Indique s\\'il existe des emplacements de symboles que vous pouvez parcourir à l\\'aide du clavier uniquement.\",\n\t\t\"Symbole {0} sur {1}, {2} pour le suivant\",\n\t\t\"Symbole {0} sur {1}\",\n\t],\n\t\"vs/editor/contrib/hover/browser/hover\": [\n\t\t\"Afficher ou focus sur pointer\",\n\t\t\"Afficher le pointeur de l\\'aperçu de définition\",\n\t\t\"Faire défiler le pointage vers le haut\",\n\t\t\"Faire défiler le pointage vers le bas\",\n\t\t\"Faire défiler vers la gauche au pointage\",\n\t\t\"Faire défiler le pointage vers la droite\",\n\t\t\"Pointer vers le haut de la page\",\n\t\t\"Pointer vers le bas de la page\",\n\t\t\"Atteindre le pointage supérieur\",\n\t\t\"Pointer vers le bas\",\n\t],\n\t\"vs/editor/contrib/hover/browser/markdownHoverParticipant\": [\n\t\t\"Chargement en cours...\",\n\t\t\"Rendu suspendu pour une longue ligne pour des raisons de performances. Cela peut être configuré via \\'editor.stopRenderingLineAfter\\'.\",\n\t\t\"La tokenisation des lignes longues est ignorée pour des raisons de performances. Cela peut être configurée via \\'editor.maxTokenizationLineLength\\'.\",\n\t],\n\t\"vs/editor/contrib/hover/browser/markerHoverParticipant\": [\n\t\t\"Voir le problème\",\n\t\t\"Aucune solution disponible dans l\\'immédiat\",\n\t\t\"Recherche de correctifs rapides...\",\n\t\t\"Aucune solution disponible dans l\\'immédiat\",\n\t\t\"Correction rapide...\",\n\t],\n\t\"vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace\": [\n\t\t\"Remplacer par la valeur précédente\",\n\t\t\"Remplacer par la valeur suivante\",\n\t],\n\t\"vs/editor/contrib/indentation/browser/indentation\": [\n\t\t\"Convertir les retraits en espaces\",\n\t\t\"Convertir les retraits en tabulations\",\n\t\t\"Taille des tabulations configurée\",\n\t\t\"Taille d’onglet par défaut\",\n\t\t\"Taille actuelle de l’onglet\",\n\t\t\"Sélectionner la taille des tabulations pour le fichier actuel\",\n\t\t\"Mettre en retrait avec des tabulations\",\n\t\t\"Mettre en retrait avec des espaces\",\n\t\t\"Modifier la taille d’affichage de l’onglet\",\n\t\t\"Détecter la mise en retrait à partir du contenu\",\n\t\t\"Remettre en retrait les lignes\",\n\t\t\"Réindenter les lignes sélectionnées\",\n\t],\n\t\"vs/editor/contrib/inlayHints/browser/inlayHintsHover\": [\n\t\t\"Double-cliquer pour insérer\",\n\t\t\"cmd + clic\",\n\t\t\"ctrl + clic\",\n\t\t\"option + clic\",\n\t\t\"alt + clic\",\n\t\t\"Accédez à Définition ({0}), cliquez avec le bouton droit pour en savoir plus.\",\n\t\t\"Accéder à Définition ({0})\",\n\t\t\"Exécuter la commande\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/commands\": [\n\t\t\"Afficher la suggestion en ligne suivante\",\n\t\t\"Afficher la suggestion en ligne précédente\",\n\t\t\"Déclencher la suggestion en ligne\",\n\t\t\"Accepter le mot suivant de la suggestion inline\",\n\t\t\"Accepter le mot\",\n\t\t\"Accepter la ligne suivante d’une suggestion en ligne\",\n\t\t\"Accepter la ligne\",\n\t\t\"Accepter la suggestion inline\",\n\t\t\"Accepter\",\n\t\t\"Masquer la suggestion inlined\",\n\t\t\"Toujours afficher la barre d’outils\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/hoverParticipant\": [\n\t\t\"Suggestion :\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys\": [\n\t\t\"Indique si une suggestion en ligne est visible\",\n\t\t\"Indique si la suggestion en ligne commence par un espace blanc\",\n\t\t\"Indique si la suggestion incluse commence par un espace blanc inférieur à ce qui serait inséré par l’onglet.\",\n\t\t\"Indique si les suggestions doivent être supprimées pour la suggestion actuelle\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsController\": [\n\t\t\"Inspecter ceci dans l’affichage accessible ({0})\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget\": [\n\t\t\"Icône d\\'affichage du prochain conseil de paramètre.\",\n\t\t\"Icône d\\'affichage du précédent conseil de paramètre.\",\n\t\t\"{0} ({1})\",\n\t\t\"Précédent\",\n\t\t\"Suivant\",\n\t],\n\t\"vs/editor/contrib/lineSelection/browser/lineSelection\": [\n\t\t\"Développer la sélection de ligne\",\n\t],\n\t\"vs/editor/contrib/linesOperations/browser/linesOperations\": [\n\t\t\"Copier la ligne en haut\",\n\t\t\"&&Copier la ligne en haut\",\n\t\t\"Copier la ligne en bas\",\n\t\t\"Co&&pier la ligne en bas\",\n\t\t\"Dupliquer la sélection\",\n\t\t\"&&Dupliquer la sélection\",\n\t\t\"Déplacer la ligne vers le haut\",\n\t\t\"Déplacer la ligne &&vers le haut\",\n\t\t\"Déplacer la ligne vers le bas\",\n\t\t\"Déplacer la &&ligne vers le bas\",\n\t\t\"Trier les lignes dans l\\'ordre croissant\",\n\t\t\"Trier les lignes dans l\\'ordre décroissant\",\n\t\t\"Supprimer les lignes dupliquées\",\n\t\t\"Découper l\\'espace blanc de fin\",\n\t\t\"Supprimer la ligne\",\n\t\t\"Mettre en retrait la ligne\",\n\t\t\"Ajouter un retrait négatif à la ligne\",\n\t\t\"Insérer une ligne au-dessus\",\n\t\t\"Insérer une ligne sous\",\n\t\t\"Supprimer tout ce qui est à gauche\",\n\t\t\"Supprimer tout ce qui est à droite\",\n\t\t\"Joindre les lignes\",\n\t\t\"Transposer des caractères autour du curseur\",\n\t\t\"Transformer en majuscule\",\n\t\t\"Transformer en minuscule\",\n\t\t\"Appliquer la casse \\\"1re lettre des mots en majuscule\\\"\",\n\t\t\"Transformer en snake case\",\n\t\t\"Transformer en casse mixte\",\n\t\t\"Transformer en kebab case\",\n\t],\n\t\"vs/editor/contrib/linkedEditing/browser/linkedEditing\": [\n\t\t\"Démarrer la modification liée\",\n\t\t\"Couleur d\\'arrière-plan quand l\\'éditeur renomme automatiquement le type.\",\n\t],\n\t\"vs/editor/contrib/links/browser/links\": [\n\t\t\"Échec de l\\'ouverture de ce lien, car il n\\'est pas bien formé : {0}\",\n\t\t\"Échec de l\\'ouverture de ce lien, car sa cible est manquante.\",\n\t\t\"Exécuter la commande\",\n\t\t\"suivre le lien\",\n\t\t\"cmd + clic\",\n\t\t\"ctrl + clic\",\n\t\t\"option + clic\",\n\t\t\"alt + clic\",\n\t\t\"Exécuter la commande {0}\",\n\t\t\"Ouvrir le lien\",\n\t],\n\t\"vs/editor/contrib/message/browser/messageController\": [\n\t\t\"Indique si l\\'éditeur affiche un message inline\",\n\t],\n\t\"vs/editor/contrib/multicursor/browser/multicursor\": [\n\t\t\"Curseur ajouté : {0}\",\n\t\t\"Curseurs ajoutés : {0}\",\n\t\t\"Ajouter un curseur au-dessus\",\n\t\t\"&&Ajouter un curseur au-dessus\",\n\t\t\"Ajouter un curseur en dessous\",\n\t\t\"Aj&&outer un curseur en dessous\",\n\t\t\"Ajouter des curseurs à la fin des lignes\",\n\t\t\"Ajouter des c&&urseurs à la fin des lignes\",\n\t\t\"Ajouter des curseurs en bas\",\n\t\t\"Ajouter des curseurs en haut\",\n\t\t\"Ajouter la sélection à la correspondance de recherche suivante\",\n\t\t\"Ajouter l\\'occurrence suiva&&nte\",\n\t\t\"Ajouter la sélection à la correspondance de recherche précédente\",\n\t\t\"Ajouter l\\'occurrence p&&récédente\",\n\t\t\"Déplacer la dernière sélection vers la correspondance de recherche suivante\",\n\t\t\"Déplacer la dernière sélection à la correspondance de recherche précédente\",\n\t\t\"Sélectionner toutes les occurrences des correspondances de la recherche\",\n\t\t\"Sélectionner toutes les &&occurrences\",\n\t\t\"Modifier toutes les occurrences\",\n\t\t\"Focus sur le curseur suivant\",\n\t\t\"Concentre le curseur suivant\",\n\t\t\"Focus sur le curseur précédent\",\n\t\t\"Concentre le curseur précédent\",\n\t],\n\t\"vs/editor/contrib/parameterHints/browser/parameterHints\": [\n\t\t\"Indicateurs des paramètres Trigger\",\n\t],\n\t\"vs/editor/contrib/parameterHints/browser/parameterHintsWidget\": [\n\t\t\"Icône d\\'affichage du prochain conseil de paramètre.\",\n\t\t\"Icône d\\'affichage du précédent conseil de paramètre.\",\n\t\t\"{0}, conseil\",\n\t\t\"Couleur de premier plan de l’élément actif dans l’indicateur de paramètre.\",\n\t],\n\t\"vs/editor/contrib/peekView/browser/peekView\": [\n\t\t\"Indique si l\\'éditeur de code actuel est intégré à l\\'aperçu\",\n\t\t\"Fermer\",\n\t\t\"Couleur d\\'arrière-plan de la zone de titre de l\\'affichage d\\'aperçu.\",\n\t\t\"Couleur du titre de l\\'affichage d\\'aperçu.\",\n\t\t\"Couleur des informations sur le titre de l\\'affichage d\\'aperçu.\",\n\t\t\"Couleur des bordures et de la flèche de l\\'affichage d\\'aperçu.\",\n\t\t\"Couleur d\\'arrière-plan de la liste des résultats de l\\'affichage d\\'aperçu.\",\n\t\t\"Couleur de premier plan des noeuds de lignes dans la liste des résultats de l\\'affichage d\\'aperçu.\",\n\t\t\"Couleur de premier plan des noeuds de fichiers dans la liste des résultats de l\\'affichage d\\'aperçu.\",\n\t\t\"Couleur d\\'arrière-plan de l\\'entrée sélectionnée dans la liste des résultats de l\\'affichage d\\'aperçu.\",\n\t\t\"Couleur de premier plan de l\\'entrée sélectionnée dans la liste des résultats de l\\'affichage d\\'aperçu.\",\n\t\t\"Couleur d\\'arrière-plan de l\\'éditeur d\\'affichage d\\'aperçu.\",\n\t\t\"Couleur d\\'arrière-plan de la bordure de l\\'éditeur d\\'affichage d\\'aperçu.\",\n\t\t\"Couleur d’arrière-plan du défilement rémanent dans l’éditeur d’affichage d’aperçu.\",\n\t\t\"Couleur de mise en surbrillance d\\'une correspondance dans la liste des résultats de l\\'affichage d\\'aperçu.\",\n\t\t\"Couleur de mise en surbrillance d\\'une correspondance dans l\\'éditeur de l\\'affichage d\\'aperçu.\",\n\t\t\"Bordure de mise en surbrillance d\\'une correspondance dans l\\'éditeur de l\\'affichage d\\'aperçu.\",\n\t],\n\t\"vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess\": [\n\t\t\"Ouvrez d\\'abord un éditeur de texte pour accéder à une ligne.\",\n\t\t\"Atteindre la ligne {0} et le caractère {1}.\",\n\t\t\"Accédez à la ligne {0}.\",\n\t\t\"Ligne actuelle : {0}, caractère : {1}. Tapez un numéro de ligne entre 1 et {2} auquel accéder.\",\n\t\t\"Ligne actuelle : {0}, caractère : {1}. Tapez un numéro de ligne auquel accéder.\",\n\t],\n\t\"vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess\": [\n\t\t\"Pour accéder à un symbole, ouvrez d\\'abord un éditeur de texte avec des informations de symbole.\",\n\t\t\"L\\'éditeur de texte actif ne fournit pas les informations de symbole.\",\n\t\t\"Aucun symbole d\\'éditeur correspondant\",\n\t\t\"Aucun symbole d\\'éditeur\",\n\t\t\"Ouvrir sur le côté\",\n\t\t\"Ouvrir en bas\",\n\t\t\"symboles ({0})\",\n\t\t\"propriétés ({0})\",\n\t\t\"méthodes ({0})\",\n\t\t\"fonctions ({0})\",\n\t\t\"constructeurs ({0})\",\n\t\t\"variables ({0})\",\n\t\t\"classes ({0})\",\n\t\t\"structs ({0})\",\n\t\t\"événements ({0})\",\n\t\t\"opérateurs ({0})\",\n\t\t\"interfaces ({0})\",\n\t\t\"espaces de noms ({0})\",\n\t\t\"packages ({0})\",\n\t\t\"paramètres de type ({0})\",\n\t\t\"modules ({0})\",\n\t\t\"propriétés ({0})\",\n\t\t\"énumérations ({0})\",\n\t\t\"membres d\\'énumération ({0})\",\n\t\t\"chaînes ({0})\",\n\t\t\"fichiers ({0})\",\n\t\t\"tableaux ({0})\",\n\t\t\"nombres ({0})\",\n\t\t\"booléens ({0})\",\n\t\t\"objets ({0})\",\n\t\t\"clés ({0})\",\n\t\t\"champs ({0})\",\n\t\t\"constantes ({0})\",\n\t],\n\t\"vs/editor/contrib/readOnlyMessage/browser/contribution\": [\n\t\t\"Impossible de modifier dans l’entrée en lecture seule\",\n\t\t\"Impossible de modifier dans l’éditeur en lecture seule\",\n\t],\n\t\"vs/editor/contrib/rename/browser/rename\": [\n\t\t\"Aucun résultat.\",\n\t\t\"Une erreur inconnue s\\'est produite lors de la résolution de l\\'emplacement de renommage\",\n\t\t\"Renommage de \\'{0}\\' en \\'{1}\\'\",\n\t\t\"Changement du nom de {0} en {1}\",\n\t\t\"\\'{0}\\' renommé en \\'{1}\\'. Récapitulatif : {2}\",\n\t\t\"Le renommage n\\'a pas pu appliquer les modifications\",\n\t\t\"Le renommage n\\'a pas pu calculer les modifications\",\n\t\t\"Renommer le symbole\",\n\t\t\"Activer/désactiver la possibilité d\\'afficher un aperçu des changements avant le renommage\",\n\t],\n\t\"vs/editor/contrib/rename/browser/renameInputField\": [\n\t\t\"Indique si le widget de renommage d\\'entrée est visible\",\n\t\t\"Renommez l\\'entrée. Tapez le nouveau nom et appuyez sur Entrée pour valider.\",\n\t\t\"{0} pour renommer, {1} pour afficher un aperçu\",\n\t],\n\t\"vs/editor/contrib/smartSelect/browser/smartSelect\": [\n\t\t\"Étendre la sélection\",\n\t\t\"Dév&&elopper la sélection\",\n\t\t\"Réduire la sélection\",\n\t\t\"&&Réduire la sélection\",\n\t],\n\t\"vs/editor/contrib/snippet/browser/snippetController2\": [\n\t\t\"Indique si l\\'éditeur est actualisé en mode extrait\",\n\t\t\"Indique s\\'il existe un taquet de tabulation suivant en mode extrait\",\n\t\t\"Indique s\\'il existe un taquet de tabulation précédent en mode extrait\",\n\t\t\"Accéder à l’espace réservé suivant...\",\n\t],\n\t\"vs/editor/contrib/snippet/browser/snippetVariables\": [\n\t\t\"Dimanche\",\n\t\t\"Lundi\",\n\t\t\"Mardi\",\n\t\t\"Mercredi\",\n\t\t\"Jeudi\",\n\t\t\"Vendredi\",\n\t\t\"Samedi\",\n\t\t\"Dim\",\n\t\t\"Lun\",\n\t\t\"Mar\",\n\t\t\"Mer\",\n\t\t\"Jeu\",\n\t\t\"Ven\",\n\t\t\"Sam\",\n\t\t\"Janvier\",\n\t\t\"Février\",\n\t\t\"Mars\",\n\t\t\"Avril\",\n\t\t\"Mai\",\n\t\t\"Juin\",\n\t\t\"Juillet\",\n\t\t\"Août\",\n\t\t\"Septembre\",\n\t\t\"Octobre\",\n\t\t\"Novembre\",\n\t\t\"Décembre\",\n\t\t\"Jan\",\n\t\t\"Fév\",\n\t\t\"Mar\",\n\t\t\"Avr\",\n\t\t\"Mai\",\n\t\t\"Juin\",\n\t\t\"Jul\",\n\t\t\"Aoû\",\n\t\t\"Sept\",\n\t\t\"Oct\",\n\t\t\"Nov\",\n\t\t\"Déc\",\n\t],\n\t\"vs/editor/contrib/stickyScroll/browser/stickyScrollActions\": [\n\t\t\"Activer/désactiver le défilement épinglé\",\n\t\t\"&&Activer/désactiver le défilement épinglé\",\n\t\t\"Défilement épinglé\",\n\t\t\"&&Défilement épinglé\",\n\t\t\"Focus sur le défilement du pense-bête\",\n\t\t\"&&Focus sur le défilement du pense-bête\",\n\t\t\"Sélectionner la ligne de défilement du pense-bête suivante\",\n\t\t\"Sélectionner la ligne de défilement du pense-bête précédente\",\n\t\t\"Atteindre la ligne de défilement pense-bête prioritaire\",\n\t\t\"Sélectionner l\\'éditeur\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggest\": [\n\t\t\"Indique si une suggestion a le focus\",\n\t\t\"Indique si les détails des suggestions sont visibles\",\n\t\t\"Indique s\\'il existe plusieurs suggestions au choix\",\n\t\t\"Indique si l\\'insertion de la suggestion actuelle entraîne un changement ou si tout a déjà été tapé\",\n\t\t\"Indique si les suggestions sont insérées quand vous appuyez sur Entrée\",\n\t\t\"Indique si la suggestion actuelle a un comportement d\\'insertion et de remplacement\",\n\t\t\"Indique si le comportement par défaut consiste à insérer ou à remplacer\",\n\t\t\"Indique si la suggestion actuelle prend en charge la résolution des détails supplémentaires\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestController\": [\n\t\t\"L\\'acceptation de \\'{0}\\' a entraîné {1} modifications supplémentaires\",\n\t\t\"Suggestions pour Trigger\",\n\t\t\"Insérer\",\n\t\t\"Insérer\",\n\t\t\"Remplacer\",\n\t\t\"Remplacer\",\n\t\t\"Insérer\",\n\t\t\"afficher moins\",\n\t\t\"afficher plus\",\n\t\t\"Réinitialiser la taille du widget de suggestion\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidget\": [\n\t\t\"Couleur d\\'arrière-plan du widget de suggestion.\",\n\t\t\"Couleur de bordure du widget de suggestion.\",\n\t\t\"Couleur de premier plan du widget de suggestion.\",\n\t\t\"Couleur de premier plan de l’entrée sélectionnée dans le widget de suggestion.\",\n\t\t\"Couleur de premier plan de l’icône de l’entrée sélectionnée dans le widget de suggestion.\",\n\t\t\"Couleur d\\'arrière-plan de l\\'entrée sélectionnée dans le widget de suggestion.\",\n\t\t\"Couleur de la surbrillance des correspondances dans le widget de suggestion.\",\n\t\t\"Couleur des mises en surbrillance dans le widget de suggestion lorsqu’un élément a le focus.\",\n\t\t\"Couleur de premier plan du statut du widget de suggestion.\",\n\t\t\"Chargement en cours...\",\n\t\t\"Pas de suggestions.\",\n\t\t\"Suggérer\",\n\t\t\"{0} {1}, {2}\",\n\t\t\"{0} {1}\",\n\t\t\"{0}, {1}\",\n\t\t\"{0}, documents : {1}\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetDetails\": [\n\t\t\"Fermer\",\n\t\t\"Chargement en cours...\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetRenderer\": [\n\t\t\"Icône d\\'affichage d\\'informations supplémentaires dans le widget de suggestion.\",\n\t\t\"Lire la suite\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetStatus\": [\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/editor/contrib/symbolIcons/browser/symbolIcons\": [\n\t\t\"Couleur de premier plan des symboles de tableau. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles booléens. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles de classe. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles de couleur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan pour les symboles de constante. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles de constructeur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles d\\'énumérateur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles de membre d\\'énumérateur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles d\\'événement. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles de champ. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles de fichier. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles de dossier. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles de fonction. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles d\\'interface. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles de clé. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles de mot clé. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles de méthode. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles de module. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles d\\'espace de noms. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles null. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles de nombre. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles d\\'objet. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles d\\'opérateur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles de package. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles de propriété. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles de référence. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles d\\'extrait de code. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles de chaîne. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles de struct. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles de texte. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles de paramètre de type. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles d\\'unité. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t\t\"Couleur de premier plan des symboles de variable. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.\",\n\t],\n\t\"vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode\": [\n\t\t\"Activer/désactiver l\\'utilisation de la touche Tab pour déplacer le focus\",\n\t\t\"Appuyer sur Tab déplacera le focus vers le prochain élément pouvant être désigné comme élément actif\",\n\t\t\"Appuyer sur Tab insérera le caractère de tabulation\",\n\t],\n\t\"vs/editor/contrib/tokenization/browser/tokenization\": [\n\t\t\"Développeur : forcer la retokenisation\",\n\t],\n\t\"vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter\": [\n\t\t\"Icône affichée avec un message d\\'avertissement dans l\\'éditeur d\\'extensions.\",\n\t\t\"Ce document contient de nombreux caractères Unicode ASCII non basiques.\",\n\t\t\"Ce document contient de nombreux caractères Unicode ambigus.\",\n\t\t\"Ce document contient de nombreux caractères Unicode invisibles.\",\n\t\t\"Le caractère {0} peut être confondu avec le caractère ASCII {1}, qui est plus courant dans le code source.\",\n\t\t\"Le caractère {0} peut être confus avec le caractère {1}, ce qui est plus courant dans le code source.\",\n\t\t\"Le caractère {0} est invisible.\",\n\t\t\"Le caractère {0} n’est pas un caractère ASCII de base.\",\n\t\t\"Ajuster les paramètres\",\n\t\t\"Désactiver la mise en surbrillance dans les commentaires\",\n\t\t\"Désactiver la mise en surbrillance des caractères dans les commentaires\",\n\t\t\"Désactiver la mise en surbrillance dans les chaînes\",\n\t\t\"Désactiver la mise en surbrillance des caractères dans les chaînes\",\n\t\t\"Désactiver la mise en surbrillance ambiguë\",\n\t\t\"Désactiver la mise en surbrillance des caractères ambigus\",\n\t\t\"Désactiver le surlignage invisible\",\n\t\t\"Désactiver la mise en surbrillance des caractères invisibles\",\n\t\t\"Désactiver la mise en surbrillance non ASCII\",\n\t\t\"Désactiver la mise en surbrillance des caractères ASCII non de base\",\n\t\t\"Afficher les options d’exclusion\",\n\t\t\"Exclure la mise en surbrillance des {0} (caractère invisible)\",\n\t\t\"Exclure {0} de la mise en surbrillance\",\n\t\t\"Autoriser les caractères Unicode plus courants dans le langage \\\"{0}\\\"\",\n\t\t\"Configurer les options de surlignage Unicode\",\n\t],\n\t\"vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators\": [\n\t\t\"Marques de fin de ligne inhabituelles\",\n\t\t\"Marques de fin de ligne inhabituelles détectées\",\n\t\t\"Le fichier « {0} »contient un ou plusieurs caractères de fin de ligne inhabituels, par exemple le séparateur de ligne (LS) ou le séparateur de paragraphe (PS).\\r\\n\\r\\nIl est recommandé de les supprimer du fichier. Vous pouvez configurer ce comportement par le biais de `editor.unusualLineTerminators`.\",\n\t\t\"&&Supprimer les marques de fin de ligne inhabituelles\",\n\t\t\"Ignorer\",\n\t],\n\t\"vs/editor/contrib/wordHighlighter/browser/highlightDecorations\": [\n\t\t\"Couleur d\\'arrière-plan d\\'un symbole pendant l\\'accès en lecture, comme la lecture d\\'une variable. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Couleur d\\'arrière-plan d\\'un symbole pendant l\\'accès en écriture, comme l\\'écriture d\\'une variable. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Couleur d’arrière-plan d’une occurrence textuelle d’un symbole. La couleur ne doit pas être opaque afin de ne pas masquer les décorations sous-jacentes.\",\n\t\t\"Couleur de bordure d\\'un symbole durant l\\'accès en lecture, par exemple la lecture d\\'une variable.\",\n\t\t\"Couleur de bordure d\\'un symbole durant l\\'accès en écriture, par exemple l\\'écriture dans une variable.\",\n\t\t\"Couleur de bordure d’une occurrence textuelle pour un symbole.\",\n\t\t\"Couleur de marqueur de la règle d\\'aperçu pour la mise en surbrillance des symboles. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Couleur de marqueur de la règle d\\'aperçu pour la mise en surbrillance des symboles d\\'accès en écriture. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Couleur de marqueur de règle d’aperçu d’une occurrence textuelle pour un symbole. La couleur ne doit pas être opaque afin de ne pas masquer les décorations sous-jacentes.\",\n\t],\n\t\"vs/editor/contrib/wordHighlighter/browser/wordHighlighter\": [\n\t\t\"Aller à la prochaine mise en évidence de symbole\",\n\t\t\"Aller à la mise en évidence de symbole précédente\",\n\t\t\"Déclencher la mise en évidence de symbole\",\n\t],\n\t\"vs/editor/contrib/wordOperations/browser/wordOperations\": [\n\t\t\"Supprimer le mot\",\n\t],\n\t\"vs/platform/action/common/actionCommonCategories\": [\n\t\t\"Afficher\",\n\t\t\"Aide\",\n\t\t\"Test\",\n\t\t\"fichier\",\n\t\t\"Préférences\",\n\t\t\"Développeur\",\n\t],\n\t\"vs/platform/actionWidget/browser/actionList\": [\n\t\t\"{0} à appliquer, {1} à afficher un aperçu\",\n\t\t\"{0} pour appliquer\",\n\t\t\"{0}, raison désactivée : {1}\",\n\t\t\"Widget d’action\",\n\t],\n\t\"vs/platform/actionWidget/browser/actionWidget\": [\n\t\t\"Couleur d\\'arrière-plan des éléments d\\'action activés dans la barre d\\'action.\",\n\t\t\"Indique si la liste des widgets d’action est visible\",\n\t\t\"Masquer le widget d’action\",\n\t\t\"Sélectionner l’action précédente\",\n\t\t\"Sélectionner l’action suivante\",\n\t\t\"Accepter l’action sélectionnée\",\n\t\t\"Aperçu de l’action sélectionnée\",\n\t],\n\t\"vs/platform/actions/browser/menuEntryActionViewItem\": [\n\t\t\"{0} ({1})\",\n\t\t\"{0} ({1})\",\n\t\t\"{0}\\r\\n[{1}] {2}\",\n\t],\n\t\"vs/platform/actions/browser/toolbar\": [\n\t\t\"Masquer\",\n\t\t\"Réinitialiser le menu\",\n\t],\n\t\"vs/platform/actions/common/menuService\": [\n\t\t\"Masquer «{0}»\",\n\t],\n\t\"vs/platform/audioCues/browser/audioCueService\": [\n\t\t\"Erreur sur la ligne\",\n\t\t\"Avertissement sur la ligne\",\n\t\t\"Zone pliée sur la ligne\",\n\t\t\"Point d’arrêt sur ligne\",\n\t\t\"Suggestion inline sur la ligne\",\n\t\t\"Correctif rapide de terminal\",\n\t\t\"Débogueur arrêté sur le point d’arrêt\",\n\t\t\"Aucun indicateur d’inlay sur la ligne\",\n\t\t\"Tâche terminée\",\n\t\t\"Échec de la tâche\",\n\t\t\"Échec de la commande de terminal\",\n\t\t\"Cloche de terminal\",\n\t\t\"Cellule de bloc-notes terminée\",\n\t\t\"Échec de la cellule de bloc-notes\",\n\t\t\"Ligne de diffusion insérée\",\n\t\t\"Ligne de diffusion supprimée\",\n\t\t\"Ligne diff modifiée\",\n\t\t\"Demande de conversation envoyée\",\n\t\t\"Réponse de conversation reçue\",\n\t\t\"Réponse de conversation en attente\",\n\t],\n\t\"vs/platform/configuration/common/configurationRegistry\": [\n\t\t\"Substitutions de configuration du langage par défaut\",\n\t\t\"Configurez les paramètres à remplacer pour le langage {0}.\",\n\t\t\"Configurez les paramètres d\\'éditeur à remplacer pour un langage.\",\n\t\t\"Ce paramètre ne prend pas en charge la configuration par langage.\",\n\t\t\"Configurez les paramètres d\\'éditeur à remplacer pour un langage.\",\n\t\t\"Ce paramètre ne prend pas en charge la configuration par langage.\",\n\t\t\"Impossible d\\'inscrire une propriété vide\",\n\t\t\"Impossible d\\'inscrire \\'{0}\\'. Ceci correspond au modèle de propriété \\'\\\\\\\\[.*\\\\\\\\]$\\' permettant de décrire les paramètres d\\'éditeur spécifiques à un langage. Utilisez la contribution \\'configurationDefaults\\'.\",\n\t\t\"Impossible d\\'inscrire \\'{0}\\'. Cette propriété est déjà inscrite.\",\n\t\t\"Impossible d’inscrire \\'{0}\\'. Le {1} de stratégie associé est déjà inscrit auprès de {2}.\",\n\t],\n\t\"vs/platform/contextkey/browser/contextKeyService\": [\n\t\t\"Commande qui retourne des informations sur les clés de contexte\",\n\t],\n\t\"vs/platform/contextkey/common/contextkey\": [\n\t\t\"Expression de clé de contexte vide\",\n\t\t\"Avez-vous oublié d’écrire une expression ? Vous pouvez également placer \\'false\\' ou \\'true\\' pour toujours donner la valeur false ou true, respectivement.\",\n\t\t\"\\'in\\' après \\'not\\'.\",\n\t\t\"parenthèse fermante \\')\\'\",\n\t\t\"Jeton inattendu\",\n\t\t\"Avez-vous oublié de placer && ou || avant le jeton ?\",\n\t\t\"Fin d’expression inattendue\",\n\t\t\"Avez-vous oublié de placer une clé de contexte ?\",\n\t\t\"Attendu : {0}\\r\\nReçu : \\'{1}\\'.\",\n\t],\n\t\"vs/platform/contextkey/common/contextkeys\": [\n\t\t\"Indique si le système d\\'exploitation est macOS\",\n\t\t\"Indique si le système d\\'exploitation est Linux\",\n\t\t\"Indique si le système d\\'exploitation est Windows\",\n\t\t\"Indique si la plateforme est un navigateur web\",\n\t\t\"Indique si le système d\\'exploitation est macOS sur une plateforme qui n\\'est pas un navigateur\",\n\t\t\"Indique si le système d’exploitation est Linux\",\n\t\t\"Indique si la plateforme est un navigateur web mobile\",\n\t\t\"Type de qualité de VS Code\",\n\t\t\"Indique si le focus clavier se trouve dans une zone d\\'entrée\",\n\t],\n\t\"vs/platform/contextkey/common/scanner\": [\n\t\t\"Voulez-vous dire {0}?\",\n\t\t\"Voulez-vous dire {0} ou {1}?\",\n\t\t\"Voulez-vous dire {0}, {1} ou {2}?\",\n\t\t\"Avez-vous oublié d’ouvrir ou de fermer le devis ?\",\n\t\t\"Avez-vous oublié d’échapper le caractère « / » (barre oblique) ? Placez deux barre obliques inverses avant d’y échapper, par ex., « \\\\\\\\/ ».\",\n\t],\n\t\"vs/platform/history/browser/contextScopedHistoryWidget\": [\n\t\t\"Indique si les suggestions sont visibles\",\n\t],\n\t\"vs/platform/keybinding/common/abstractKeybindingService\": [\n\t\t\"Touche ({0}) utilisée. En attente d\\'une seconde touche...\",\n\t\t\"({0}) a été enfoncé. En attente de la touche suivante de la pression...\",\n\t\t\"La combinaison de touches ({0}, {1}) n’est pas une commande.\",\n\t\t\"La combinaison de touches ({0}, {1}) n’est pas une commande.\",\n\t],\n\t\"vs/platform/list/browser/listService\": [\n\t\t\"Banc d\\'essai\",\n\t\t\"Mappe vers \\'Contrôle\\' dans Windows et Linux, et vers \\'Commande\\' dans macOS.\",\n\t\t\"Mappe vers \\'Alt\\' dans Windows et Linux, et vers \\'Option\\' dans macOS.\",\n\t\t\"Le modificateur à utiliser pour ajouter un élément dans les arbres et listes pour une sélection multiple avec la souris (par exemple dans l’Explorateur, les éditeurs ouverts et la vue scm). Les mouvements de la souris \\'Ouvrir à côté\\' (si pris en charge) s\\'adapteront tels qu’ils n\\'entrent pas en conflit avec le modificateur multiselect.\",\n\t\t\"Contrôle l\\'ouverture des éléments dans les arborescences et les listes à l\\'aide de la souris (si cela est pris en charge). Notez que certaines arborescences et listes peuvent choisir d\\'ignorer ce paramètre, s\\'il est non applicable.\",\n\t\t\"Contrôle si les listes et les arborescences prennent en charge le défilement horizontal dans le banc d\\'essai. Avertissement : L\\'activation de ce paramètre a un impact sur les performances.\",\n\t\t\"Contrôle si les clics dans la barre de défilement page par page.\",\n\t\t\"Contrôle la mise en retrait de l\\'arborescence, en pixels.\",\n\t\t\"Contrôle si l\\'arborescence doit afficher les repères de mise en retrait.\",\n\t\t\"Détermine si les listes et les arborescences ont un défilement fluide.\",\n\t\t\"Un multiplicateur à utiliser sur les `deltaX` et `deltaY` des événements de défilement de roulette de souris.\",\n\t\t\"Multiplicateur de vitesse de défilement quand vous appuyez sur \\'Alt\\'.\",\n\t\t\"Mettez en surbrillance les éléments lors de la recherche. La navigation vers le haut et le bas traverse uniquement les éléments en surbrillance.\",\n\t\t\"Filtrez des éléments lors de la recherche.\",\n\t\t\"Contrôle le mode de recherche par défaut pour les listes et les arborescences dans Workbench.\",\n\t\t\"La navigation au clavier Simple place le focus sur les éléments qui correspondent à l\\'entrée de clavier. La mise en correspondance est effectuée sur les préfixes uniquement.\",\n\t\t\"La navigation de mise en surbrillance au clavier met en surbrillance les éléments qui correspondent à l\\'entrée de clavier. La navigation ultérieure vers le haut ou vers le bas parcourt uniquement les éléments mis en surbrillance.\",\n\t\t\"La navigation au clavier Filtrer filtre et masque tous les éléments qui ne correspondent pas à l\\'entrée de clavier.\",\n\t\t\"Contrôle le style de navigation au clavier pour les listes et les arborescences dans le banc d\\'essai. Les options sont Simple, Mise en surbrillance et Filtrer.\",\n\t\t\"Utilisez \\'workbench.list.defaultFindMode\\' et \\'workbench.list.typeNavigationMode\\' à la place.\",\n\t\t\"Utilisez la correspondance approximative lors de la recherche.\",\n\t\t\"Utilisez des correspondances contiguës lors de la recherche.\",\n\t\t\"Contrôle le type de correspondance utilisé lors de la recherche de listes et d’arborescences dans le banc d’essai.\",\n\t\t\"Contrôle la façon dont les dossiers de l\\'arborescence sont développés quand vous cliquez sur les noms de dossiers. Notez que certaines arborescences et listes peuvent choisir d\\'ignorer ce paramètre, s\\'il est non applicable.\",\n\t\t\"Contrôle le fonctionnement de la navigation de type dans les listes et les arborescences du banc d\\'essai. Quand la valeur est \\'trigger\\', la navigation de type commence une fois que la commande \\'list.triggerTypeNavigation\\' est exécutée.\",\n\t],\n\t\"vs/platform/markers/common/markers\": [\n\t\t\"Erreur\",\n\t\t\"Avertissement\",\n\t\t\"Info\",\n\t],\n\t\"vs/platform/quickinput/browser/commandsQuickAccess\": [\n\t\t\"récemment utilisées\",\n\t\t\"commandes similaires\",\n\t\t\"utilisés le plus souvent\",\n\t\t\"autres commandes\",\n\t\t\"commandes similaires\",\n\t\t\"{0}, {1}\",\n\t\t\"La commande « {0} » a entraîné une erreur\",\n\t],\n\t\"vs/platform/quickinput/browser/helpQuickAccess\": [\n\t\t\"{0}, {1}\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInput\": [\n\t\t\"Précédent\",\n\t\t\"Appuyez sur \\'Entrée\\' pour confirmer votre saisie, ou sur \\'Échap\\' pour l\\'annuler\",\n\t\t\"{0}/{1}\",\n\t\t\"Taper pour affiner les résultats.\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInputController\": [\n\t\t\"Activer/désactiver toutes les cases à cocher\",\n\t\t\"{0} résultats\",\n\t\t\"{0} Sélectionnés\",\n\t\t\"OK\",\n\t\t\"Personnalisé\",\n\t\t\"Précédent ({0})\",\n\t\t\"Retour\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInputList\": [\n\t\t\"Entrée rapide\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInputUtils\": [\n\t\t\"Cliquer pour exécuter la commande \\'{0}\\'\",\n\t],\n\t\"vs/platform/theme/common/colorRegistry\": [\n\t\t\"Couleur de premier plan globale. Cette couleur est utilisée si elle n\\'est pas remplacée par un composant.\",\n\t\t\"Premier plan globale pour les éléments désactivés. Cette couleur est utilisée si elle n\\'est pas remplacée par un composant.\",\n\t\t\"Couleur principale de premier plan pour les messages d\\'erreur. Cette couleur est utilisée uniquement si elle n\\'est pas redéfinie par un composant.\",\n\t\t\"Couleur de premier plan du texte descriptif fournissant des informations supplémentaires, par exemple pour un label.\",\n\t\t\"Couleur par défaut des icônes du banc d\\'essai.\",\n\t\t\"Couleur de bordure globale des éléments ayant le focus. Cette couleur est utilisée si elle n\\'est pas remplacée par un composant.\",\n\t\t\"Bordure supplémentaire autour des éléments pour les séparer des autres et obtenir un meilleur contraste.\",\n\t\t\"Bordure supplémentaire autour des éléments actifs pour les séparer des autres et obtenir un meilleur contraste.\",\n\t\t\"La couleur d\\'arrière-plan des sélections de texte dans le banc d\\'essai (par ex., pour les champs d\\'entrée ou les zones de texte). Notez que cette couleur ne s\\'applique pas aux sélections dans l\\'éditeur et le terminal.\",\n\t\t\"Couleur pour les séparateurs de texte.\",\n\t\t\"Couleur des liens dans le texte.\",\n\t\t\"Couleur de premier plan pour les liens dans le texte lorsqu\\'ils sont cliqués ou survolés.\",\n\t\t\"Couleur des segments de texte préformatés.\",\n\t\t\"Couleur d\\'arrière-plan des citations dans le texte.\",\n\t\t\"Couleur de bordure des citations dans le texte.\",\n\t\t\"Couleur d\\'arrière-plan des blocs de code dans le texte.\",\n\t\t\"Couleur de l\\'ombre des widgets, comme rechercher/remplacer, au sein de l\\'éditeur.\",\n\t\t\"Couleur de bordure des widgets, comme rechercher/remplacer au sein de l\\'éditeur.\",\n\t\t\"Arrière-plan de la zone d\\'entrée.\",\n\t\t\"Premier plan de la zone d\\'entrée.\",\n\t\t\"Bordure de la zone d\\'entrée.\",\n\t\t\"Couleur de la bordure des options activées dans les champs d\\'entrée.\",\n\t\t\"Couleur d\\'arrière-plan des options activées dans les champs d\\'entrée.\",\n\t\t\"Couleur de pointage d’arrière-plan des options dans les champs d’entrée.\",\n\t\t\"Couleur de premier plan des options activées dans les champs d\\'entrée.\",\n\t\t\"Couleur de premier plan de la zone d\\'entrée pour le texte d\\'espace réservé.\",\n\t\t\"Couleur d\\'arrière-plan de la validation d\\'entrée pour la gravité des informations.\",\n\t\t\"Couleur de premier plan de validation de saisie pour la sévérité Information.\",\n\t\t\"Couleur de bordure de la validation d\\'entrée pour la gravité des informations.\",\n\t\t\"Couleur d\\'arrière-plan de la validation d\\'entrée pour la gravité de l\\'avertissement.\",\n\t\t\"Couleur de premier plan de la validation de la saisie pour la sévérité Avertissement.\",\n\t\t\"Couleur de bordure de la validation d\\'entrée pour la gravité de l\\'avertissement.\",\n\t\t\"Couleur d\\'arrière-plan de la validation d\\'entrée pour la gravité de l\\'erreur.\",\n\t\t\"Couleur de premier plan de la validation de saisie pour la sévérité Erreur.\",\n\t\t\"Couleur de bordure de la validation d\\'entrée pour la gravité de l\\'erreur. \",\n\t\t\"Arrière-plan de la liste déroulante.\",\n\t\t\"Arrière-plan de la liste déroulante.\",\n\t\t\"Premier plan de la liste déroulante.\",\n\t\t\"Bordure de la liste déroulante.\",\n\t\t\"Couleur de premier plan du bouton.\",\n\t\t\"Couleur du séparateur de boutons.\",\n\t\t\"Couleur d\\'arrière-plan du bouton.\",\n\t\t\"Couleur d\\'arrière-plan du bouton pendant le pointage.\",\n\t\t\"Couleur de bordure du bouton.\",\n\t\t\"Couleur de premier plan du bouton secondaire.\",\n\t\t\"Couleur d\\'arrière-plan du bouton secondaire.\",\n\t\t\"Couleur d\\'arrière-plan du bouton secondaire au moment du pointage.\",\n\t\t\"Couleur de fond des badges. Les badges sont de courts libellés d\\'information, ex. le nombre de résultats de recherche.\",\n\t\t\"Couleur des badges. Les badges sont de courts libellés d\\'information, ex. le nombre de résultats de recherche.\",\n\t\t\"Ombre de la barre de défilement pour indiquer que la vue défile.\",\n\t\t\"Couleur de fond du curseur de la barre de défilement.\",\n\t\t\"Couleur de fond du curseur de la barre de défilement lors du survol.\",\n\t\t\"Couleur d’arrière-plan de la barre de défilement lorsqu\\'on clique dessus.\",\n\t\t\"Couleur de fond pour la barre de progression qui peut s\\'afficher lors d\\'opérations longues.\",\n\t\t\"Couleur d\\'arrière-plan du texte d\\'erreur dans l\\'éditeur. La couleur ne doit pas être opaque pour ne pas masquer les décorations sous-jacentes.\",\n\t\t\"Couleur de premier plan de la ligne ondulée marquant les erreurs dans l\\'éditeur.\",\n\t\t\"Si cette option est définie, couleur des doubles soulignements pour les erreurs dans l’éditeur.\",\n\t\t\"Couleur d\\'arrière-plan du texte d\\'avertissement dans l\\'éditeur. La couleur ne doit pas être opaque pour ne pas masquer les décorations sous-jacentes.\",\n\t\t\"Couleur de premier plan de la ligne ondulée marquant les avertissements dans l\\'éditeur.\",\n\t\t\"Si cette option est définie, couleur des doubles soulignements pour les avertissements dans l’éditeur.\",\n\t\t\"Couleur d\\'arrière-plan du texte d\\'information dans l\\'éditeur. La couleur ne doit pas être opaque pour ne pas masquer les décorations sous-jacentes.\",\n\t\t\"Couleur de premier plan de la ligne ondulée marquant les informations dans l\\'éditeur.\",\n\t\t\"Si cette option est définie, couleur des doubles soulignements pour les informations dans l’éditeur.\",\n\t\t\"Couleur de premier plan de la ligne ondulée d\\'indication dans l\\'éditeur.\",\n\t\t\"Si cette option est définie, couleur des doubles soulignements pour les conseils dans l’éditeur.\",\n\t\t\"Couleur de bordure des fenêtres coulissantes.\",\n\t\t\"Couleur d\\'arrière-plan de l\\'éditeur.\",\n\t\t\"Couleur de premier plan par défaut de l\\'éditeur.\",\n\t\t\"Couleur d’arrière-plan du défilement pense-bête pour l’éditeur\",\n\t\t\"Faire défiler l’écran sur la couleur d’arrière-plan du pointage pour l’éditeur\",\n\t\t\"Couleur d\\'arrière-plan des gadgets de l\\'éditeur tels que rechercher/remplacer.\",\n\t\t\"Couleur de premier plan des widgets de l\\'éditeur, notamment Rechercher/remplacer.\",\n\t\t\"Couleur de bordure des widgets de l\\'éditeur. La couleur est utilisée uniquement si le widget choisit d\\'avoir une bordure et si la couleur n\\'est pas remplacée par un widget.\",\n\t\t\"Couleur de bordure de la barre de redimensionnement des widgets de l\\'éditeur. La couleur est utilisée uniquement si le widget choisit une bordure de redimensionnement et si la couleur n\\'est pas remplacée par un widget.\",\n\t\t\"Couleur d\\'arrière-plan du sélecteur rapide. Le widget de sélecteur rapide est le conteneur de sélecteurs comme la palette de commandes.\",\n\t\t\"Couleur de premier plan du sélecteur rapide. Le widget de sélecteur rapide est le conteneur de sélecteurs comme la palette de commandes.\",\n\t\t\"Couleur d\\'arrière-plan du titre du sélecteur rapide. Le widget de sélecteur rapide est le conteneur de sélecteurs comme la palette de commandes.\",\n\t\t\"Couleur du sélecteur rapide pour les étiquettes de regroupement.\",\n\t\t\"Couleur du sélecteur rapide pour les bordures de regroupement.\",\n\t\t\"Couleur d’arrière-plan d’étiquette de combinaison de touches. L’étiquette est utilisée pour représenter un raccourci clavier.\",\n\t\t\"Couleur de premier plan d’étiquette de combinaison de touches. L’étiquette est utilisée pour représenter un raccourci clavier.\",\n\t\t\"Couleur de bordure de la combinaison de touches. L’étiquette est utilisée pour représenter un raccourci clavier.\",\n\t\t\"Couleur de bordure du bas d’étiquette de combinaison de touches. L’étiquette est utilisée pour représenter un raccourci clavier.\",\n\t\t\"Couleur de la sélection de l\\'éditeur.\",\n\t\t\"Couleur du texte sélectionné pour le contraste élevé.\",\n\t\t\"Couleur de la sélection dans un éditeur inactif. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Couleur des régions dont le contenu est le même que celui de la sélection. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Couleur de bordure des régions dont le contenu est identique à la sélection.\",\n\t\t\"Couleur du résultat de recherche actif.\",\n\t\t\"Couleur des autres correspondances de recherche. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Couleur de la plage limitant la recherche. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Couleur de bordure du résultat de recherche actif.\",\n\t\t\"Couleur de bordure des autres résultats de recherche.\",\n\t\t\"Couleur de bordure de la plage limitant la recherche. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Couleur des correspondances de requête de l\\'éditeur de recherche.\",\n\t\t\"Couleur de bordure des correspondances de requête de l\\'éditeur de recherche.\",\n\t\t\"Couleur du texte dans le message d’achèvement de la viewlet de recherche.\",\n\t\t\"Surlignage sous le mot sélectionné par pointage. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Couleur d\\'arrière-plan du pointage de l\\'éditeur.\",\n\t\t\"Couleur de premier plan du pointage de l\\'éditeur.\",\n\t\t\"Couleur de bordure du pointage de l\\'éditeur.\",\n\t\t\"Couleur d\\'arrière-plan de la barre d\\'état du pointage de l\\'éditeur.\",\n\t\t\"Couleur des liens actifs.\",\n\t\t\"Couleur de premier plan des indicateurs inline\",\n\t\t\"Couleur d\\'arrière-plan des indicateurs inline\",\n\t\t\"Couleur de premier plan des indicateurs inline pour les types\",\n\t\t\"Couleur d\\'arrière-plan des indicateurs inline pour les types\",\n\t\t\"Couleur de premier plan des indicateurs inline pour les paramètres\",\n\t\t\"Couleur d\\'arrière-plan des indicateurs inline pour les paramètres\",\n\t\t\"Couleur utilisée pour l\\'icône d\\'ampoule suggérant des actions.\",\n\t\t\"Couleur utilisée pour l\\'icône d\\'ampoule suggérant des actions de correction automatique.\",\n\t\t\"Couleur d\\'arrière-plan du texte inséré. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Couleur d\\'arrière-plan du texte supprimé. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Couleur d\\'arrière-plan des lignes insérées. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Couleur d\\'arrière-plan des lignes supprimées. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Couleur d’arrière-plan de la marge où les lignes ont été insérées\",\n\t\t\"Couleur d’arrière-plan de la marge où les lignes ont été supprimées\",\n\t\t\"Premier plan de la règle de vue d’ensemble des différences pour le contenu inséré\",\n\t\t\"Premier plan de la règle de vue d’ensemble des différences pour le contenu supprimé\",\n\t\t\"Couleur de contour du texte inséré.\",\n\t\t\"Couleur de contour du texte supprimé.\",\n\t\t\"Couleur de bordure entre les deux éditeurs de texte.\",\n\t\t\"Couleur du remplissage diagonal de l\\'éditeur de différences. Le remplissage diagonal est utilisé dans les vues de différences côte à côte.\",\n\t\t\"Couleur d’arrière-plan des blocs inchangés dans l’éditeur de différences.\",\n\t\t\"Couleur de premier plan des blocs inchangés dans l’éditeur de différences.\",\n\t\t\"Couleur d’arrière-plan du code inchangé dans l’éditeur de différences.\",\n\t\t\"Couleur d\\'arrière-plan de la liste/l\\'arborescence pour l\\'élément ayant le focus quand la liste/l\\'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l\\'est pas quand elle est inactive.\",\n\t\t\"Couleur de premier plan de la liste/l\\'arborescence pour l\\'élément ayant le focus quand la liste/l\\'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l\\'est pas quand elle est inactive.\",\n\t\t\"Couleur de contour de la liste/l\\'arborescence pour l\\'élément ayant le focus quand la liste/l\\'arborescence est active. Une liste/arborescence active a le focus clavier, contrairement à une liste/arborescence inactive.\",\n\t\t\"Couleur de contour de liste/arborescence pour l’élément ciblé lorsque la liste/l’arborescence est active et sélectionnée. Une liste/arborescence active dispose d’un focus clavier, ce qui n’est pas le cas d’une arborescence inactive.\",\n\t\t\"Couleur d\\'arrière-plan de la liste/l\\'arborescence de l\\'élément sélectionné quand la liste/l\\'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l\\'est pas quand elle est inactive.\",\n\t\t\"Couleur de premier plan de la liste/l\\'arborescence pour l\\'élément sélectionné quand la liste/l\\'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l\\'est pas quand elle est inactive.\",\n\t\t\"Couleur de premier plan de l’icône Liste/l\\'arborescence pour l\\'élément sélectionné quand la liste/l\\'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l\\'est pas quand elle est inactive.\",\n\t\t\"Couleur d\\'arrière-plan de la liste/l\\'arborescence pour l\\'élément sélectionné quand la liste/l\\'arborescence est inactive. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l\\'est pas quand elle est inactive.\",\n\t\t\"Couleur de premier plan de la liste/l\\'arborescence pour l\\'élément sélectionné quand la liste/l\\'arborescence est inactive. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l\\'est pas quand elle est inactive.\",\n\t\t\"Couleur de premier plan de l’icône Liste/l\\'arborescence pour l\\'élément sélectionné quand la liste/l\\'arborescence est inactive. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l\\'est pas quand elle est inactive.\",\n\t\t\"Couleur d\\'arrière-plan de la liste/l\\'arborescence pour l\\'élément ayant le focus quand la liste/l\\'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier (elle ne l\\'est pas quand elle est inactive).\",\n\t\t\"Couleur de contour de la liste/l\\'arborescence pour l\\'élément ayant le focus quand la liste/l\\'arborescence est inactive. Une liste/arborescence active a le focus clavier, contrairement à une liste/arborescence inactive.\",\n\t\t\"Arrière-plan de la liste/l\\'arborescence pendant le pointage sur des éléments avec la souris.\",\n\t\t\"Premier plan de la liste/l\\'arborescence pendant le pointage sur des éléments avec la souris.\",\n\t\t\"Arrière-plan de l\\'opération de glisser-déplacer dans une liste/arborescence pendant le déplacement d\\'éléments avec la souris.\",\n\t\t\"Couleur de premier plan dans la liste/l\\'arborescence pour la surbrillance des correspondances pendant la recherche dans une liste/arborescence.\",\n\t\t\"Couleur de premier plan de la liste ou l’arborescence pour la surbrillance des correspondances sur les éléments ayant le focus pendant la recherche dans une liste/arborescence.\",\n\t\t\"Couleur de premier plan de liste/arbre pour les éléments non valides, par exemple une racine non résolue dans l’Explorateur.\",\n\t\t\"Couleur de premier plan des éléments de la liste contenant des erreurs.\",\n\t\t\"Couleur de premier plan des éléments de liste contenant des avertissements.\",\n\t\t\"Couleur d\\'arrière-plan du widget de filtre de type dans les listes et les arborescences.\",\n\t\t\"Couleur de contour du widget de filtre de type dans les listes et les arborescences.\",\n\t\t\"Couleur de contour du widget de filtre de type dans les listes et les arborescences, en l\\'absence de correspondance.\",\n\t\t\"Appliquez une ombre à la couleur du widget filtre de type dans les listes et les arborescences.\",\n\t\t\"Couleur d\\'arrière-plan de la correspondance filtrée.\",\n\t\t\"Couleur de bordure de la correspondance filtrée.\",\n\t\t\"Couleur de trait de l\\'arborescence pour les repères de mise en retrait.\",\n\t\t\"Couleur de trait d’arborescence pour les repères de mise en retrait qui ne sont pas actifs.\",\n\t\t\"Couleur de la bordure du tableau entre les colonnes.\",\n\t\t\"Couleur d\\'arrière-plan pour les lignes de tableau impaires.\",\n\t\t\"Couleur de premier plan de la liste/l\\'arborescence des éléments atténués.\",\n\t\t\"Couleur de fond du widget Case à cocher.\",\n\t\t\"Couleur d’arrière-plan du widget de case à cocher lorsque l’élément dans lequel il se trouve est sélectionné.\",\n\t\t\"Couleur de premier plan du widget Case à cocher.\",\n\t\t\"Couleur de bordure du widget Case à cocher.\",\n\t\t\"Couleur de bordure du widget de case à cocher lorsque l’élément dans lequel il se trouve est sélectionné.\",\n\t\t\"Utilisez quickInputList.focusBackground à la place\",\n\t\t\"Couleur de premier plan du sélecteur rapide pour l’élément ayant le focus.\",\n\t\t\"Couleur de premier plan de l’icône du sélecteur rapide pour l’élément ayant le focus.\",\n\t\t\"Couleur d\\'arrière-plan du sélecteur rapide pour l\\'élément ayant le focus.\",\n\t\t\"Couleur de bordure des menus.\",\n\t\t\"Couleur de premier plan des éléments de menu.\",\n\t\t\"Couleur d\\'arrière-plan des éléments de menu.\",\n\t\t\"Couleur de premier plan de l\\'élément de menu sélectionné dans les menus.\",\n\t\t\"Couleur d\\'arrière-plan de l\\'élément de menu sélectionné dans les menus.\",\n\t\t\"Couleur de bordure de l\\'élément de menu sélectionné dans les menus.\",\n\t\t\"Couleur d\\'un élément de menu séparateur dans les menus.\",\n\t\t\"Arrière-plan de la barre d’outils lors du survol des actions à l’aide de la souris\",\n\t\t\"Contour de la barre d’outils lors du survol des actions à l’aide de la souris\",\n\t\t\"Arrière-plan de la barre d’outils quand la souris est maintenue sur des actions\",\n\t\t\"Couleur d’arrière-plan de mise en surbrillance d’un extrait tabstop.\",\n\t\t\"Couleur de bordure de mise en surbrillance d’un extrait tabstop.\",\n\t\t\"Couleur d’arrière-plan de mise en surbrillance du tabstop final d’un extrait.\",\n\t\t\"Mettez en surbrillance la couleur de bordure du dernier taquet de tabulation d\\'un extrait de code.\",\n\t\t\"Couleur des éléments de navigation avec le focus.\",\n\t\t\"Couleur de fond des éléments de navigation.\",\n\t\t\"Couleur des éléments de navigation avec le focus.\",\n\t\t\"Couleur des éléments de navigation sélectionnés.\",\n\t\t\"Couleur de fond du sélecteur d’élément de navigation.\",\n\t\t\"Arrière-plan d\\'en-tête actuel dans les conflits de fusion inline. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Arrière-plan de contenu actuel dans les conflits de fusion inline. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Arrière-plan d\\'en-tête entrant dans les conflits de fusion inline. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Arrière-plan de contenu entrant dans les conflits de fusion inline. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Arrière-plan d\\'en-tête de l\\'ancêtre commun dans les conflits de fusion inline. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Arrière-plan de contenu de l\\'ancêtre commun dans les conflits de fusion inline. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Couleur de bordure des en-têtes et du séparateur dans les conflits de fusion inline.\",\n\t\t\"Premier plan de la règle d\\'aperçu actuelle pour les conflits de fusion inline.\",\n\t\t\"Premier plan de la règle d\\'aperçu entrante pour les conflits de fusion inline.\",\n\t\t\"Arrière-plan de la règle d\\'aperçu de l\\'ancêtre commun dans les conflits de fusion inline.\",\n\t\t\"Couleur de marqueur de la règle d\\'aperçu pour rechercher les correspondances. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Couleur de marqueur de la règle d\\'aperçu pour la mise en surbrillance des sélections. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.\",\n\t\t\"Couleur de marqueur de la minimap pour les correspondances.\",\n\t\t\"Couleur de marqueur minimap pour les sélections répétées de l’éditeur.\",\n\t\t\"Couleur de marqueur du minimap pour la sélection de l\\'éditeur.\",\n\t\t\"Couleur de marqueur de minimap pour les informations.\",\n\t\t\"Couleur de marqueur de minimap pour les avertissements.\",\n\t\t\"Couleur de marqueur de minimap pour les erreurs.\",\n\t\t\"Couleur d\\'arrière-plan du minimap.\",\n\t\t\"Opacité des éléments de premier plan rendus dans la minimap. Par exemple, « #000000c0 » affiche les éléments avec une opacité de 75 %.\",\n\t\t\"Couleur d\\'arrière-plan du curseur de minimap.\",\n\t\t\"Couleur d\\'arrière-plan du curseur de minimap pendant le survol.\",\n\t\t\"Couleur d\\'arrière-plan du curseur de minimap pendant un clic.\",\n\t\t\"Couleur utilisée pour l\\'icône d\\'erreur des problèmes.\",\n\t\t\"Couleur utilisée pour l\\'icône d\\'avertissement des problèmes.\",\n\t\t\"Couleur utilisée pour l\\'icône d\\'informations des problèmes.\",\n\t\t\"Couleur de premier plan utilisée dans les graphiques.\",\n\t\t\"Couleur utilisée pour les lignes horizontales dans les graphiques.\",\n\t\t\"Couleur rouge utilisée dans les visualisations de graphiques.\",\n\t\t\"Couleur bleue utilisée dans les visualisations de graphiques.\",\n\t\t\"Couleur jaune utilisée dans les visualisations de graphiques.\",\n\t\t\"Couleur orange utilisée dans les visualisations de graphiques.\",\n\t\t\"Couleur verte utilisée dans les visualisations de graphiques.\",\n\t\t\"Couleur violette utilisée dans les visualisations de graphiques.\",\n\t],\n\t\"vs/platform/theme/common/iconRegistry\": [\n\t\t\"ID de la police à utiliser. Si aucune valeur n\\'est définie, la police définie en premier est utilisée.\",\n\t\t\"Caractère de police associé à la définition d\\'icône.\",\n\t\t\"Icône de l\\'action de fermeture dans les widgets.\",\n\t\t\"Icône d\\'accès à l\\'emplacement précédent de l\\'éditeur.\",\n\t\t\"Icône d\\'accès à l\\'emplacement suivant de l\\'éditeur.\",\n\t],\n\t\"vs/platform/undoRedo/common/undoRedoService\": [\n\t\t\"Les fichiers suivants ont été fermés et modifiés sur le disque : {0}.\",\n\t\t\"Les fichiers suivants ont été modifiés de manière incompatible : {0}.\",\n\t\t\"Impossible d\\'annuler \\'{0}\\' dans tous les fichiers. {1}\",\n\t\t\"Impossible d\\'annuler \\'{0}\\' dans tous les fichiers. {1}\",\n\t\t\"Impossible d\\'annuler \\'{0}\\' dans tous les fichiers, car des modifications ont été apportées à {1}\",\n\t\t\"Impossible d\\'annuler \\'{0}\\' dans tous les fichiers, car une opération d\\'annulation ou de rétablissement est déjà en cours d\\'exécution sur {1}\",\n\t\t\"Impossible d\\'annuler \\'{0}\\' dans tous les fichiers, car une opération d\\'annulation ou de rétablissement s\\'est produite dans l\\'intervalle\",\n\t\t\"Souhaitez-vous annuler \\'{0}\\' dans tous les fichiers ?\",\n\t\t\"&&Annuler dans {0} fichiers\",\n\t\t\"Annuler ce &&fichier\",\n\t\t\"Impossible d\\'annuler \\'{0}\\', car une opération d\\'annulation ou de rétablissement est déjà en cours d\\'exécution.\",\n\t\t\"Voulez-vous annuler \\'{0}\\' ?\",\n\t\t\"&&Oui\",\n\t\t\"Non\",\n\t\t\"Impossible de répéter \\'{0}\\' dans tous les fichiers. {1}\",\n\t\t\"Impossible de répéter \\'{0}\\' dans tous les fichiers. {1}\",\n\t\t\"Impossible de répéter \\'{0}\\' dans tous les fichiers, car des modifications ont été apportées à {1}\",\n\t\t\"Impossible de rétablir \\'{0}\\' dans tous les fichiers, car une opération d\\'annulation ou de rétablissement est déjà en cours d\\'exécution pour {1}\",\n\t\t\"Impossible de rétablir \\'{0}\\' dans tous les fichiers, car une opération d\\'annulation ou de rétablissement s\\'est produite dans l\\'intervalle\",\n\t\t\"Impossible de rétablir \\'{0}\\', car une opération d\\'annulation ou de rétablissement est déjà en cours d\\'exécution.\",\n\t],\n\t\"vs/platform/workspace/common/workspace\": [\n\t\t\"Espace de travail de code\",\n\t]\n});"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DA<PERSON>,OAAO,+BAAgC,CACtC,+CAAgD,CAC/C,WACD,EACA,yCAA0C,CACzC,WACD,EACA,gDAAiD,CAChD,qBACA,aACA,yCACD,EACA,4CAA6C,CAC5C,YACA,uBACD,EACA,uCAAwC,CACvC,4DACA,mLACD,EACA,8CAA+C,CAC9C,eACD,EACA,uCAAwC,CACvC,kBACA,yBACA,gBACA,yBACA,sBACD,EACA,qDAAsD,CACrD,gBACD,EACA,+CAAgD,CAC/C,sBACD,EACA,qCAAsC,CACrC,mBACD,EACA,uCAAwC,CACvC,UACA,+BACA,oBACA,kCACA,kCACA,SACA,gCACD,EACA,yBAA0B,CACzB,QACD,EACA,8BAA+B,CAC9B,WACA,6CACA,kGACA,kGACA,gCACA,iGACD,EACA,kCAAmC,CAClC,OACA,MACA,MACA,UACA,OACA,MACA,MACA,QACA,cACA,MACA,SACA,WACA,cACA,MACA,MACA,UACA,cACA,MACA,MACA,OACD,EACA,0BAA2B,CAC1B,GACD,EACA,+CAAgD,CAC/C,aACA,8DACA,gFACA,yRACA,yOACD,EACA,iCAAkC,CACjC,yFACA,yFACA,mCACD,EACA,qCAAsC,CACrC,YACA,UACA,gBACA,cACA,yBACA,sBACD,EACA,4CAA6C,CAC5C,oSACA,2CACD,EACA,2DAA4D,CAC3D,qEACA,oEACA,oEACA,SACA,kFACA,0BACA,wBACA,4BACA,4FACA,OACA,6BACA,sDACA,iCACA,2BACD,EACA,6CAA8C,CAC7C,4EACA,4FACD,EACA,kDAAmD,CAClD,+FACA,iGACA,0CACD,EACA,8DAA+D,CAC9D,gEACA,0EACA,8EACA,wDACA,4CACA,+BACA,wBACA,qCACA,iDACA,+CACA,8BACA,4CACA,wCACA,8CACD,EACA,wDAAyD,CACxD,oEACD,EACA,kEAAmE,CAClE,oCACA,0EACA,gBACA,2EACA,yBACA,gCACD,EACA,kEAAmE,CAClE,kCACA,+BACA,iCACA,8BACA,qCACA,oCACA,yBACD,EACA,uDAAwD,CACvD,kEACA,4EACA,2CACA,oDACD,EACA,oDAAqD,CACpD,aACA,+JACA,8OACA,yJACA,uIACA,gEACA,kIACA,wHACA,yDACA,0EACA,uDACA,+GACA,0EACA,gFACA,0HACA,kGACA,yIACA,sGACA,uGACA,mHACA,2SACA,kFACA,sEACA,sEACA,wJACA,sEACA,sEACA,oIACA,sHACA,mHACA,qGACA,uHACA,uJACA,sHACA,2HACA,gDACA,gEACA,yGACA,qEACA,yDACA,sDACA,sFACA,kFACA,gGACA,mHACA,+GACA,oKACD,EACA,wCAAyC,CACxC,6FACA,oEACA,wEACA,kJACA,mEACA,sKACA,gFACA,+FACA,wFACA,gJACA,wFACA,mIACA,uFACA,sEACA,sHACA,0FACA,oHACA,uOACA,yIACA,2NACA,gLACA,0QACA,8MACA,kMACA,2QACA,6CACA,iHACA,uIACA,4DACA,8DACA,gGACA,4MACA,wHACA,gIACA,yHACA,8HACA,4HACA,yHACA,iIACA,0HACA,4HACA,2HACA,6CACA,sFACA,+FACA,gIACA,4FACA,+PACA,kMACA,mMACA,sEACA,sGACA,sEACA,uRACA,+GACA,uDACA,gDACA,wGACA,4GACA,sDACA,uPACA,uJACA,oFACA;AAAA;AAAA;AAAA,iGACA,6CACA,4DACA,0FACA,0HACA,yHACA,oCACA,uDACA,oDACA,yEACA,yFACA,wFACA,oHACA,0GACA,yJACA,mIACA,wEACA,0EACA,iDACA,uDACA,yDACA,gFACA,oeACA,mDACA,6DACA,4GACA,iEACA,mDACA,4FACA,0CACA,mNACA,8FACA,4DACA,+DACA,uEACA,gGACA,8DACA,iEACA,yEACA,kDACA,oDACA,kHACA,wOACA,gIACA,wOACA,qIACA,4IACA,iFACA,iHACA,+GACA,oHACA,0GACA,2EACA,qPACA,sJACA,+FACA,oDACA,0FACA,oDACA,4EACA,2FACA,iFACA,gEACA,wFACA,4FACA,gFACA,iDACA,mHACA,+DACA,kGACA,4EACA,0EACA,kLACA,gHACA,yFACA,kLACA,oGACA,qGACA,4IACA,gHACA,oYACA,8EACA,+EACA,qFACA,0GACA,0IACA,6GACA,2KACA,sEACA,wEACA,2EACA,6FACA,iTACA,qEACA,wEACA,qEACA,sEACA,yEACA,sEACA,wEACA,qEACA,wEACA,oEACA,qEACA,wEACA,oEACA,0EACA,uEACA,oEACA,qEACA,oEACA,yEACA,2EACA,sEACA,6EACA,uEACA,2EACA,4EACA,mGACA,mIACA,kFACA,iFACA,6EACA,8EACA,2DACA,+LACA,yLACA,sHACA,mJACA,8EACA,qLACA,qGACA,8IACA,qPACA,sGACA,+NACA,gXACA,0BACA,6FACA,6GACA,4GACA,8IACA,2GACA,+FACA,8HACA,wHACA,wHACA,2GACA,oFACA,yGACA,wGACA,2IACA,2DACA,yDACA,0GACA,mLACA,mPACA,6JACA,6GACA,oDACA,oDACA,uIACA,yLACA,gDACA,mDACA,iIACA,gIACA,0HACA,6GACA,gHACA,6IACA,+IACA,yFACA,yFACA,qDACA,yDACA,2IACA,4DACA,2EACA,mCACA,gOACA,yGACA,qDACA,iFACA,qFACA,wGACA,0DACA,8EACA,0CACA,+FACA,2EACA,6DACA,oIACA,8DACA,8DACA,+EACA,4FACA,kNACA,wGACA,qCACA,qMACA,qGACA,mIACA,+FACA,kDACA,wNACA,wFACA,sDACA,4HACA,yJACA,yDACA,iFACA,uEACA,qUACA,iDACA,2CACA,uGACA,sGACA,sGACA,kGACA,2DACA,mEACA,kHACA,2GACA,sGACA,qEACA,oEACA,mFACA,2FACA,4DACA,6GACA,yHACA,kFACA,yEACA,gFACA,2FACA,sEACA,4HACA,iFACA,4NACA,gFACA,+GACA,gDACA,0FACA,8FACA,wEACA,8CACA,4DACA,gFACA,iFACA,2EACA,yDACA,yHACA,oEACA,6JACA,sHACA,gJACA,mIACA,sDACA,mMACA,mKACA,yHACA,oGACA,mDACA,8JACA,2CACA,gFACA,8DACA,kFACA,+FACA,mFACA,uDACA,kLACA,kHACA,0HACA,gEACA,yGACA,iEACA,sFACA,8DACA,iIACA,yJACA,gHACD,EACA,4CAA6C,CAC5C,gGACA,0FACA,iNACA,mFACA,gOACA,mFACA,sCACA,yIACA,gDACA,mDACA,qDACA,gHACA,yDACA,4HACA,yDACA,yDACA,yDACA,yDACA,yDACA,yDACA,8DACA,8DACA,8DACA,8DACA,8DACA,8DACA,2DACA,+FACA,2DACA,gHACA,wCACA,wCACA,4DACA,qDACA,oDACA,6FACA,gIACA,gFACA,2TACA,iEACA,sEACA,+EACA,gLACA,mEACA,0EACA,wEACA,qHACA,qHACA,qHACA,qHACA,qHACA,qHACA,yDACA,uJACA,uJACA,uJACA,uJACA,uJACA,uJACA,qJACA,qJACA,qJACA,qJACA,qJACA,qJACA,uFACA,kFACD,EACA,qCAAsC,CACrC,uEACA,+HACA,mGACA,oDACA,qEACA,wFACA,yFACA,wDACA,6GACA,oDACA,8DACA,oDACA,+EACA,qDACA,2DACA,0DACA,2DACA,8DACA,kEACA,6FACA,4CACA,6EACA,6DACA,mEACA,6DACA,8DACA,+DACA,qEACA,uDACA,sFACA,0EACA,8DACA,wDACA,qEACA,gEACA,+EACA,+FACA,uFACA,sGACD,EACA,6BAA8B,CAC7B,UACA,aACA,SACA,YACA,eACA,oBACA,6BACA,kBACA,QACA,UACA,WACA,YACA,SACA,aACA,SACA,iBACA,OACA,SACA,QACA,eACA,UACA,kBACA,YACA,SACA,uBACA,WACA,WACD,EACA,2CAA4C,CAC3C,YACD,EACA,mCAAoC,CACnC,iBACD,EACA,qCAAsC,CACrC,2CACA,sCACA,mDACA,uBACA,0CACA,2BACA,4CACA,0BACA,+DACA,+DACA,qCACD,EACA,+CAAgD,CAC/C,yBACA,sBACD,EACA,sDAAuD,CACtD,wBACA,+BACA,qCACA,oCACA,wCACA,iCACD,EACA,4DAA6D,CAC5D,qFACA,uBACA,mCACA,yBACA,yBACD,EACA,4DAA6D,CAC5D,qDACA,oDACD,EACA,sDAAuD,CACtD,wBACD,EACA,gDAAiD,CAChD,WACA,SACA,SACA,SACA,WACA,SACA,SACA,SACA,qBACA,qBACA,WACA,WACA,WACA,WACA,SACA,SACA,SACA,sCACD,EACA,kDAAmD,CAClD,2EACD,EACA,0DAA2D,CAC1D,0CACA,kEACA,iEACA,6EACA,qDACA,yFACA,uBACA,mCACA,sEACA,8CACA,iDACA,mCACA,cACA,6DACA,+CACA,kDACA,oCACA,sBACA,2DACA,6CACA,gDACA,iCACA,6BACA,iDACA,gBACA,yCACA,8BACA,wCACD,EACA,+DAAgE,CAC/D,uGACA,2IACD,EACA,4DAA6D,CAC5D,8DACA,0BACA,8CACD,EACA,sDAAuD,CACtD,yBACA,mBACA,WACA,SACA,iBACA,cACA,cACA,eACD,EACA,uDAAwD,CACvD,gFACA,qCACA,8BACD,EACA,wDAAyD,CACxD,wDACA,8BACD,EACA,0DAA2D,CAC1D,sFACA,kDACD,EACA,qEAAsE,CACrE,uEACA,yEACA,sCACA,kEACD,EACA,4CAA6C,CAC5C,gDACA,6CACA,kCACA,oCACA,+CACA,2CACD,EACA,oDAAqD,CACpD,UACA,6BACA,mBACA,gBACA,cACA,UACA,UACA,oBACA,WACA,6CACD,EACA,kDAAmD,CAClD,wBACA,yBACD,EACA,kEAAmE,CAClE,wBACA,8HACD,EACA,gEAAiE,CAChE,yCACA,qCACA,kEACA,uCACA,2CACD,EACA,6DAA8D,CAC7D,gBACA,2BACA,qBACA,oBACA,yCACA,uCACA,kDACA,8CACD,EACA,uEAAwE,CACvE,mHACD,EACA,qEAAsE,CACrE,qDACA,yCACA,qEACD,EACA,+DAAgE,CAC/D,sHACD,EACA,gDAAiD,CAChD,iFACA,aACA,eACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA,6BACA,kCACA,qBACA,6BACA,uCACA,4DACA,yFACA,mDACA,mDACA,wBACA,gCACA,YACA,aACD,EACA,4CAA6C,CAC5C,oGACA,2FACA,iGACA,8EACA,mFACA,+FACA,uFACA,uBACA,aACA,aACA,kCACA,0BACA,kCACA,SACA,YACA,YACA,YACA,iBACA,wCACA,qJACA,cACA,oBACA,mBACA,2BACA,oCACA,2BACA,kNACD,EACA,4CAA6C,CAC5C,aACA,wCACA,QACA,kCACA,mCACA,wCACA,gCACA,mCACA,yDACA,8DACA,aACA,kBACA,0BACA,sDACA,8CACA,8DACA,2CACA,sBACD,EACA,uDAAwD,CACvD,6IACA,kEACA,kFACA,4EACA,kGACA,uGACD,EACA,8CAA+C,CAC9C,6DACA,mEACA,wEACD,EACA,0CAA2C,CAC1C,6DACA,8DACA,uEACA,uEACD,EACA,iDAAkD,CACjD,8BACA,iCACD,EACA,gDAAiD,CAChD,6DACA,sCACA,qEACA,6CACA,2EACA,wBACA,mFACA,+BACD,EACA,sDAAuD,CACtD,SACA,gBACA,OACA,UACA,iBACA,iCACA,gCACA,+EACA,0GACA,sFACA,0GACA,kFACA,6GACA,6EACD,EACA,oDAAqD,CACpD,YACA,iBACA,uCACA,4BACA,6BACA,+BACA,4CACA,gCACA,kBACA,mCACA,wBACA,oCACA,gCACA,mCACA,wBACA,iCACA,yBACA,+CACA,oCACA,qCACA,6CACA,wCACA,qBACA,2CACA,gCACA,mCACA,qCACA,mCACA,oCACA,yBACA,iCACA,mCACA,mBACA,iCACA,mBACA,uBACA,eACA,6CACA,kBACD,EACA,qEAAsE,CACrE,8CACD,EACA,iEAAkE,CACjE,4JACA,yBACA,WACD,EACA,2DAA4D,CAC3D,uBACA,sBACA,kBACD,EACA,6DAA8D,CAC7D,2BACA,oBACA,kBACD,EACA,uDAAwD,CACvD,iDACA,oDACA,yCACA,4CACA,4BACA,wBACA,2BACA,mCACD,EACA,wDAAyD,CACxD,gHACA,2CACA,qBACD,EACA,wCAAyC,CACxC,gCACA,uDACA,4CACA,2CACA,8CACA,8CACA,kCACA,iCACA,qCACA,qBACD,EACA,2DAA4D,CAC3D,yBACA,6IACA,6JACD,EACA,yDAA0D,CACzD,sBACA,gDACA,qCACA,gDACA,sBACD,EACA,0DAA2D,CAC1D,2CACA,kCACD,EACA,oDAAqD,CACpD,oCACA,wCACA,uCACA,qCACA,mCACA,mEACA,yCACA,qCACA,uDACA,wDACA,iCACA,8CACD,EACA,uDAAwD,CACvD,iCACA,aACA,cACA,gBACA,aACA,yFACA,sCACA,yBACD,EACA,uDAAwD,CACvD,2CACA,mDACA,uCACA,kDACA,kBACA,4DACA,oBACA,gCACA,WACA,gCACA,0CACD,EACA,+DAAgE,CAC/D,cACD,EACA,0EAA2E,CAC1E,iDACA,iEACA,gIACA,sFACD,EACA,0EAA2E,CAC1E,uDACD,EACA,2EAA4E,CAC3E,4DACA,mEACA,YACA,kBACA,SACD,EACA,wDAAyD,CACxD,wCACD,EACA,4DAA6D,CAC5D,0BACA,4BACA,yBACA,2BACA,4BACA,8BACA,oCACA,sCACA,mCACA,qCACA,0CACA,+CACA,qCACA,oCACA,qBACA,6BACA,8CACA,iCACA,4BACA,wCACA,wCACA,qBACA,iDACA,2BACA,2BACA,wDACA,4BACA,6BACA,2BACD,EACA,wDAAyD,CACxD,sCACA,+EACD,EACA,wCAAyC,CACxC,8EACA,kEACA,0BACA,iBACA,aACA,cACA,gBACA,aACA,8BACA,gBACD,EACA,sDAAuD,CACtD,mDACD,EACA,oDAAqD,CACpD,6BACA,+BACA,+BACA,iCACA,gCACA,kCACA,8CACA,gDACA,8BACA,+BACA,uEACA,kCACA,+EACA,0CACA,uFACA,+FACA,6EACA,2CACA,kCACA,+BACA,+BACA,uCACA,sCACD,EACA,0DAA2D,CAC1D,uCACD,EACA,gEAAiE,CAChE,4DACA,mEACA,eACA,+FACD,EACA,8CAA+C,CAC9C,4EACA,SACA,4EACA,+CACA,oEACA,sEACA,qFACA,0GACA,4GACA,yHACA,uHACA,qEACA,mFACA,wHACA,kHACA,qGACA,oGACD,EACA,4DAA6D,CAC5D,wEACA,iDACA,gCACA,sHACA,gGACD,EACA,8DAA+D,CAC9D,2GACA,0EACA,2CACA,6BACA,2BACA,gBACA,iBACA,yBACA,oBACA,kBACA,sBACA,kBACA,gBACA,gBACA,yBACA,sBACA,mBACA,wBACA,iBACA,8BACA,gBACA,yBACA,2BACA,oCACA,mBACA,iBACA,iBACA,gBACA,oBACA,eACA,gBACA,eACA,kBACD,EACA,yDAA0D,CACzD,gEACA,gEACD,EACA,0CAA2C,CAC1C,qBACA,4FACA,8BACA,kCACA,oDACA,sDACA,qDACA,sBACA,oGACD,EACA,oDAAqD,CACpD,4DACA,oFACA,mDACD,EACA,oDAAqD,CACpD,6BACA,kCACA,6BACA,8BACD,EACA,uDAAwD,CACvD,2DACA,sEACA,8EACA,wDACD,EACA,qDAAsD,CACrD,WACA,QACA,QACA,WACA,QACA,WACA,SACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,UACA,aACA,OACA,QACA,MACA,OACA,UACA,UACA,YACA,UACA,WACA,cACA,MACA,SACA,MACA,MACA,MACA,OACA,MACA,SACA,OACA,MACA,MACA,QACD,EACA,6DAA8D,CAC7D,uDACA,yDACA,8BACA,gCACA,8CACA,gDACA,sEACA,8EACA,gEACA,8BACD,EACA,4CAA6C,CAC5C,uCACA,0DACA,qDACA,uHACA,kFACA,qFACA,sFACA,sGACD,EACA,sDAAuD,CACtD,kFACA,2BACA,aACA,aACA,YACA,YACA,aACA,iBACA,gBACA,oDACD,EACA,kDAAmD,CAClD,qDACA,8CACA,mDACA,+FACA,kHACA,4FACA,+EACA,0GACA,6DACA,yBACA,sBACA,cACA,eACA,UACA,WACA,yBACD,EACA,yDAA0D,CACzD,SACA,wBACD,EACA,0DAA2D,CAC1D,uFACA,eACD,EACA,wDAAyD,CACxD,WACD,EACA,oDAAqD,CACpD,8IACA,+IACA,6IACA,8IACA,qJACA,mJACA,uJACA,iKACA,qJACA,4IACA,8IACA,8IACA,+IACA,+IACA,6IACA,iJACA,iJACA,6IACA,oJACA,wIACA,6IACA,2IACA,kJACA,8IACA,sJACA,sJACA,qJACA,gJACA,6IACA,4IACA,2JACA,8IACA,8IACD,EACA,kEAAmE,CAClE,iFACA,+HACA,2DACD,EACA,sDAAuD,CACtD,8CACD,EACA,kEAAmE,CAClE,uFACA,6EACA,kEACA,qEACA,sHACA,iHACA,qCACA,oEACA,4BACA,8DACA,gFACA,4DACA,8EACA,mDACA,kEACA,wCACA,qEACA,kDACA,4EACA,wCACA,mEACA,yCACA,0EACA,8CACD,EACA,0EAA2E,CAC1E,wCACA,wDACA,wUACA,wDACA,SACD,EACA,iEAAkE,CACjE,+LACA,sMACA,mLACA,uGACA,iHACA,sEACA,kLACA,4MACA,kMACD,EACA,4DAA6D,CAC5D,yDACA,gEACA,iDACD,EACA,0DAA2D,CAC1D,kBACD,EACA,mDAAoD,CACnD,WACA,OACA,OACA,UACA,oBACA,gBACD,EACA,8CAA+C,CAC9C,qDACA,qBACA,qCACA,sBACD,EACA,gDAAiD,CAChD,2FACA,4DACA,kCACA,iDACA,yCACA,4CACA,+CACD,EACA,sDAAuD,CACtD,YACA,YACA;AAAA,UACD,EACA,sCAAuC,CACtC,UACA,0BACD,EACA,yCAA0C,CACzC,qBACD,EACA,gDAAiD,CAChD,sBACA,6BACA,6BACA,kCACA,iCACA,+BACA,yDACA,6CACA,uBACA,0BACA,sCACA,qBACA,oCACA,uCACA,mCACA,kCACA,yBACA,qCACA,sCACA,uCACD,EACA,yDAA0D,CACzD,0DACA,mEACA,4EACA,uEACA,4EACA,uEACA,iDACA,yOACA,8EACA,8GACD,EACA,mDAAoD,CACnD,oEACD,EACA,2CAA4C,CAC3C,wCACA,wKACA,uBACA,6BACA,kBACA,0DACA,mCACA,yDACA;AAAA,iBACD,EACA,4CAA6C,CAC5C,oDACA,oDACA,sDACA,iDACA,mGACA,yDACA,wDACA,gCACA,iEACD,EACA,wCAAyC,CACxC,wBACA,+BACA,oCACA,4DACA,gLACD,EACA,yDAA0D,CACzD,0CACD,EACA,0DAA2D,CAC1D,+DACA,mFACA,oEACA,mEACD,EACA,uCAAwC,CACvC,eACA,iFACA,uEACA,sXACA,yPACA,wMACA,yEACA,+DACA,iFACA,+EACA,4HACA,2EACA,+JACA,mDACA,sGACA,kMACA,6PACA,kIACA,qKACA,kGACA,iEACA,kEACA,qIACA,iPACA,sPACD,EACA,qCAAsC,CACrC,SACA,gBACA,MACD,EACA,qDAAsD,CACrD,4BACA,uBACA,8BACA,mBACA,uBACA,WACA,6DACD,EACA,iDAAkD,CACjD,UACD,EACA,4CAA6C,CAC5C,kBACA,wFACA,UACA,sCACD,EACA,sDAAuD,CACtD,qDACA,sBACA,yBACA,KACA,kBACA,wBACA,QACD,EACA,gDAAiD,CAChD,kBACD,EACA,iDAAkD,CACjD,4CACD,EACA,yCAA0C,CACzC,kHACA,gJACA,2JACA,0HACA,uDACA,+IACA,uHACA,8HACA,2OACA,4CACA,mCACA,kGACA,mDACA,yDACA,kDACA,6DACA,uFACA,sFACA,0CACA,uCACA,kCACA,6EACA,iFACA,2FACA,+EACA,uFACA,8FACA,yFACA,uFACA,gGACA,iGACA,yFACA,yFACA,uFACA,mFACA,6CACA,6CACA,0CACA,qCACA,qCACA,uCACA,uCACA,2DACA,gCACA,gDACA,kDACA,wEACA,+HACA,uHACA,yEACA,2DACA,0EACA,uFACA,iGACA,6JACA,yFACA,6GACA,oKACA,gGACA,oHACA,kKACA,8FACA,kHACA,iFACA,8GACA,mDACA,6CACA,yDACA,uFACA,4GACA,uFACA,uFACA,wLACA,sOACA,sJACA,oJACA,+JACA,yEACA,oEACA,8JACA,uJACA,iIACA,yJACA,8CACA,oEACA,+IACA,4KACA,wFACA,6CACA,yIACA,mIACA,wDACA,2DACA,8IACA,0EACA,qFACA,oFACA,+IACA,yDACA,uDACA,kDACA,+EACA,4BACA,iDACA,mDACA,gEACA,kEACA,wEACA,0EACA,0EACA,oGACA,yIACA,wIACA,6IACA,4IACA,2FACA,0FACA,qGACA,oGACA,4CACA,2CACA,0DACA,kKACA,kGACA,2FACA,+FACA,0PACA,wPACA,oOACA,mRACA,2PACA,2PACA,wQACA,+PACA,6PACA,0QACA,2PACA,sOACA,wGACA,qGACA,kJACA,kJACA,8LACA,6IACA,gFACA,oFACA,8FACA,uFACA,uHACA,qGACA,6DACA,sDACA,6EACA,sGACA,uDACA,iEACA,wFACA,8CACA,4IACA,sDACA,iDACA,gIACA,wDACA,2FACA,8GACA,wFACA,gCACA,sDACA,wDACA,uFACA,yFACA,kFACA,mEACA,qGACA,6FACA,0FACA,oFACA,wEACA,6FACA,qGACA,0DACA,oDACA,0DACA,+DACA,sEACA,gKACA,8JACA,iKACA,+JACA,gLACA,8KACA,6FACA,uFACA,uFACA,wGACA,4KACA,uLACA,8DACA,6FACA,uEACA,wDACA,0DACA,mDACA,wCACA,iKACA,mDACA,qEACA,mEACA,iEACA,wEACA,uEACA,2DACA,wEACA,mEACA,mEACA,mEACA,oEACA,mEACA,qEACD,EACA,wCAAyC,CACxC,qHACA,sEACA,sDACA,0EACA,iEACD,EACA,8CAA+C,CAC9C,uFACA,oFACA,yDACA,yDACA,+GACA,8JACA,gJACA,2DACA,8BACA,uBACA,gIACA,iCACA,QACA,MACA,gEACA,gEACA,sHACA,oKACA,qJACA,oIACD,EACA,yCAA0C,CACzC,2BACD,CACD,CAAC", "names": [], "file": "editor.main.nls.fr.js"}
/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/(function(){var X=["require","exports","vs/editor/common/core/range","vs/editor/common/core/offsetRange","vs/editor/common/core/position","vs/base/common/errors","vs/base/common/strings","vs/base/common/arrays","vs/editor/common/diff/defaultLinesDiffComputer/algorithms/diffAlgorithm","vs/base/common/event","vs/editor/common/core/lineRange","vs/base/common/arraysFind","vs/base/common/assert","vs/base/common/lifecycle","vs/base/common/objects","vs/editor/common/diff/defaultLinesDiffComputer/utils","vs/editor/common/diff/rangeMapping","vs/base/common/platform","vs/base/common/uri","vs/nls","vs/base/common/functional","vs/base/common/iterator","vs/base/common/linkedList","vs/base/common/stopwatch","vs/base/common/diff/diff","vs/base/common/types","vs/base/common/uint","vs/editor/common/core/characterClassifier","vs/editor/common/core/wordHelper","vs/editor/common/diff/defaultLinesDiffComputer/algorithms/myersDiffAlgorithm","vs/editor/common/diff/defaultLinesDiffComputer/linesSliceCharSequence","vs/editor/common/diff/linesDiffComputer","vs/base/common/cache","vs/base/common/color","vs/base/common/diff/diffChange","vs/base/common/keyCodes","vs/base/common/lazy","vs/base/common/map","vs/base/common/cancellation","vs/base/common/hash","vs/base/common/codicons","vs/editor/common/core/selection","vs/editor/common/core/wordCharacterClassifier","vs/editor/common/diff/defaultLinesDiffComputer/heuristicSequenceOptimizations","vs/editor/common/diff/defaultLinesDiffComputer/lineSequence","vs/editor/common/diff/defaultLinesDiffComputer/algorithms/dynamicProgrammingDiffing","vs/editor/common/diff/defaultLinesDiffComputer/computeMovedLines","vs/editor/common/diff/defaultLinesDiffComputer/defaultLinesDiffComputer","vs/editor/common/diff/legacyLinesDiffComputer","vs/editor/common/diff/linesDiffComputers","vs/editor/common/languages/defaultDocumentColorsComputer","vs/editor/common/languages/linkComputer","vs/editor/common/languages/supports/inplaceReplaceSupport","vs/editor/common/model","vs/editor/common/model/prefixSumComputer","vs/editor/common/model/mirrorTextModel","vs/editor/common/model/textModelSearch","vs/editor/common/services/unicodeTextModelHighlighter","vs/editor/common/standalone/standaloneEnums","vs/editor/common/tokenizationRegistry","vs/nls!vs/base/common/platform","vs/nls!vs/base/common/worker/simpleWorker","vs/base/common/process","vs/base/common/path","vs/nls!vs/editor/common/languages","vs/editor/common/languages","vs/editor/common/services/editorBaseApi","vs/base/common/worker/simpleWorker","vs/editor/common/services/editorSimpleWorker"],J=function(T){for(var n=[],M=0,A=T.length;M<A;M++)n[M]=X[T[M]];return n};const Ne=this,Re=typeof global=="object"?global:{};var ae;(function(T){T.global=Ne;class n{get isWindows(){return this._detect(),this._isWindows}get isNode(){return this._detect(),this._isNode}get isElectronRenderer(){return this._detect(),this._isElectronRenderer}get isWebWorker(){return this._detect(),this._isWebWorker}get isElectronNodeIntegrationWebWorker(){return this._detect(),this._isElectronNodeIntegrationWebWorker}constructor(){this._detected=!1,this._isWindows=!1,this._isNode=!1,this._isElectronRenderer=!1,this._isWebWorker=!1,this._isElectronNodeIntegrationWebWorker=!1}_detect(){this._detected||(this._detected=!0,this._isWindows=n._isWindows(),this._isNode=typeof module<"u"&&!!module.exports,this._isElectronRenderer=typeof process<"u"&&typeof process.versions<"u"&&typeof process.versions.electron<"u"&&process.type==="renderer",this._isWebWorker=typeof T.global.importScripts=="function",this._isElectronNodeIntegrationWebWorker=this._isWebWorker&&typeof process<"u"&&typeof process.versions<"u"&&typeof process.versions.electron<"u"&&process.type==="worker")}static _isWindows(){return typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.indexOf("Windows")>=0?!0:typeof process<"u"?process.platform==="win32":!1}}T.Environment=n})(ae||(ae={}));var ae;(function(T){class n{constructor(d,m,L){this.type=d,this.detail=m,this.timestamp=L}}T.LoaderEvent=n;class M{constructor(d){this._events=[new n(1,"",d)]}record(d,m){this._events.push(new n(d,m,T.Utilities.getHighPerformanceTimestamp()))}getEvents(){return this._events}}T.LoaderEventRecorder=M;class A{record(d,m){}getEvents(){return[]}}A.INSTANCE=new A,T.NullLoaderEventRecorder=A})(ae||(ae={}));var ae;(function(T){class n{static fileUriToFilePath(A,i){if(i=decodeURI(i).replace(/%23/g,"#"),A){if(/^file:\/\/\//.test(i))return i.substr(8);if(/^file:\/\//.test(i))return i.substr(5)}else if(/^file:\/\//.test(i))return i.substr(7);return i}static startsWith(A,i){return A.length>=i.length&&A.substr(0,i.length)===i}static endsWith(A,i){return A.length>=i.length&&A.substr(A.length-i.length)===i}static containsQueryString(A){return/^[^\#]*\?/gi.test(A)}static isAbsolutePath(A){return/^((http:\/\/)|(https:\/\/)|(file:\/\/)|(\/))/.test(A)}static forEachProperty(A,i){if(A){let d;for(d in A)A.hasOwnProperty(d)&&i(d,A[d])}}static isEmpty(A){let i=!0;return n.forEachProperty(A,()=>{i=!1}),i}static recursiveClone(A){if(!A||typeof A!="object"||A instanceof RegExp||!Array.isArray(A)&&Object.getPrototypeOf(A)!==Object.prototype)return A;let i=Array.isArray(A)?[]:{};return n.forEachProperty(A,(d,m)=>{m&&typeof m=="object"?i[d]=n.recursiveClone(m):i[d]=m}),i}static generateAnonymousModule(){return"===anonymous"+n.NEXT_ANONYMOUS_ID+++"==="}static isAnonymousModule(A){return n.startsWith(A,"===anonymous")}static getHighPerformanceTimestamp(){return this.PERFORMANCE_NOW_PROBED||(this.PERFORMANCE_NOW_PROBED=!0,this.HAS_PERFORMANCE_NOW=T.global.performance&&typeof T.global.performance.now=="function"),this.HAS_PERFORMANCE_NOW?T.global.performance.now():Date.now()}}n.NEXT_ANONYMOUS_ID=1,n.PERFORMANCE_NOW_PROBED=!1,n.HAS_PERFORMANCE_NOW=!1,T.Utilities=n})(ae||(ae={}));var ae;(function(T){function n(i){if(i instanceof Error)return i;const d=new Error(i.message||String(i)||"Unknown Error");return i.stack&&(d.stack=i.stack),d}T.ensureError=n;class M{static validateConfigurationOptions(d){function m(L){if(L.phase==="loading"){console.error('Loading "'+L.moduleId+'" failed'),console.error(L),console.error("Here are the modules that depend on it:"),console.error(L.neededBy);return}if(L.phase==="factory"){console.error('The factory function of "'+L.moduleId+'" has thrown an exception'),console.error(L),console.error("Here are the modules that depend on it:"),console.error(L.neededBy);return}}if(d=d||{},typeof d.baseUrl!="string"&&(d.baseUrl=""),typeof d.isBuild!="boolean"&&(d.isBuild=!1),typeof d.paths!="object"&&(d.paths={}),typeof d.config!="object"&&(d.config={}),typeof d.catchError>"u"&&(d.catchError=!1),typeof d.recordStats>"u"&&(d.recordStats=!1),typeof d.urlArgs!="string"&&(d.urlArgs=""),typeof d.onError!="function"&&(d.onError=m),Array.isArray(d.ignoreDuplicateModules)||(d.ignoreDuplicateModules=[]),d.baseUrl.length>0&&(T.Utilities.endsWith(d.baseUrl,"/")||(d.baseUrl+="/")),typeof d.cspNonce!="string"&&(d.cspNonce=""),typeof d.preferScriptTags>"u"&&(d.preferScriptTags=!1),d.nodeCachedData&&typeof d.nodeCachedData=="object"&&(typeof d.nodeCachedData.seed!="string"&&(d.nodeCachedData.seed="seed"),(typeof d.nodeCachedData.writeDelay!="number"||d.nodeCachedData.writeDelay<0)&&(d.nodeCachedData.writeDelay=1e3*7),!d.nodeCachedData.path||typeof d.nodeCachedData.path!="string")){const L=n(new Error("INVALID cached data configuration, 'path' MUST be set"));L.phase="configuration",d.onError(L),d.nodeCachedData=void 0}return d}static mergeConfigurationOptions(d=null,m=null){let L=T.Utilities.recursiveClone(m||{});return T.Utilities.forEachProperty(d,(h,o)=>{h==="ignoreDuplicateModules"&&typeof L.ignoreDuplicateModules<"u"?L.ignoreDuplicateModules=L.ignoreDuplicateModules.concat(o):h==="paths"&&typeof L.paths<"u"?T.Utilities.forEachProperty(o,(w,e)=>L.paths[w]=e):h==="config"&&typeof L.config<"u"?T.Utilities.forEachProperty(o,(w,e)=>L.config[w]=e):L[h]=T.Utilities.recursiveClone(o)}),M.validateConfigurationOptions(L)}}T.ConfigurationOptionsUtil=M;class A{constructor(d,m){if(this._env=d,this.options=M.mergeConfigurationOptions(m),this._createIgnoreDuplicateModulesMap(),this._createSortedPathsRules(),this.options.baseUrl===""&&this.options.nodeRequire&&this.options.nodeRequire.main&&this.options.nodeRequire.main.filename&&this._env.isNode){let L=this.options.nodeRequire.main.filename,h=Math.max(L.lastIndexOf("/"),L.lastIndexOf("\\"));this.options.baseUrl=L.substring(0,h+1)}}_createIgnoreDuplicateModulesMap(){this.ignoreDuplicateModulesMap={};for(let d=0;d<this.options.ignoreDuplicateModules.length;d++)this.ignoreDuplicateModulesMap[this.options.ignoreDuplicateModules[d]]=!0}_createSortedPathsRules(){this.sortedPathsRules=[],T.Utilities.forEachProperty(this.options.paths,(d,m)=>{Array.isArray(m)?this.sortedPathsRules.push({from:d,to:m}):this.sortedPathsRules.push({from:d,to:[m]})}),this.sortedPathsRules.sort((d,m)=>m.from.length-d.from.length)}cloneAndMerge(d){return new A(this._env,M.mergeConfigurationOptions(d,this.options))}getOptionsLiteral(){return this.options}_applyPaths(d){let m;for(let L=0,h=this.sortedPathsRules.length;L<h;L++)if(m=this.sortedPathsRules[L],T.Utilities.startsWith(d,m.from)){let o=[];for(let w=0,e=m.to.length;w<e;w++)o.push(m.to[w]+d.substr(m.from.length));return o}return[d]}_addUrlArgsToUrl(d){return T.Utilities.containsQueryString(d)?d+"&"+this.options.urlArgs:d+"?"+this.options.urlArgs}_addUrlArgsIfNecessaryToUrl(d){return this.options.urlArgs?this._addUrlArgsToUrl(d):d}_addUrlArgsIfNecessaryToUrls(d){if(this.options.urlArgs)for(let m=0,L=d.length;m<L;m++)d[m]=this._addUrlArgsToUrl(d[m]);return d}moduleIdToPaths(d){if(this._env.isNode&&this.options.amdModulesPattern instanceof RegExp&&!this.options.amdModulesPattern.test(d))return this.isBuild()?["empty:"]:["node|"+d];let m=d,L;if(!T.Utilities.endsWith(m,".js")&&!T.Utilities.isAbsolutePath(m)){L=this._applyPaths(m);for(let h=0,o=L.length;h<o;h++)this.isBuild()&&L[h]==="empty:"||(T.Utilities.isAbsolutePath(L[h])||(L[h]=this.options.baseUrl+L[h]),!T.Utilities.endsWith(L[h],".js")&&!T.Utilities.containsQueryString(L[h])&&(L[h]=L[h]+".js"))}else!T.Utilities.endsWith(m,".js")&&!T.Utilities.containsQueryString(m)&&(m=m+".js"),L=[m];return this._addUrlArgsIfNecessaryToUrls(L)}requireToUrl(d){let m=d;return T.Utilities.isAbsolutePath(m)||(m=this._applyPaths(m)[0],T.Utilities.isAbsolutePath(m)||(m=this.options.baseUrl+m)),this._addUrlArgsIfNecessaryToUrl(m)}isBuild(){return this.options.isBuild}shouldInvokeFactory(d){return!!(!this.options.isBuild||T.Utilities.isAnonymousModule(d)||this.options.buildForceInvokeFactory&&this.options.buildForceInvokeFactory[d])}isDuplicateMessageIgnoredFor(d){return this.ignoreDuplicateModulesMap.hasOwnProperty(d)}getConfigForModule(d){if(this.options.config)return this.options.config[d]}shouldCatchError(){return this.options.catchError}shouldRecordStats(){return this.options.recordStats}onError(d){this.options.onError(d)}}T.Configuration=A})(ae||(ae={}));var ae;(function(T){class n{constructor(o){this._env=o,this._scriptLoader=null,this._callbackMap={}}load(o,w,e,a){if(!this._scriptLoader)if(this._env.isWebWorker)this._scriptLoader=new i;else if(this._env.isElectronRenderer){const{preferScriptTags:l}=o.getConfig().getOptionsLiteral();l?this._scriptLoader=new M:this._scriptLoader=new d(this._env)}else this._env.isNode?this._scriptLoader=new d(this._env):this._scriptLoader=new M;let c={callback:e,errorback:a};if(this._callbackMap.hasOwnProperty(w)){this._callbackMap[w].push(c);return}this._callbackMap[w]=[c],this._scriptLoader.load(o,w,()=>this.triggerCallback(w),l=>this.triggerErrorback(w,l))}triggerCallback(o){let w=this._callbackMap[o];delete this._callbackMap[o];for(let e=0;e<w.length;e++)w[e].callback()}triggerErrorback(o,w){let e=this._callbackMap[o];delete this._callbackMap[o];for(let a=0;a<e.length;a++)e[a].errorback(w)}}class M{attachListeners(o,w,e){let a=()=>{o.removeEventListener("load",c),o.removeEventListener("error",l)},c=f=>{a(),w()},l=f=>{a(),e(f)};o.addEventListener("load",c),o.addEventListener("error",l)}load(o,w,e,a){if(/^node\|/.test(w)){let c=o.getConfig().getOptionsLiteral(),l=m(o.getRecorder(),c.nodeRequire||T.global.nodeRequire),f=w.split("|"),g=null;try{g=l(f[1])}catch(S){a(S);return}o.enqueueDefineAnonymousModule([],()=>g),e()}else{let c=document.createElement("script");c.setAttribute("async","async"),c.setAttribute("type","text/javascript"),this.attachListeners(c,e,a);const{trustedTypesPolicy:l}=o.getConfig().getOptionsLiteral();l&&(w=l.createScriptURL(w)),c.setAttribute("src",w);const{cspNonce:f}=o.getConfig().getOptionsLiteral();f&&c.setAttribute("nonce",f),document.getElementsByTagName("head")[0].appendChild(c)}}}function A(h){const{trustedTypesPolicy:o}=h.getConfig().getOptionsLiteral();try{return(o?self.eval(o.createScript("","true")):new Function("true")).call(self),!0}catch{return!1}}class i{constructor(){this._cachedCanUseEval=null}_canUseEval(o){return this._cachedCanUseEval===null&&(this._cachedCanUseEval=A(o)),this._cachedCanUseEval}load(o,w,e,a){if(/^node\|/.test(w)){const c=o.getConfig().getOptionsLiteral(),l=m(o.getRecorder(),c.nodeRequire||T.global.nodeRequire),f=w.split("|");let g=null;try{g=l(f[1])}catch(S){a(S);return}o.enqueueDefineAnonymousModule([],function(){return g}),e()}else{const{trustedTypesPolicy:c}=o.getConfig().getOptionsLiteral();if(!(/^((http:)|(https:)|(file:))/.test(w)&&w.substring(0,self.origin.length)!==self.origin)&&this._canUseEval(o)){fetch(w).then(f=>{if(f.status!==200)throw new Error(f.statusText);return f.text()}).then(f=>{f=`${f}
//# sourceURL=${w}`,(c?self.eval(c.createScript("",f)):new Function(f)).call(self),e()}).then(void 0,a);return}try{c&&(w=c.createScriptURL(w)),importScripts(w),e()}catch(f){a(f)}}}}class d{constructor(o){this._env=o,this._didInitialize=!1,this._didPatchNodeRequire=!1}_init(o){this._didInitialize||(this._didInitialize=!0,this._fs=o("fs"),this._vm=o("vm"),this._path=o("path"),this._crypto=o("crypto"))}_initNodeRequire(o,w){const{nodeCachedData:e}=w.getConfig().getOptionsLiteral();if(!e||this._didPatchNodeRequire)return;this._didPatchNodeRequire=!0;const a=this,c=o("module");function l(f){const g=f.constructor;let S=function(E){try{return f.require(E)}finally{}};return S.resolve=function(E,y){return g._resolveFilename(E,f,!1,y)},S.resolve.paths=function(E){return g._resolveLookupPaths(E,f)},S.main=process.mainModule,S.extensions=g._extensions,S.cache=g._cache,S}c.prototype._compile=function(f,g){const S=c.wrap(f.replace(/^#!.*/,"")),_=w.getRecorder(),E=a._getCachedDataPath(e,g),y={filename:g};let v;try{const N=a._fs.readFileSync(E);v=N.slice(0,16),y.cachedData=N.slice(16),_.record(60,E)}catch{_.record(61,E)}const r=new a._vm.Script(S,y),s=r.runInThisContext(y),u=a._path.dirname(g),p=l(this),b=[this.exports,p,this,g,u,process,Re,Buffer],C=s.apply(this.exports,b);return a._handleCachedData(r,S,E,!y.cachedData,w),a._verifyCachedData(r,S,E,v,w),C}}load(o,w,e,a){const c=o.getConfig().getOptionsLiteral(),l=m(o.getRecorder(),c.nodeRequire||T.global.nodeRequire),f=c.nodeInstrumenter||function(S){return S};this._init(l),this._initNodeRequire(l,o);let g=o.getRecorder();if(/^node\|/.test(w)){let S=w.split("|"),_=null;try{_=l(S[1])}catch(E){a(E);return}o.enqueueDefineAnonymousModule([],()=>_),e()}else{w=T.Utilities.fileUriToFilePath(this._env.isWindows,w);const S=this._path.normalize(w),_=this._getElectronRendererScriptPathOrUri(S),E=!!c.nodeCachedData,y=E?this._getCachedDataPath(c.nodeCachedData,w):void 0;this._readSourceAndCachedData(S,y,g,(v,r,s,u)=>{if(v){a(v);return}let p;r.charCodeAt(0)===d._BOM?p=d._PREFIX+r.substring(1)+d._SUFFIX:p=d._PREFIX+r+d._SUFFIX,p=f(p,S);const b={filename:_,cachedData:s},C=this._createAndEvalScript(o,p,b,e,a);this._handleCachedData(C,p,y,E&&!s,o),this._verifyCachedData(C,p,y,u,o)})}}_createAndEvalScript(o,w,e,a,c){const l=o.getRecorder();l.record(31,e.filename);const f=new this._vm.Script(w,e),g=f.runInThisContext(e),S=o.getGlobalAMDDefineFunc();let _=!1;const E=function(){return _=!0,S.apply(null,arguments)};return E.amd=S.amd,g.call(T.global,o.getGlobalAMDRequireFunc(),E,e.filename,this._path.dirname(e.filename)),l.record(32,e.filename),_?a():c(new Error(`Didn't receive define call in ${e.filename}!`)),f}_getElectronRendererScriptPathOrUri(o){if(!this._env.isElectronRenderer)return o;let w=o.match(/^([a-z])\:(.*)/i);return w?`file:///${(w[1].toUpperCase()+":"+w[2]).replace(/\\/g,"/")}`:`file://${o}`}_getCachedDataPath(o,w){const e=this._crypto.createHash("md5").update(w,"utf8").update(o.seed,"utf8").update(process.arch,"").digest("hex"),a=this._path.basename(w).replace(/\.js$/,"");return this._path.join(o.path,`${a}-${e}.code`)}_handleCachedData(o,w,e,a,c){o.cachedDataRejected?this._fs.unlink(e,l=>{c.getRecorder().record(62,e),this._createAndWriteCachedData(o,w,e,c),l&&c.getConfig().onError(l)}):a&&this._createAndWriteCachedData(o,w,e,c)}_createAndWriteCachedData(o,w,e,a){let c=Math.ceil(a.getConfig().getOptionsLiteral().nodeCachedData.writeDelay*(1+Math.random())),l=-1,f=0,g;const S=()=>{setTimeout(()=>{g||(g=this._crypto.createHash("md5").update(w,"utf8").digest());const _=o.createCachedData();if(!(_.length===0||_.length===l||f>=5)){if(_.length<l){S();return}l=_.length,this._fs.writeFile(e,Buffer.concat([g,_]),E=>{E&&a.getConfig().onError(E),a.getRecorder().record(63,e),S()})}},c*Math.pow(4,f++))};S()}_readSourceAndCachedData(o,w,e,a){if(!w)this._fs.readFile(o,{encoding:"utf8"},a);else{let c,l,f,g=2;const S=_=>{_?a(_):--g===0&&a(void 0,c,l,f)};this._fs.readFile(o,{encoding:"utf8"},(_,E)=>{c=E,S(_)}),this._fs.readFile(w,(_,E)=>{!_&&E&&E.length>0?(f=E.slice(0,16),l=E.slice(16),e.record(60,w)):e.record(61,w),S()})}}_verifyCachedData(o,w,e,a,c){a&&(o.cachedDataRejected||setTimeout(()=>{const l=this._crypto.createHash("md5").update(w,"utf8").digest();a.equals(l)||(c.getConfig().onError(new Error(`FAILED TO VERIFY CACHED DATA, deleting stale '${e}' now, but a RESTART IS REQUIRED`)),this._fs.unlink(e,f=>{f&&c.getConfig().onError(f)}))},Math.ceil(5e3*(1+Math.random()))))}}d._BOM=65279,d._PREFIX="(function (require, define, __filename, __dirname) { ",d._SUFFIX=`
});`;function m(h,o){if(o.__$__isRecorded)return o;const w=function(a){h.record(33,a);try{return o(a)}finally{h.record(34,a)}};return w.__$__isRecorded=!0,w}T.ensureRecordedNodeRequire=m;function L(h){return new n(h)}T.createScriptLoader=L})(ae||(ae={}));var ae;(function(T){class n{constructor(h){let o=h.lastIndexOf("/");o!==-1?this.fromModulePath=h.substr(0,o+1):this.fromModulePath=""}static _normalizeModuleId(h){let o=h,w;for(w=/\/\.\//;w.test(o);)o=o.replace(w,"/");for(o=o.replace(/^\.\//g,""),w=/\/(([^\/])|([^\/][^\/\.])|([^\/\.][^\/])|([^\/][^\/][^\/]+))\/\.\.\//;w.test(o);)o=o.replace(w,"/");return o=o.replace(/^(([^\/])|([^\/][^\/\.])|([^\/\.][^\/])|([^\/][^\/][^\/]+))\/\.\.\//,""),o}resolveModule(h){let o=h;return T.Utilities.isAbsolutePath(o)||(T.Utilities.startsWith(o,"./")||T.Utilities.startsWith(o,"../"))&&(o=n._normalizeModuleId(this.fromModulePath+o)),o}}n.ROOT=new n(""),T.ModuleIdResolver=n;class M{constructor(h,o,w,e,a,c){this.id=h,this.strId=o,this.dependencies=w,this._callback=e,this._errorback=a,this.moduleIdResolver=c,this.exports={},this.error=null,this.exportsPassedIn=!1,this.unresolvedDependenciesCount=this.dependencies.length,this._isComplete=!1}static _safeInvokeFunction(h,o){try{return{returnedValue:h.apply(T.global,o),producedError:null}}catch(w){return{returnedValue:null,producedError:w}}}static _invokeFactory(h,o,w,e){return h.shouldInvokeFactory(o)?h.shouldCatchError()?this._safeInvokeFunction(w,e):{returnedValue:w.apply(T.global,e),producedError:null}:{returnedValue:null,producedError:null}}complete(h,o,w,e){this._isComplete=!0;let a=null;if(this._callback)if(typeof this._callback=="function"){h.record(21,this.strId);let c=M._invokeFactory(o,this.strId,this._callback,w);a=c.producedError,h.record(22,this.strId),!a&&typeof c.returnedValue<"u"&&(!this.exportsPassedIn||T.Utilities.isEmpty(this.exports))&&(this.exports=c.returnedValue)}else this.exports=this._callback;if(a){let c=T.ensureError(a);c.phase="factory",c.moduleId=this.strId,c.neededBy=e(this.id),this.error=c,o.onError(c)}this.dependencies=null,this._callback=null,this._errorback=null,this.moduleIdResolver=null}onDependencyError(h){return this._isComplete=!0,this.error=h,this._errorback?(this._errorback(h),!0):!1}isComplete(){return this._isComplete}}T.Module=M;class A{constructor(){this._nextId=0,this._strModuleIdToIntModuleId=new Map,this._intModuleIdToStrModuleId=[],this.getModuleId("exports"),this.getModuleId("module"),this.getModuleId("require")}getMaxModuleId(){return this._nextId}getModuleId(h){let o=this._strModuleIdToIntModuleId.get(h);return typeof o>"u"&&(o=this._nextId++,this._strModuleIdToIntModuleId.set(h,o),this._intModuleIdToStrModuleId[o]=h),o}getStrModuleId(h){return this._intModuleIdToStrModuleId[h]}}class i{constructor(h){this.id=h}}i.EXPORTS=new i(0),i.MODULE=new i(1),i.REQUIRE=new i(2),T.RegularDependency=i;class d{constructor(h,o,w){this.id=h,this.pluginId=o,this.pluginParam=w}}T.PluginDependency=d;class m{constructor(h,o,w,e,a=0){this._env=h,this._scriptLoader=o,this._loaderAvailableTimestamp=a,this._defineFunc=w,this._requireFunc=e,this._moduleIdProvider=new A,this._config=new T.Configuration(this._env),this._hasDependencyCycle=!1,this._modules2=[],this._knownModules2=[],this._inverseDependencies2=[],this._inversePluginDependencies2=new Map,this._currentAnonymousDefineCall=null,this._recorder=null,this._buildInfoPath=[],this._buildInfoDefineStack=[],this._buildInfoDependencies=[],this._requireFunc.moduleManager=this}reset(){return new m(this._env,this._scriptLoader,this._defineFunc,this._requireFunc,this._loaderAvailableTimestamp)}getGlobalAMDDefineFunc(){return this._defineFunc}getGlobalAMDRequireFunc(){return this._requireFunc}static _findRelevantLocationInStack(h,o){let w=c=>c.replace(/\\/g,"/"),e=w(h),a=o.split(/\n/);for(let c=0;c<a.length;c++){let l=a[c].match(/(.*):(\d+):(\d+)\)?$/);if(l){let f=l[1],g=l[2],S=l[3],_=Math.max(f.lastIndexOf(" ")+1,f.lastIndexOf("(")+1);if(f=f.substr(_),f=w(f),f===e){let E={line:parseInt(g,10),col:parseInt(S,10)};return E.line===1&&(E.col-=53),E}}}throw new Error("Could not correlate define call site for needle "+h)}getBuildInfo(){if(!this._config.isBuild())return null;let h=[],o=0;for(let w=0,e=this._modules2.length;w<e;w++){let a=this._modules2[w];if(!a)continue;let c=this._buildInfoPath[a.id]||null,l=this._buildInfoDefineStack[a.id]||null,f=this._buildInfoDependencies[a.id];h[o++]={id:a.strId,path:c,defineLocation:c&&l?m._findRelevantLocationInStack(c,l):null,dependencies:f,shim:null,exports:a.exports}}return h}getRecorder(){return this._recorder||(this._config.shouldRecordStats()?this._recorder=new T.LoaderEventRecorder(this._loaderAvailableTimestamp):this._recorder=T.NullLoaderEventRecorder.INSTANCE),this._recorder}getLoaderEvents(){return this.getRecorder().getEvents()}enqueueDefineAnonymousModule(h,o){if(this._currentAnonymousDefineCall!==null)throw new Error("Can only have one anonymous define call per script file");let w=null;this._config.isBuild()&&(w=new Error("StackLocation").stack||null),this._currentAnonymousDefineCall={stack:w,dependencies:h,callback:o}}defineModule(h,o,w,e,a,c=new n(h)){let l=this._moduleIdProvider.getModuleId(h);if(this._modules2[l]){this._config.isDuplicateMessageIgnoredFor(h)||console.warn("Duplicate definition of module '"+h+"'");return}let f=new M(l,h,this._normalizeDependencies(o,c),w,e,c);this._modules2[l]=f,this._config.isBuild()&&(this._buildInfoDefineStack[l]=a,this._buildInfoDependencies[l]=(f.dependencies||[]).map(g=>this._moduleIdProvider.getStrModuleId(g.id))),this._resolve(f)}_normalizeDependency(h,o){if(h==="exports")return i.EXPORTS;if(h==="module")return i.MODULE;if(h==="require")return i.REQUIRE;let w=h.indexOf("!");if(w>=0){let e=o.resolveModule(h.substr(0,w)),a=o.resolveModule(h.substr(w+1)),c=this._moduleIdProvider.getModuleId(e+"!"+a),l=this._moduleIdProvider.getModuleId(e);return new d(c,l,a)}return new i(this._moduleIdProvider.getModuleId(o.resolveModule(h)))}_normalizeDependencies(h,o){let w=[],e=0;for(let a=0,c=h.length;a<c;a++)w[e++]=this._normalizeDependency(h[a],o);return w}_relativeRequire(h,o,w,e){if(typeof o=="string")return this.synchronousRequire(o,h);this.defineModule(T.Utilities.generateAnonymousModule(),o,w,e,null,h)}synchronousRequire(h,o=new n(h)){let w=this._normalizeDependency(h,o),e=this._modules2[w.id];if(!e)throw new Error("Check dependency list! Synchronous require cannot resolve module '"+h+"'. This is the first mention of this module!");if(!e.isComplete())throw new Error("Check dependency list! Synchronous require cannot resolve module '"+h+"'. This module has not been resolved completely yet.");if(e.error)throw e.error;return e.exports}configure(h,o){let w=this._config.shouldRecordStats();o?this._config=new T.Configuration(this._env,h):this._config=this._config.cloneAndMerge(h),this._config.shouldRecordStats()&&!w&&(this._recorder=null)}getConfig(){return this._config}_onLoad(h){if(this._currentAnonymousDefineCall!==null){let o=this._currentAnonymousDefineCall;this._currentAnonymousDefineCall=null,this.defineModule(this._moduleIdProvider.getStrModuleId(h),o.dependencies,o.callback,null,o.stack)}}_createLoadError(h,o){let w=this._moduleIdProvider.getStrModuleId(h),e=(this._inverseDependencies2[h]||[]).map(c=>this._moduleIdProvider.getStrModuleId(c));const a=T.ensureError(o);return a.phase="loading",a.moduleId=w,a.neededBy=e,a}_onLoadError(h,o){const w=this._createLoadError(h,o);this._modules2[h]||(this._modules2[h]=new M(h,this._moduleIdProvider.getStrModuleId(h),[],()=>{},null,null));let e=[];for(let l=0,f=this._moduleIdProvider.getMaxModuleId();l<f;l++)e[l]=!1;let a=!1,c=[];for(c.push(h),e[h]=!0;c.length>0;){let l=c.shift(),f=this._modules2[l];f&&(a=f.onDependencyError(w)||a);let g=this._inverseDependencies2[l];if(g)for(let S=0,_=g.length;S<_;S++){let E=g[S];e[E]||(c.push(E),e[E]=!0)}}a||this._config.onError(w)}_hasDependencyPath(h,o){let w=this._modules2[h];if(!w)return!1;let e=[];for(let c=0,l=this._moduleIdProvider.getMaxModuleId();c<l;c++)e[c]=!1;let a=[];for(a.push(w),e[h]=!0;a.length>0;){let l=a.shift().dependencies;if(l)for(let f=0,g=l.length;f<g;f++){let S=l[f];if(S.id===o)return!0;let _=this._modules2[S.id];_&&!e[S.id]&&(e[S.id]=!0,a.push(_))}}return!1}_findCyclePath(h,o,w){if(h===o||w===50)return[h];let e=this._modules2[h];if(!e)return null;let a=e.dependencies;if(a)for(let c=0,l=a.length;c<l;c++){let f=this._findCyclePath(a[c].id,o,w+1);if(f!==null)return f.push(h),f}return null}_createRequire(h){let o=(w,e,a)=>this._relativeRequire(h,w,e,a);return o.toUrl=w=>this._config.requireToUrl(h.resolveModule(w)),o.getStats=()=>this.getLoaderEvents(),o.hasDependencyCycle=()=>this._hasDependencyCycle,o.config=(w,e=!1)=>{this.configure(w,e)},o.__$__nodeRequire=T.global.nodeRequire,o}_loadModule(h){if(this._modules2[h]||this._knownModules2[h])return;this._knownModules2[h]=!0;let o=this._moduleIdProvider.getStrModuleId(h),w=this._config.moduleIdToPaths(o),e=/^@[^\/]+\/[^\/]+$/;this._env.isNode&&(o.indexOf("/")===-1||e.test(o))&&w.push("node|"+o);let a=-1,c=l=>{if(a++,a>=w.length)this._onLoadError(h,l);else{let f=w[a],g=this.getRecorder();if(this._config.isBuild()&&f==="empty:"){this._buildInfoPath[h]=f,this.defineModule(this._moduleIdProvider.getStrModuleId(h),[],null,null,null),this._onLoad(h);return}g.record(10,f),this._scriptLoader.load(this,f,()=>{this._config.isBuild()&&(this._buildInfoPath[h]=f),g.record(11,f),this._onLoad(h)},S=>{g.record(12,f),c(S)})}};c(null)}_loadPluginDependency(h,o){if(this._modules2[o.id]||this._knownModules2[o.id])return;this._knownModules2[o.id]=!0;let w=e=>{this.defineModule(this._moduleIdProvider.getStrModuleId(o.id),[],e,null,null)};w.error=e=>{this._config.onError(this._createLoadError(o.id,e))},h.load(o.pluginParam,this._createRequire(n.ROOT),w,this._config.getOptionsLiteral())}_resolve(h){let o=h.dependencies;if(o)for(let w=0,e=o.length;w<e;w++){let a=o[w];if(a===i.EXPORTS){h.exportsPassedIn=!0,h.unresolvedDependenciesCount--;continue}if(a===i.MODULE){h.unresolvedDependenciesCount--;continue}if(a===i.REQUIRE){h.unresolvedDependenciesCount--;continue}let c=this._modules2[a.id];if(c&&c.isComplete()){if(c.error){h.onDependencyError(c.error);return}h.unresolvedDependenciesCount--;continue}if(this._hasDependencyPath(a.id,h.id)){this._hasDependencyCycle=!0,console.warn("There is a dependency cycle between '"+this._moduleIdProvider.getStrModuleId(a.id)+"' and '"+this._moduleIdProvider.getStrModuleId(h.id)+"'. The cyclic path follows:");let l=this._findCyclePath(a.id,h.id,0)||[];l.reverse(),l.push(a.id),console.warn(l.map(f=>this._moduleIdProvider.getStrModuleId(f)).join(` => 
`)),h.unresolvedDependenciesCount--;continue}if(this._inverseDependencies2[a.id]=this._inverseDependencies2[a.id]||[],this._inverseDependencies2[a.id].push(h.id),a instanceof d){let l=this._modules2[a.pluginId];if(l&&l.isComplete()){this._loadPluginDependency(l.exports,a);continue}let f=this._inversePluginDependencies2.get(a.pluginId);f||(f=[],this._inversePluginDependencies2.set(a.pluginId,f)),f.push(a),this._loadModule(a.pluginId);continue}this._loadModule(a.id)}h.unresolvedDependenciesCount===0&&this._onModuleComplete(h)}_onModuleComplete(h){let o=this.getRecorder();if(h.isComplete())return;let w=h.dependencies,e=[];if(w)for(let f=0,g=w.length;f<g;f++){let S=w[f];if(S===i.EXPORTS){e[f]=h.exports;continue}if(S===i.MODULE){e[f]={id:h.strId,config:()=>this._config.getConfigForModule(h.strId)};continue}if(S===i.REQUIRE){e[f]=this._createRequire(h.moduleIdResolver);continue}let _=this._modules2[S.id];if(_){e[f]=_.exports;continue}e[f]=null}const a=f=>(this._inverseDependencies2[f]||[]).map(g=>this._moduleIdProvider.getStrModuleId(g));h.complete(o,this._config,e,a);let c=this._inverseDependencies2[h.id];if(this._inverseDependencies2[h.id]=null,c)for(let f=0,g=c.length;f<g;f++){let S=c[f],_=this._modules2[S];_.unresolvedDependenciesCount--,_.unresolvedDependenciesCount===0&&this._onModuleComplete(_)}let l=this._inversePluginDependencies2.get(h.id);if(l){this._inversePluginDependencies2.delete(h.id);for(let f=0,g=l.length;f<g;f++)this._loadPluginDependency(h.exports,l[f])}}}T.ModuleManager=m})(ae||(ae={}));var Y,ae;(function(T){const n=new T.Environment;let M=null;const A=function(L,h,o){typeof L!="string"&&(o=h,h=L,L=null),(typeof h!="object"||!Array.isArray(h))&&(o=h,h=null),h||(h=["require","exports","module"]),L?M.defineModule(L,h,o,null,null):M.enqueueDefineAnonymousModule(h,o)};A.amd={jQuery:!0};const i=function(L,h=!1){M.configure(L,h)},d=function(){if(arguments.length===1){if(arguments[0]instanceof Object&&!Array.isArray(arguments[0])){i(arguments[0]);return}if(typeof arguments[0]=="string")return M.synchronousRequire(arguments[0])}if((arguments.length===2||arguments.length===3)&&Array.isArray(arguments[0])){M.defineModule(T.Utilities.generateAnonymousModule(),arguments[0],arguments[1],arguments[2],null);return}throw new Error("Unrecognized require call")};d.config=i,d.getConfig=function(){return M.getConfig().getOptionsLiteral()},d.reset=function(){M=M.reset()},d.getBuildInfo=function(){return M.getBuildInfo()},d.getStats=function(){return M.getLoaderEvents()},d.define=A;function m(){if(typeof T.global.require<"u"||typeof require<"u"){const L=T.global.require||require;if(typeof L=="function"&&typeof L.resolve=="function"){const h=T.ensureRecordedNodeRequire(M.getRecorder(),L);T.global.nodeRequire=h,d.nodeRequire=h,d.__$__nodeRequire=h}}n.isNode&&!n.isElectronRenderer&&!n.isElectronNodeIntegrationWebWorker?module.exports=d:(n.isElectronRenderer||(T.global.define=A),T.global.require=d)}T.init=m,(typeof T.global.define!="function"||!T.global.define.amd)&&(M=new T.ModuleManager(n,T.createScriptLoader(n),A,d,T.Utilities.getHighPerformanceTimestamp()),typeof T.global.require<"u"&&typeof T.global.require!="function"&&d.config(T.global.require),Y=function(){return A.apply(null,arguments)},Y.amd=A.amd,typeof doNotInitLoader>"u"&&m())})(ae||(ae={}));var ue=this&&this.__awaiter||function(T,n,M,A){function i(d){return d instanceof M?d:new M(function(m){m(d)})}return new(M||(M=Promise))(function(d,m){function L(w){try{o(A.next(w))}catch(e){m(e)}}function h(w){try{o(A.throw(w))}catch(e){m(e)}}function o(w){w.done?d(w.value):i(w.value).then(L,h)}o((A=A.apply(T,n||[])).next())})};Y(X[19],J([0,1]),function(T,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.load=n.create=n.setPseudoTranslation=n.getConfiguredDefaultLocale=n.localize=void 0;let M=typeof document<"u"&&document.location&&document.location.hash.indexOf("pseudo=true")>=0;const A="i-default";function i(l,f){let g;return f.length===0?g=l:g=l.replace(/\{(\d+)\}/g,(S,_)=>{const E=_[0],y=f[E];let v=S;return typeof y=="string"?v=y:(typeof y=="number"||typeof y=="boolean"||y===void 0||y===null)&&(v=String(y)),v}),M&&(g="\uFF3B"+g.replace(/[aouei]/g,"$&$&")+"\uFF3D"),g}function d(l,f){let g=l[f];return g||(g=l["*"],g)?g:null}function m(l){return l.charAt(l.length-1)==="/"?l:l+"/"}function L(l,f,g){return ue(this,void 0,void 0,function*(){const S=m(l)+m(f)+"vscode/"+m(g),_=yield fetch(S);if(_.ok)return yield _.json();throw new Error(`${_.status} - ${_.statusText}`)})}function h(l){return function(f,g){const S=Array.prototype.slice.call(arguments,2);return i(l[f],S)}}function o(l,f,...g){return i(f,g)}n.localize=o;function w(l){}n.getConfiguredDefaultLocale=w;function e(l){M=l}n.setPseudoTranslation=e;function a(l,f){var g;return{localize:h(f[l]),getConfiguredDefaultLocale:(g=f.getConfiguredDefaultLocale)!==null&&g!==void 0?g:S=>{}}}n.create=a;function c(l,f,g,S){var _;const E=(_=S["vs/nls"])!==null&&_!==void 0?_:{};if(!l||l.length===0)return g({localize:o,getConfiguredDefaultLocale:()=>{var u;return(u=E.availableLanguages)===null||u===void 0?void 0:u["*"]}});const y=E.availableLanguages?d(E.availableLanguages,l):null,v=y===null||y===A;let r=".nls";v||(r=r+"."+y);const s=u=>{Array.isArray(u)?u.localize=h(u):u.localize=h(u[l]),u.getConfiguredDefaultLocale=()=>{var p;return(p=E.availableLanguages)===null||p===void 0?void 0:p["*"]},g(u)};typeof E.loadBundle=="function"?E.loadBundle(l,y,(u,p)=>{u?f([l+".nls"],s):s(p)}):E.translationServiceUrl&&!v?ue(this,void 0,void 0,function*(){var u;try{const p=yield L(E.translationServiceUrl,y,l);return s(p)}catch(p){if(!y.includes("-"))return console.error(p),f([l+".nls"],s);try{const b=y.split("-")[0],C=yield L(E.translationServiceUrl,b,l);return(u=E.availableLanguages)!==null&&u!==void 0||(E.availableLanguages={}),E.availableLanguages["*"]=b,s(C)}catch(b){return console.error(b),f([l+".nls"],s)}}}):f([l+r],s,u=>{if(r===".nls"){console.error("Failed trying to load default language strings",u);return}console.error(`Failed to load message bundle for language ${y}. Falling back to the default language:`,u),f([l+".nls"],s)})}n.load=c}),function(){const T=globalThis.MonacoEnvironment,n=T&&T.baseUrl?T.baseUrl:"../../../";function M(w,e){var a;if(T?.createTrustedTypesPolicy)try{return T.createTrustedTypesPolicy(w,e)}catch(c){console.warn(c);return}try{return(a=self.trustedTypes)===null||a===void 0?void 0:a.createPolicy(w,e)}catch(c){console.warn(c);return}}const A=M("amdLoader",{createScriptURL:w=>w,createScript:(w,...e)=>{const a=e.slice(0,-1).join(","),c=e.pop().toString();return`(function anonymous(${a}) { ${c}
})`}});function i(){try{return(A?globalThis.eval(A.createScript("","true")):new Function("true")).call(globalThis),!0}catch{return!1}}function d(){return new Promise((w,e)=>{if(typeof globalThis.define=="function"&&globalThis.define.amd)return w();const a=n+"vs/loader.js";if(!(/^((http:)|(https:)|(file:))/.test(a)&&a.substring(0,globalThis.origin.length)!==globalThis.origin)&&i()){fetch(a).then(l=>{if(l.status!==200)throw new Error(l.statusText);return l.text()}).then(l=>{l=`${l}
//# sourceURL=${a}`,(A?globalThis.eval(A.createScript("",l)):new Function(l)).call(globalThis),w()}).then(void 0,e);return}A?importScripts(A.createScriptURL(a)):importScripts(a),w()})}function m(){require.config({baseUrl:n,catchError:!0,trustedTypesPolicy:A,amdModulesPattern:/^vs\//})}function L(w){d().then(()=>{m(),require([w],function(e){setTimeout(function(){const a=e.create((c,l)=>{globalThis.postMessage(c,l)},null);for(globalThis.onmessage=c=>a.onmessage(c.data,c.ports);o.length>0;){const c=o.shift();a.onmessage(c.data,c.ports)}},0)})})}typeof globalThis.define=="function"&&globalThis.define.amd&&m();let h=!0;const o=[];globalThis.onmessage=w=>{if(!h){o.push(w);return}h=!1,L(w.data)}}(),Y(X[7],J([0,1]),function(T,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.CallbackIterable=n.ArrayQueue=n.reverseOrder=n.booleanComparator=n.numberComparator=n.tieBreakComparators=n.compareBy=n.CompareResult=n.splice=n.insertInto=n.asArray=n.pushMany=n.pushToEnd=n.pushToStart=n.arrayInsert=n.range=n.firstOrDefault=n.distinct=n.isNonEmptyArray=n.isFalsyOrEmpty=n.coalesceInPlace=n.coalesce=n.forEachWithNeighbors=n.forEachAdjacent=n.groupAdjacentBy=n.groupBy=n.quickSelect=n.binarySearch2=n.binarySearch=n.removeFastWithoutKeepingOrder=n.equals=n.tail2=n.tail=void 0;function M(x,O=0){return x[x.length-(1+O)]}n.tail=M;function A(x){if(x.length===0)throw new Error("Invalid tail call");return[x.slice(0,x.length-1),x[x.length-1]]}n.tail2=A;function i(x,O,F=(H,V)=>H===V){if(x===O)return!0;if(!x||!O||x.length!==O.length)return!1;for(let H=0,V=x.length;H<V;H++)if(!F(x[H],O[H]))return!1;return!0}n.equals=i;function d(x,O){const F=x.length-1;O<F&&(x[O]=x[F]),x.pop()}n.removeFastWithoutKeepingOrder=d;function m(x,O,F){return L(x.length,H=>F(x[H],O))}n.binarySearch=m;function L(x,O){let F=0,H=x-1;for(;F<=H;){const V=(F+H)/2|0,t=O(V);if(t<0)F=V+1;else if(t>0)H=V-1;else return V}return-(F+1)}n.binarySearch2=L;function h(x,O,F){if(x=x|0,x>=O.length)throw new TypeError("invalid index");const H=O[Math.floor(O.length*Math.random())],V=[],t=[],ne=[];for(const re of O){const le=F(re,H);le<0?V.push(re):le>0?t.push(re):ne.push(re)}return x<V.length?h(x,V,F):x<V.length+ne.length?ne[0]:h(x-(V.length+ne.length),t,F)}n.quickSelect=h;function o(x,O){const F=[];let H;for(const V of x.slice(0).sort(O))!H||O(H[0],V)!==0?(H=[V],F.push(H)):H.push(V);return F}n.groupBy=o;function*w(x,O){let F,H;for(const V of x)H!==void 0&&O(H,V)?F.push(V):(F&&(yield F),F=[V]),H=V;F&&(yield F)}n.groupAdjacentBy=w;function e(x,O){for(let F=0;F<=x.length;F++)O(F===0?void 0:x[F-1],F===x.length?void 0:x[F])}n.forEachAdjacent=e;function a(x,O){for(let F=0;F<x.length;F++)O(F===0?void 0:x[F-1],x[F],F+1===x.length?void 0:x[F+1])}n.forEachWithNeighbors=a;function c(x){return x.filter(O=>!!O)}n.coalesce=c;function l(x){let O=0;for(let F=0;F<x.length;F++)x[F]&&(x[O]=x[F],O+=1);x.length=O}n.coalesceInPlace=l;function f(x){return!Array.isArray(x)||x.length===0}n.isFalsyOrEmpty=f;function g(x){return Array.isArray(x)&&x.length>0}n.isNonEmptyArray=g;function S(x,O=F=>F){const F=new Set;return x.filter(H=>{const V=O(H);return F.has(V)?!1:(F.add(V),!0)})}n.distinct=S;function _(x,O){return x.length>0?x[0]:O}n.firstOrDefault=_;function E(x,O){let F=typeof O=="number"?x:0;typeof O=="number"?F=x:(F=0,O=x);const H=[];if(F<=O)for(let V=F;V<O;V++)H.push(V);else for(let V=F;V>O;V--)H.push(V);return H}n.range=E;function y(x,O,F){const H=x.slice(0,O),V=x.slice(O);return H.concat(F,V)}n.arrayInsert=y;function v(x,O){const F=x.indexOf(O);F>-1&&(x.splice(F,1),x.unshift(O))}n.pushToStart=v;function r(x,O){const F=x.indexOf(O);F>-1&&(x.splice(F,1),x.push(O))}n.pushToEnd=r;function s(x,O){for(const F of O)x.push(F)}n.pushMany=s;function u(x){return Array.isArray(x)?x:[x]}n.asArray=u;function p(x,O,F){const H=C(x,O),V=x.length,t=F.length;x.length=V+t;for(let ne=V-1;ne>=H;ne--)x[ne+t]=x[ne];for(let ne=0;ne<t;ne++)x[ne+H]=F[ne]}n.insertInto=p;function b(x,O,F,H){const V=C(x,O);let t=x.splice(V,F);return t===void 0&&(t=[]),p(x,V,H),t}n.splice=b;function C(x,O){return O<0?Math.max(O+x.length,0):Math.min(O,x.length)}var N;(function(x){function O(t){return t<0}x.isLessThan=O;function F(t){return t<=0}x.isLessThanOrEqual=F;function H(t){return t>0}x.isGreaterThan=H;function V(t){return t===0}x.isNeitherLessOrGreaterThan=V,x.greaterThan=1,x.lessThan=-1,x.neitherLessOrGreaterThan=0})(N||(n.CompareResult=N={}));function R(x,O){return(F,H)=>O(x(F),x(H))}n.compareBy=R;function D(...x){return(O,F)=>{for(const H of x){const V=H(O,F);if(!N.isNeitherLessOrGreaterThan(V))return V}return N.neitherLessOrGreaterThan}}n.tieBreakComparators=D;const k=(x,O)=>x-O;n.numberComparator=k;const U=(x,O)=>(0,n.numberComparator)(x?1:0,O?1:0);n.booleanComparator=U;function I(x){return(O,F)=>-x(O,F)}n.reverseOrder=I;class B{constructor(O){this.items=O,this.firstIdx=0,this.lastIdx=this.items.length-1}get length(){return this.lastIdx-this.firstIdx+1}takeWhile(O){let F=this.firstIdx;for(;F<this.items.length&&O(this.items[F]);)F++;const H=F===this.firstIdx?null:this.items.slice(this.firstIdx,F);return this.firstIdx=F,H}takeFromEndWhile(O){let F=this.lastIdx;for(;F>=0&&O(this.items[F]);)F--;const H=F===this.lastIdx?null:this.items.slice(F+1,this.lastIdx+1);return this.lastIdx=F,H}peek(){if(this.length!==0)return this.items[this.firstIdx]}dequeue(){const O=this.items[this.firstIdx];return this.firstIdx++,O}takeCount(O){const F=this.items.slice(this.firstIdx,this.firstIdx+O);return this.firstIdx+=O,F}}n.ArrayQueue=B;class z{constructor(O){this.iterate=O}toArray(){const O=[];return this.iterate(F=>(O.push(F),!0)),O}filter(O){return new z(F=>this.iterate(H=>O(H)?F(H):!0))}map(O){return new z(F=>this.iterate(H=>F(O(H))))}findLast(O){let F;return this.iterate(H=>(O(H)&&(F=H),!0)),F}findLastMaxBy(O){let F,H=!0;return this.iterate(V=>((H||N.isGreaterThan(O(V,F)))&&(H=!1,F=V),!0)),F}}n.CallbackIterable=z,z.empty=new z(x=>{})}),Y(X[11],J([0,1]),function(T,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.mapFindFirst=n.findMaxIdxBy=n.findFirstMinBy=n.findLastMaxBy=n.findFirstMaxBy=n.MonotonousArray=n.findFirstIdxMonotonousOrArrLen=n.findFirstMonotonous=n.findLastIdxMonotonous=n.findLastMonotonous=n.findLastIdx=n.findLast=void 0;function M(l,f,g){const S=A(l,f);if(S!==-1)return l[S]}n.findLast=M;function A(l,f,g=l.length-1){for(let S=g;S>=0;S--){const _=l[S];if(f(_))return S}return-1}n.findLastIdx=A;function i(l,f){const g=d(l,f);return g===-1?void 0:l[g]}n.findLastMonotonous=i;function d(l,f,g=0,S=l.length){let _=g,E=S;for(;_<E;){const y=Math.floor((_+E)/2);f(l[y])?_=y+1:E=y}return _-1}n.findLastIdxMonotonous=d;function m(l,f){const g=L(l,f);return g===l.length?void 0:l[g]}n.findFirstMonotonous=m;function L(l,f,g=0,S=l.length){let _=g,E=S;for(;_<E;){const y=Math.floor((_+E)/2);f(l[y])?E=y:_=y+1}return _}n.findFirstIdxMonotonousOrArrLen=L;class h{constructor(f){this._array=f,this._findLastMonotonousLastIdx=0}findLastMonotonous(f){if(h.assertInvariants){if(this._prevFindLastPredicate){for(const S of this._array)if(this._prevFindLastPredicate(S)&&!f(S))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.")}this._prevFindLastPredicate=f}const g=d(this._array,f,this._findLastMonotonousLastIdx);return this._findLastMonotonousLastIdx=g+1,g===-1?void 0:this._array[g]}}n.MonotonousArray=h,h.assertInvariants=!1;function o(l,f){if(l.length===0)return;let g=l[0];for(let S=1;S<l.length;S++){const _=l[S];f(_,g)>0&&(g=_)}return g}n.findFirstMaxBy=o;function w(l,f){if(l.length===0)return;let g=l[0];for(let S=1;S<l.length;S++){const _=l[S];f(_,g)>=0&&(g=_)}return g}n.findLastMaxBy=w;function e(l,f){return o(l,(g,S)=>-f(g,S))}n.findFirstMinBy=e;function a(l,f){if(l.length===0)return-1;let g=0;for(let S=1;S<l.length;S++){const _=l[S];f(_,l[g])>0&&(g=S)}return g}n.findMaxIdxBy=a;function c(l,f){for(const g of l){const S=f(g);if(S!==void 0)return S}}n.mapFindFirst=c}),Y(X[32],J([0,1]),function(T,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.CachedFunction=n.LRUCachedFunction=void 0;class M{constructor(d){this.fn=d,this.lastCache=void 0,this.lastArgKey=void 0}get(d){const m=JSON.stringify(d);return this.lastArgKey!==m&&(this.lastArgKey=m,this.lastCache=this.fn(d)),this.lastCache}}n.LRUCachedFunction=M;class A{get cachedValues(){return this._map}constructor(d){this.fn=d,this._map=new Map}get(d){if(this._map.has(d))return this._map.get(d);const m=this.fn(d);return this._map.set(d,m),m}}n.CachedFunction=A}),Y(X[33],J([0,1]),function(T,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Color=n.HSVA=n.HSLA=n.RGBA=void 0;function M(L,h){const o=Math.pow(10,h);return Math.round(L*o)/o}class A{constructor(h,o,w,e=1){this._rgbaBrand=void 0,this.r=Math.min(255,Math.max(0,h))|0,this.g=Math.min(255,Math.max(0,o))|0,this.b=Math.min(255,Math.max(0,w))|0,this.a=M(Math.max(Math.min(1,e),0),3)}static equals(h,o){return h.r===o.r&&h.g===o.g&&h.b===o.b&&h.a===o.a}}n.RGBA=A;class i{constructor(h,o,w,e){this._hslaBrand=void 0,this.h=Math.max(Math.min(360,h),0)|0,this.s=M(Math.max(Math.min(1,o),0),3),this.l=M(Math.max(Math.min(1,w),0),3),this.a=M(Math.max(Math.min(1,e),0),3)}static equals(h,o){return h.h===o.h&&h.s===o.s&&h.l===o.l&&h.a===o.a}static fromRGBA(h){const o=h.r/255,w=h.g/255,e=h.b/255,a=h.a,c=Math.max(o,w,e),l=Math.min(o,w,e);let f=0,g=0;const S=(l+c)/2,_=c-l;if(_>0){switch(g=Math.min(S<=.5?_/(2*S):_/(2-2*S),1),c){case o:f=(w-e)/_+(w<e?6:0);break;case w:f=(e-o)/_+2;break;case e:f=(o-w)/_+4;break}f*=60,f=Math.round(f)}return new i(f,g,S,a)}static _hue2rgb(h,o,w){return w<0&&(w+=1),w>1&&(w-=1),w<1/6?h+(o-h)*6*w:w<1/2?o:w<2/3?h+(o-h)*(2/3-w)*6:h}static toRGBA(h){const o=h.h/360,{s:w,l:e,a}=h;let c,l,f;if(w===0)c=l=f=e;else{const g=e<.5?e*(1+w):e+w-e*w,S=2*e-g;c=i._hue2rgb(S,g,o+1/3),l=i._hue2rgb(S,g,o),f=i._hue2rgb(S,g,o-1/3)}return new A(Math.round(c*255),Math.round(l*255),Math.round(f*255),a)}}n.HSLA=i;class d{constructor(h,o,w,e){this._hsvaBrand=void 0,this.h=Math.max(Math.min(360,h),0)|0,this.s=M(Math.max(Math.min(1,o),0),3),this.v=M(Math.max(Math.min(1,w),0),3),this.a=M(Math.max(Math.min(1,e),0),3)}static equals(h,o){return h.h===o.h&&h.s===o.s&&h.v===o.v&&h.a===o.a}static fromRGBA(h){const o=h.r/255,w=h.g/255,e=h.b/255,a=Math.max(o,w,e),c=Math.min(o,w,e),l=a-c,f=a===0?0:l/a;let g;return l===0?g=0:a===o?g=((w-e)/l%6+6)%6:a===w?g=(e-o)/l+2:g=(o-w)/l+4,new d(Math.round(g*60),f,a,h.a)}static toRGBA(h){const{h:o,s:w,v:e,a}=h,c=e*w,l=c*(1-Math.abs(o/60%2-1)),f=e-c;let[g,S,_]=[0,0,0];return o<60?(g=c,S=l):o<120?(g=l,S=c):o<180?(S=c,_=l):o<240?(S=l,_=c):o<300?(g=l,_=c):o<=360&&(g=c,_=l),g=Math.round((g+f)*255),S=Math.round((S+f)*255),_=Math.round((_+f)*255),new A(g,S,_,a)}}n.HSVA=d;class m{static fromHex(h){return m.Format.CSS.parseHex(h)||m.red}static equals(h,o){return!h&&!o?!0:!h||!o?!1:h.equals(o)}get hsla(){return this._hsla?this._hsla:i.fromRGBA(this.rgba)}get hsva(){return this._hsva?this._hsva:d.fromRGBA(this.rgba)}constructor(h){if(h)if(h instanceof A)this.rgba=h;else if(h instanceof i)this._hsla=h,this.rgba=i.toRGBA(h);else if(h instanceof d)this._hsva=h,this.rgba=d.toRGBA(h);else throw new Error("Invalid color ctor argument");else throw new Error("Color needs a value")}equals(h){return!!h&&A.equals(this.rgba,h.rgba)&&i.equals(this.hsla,h.hsla)&&d.equals(this.hsva,h.hsva)}getRelativeLuminance(){const h=m._relativeLuminanceForComponent(this.rgba.r),o=m._relativeLuminanceForComponent(this.rgba.g),w=m._relativeLuminanceForComponent(this.rgba.b),e=.2126*h+.7152*o+.0722*w;return M(e,4)}static _relativeLuminanceForComponent(h){const o=h/255;return o<=.03928?o/12.92:Math.pow((o+.055)/1.055,2.4)}isLighter(){return(this.rgba.r*299+this.rgba.g*587+this.rgba.b*114)/1e3>=128}isLighterThan(h){const o=this.getRelativeLuminance(),w=h.getRelativeLuminance();return o>w}isDarkerThan(h){const o=this.getRelativeLuminance(),w=h.getRelativeLuminance();return o<w}lighten(h){return new m(new i(this.hsla.h,this.hsla.s,this.hsla.l+this.hsla.l*h,this.hsla.a))}darken(h){return new m(new i(this.hsla.h,this.hsla.s,this.hsla.l-this.hsla.l*h,this.hsla.a))}transparent(h){const{r:o,g:w,b:e,a}=this.rgba;return new m(new A(o,w,e,a*h))}isTransparent(){return this.rgba.a===0}isOpaque(){return this.rgba.a===1}opposite(){return new m(new A(255-this.rgba.r,255-this.rgba.g,255-this.rgba.b,this.rgba.a))}makeOpaque(h){if(this.isOpaque()||h.rgba.a!==1)return this;const{r:o,g:w,b:e,a}=this.rgba;return new m(new A(h.rgba.r-a*(h.rgba.r-o),h.rgba.g-a*(h.rgba.g-w),h.rgba.b-a*(h.rgba.b-e),1))}toString(){return this._toString||(this._toString=m.Format.CSS.format(this)),this._toString}static getLighterColor(h,o,w){if(h.isLighterThan(o))return h;w=w||.5;const e=h.getRelativeLuminance(),a=o.getRelativeLuminance();return w=w*(a-e)/a,h.lighten(w)}static getDarkerColor(h,o,w){if(h.isDarkerThan(o))return h;w=w||.5;const e=h.getRelativeLuminance(),a=o.getRelativeLuminance();return w=w*(e-a)/e,h.darken(w)}}n.Color=m,m.white=new m(new A(255,255,255,1)),m.black=new m(new A(0,0,0,1)),m.red=new m(new A(255,0,0,1)),m.blue=new m(new A(0,0,255,1)),m.green=new m(new A(0,255,0,1)),m.cyan=new m(new A(0,255,255,1)),m.lightgrey=new m(new A(211,211,211,1)),m.transparent=new m(new A(0,0,0,0)),function(L){let h;(function(o){let w;(function(e){function a(r){return r.rgba.a===1?`rgb(${r.rgba.r}, ${r.rgba.g}, ${r.rgba.b})`:L.Format.CSS.formatRGBA(r)}e.formatRGB=a;function c(r){return`rgba(${r.rgba.r}, ${r.rgba.g}, ${r.rgba.b}, ${+r.rgba.a.toFixed(2)})`}e.formatRGBA=c;function l(r){return r.hsla.a===1?`hsl(${r.hsla.h}, ${(r.hsla.s*100).toFixed(2)}%, ${(r.hsla.l*100).toFixed(2)}%)`:L.Format.CSS.formatHSLA(r)}e.formatHSL=l;function f(r){return`hsla(${r.hsla.h}, ${(r.hsla.s*100).toFixed(2)}%, ${(r.hsla.l*100).toFixed(2)}%, ${r.hsla.a.toFixed(2)})`}e.formatHSLA=f;function g(r){const s=r.toString(16);return s.length!==2?"0"+s:s}function S(r){return`#${g(r.rgba.r)}${g(r.rgba.g)}${g(r.rgba.b)}`}e.formatHex=S;function _(r,s=!1){return s&&r.rgba.a===1?L.Format.CSS.formatHex(r):`#${g(r.rgba.r)}${g(r.rgba.g)}${g(r.rgba.b)}${g(Math.round(r.rgba.a*255))}`}e.formatHexA=_;function E(r){return r.isOpaque()?L.Format.CSS.formatHex(r):L.Format.CSS.formatRGBA(r)}e.format=E;function y(r){const s=r.length;if(s===0||r.charCodeAt(0)!==35)return null;if(s===7){const u=16*v(r.charCodeAt(1))+v(r.charCodeAt(2)),p=16*v(r.charCodeAt(3))+v(r.charCodeAt(4)),b=16*v(r.charCodeAt(5))+v(r.charCodeAt(6));return new L(new A(u,p,b,1))}if(s===9){const u=16*v(r.charCodeAt(1))+v(r.charCodeAt(2)),p=16*v(r.charCodeAt(3))+v(r.charCodeAt(4)),b=16*v(r.charCodeAt(5))+v(r.charCodeAt(6)),C=16*v(r.charCodeAt(7))+v(r.charCodeAt(8));return new L(new A(u,p,b,C/255))}if(s===4){const u=v(r.charCodeAt(1)),p=v(r.charCodeAt(2)),b=v(r.charCodeAt(3));return new L(new A(16*u+u,16*p+p,16*b+b))}if(s===5){const u=v(r.charCodeAt(1)),p=v(r.charCodeAt(2)),b=v(r.charCodeAt(3)),C=v(r.charCodeAt(4));return new L(new A(16*u+u,16*p+p,16*b+b,(16*C+C)/255))}return null}e.parseHex=y;function v(r){switch(r){case 48:return 0;case 49:return 1;case 50:return 2;case 51:return 3;case 52:return 4;case 53:return 5;case 54:return 6;case 55:return 7;case 56:return 8;case 57:return 9;case 97:return 10;case 65:return 10;case 98:return 11;case 66:return 11;case 99:return 12;case 67:return 12;case 100:return 13;case 68:return 13;case 101:return 14;case 69:return 14;case 102:return 15;case 70:return 15}return 0}})(w=o.CSS||(o.CSS={}))})(h=L.Format||(L.Format={}))}(m||(n.Color=m={}))}),Y(X[34],J([0,1]),function(T,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.DiffChange=void 0;class M{constructor(i,d,m,L){this.originalStart=i,this.originalLength=d,this.modifiedStart=m,this.modifiedLength=L}getOriginalEnd(){return this.originalStart+this.originalLength}getModifiedEnd(){return this.modifiedStart+this.modifiedLength}}n.DiffChange=M}),Y(X[5],J([0,1]),function(T,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.BugIndicatingError=n.ErrorNoTelemetry=n.NotSupportedError=n.illegalState=n.illegalArgument=n.canceled=n.CancellationError=n.isCancellationError=n.transformErrorForSerialization=n.onUnexpectedExternalError=n.onUnexpectedError=n.errorHandler=n.ErrorHandler=void 0;class M{constructor(){this.listeners=[],this.unexpectedErrorHandler=function(g){setTimeout(()=>{throw g.stack?c.isErrorNoTelemetry(g)?new c(g.message+`

`+g.stack):new Error(g.message+`

`+g.stack):g},0)}}emit(g){this.listeners.forEach(S=>{S(g)})}onUnexpectedError(g){this.unexpectedErrorHandler(g),this.emit(g)}onUnexpectedExternalError(g){this.unexpectedErrorHandler(g)}}n.ErrorHandler=M,n.errorHandler=new M;function A(f){L(f)||n.errorHandler.onUnexpectedError(f)}n.onUnexpectedError=A;function i(f){L(f)||n.errorHandler.onUnexpectedExternalError(f)}n.onUnexpectedExternalError=i;function d(f){if(f instanceof Error){const{name:g,message:S}=f,_=f.stacktrace||f.stack;return{$isError:!0,name:g,message:S,stack:_,noTelemetry:c.isErrorNoTelemetry(f)}}return f}n.transformErrorForSerialization=d;const m="Canceled";function L(f){return f instanceof h?!0:f instanceof Error&&f.name===m&&f.message===m}n.isCancellationError=L;class h extends Error{constructor(){super(m),this.name=this.message}}n.CancellationError=h;function o(){const f=new Error(m);return f.name=f.message,f}n.canceled=o;function w(f){return f?new Error(`Illegal argument: ${f}`):new Error("Illegal argument")}n.illegalArgument=w;function e(f){return f?new Error(`Illegal state: ${f}`):new Error("Illegal state")}n.illegalState=e;class a extends Error{constructor(g){super("NotSupported"),g&&(this.message=g)}}n.NotSupportedError=a;class c extends Error{constructor(g){super(g),this.name="CodeExpectedError"}static fromError(g){if(g instanceof c)return g;const S=new c;return S.message=g.message,S.stack=g.stack,S}static isErrorNoTelemetry(g){return g.name==="CodeExpectedError"}}n.ErrorNoTelemetry=c;class l extends Error{constructor(g){super(g||"An unexpected bug occurred."),Object.setPrototypeOf(this,l.prototype)}}n.BugIndicatingError=l}),Y(X[12],J([0,1,5]),function(T,n,M){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.checkAdjacentItems=n.assertFn=n.assertNever=n.ok=void 0;function A(L,h){if(!L)throw new Error(h?`Assertion failed (${h})`:"Assertion Failed")}n.ok=A;function i(L,h="Unreachable"){throw new Error(h)}n.assertNever=i;function d(L){if(!L()){debugger;L(),(0,M.onUnexpectedError)(new M.BugIndicatingError("Assertion Failed"))}}n.assertFn=d;function m(L,h){let o=0;for(;o<L.length-1;){const w=L[o],e=L[o+1];if(!h(w,e))return!1;o++}return!0}n.checkAdjacentItems=m}),Y(X[20],J([0,1]),function(T,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.createSingleCallFunction=void 0;function M(A){const i=this;let d=!1,m;return function(){return d||(d=!0,m=A.apply(i,arguments)),m}}n.createSingleCallFunction=M}),Y(X[21],J([0,1]),function(T,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Iterable=void 0;var M;(function(A){function i(v){return v&&typeof v=="object"&&typeof v[Symbol.iterator]=="function"}A.is=i;const d=Object.freeze([]);function m(){return d}A.empty=m;function*L(v){yield v}A.single=L;function h(v){return i(v)?v:L(v)}A.wrap=h;function o(v){return v||d}A.from=o;function*w(v){for(let r=v.length-1;r>=0;r--)yield v[r]}A.reverse=w;function e(v){return!v||v[Symbol.iterator]().next().done===!0}A.isEmpty=e;function a(v){return v[Symbol.iterator]().next().value}A.first=a;function c(v,r){for(const s of v)if(r(s))return!0;return!1}A.some=c;function l(v,r){for(const s of v)if(r(s))return s}A.find=l;function*f(v,r){for(const s of v)r(s)&&(yield s)}A.filter=f;function*g(v,r){let s=0;for(const u of v)yield r(u,s++)}A.map=g;function*S(...v){for(const r of v)for(const s of r)yield s}A.concat=S;function _(v,r,s){let u=s;for(const p of v)u=r(u,p);return u}A.reduce=_;function*E(v,r,s=v.length){for(r<0&&(r+=v.length),s<0?s+=v.length:s>v.length&&(s=v.length);r<s;r++)yield v[r]}A.slice=E;function y(v,r=Number.POSITIVE_INFINITY){const s=[];if(r===0)return[s,v];const u=v[Symbol.iterator]();for(let p=0;p<r;p++){const b=u.next();if(b.done)return[s,A.empty()];s.push(b.value)}return[s,{[Symbol.iterator](){return u}}]}A.consume=y})(M||(n.Iterable=M={}))}),Y(X[35],J([0,1]),function(T,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.KeyChord=n.KeyCodeUtils=n.IMMUTABLE_KEY_CODE_TO_CODE=n.IMMUTABLE_CODE_TO_KEY_CODE=n.NATIVE_WINDOWS_KEY_CODE_TO_KEY_CODE=n.EVENT_KEY_CODE_MAP=void 0;class M{constructor(){this._keyCodeToStr=[],this._strToKeyCode=Object.create(null)}define(a,c){this._keyCodeToStr[a]=c,this._strToKeyCode[c.toLowerCase()]=a}keyCodeToStr(a){return this._keyCodeToStr[a]}strToKeyCode(a){return this._strToKeyCode[a.toLowerCase()]||0}}const A=new M,i=new M,d=new M;n.EVENT_KEY_CODE_MAP=new Array(230),n.NATIVE_WINDOWS_KEY_CODE_TO_KEY_CODE={};const m=[],L=Object.create(null),h=Object.create(null);n.IMMUTABLE_CODE_TO_KEY_CODE=[],n.IMMUTABLE_KEY_CODE_TO_CODE=[];for(let e=0;e<=193;e++)n.IMMUTABLE_CODE_TO_KEY_CODE[e]=-1;for(let e=0;e<=132;e++)n.IMMUTABLE_KEY_CODE_TO_CODE[e]=-1;(function(){const e="",a=[[1,0,"None",0,"unknown",0,"VK_UNKNOWN",e,e],[1,1,"Hyper",0,e,0,e,e,e],[1,2,"Super",0,e,0,e,e,e],[1,3,"Fn",0,e,0,e,e,e],[1,4,"FnLock",0,e,0,e,e,e],[1,5,"Suspend",0,e,0,e,e,e],[1,6,"Resume",0,e,0,e,e,e],[1,7,"Turbo",0,e,0,e,e,e],[1,8,"Sleep",0,e,0,"VK_SLEEP",e,e],[1,9,"WakeUp",0,e,0,e,e,e],[0,10,"KeyA",31,"A",65,"VK_A",e,e],[0,11,"KeyB",32,"B",66,"VK_B",e,e],[0,12,"KeyC",33,"C",67,"VK_C",e,e],[0,13,"KeyD",34,"D",68,"VK_D",e,e],[0,14,"KeyE",35,"E",69,"VK_E",e,e],[0,15,"KeyF",36,"F",70,"VK_F",e,e],[0,16,"KeyG",37,"G",71,"VK_G",e,e],[0,17,"KeyH",38,"H",72,"VK_H",e,e],[0,18,"KeyI",39,"I",73,"VK_I",e,e],[0,19,"KeyJ",40,"J",74,"VK_J",e,e],[0,20,"KeyK",41,"K",75,"VK_K",e,e],[0,21,"KeyL",42,"L",76,"VK_L",e,e],[0,22,"KeyM",43,"M",77,"VK_M",e,e],[0,23,"KeyN",44,"N",78,"VK_N",e,e],[0,24,"KeyO",45,"O",79,"VK_O",e,e],[0,25,"KeyP",46,"P",80,"VK_P",e,e],[0,26,"KeyQ",47,"Q",81,"VK_Q",e,e],[0,27,"KeyR",48,"R",82,"VK_R",e,e],[0,28,"KeyS",49,"S",83,"VK_S",e,e],[0,29,"KeyT",50,"T",84,"VK_T",e,e],[0,30,"KeyU",51,"U",85,"VK_U",e,e],[0,31,"KeyV",52,"V",86,"VK_V",e,e],[0,32,"KeyW",53,"W",87,"VK_W",e,e],[0,33,"KeyX",54,"X",88,"VK_X",e,e],[0,34,"KeyY",55,"Y",89,"VK_Y",e,e],[0,35,"KeyZ",56,"Z",90,"VK_Z",e,e],[0,36,"Digit1",22,"1",49,"VK_1",e,e],[0,37,"Digit2",23,"2",50,"VK_2",e,e],[0,38,"Digit3",24,"3",51,"VK_3",e,e],[0,39,"Digit4",25,"4",52,"VK_4",e,e],[0,40,"Digit5",26,"5",53,"VK_5",e,e],[0,41,"Digit6",27,"6",54,"VK_6",e,e],[0,42,"Digit7",28,"7",55,"VK_7",e,e],[0,43,"Digit8",29,"8",56,"VK_8",e,e],[0,44,"Digit9",30,"9",57,"VK_9",e,e],[0,45,"Digit0",21,"0",48,"VK_0",e,e],[1,46,"Enter",3,"Enter",13,"VK_RETURN",e,e],[1,47,"Escape",9,"Escape",27,"VK_ESCAPE",e,e],[1,48,"Backspace",1,"Backspace",8,"VK_BACK",e,e],[1,49,"Tab",2,"Tab",9,"VK_TAB",e,e],[1,50,"Space",10,"Space",32,"VK_SPACE",e,e],[0,51,"Minus",88,"-",189,"VK_OEM_MINUS","-","OEM_MINUS"],[0,52,"Equal",86,"=",187,"VK_OEM_PLUS","=","OEM_PLUS"],[0,53,"BracketLeft",92,"[",219,"VK_OEM_4","[","OEM_4"],[0,54,"BracketRight",94,"]",221,"VK_OEM_6","]","OEM_6"],[0,55,"Backslash",93,"\\",220,"VK_OEM_5","\\","OEM_5"],[0,56,"IntlHash",0,e,0,e,e,e],[0,57,"Semicolon",85,";",186,"VK_OEM_1",";","OEM_1"],[0,58,"Quote",95,"'",222,"VK_OEM_7","'","OEM_7"],[0,59,"Backquote",91,"`",192,"VK_OEM_3","`","OEM_3"],[0,60,"Comma",87,",",188,"VK_OEM_COMMA",",","OEM_COMMA"],[0,61,"Period",89,".",190,"VK_OEM_PERIOD",".","OEM_PERIOD"],[0,62,"Slash",90,"/",191,"VK_OEM_2","/","OEM_2"],[1,63,"CapsLock",8,"CapsLock",20,"VK_CAPITAL",e,e],[1,64,"F1",59,"F1",112,"VK_F1",e,e],[1,65,"F2",60,"F2",113,"VK_F2",e,e],[1,66,"F3",61,"F3",114,"VK_F3",e,e],[1,67,"F4",62,"F4",115,"VK_F4",e,e],[1,68,"F5",63,"F5",116,"VK_F5",e,e],[1,69,"F6",64,"F6",117,"VK_F6",e,e],[1,70,"F7",65,"F7",118,"VK_F7",e,e],[1,71,"F8",66,"F8",119,"VK_F8",e,e],[1,72,"F9",67,"F9",120,"VK_F9",e,e],[1,73,"F10",68,"F10",121,"VK_F10",e,e],[1,74,"F11",69,"F11",122,"VK_F11",e,e],[1,75,"F12",70,"F12",123,"VK_F12",e,e],[1,76,"PrintScreen",0,e,0,e,e,e],[1,77,"ScrollLock",84,"ScrollLock",145,"VK_SCROLL",e,e],[1,78,"Pause",7,"PauseBreak",19,"VK_PAUSE",e,e],[1,79,"Insert",19,"Insert",45,"VK_INSERT",e,e],[1,80,"Home",14,"Home",36,"VK_HOME",e,e],[1,81,"PageUp",11,"PageUp",33,"VK_PRIOR",e,e],[1,82,"Delete",20,"Delete",46,"VK_DELETE",e,e],[1,83,"End",13,"End",35,"VK_END",e,e],[1,84,"PageDown",12,"PageDown",34,"VK_NEXT",e,e],[1,85,"ArrowRight",17,"RightArrow",39,"VK_RIGHT","Right",e],[1,86,"ArrowLeft",15,"LeftArrow",37,"VK_LEFT","Left",e],[1,87,"ArrowDown",18,"DownArrow",40,"VK_DOWN","Down",e],[1,88,"ArrowUp",16,"UpArrow",38,"VK_UP","Up",e],[1,89,"NumLock",83,"NumLock",144,"VK_NUMLOCK",e,e],[1,90,"NumpadDivide",113,"NumPad_Divide",111,"VK_DIVIDE",e,e],[1,91,"NumpadMultiply",108,"NumPad_Multiply",106,"VK_MULTIPLY",e,e],[1,92,"NumpadSubtract",111,"NumPad_Subtract",109,"VK_SUBTRACT",e,e],[1,93,"NumpadAdd",109,"NumPad_Add",107,"VK_ADD",e,e],[1,94,"NumpadEnter",3,e,0,e,e,e],[1,95,"Numpad1",99,"NumPad1",97,"VK_NUMPAD1",e,e],[1,96,"Numpad2",100,"NumPad2",98,"VK_NUMPAD2",e,e],[1,97,"Numpad3",101,"NumPad3",99,"VK_NUMPAD3",e,e],[1,98,"Numpad4",102,"NumPad4",100,"VK_NUMPAD4",e,e],[1,99,"Numpad5",103,"NumPad5",101,"VK_NUMPAD5",e,e],[1,100,"Numpad6",104,"NumPad6",102,"VK_NUMPAD6",e,e],[1,101,"Numpad7",105,"NumPad7",103,"VK_NUMPAD7",e,e],[1,102,"Numpad8",106,"NumPad8",104,"VK_NUMPAD8",e,e],[1,103,"Numpad9",107,"NumPad9",105,"VK_NUMPAD9",e,e],[1,104,"Numpad0",98,"NumPad0",96,"VK_NUMPAD0",e,e],[1,105,"NumpadDecimal",112,"NumPad_Decimal",110,"VK_DECIMAL",e,e],[0,106,"IntlBackslash",97,"OEM_102",226,"VK_OEM_102",e,e],[1,107,"ContextMenu",58,"ContextMenu",93,e,e,e],[1,108,"Power",0,e,0,e,e,e],[1,109,"NumpadEqual",0,e,0,e,e,e],[1,110,"F13",71,"F13",124,"VK_F13",e,e],[1,111,"F14",72,"F14",125,"VK_F14",e,e],[1,112,"F15",73,"F15",126,"VK_F15",e,e],[1,113,"F16",74,"F16",127,"VK_F16",e,e],[1,114,"F17",75,"F17",128,"VK_F17",e,e],[1,115,"F18",76,"F18",129,"VK_F18",e,e],[1,116,"F19",77,"F19",130,"VK_F19",e,e],[1,117,"F20",78,"F20",131,"VK_F20",e,e],[1,118,"F21",79,"F21",132,"VK_F21",e,e],[1,119,"F22",80,"F22",133,"VK_F22",e,e],[1,120,"F23",81,"F23",134,"VK_F23",e,e],[1,121,"F24",82,"F24",135,"VK_F24",e,e],[1,122,"Open",0,e,0,e,e,e],[1,123,"Help",0,e,0,e,e,e],[1,124,"Select",0,e,0,e,e,e],[1,125,"Again",0,e,0,e,e,e],[1,126,"Undo",0,e,0,e,e,e],[1,127,"Cut",0,e,0,e,e,e],[1,128,"Copy",0,e,0,e,e,e],[1,129,"Paste",0,e,0,e,e,e],[1,130,"Find",0,e,0,e,e,e],[1,131,"AudioVolumeMute",117,"AudioVolumeMute",173,"VK_VOLUME_MUTE",e,e],[1,132,"AudioVolumeUp",118,"AudioVolumeUp",175,"VK_VOLUME_UP",e,e],[1,133,"AudioVolumeDown",119,"AudioVolumeDown",174,"VK_VOLUME_DOWN",e,e],[1,134,"NumpadComma",110,"NumPad_Separator",108,"VK_SEPARATOR",e,e],[0,135,"IntlRo",115,"ABNT_C1",193,"VK_ABNT_C1",e,e],[1,136,"KanaMode",0,e,0,e,e,e],[0,137,"IntlYen",0,e,0,e,e,e],[1,138,"Convert",0,e,0,e,e,e],[1,139,"NonConvert",0,e,0,e,e,e],[1,140,"Lang1",0,e,0,e,e,e],[1,141,"Lang2",0,e,0,e,e,e],[1,142,"Lang3",0,e,0,e,e,e],[1,143,"Lang4",0,e,0,e,e,e],[1,144,"Lang5",0,e,0,e,e,e],[1,145,"Abort",0,e,0,e,e,e],[1,146,"Props",0,e,0,e,e,e],[1,147,"NumpadParenLeft",0,e,0,e,e,e],[1,148,"NumpadParenRight",0,e,0,e,e,e],[1,149,"NumpadBackspace",0,e,0,e,e,e],[1,150,"NumpadMemoryStore",0,e,0,e,e,e],[1,151,"NumpadMemoryRecall",0,e,0,e,e,e],[1,152,"NumpadMemoryClear",0,e,0,e,e,e],[1,153,"NumpadMemoryAdd",0,e,0,e,e,e],[1,154,"NumpadMemorySubtract",0,e,0,e,e,e],[1,155,"NumpadClear",131,"Clear",12,"VK_CLEAR",e,e],[1,156,"NumpadClearEntry",0,e,0,e,e,e],[1,0,e,5,"Ctrl",17,"VK_CONTROL",e,e],[1,0,e,4,"Shift",16,"VK_SHIFT",e,e],[1,0,e,6,"Alt",18,"VK_MENU",e,e],[1,0,e,57,"Meta",91,"VK_COMMAND",e,e],[1,157,"ControlLeft",5,e,0,"VK_LCONTROL",e,e],[1,158,"ShiftLeft",4,e,0,"VK_LSHIFT",e,e],[1,159,"AltLeft",6,e,0,"VK_LMENU",e,e],[1,160,"MetaLeft",57,e,0,"VK_LWIN",e,e],[1,161,"ControlRight",5,e,0,"VK_RCONTROL",e,e],[1,162,"ShiftRight",4,e,0,"VK_RSHIFT",e,e],[1,163,"AltRight",6,e,0,"VK_RMENU",e,e],[1,164,"MetaRight",57,e,0,"VK_RWIN",e,e],[1,165,"BrightnessUp",0,e,0,e,e,e],[1,166,"BrightnessDown",0,e,0,e,e,e],[1,167,"MediaPlay",0,e,0,e,e,e],[1,168,"MediaRecord",0,e,0,e,e,e],[1,169,"MediaFastForward",0,e,0,e,e,e],[1,170,"MediaRewind",0,e,0,e,e,e],[1,171,"MediaTrackNext",124,"MediaTrackNext",176,"VK_MEDIA_NEXT_TRACK",e,e],[1,172,"MediaTrackPrevious",125,"MediaTrackPrevious",177,"VK_MEDIA_PREV_TRACK",e,e],[1,173,"MediaStop",126,"MediaStop",178,"VK_MEDIA_STOP",e,e],[1,174,"Eject",0,e,0,e,e,e],[1,175,"MediaPlayPause",127,"MediaPlayPause",179,"VK_MEDIA_PLAY_PAUSE",e,e],[1,176,"MediaSelect",128,"LaunchMediaPlayer",181,"VK_MEDIA_LAUNCH_MEDIA_SELECT",e,e],[1,177,"LaunchMail",129,"LaunchMail",180,"VK_MEDIA_LAUNCH_MAIL",e,e],[1,178,"LaunchApp2",130,"LaunchApp2",183,"VK_MEDIA_LAUNCH_APP2",e,e],[1,179,"LaunchApp1",0,e,0,"VK_MEDIA_LAUNCH_APP1",e,e],[1,180,"SelectTask",0,e,0,e,e,e],[1,181,"LaunchScreenSaver",0,e,0,e,e,e],[1,182,"BrowserSearch",120,"BrowserSearch",170,"VK_BROWSER_SEARCH",e,e],[1,183,"BrowserHome",121,"BrowserHome",172,"VK_BROWSER_HOME",e,e],[1,184,"BrowserBack",122,"BrowserBack",166,"VK_BROWSER_BACK",e,e],[1,185,"BrowserForward",123,"BrowserForward",167,"VK_BROWSER_FORWARD",e,e],[1,186,"BrowserStop",0,e,0,"VK_BROWSER_STOP",e,e],[1,187,"BrowserRefresh",0,e,0,"VK_BROWSER_REFRESH",e,e],[1,188,"BrowserFavorites",0,e,0,"VK_BROWSER_FAVORITES",e,e],[1,189,"ZoomToggle",0,e,0,e,e,e],[1,190,"MailReply",0,e,0,e,e,e],[1,191,"MailForward",0,e,0,e,e,e],[1,192,"MailSend",0,e,0,e,e,e],[1,0,e,114,"KeyInComposition",229,e,e,e],[1,0,e,116,"ABNT_C2",194,"VK_ABNT_C2",e,e],[1,0,e,96,"OEM_8",223,"VK_OEM_8",e,e],[1,0,e,0,e,0,"VK_KANA",e,e],[1,0,e,0,e,0,"VK_HANGUL",e,e],[1,0,e,0,e,0,"VK_JUNJA",e,e],[1,0,e,0,e,0,"VK_FINAL",e,e],[1,0,e,0,e,0,"VK_HANJA",e,e],[1,0,e,0,e,0,"VK_KANJI",e,e],[1,0,e,0,e,0,"VK_CONVERT",e,e],[1,0,e,0,e,0,"VK_NONCONVERT",e,e],[1,0,e,0,e,0,"VK_ACCEPT",e,e],[1,0,e,0,e,0,"VK_MODECHANGE",e,e],[1,0,e,0,e,0,"VK_SELECT",e,e],[1,0,e,0,e,0,"VK_PRINT",e,e],[1,0,e,0,e,0,"VK_EXECUTE",e,e],[1,0,e,0,e,0,"VK_SNAPSHOT",e,e],[1,0,e,0,e,0,"VK_HELP",e,e],[1,0,e,0,e,0,"VK_APPS",e,e],[1,0,e,0,e,0,"VK_PROCESSKEY",e,e],[1,0,e,0,e,0,"VK_PACKET",e,e],[1,0,e,0,e,0,"VK_DBE_SBCSCHAR",e,e],[1,0,e,0,e,0,"VK_DBE_DBCSCHAR",e,e],[1,0,e,0,e,0,"VK_ATTN",e,e],[1,0,e,0,e,0,"VK_CRSEL",e,e],[1,0,e,0,e,0,"VK_EXSEL",e,e],[1,0,e,0,e,0,"VK_EREOF",e,e],[1,0,e,0,e,0,"VK_PLAY",e,e],[1,0,e,0,e,0,"VK_ZOOM",e,e],[1,0,e,0,e,0,"VK_NONAME",e,e],[1,0,e,0,e,0,"VK_PA1",e,e],[1,0,e,0,e,0,"VK_OEM_CLEAR",e,e]],c=[],l=[];for(const f of a){const[g,S,_,E,y,v,r,s,u]=f;if(l[S]||(l[S]=!0,m[S]=_,L[_]=S,h[_.toLowerCase()]=S,g&&(n.IMMUTABLE_CODE_TO_KEY_CODE[S]=E,E!==0&&E!==3&&E!==5&&E!==4&&E!==6&&E!==57&&(n.IMMUTABLE_KEY_CODE_TO_CODE[E]=S))),!c[E]){if(c[E]=!0,!y)throw new Error(`String representation missing for key code ${E} around scan code ${_}`);A.define(E,y),i.define(E,s||y),d.define(E,u||s||y)}v&&(n.EVENT_KEY_CODE_MAP[v]=E),r&&(n.NATIVE_WINDOWS_KEY_CODE_TO_KEY_CODE[r]=E)}n.IMMUTABLE_KEY_CODE_TO_CODE[3]=46})();var o;(function(e){function a(_){return A.keyCodeToStr(_)}e.toString=a;function c(_){return A.strToKeyCode(_)}e.fromString=c;function l(_){return i.keyCodeToStr(_)}e.toUserSettingsUS=l;function f(_){return d.keyCodeToStr(_)}e.toUserSettingsGeneral=f;function g(_){return i.strToKeyCode(_)||d.strToKeyCode(_)}e.fromUserSettings=g;function S(_){if(_>=98&&_<=113)return null;switch(_){case 16:return"Up";case 18:return"Down";case 15:return"Left";case 17:return"Right"}return A.keyCodeToStr(_)}e.toElectronAccelerator=S})(o||(n.KeyCodeUtils=o={}));function w(e,a){const c=(a&65535)<<16>>>0;return(e|c)>>>0}n.KeyChord=w}),Y(X[36],J([0,1]),function(T,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Lazy=void 0;class M{constructor(i){this.executor=i,this._didRun=!1}get value(){if(!this._didRun)try{this._value=this.executor()}catch(i){this._error=i}finally{this._didRun=!0}if(this._error)throw this._error;return this._value}get rawValue(){return this._value}}n.Lazy=M}),Y(X[13],J([0,1,20,21]),function(T,n,M,A){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.DisposableMap=n.ImmortalReference=n.RefCountedDisposable=n.MutableDisposable=n.Disposable=n.DisposableStore=n.toDisposable=n.combinedDisposable=n.dispose=n.isDisposable=n.markAsSingleton=n.markAsDisposed=n.trackDisposable=n.setDisposableTracker=void 0;const i=!1;let d=null;function m(r){d=r}if(n.setDisposableTracker=m,i){const r="__is_disposable_tracked__";m(new class{trackDisposable(s){const u=new Error("Potentially leaked disposable").stack;setTimeout(()=>{s[r]||console.log(u)},3e3)}setParent(s,u){if(s&&s!==S.None)try{s[r]=!0}catch{}}markAsDisposed(s){if(s&&s!==S.None)try{s[r]=!0}catch{}}markAsSingleton(s){}})}function L(r){return d?.trackDisposable(r),r}n.trackDisposable=L;function h(r){d?.markAsDisposed(r)}n.markAsDisposed=h;function o(r,s){d?.setParent(r,s)}function w(r,s){if(d)for(const u of r)d.setParent(u,s)}function e(r){return d?.markAsSingleton(r),r}n.markAsSingleton=e;function a(r){return typeof r.dispose=="function"&&r.dispose.length===0}n.isDisposable=a;function c(r){if(A.Iterable.is(r)){const s=[];for(const u of r)if(u)try{u.dispose()}catch(p){s.push(p)}if(s.length===1)throw s[0];if(s.length>1)throw new AggregateError(s,"Encountered errors while disposing of store");return Array.isArray(r)?[]:r}else if(r)return r.dispose(),r}n.dispose=c;function l(...r){const s=f(()=>c(r));return w(r,s),s}n.combinedDisposable=l;function f(r){const s=L({dispose:(0,M.createSingleCallFunction)(()=>{h(s),r()})});return s}n.toDisposable=f;class g{constructor(){this._toDispose=new Set,this._isDisposed=!1,L(this)}dispose(){this._isDisposed||(h(this),this._isDisposed=!0,this.clear())}get isDisposed(){return this._isDisposed}clear(){if(this._toDispose.size!==0)try{c(this._toDispose)}finally{this._toDispose.clear()}}add(s){if(!s)return s;if(s===this)throw new Error("Cannot register a disposable on itself!");return o(s,this),this._isDisposed?g.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this._toDispose.add(s),s}deleteAndLeak(s){s&&this._toDispose.has(s)&&(this._toDispose.delete(s),o(s,null))}}n.DisposableStore=g,g.DISABLE_DISPOSED_WARNING=!1;class S{constructor(){this._store=new g,L(this),o(this._store,this)}dispose(){h(this),this._store.dispose()}_register(s){if(s===this)throw new Error("Cannot register a disposable on itself!");return this._store.add(s)}}n.Disposable=S,S.None=Object.freeze({dispose(){}});class _{constructor(){this._isDisposed=!1,L(this)}get value(){return this._isDisposed?void 0:this._value}set value(s){var u;this._isDisposed||s===this._value||((u=this._value)===null||u===void 0||u.dispose(),s&&o(s,this),this._value=s)}clear(){this.value=void 0}dispose(){var s;this._isDisposed=!0,h(this),(s=this._value)===null||s===void 0||s.dispose(),this._value=void 0}}n.MutableDisposable=_;class E{constructor(s){this._disposable=s,this._counter=1}acquire(){return this._counter++,this}release(){return--this._counter===0&&this._disposable.dispose(),this}}n.RefCountedDisposable=E;class y{constructor(s){this.object=s}dispose(){}}n.ImmortalReference=y;class v{constructor(){this._store=new Map,this._isDisposed=!1,L(this)}dispose(){h(this),this._isDisposed=!0,this.clearAndDisposeAll()}clearAndDisposeAll(){if(this._store.size)try{c(this._store.values())}finally{this._store.clear()}}get(s){return this._store.get(s)}set(s,u,p=!1){var b;this._isDisposed&&console.warn(new Error("Trying to add a disposable to a DisposableMap that has already been disposed of. The added object will be leaked!").stack),p||(b=this._store.get(s))===null||b===void 0||b.dispose(),this._store.set(s,u)}deleteAndDispose(s){var u;(u=this._store.get(s))===null||u===void 0||u.dispose(),this._store.delete(s)}[Symbol.iterator](){return this._store[Symbol.iterator]()}}n.DisposableMap=v}),Y(X[22],J([0,1]),function(T,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.LinkedList=void 0;class M{constructor(d){this.element=d,this.next=M.Undefined,this.prev=M.Undefined}}M.Undefined=new M(void 0);class A{constructor(){this._first=M.Undefined,this._last=M.Undefined,this._size=0}get size(){return this._size}isEmpty(){return this._first===M.Undefined}clear(){let d=this._first;for(;d!==M.Undefined;){const m=d.next;d.prev=M.Undefined,d.next=M.Undefined,d=m}this._first=M.Undefined,this._last=M.Undefined,this._size=0}unshift(d){return this._insert(d,!1)}push(d){return this._insert(d,!0)}_insert(d,m){const L=new M(d);if(this._first===M.Undefined)this._first=L,this._last=L;else if(m){const o=this._last;this._last=L,L.prev=o,o.next=L}else{const o=this._first;this._first=L,L.next=o,o.prev=L}this._size+=1;let h=!1;return()=>{h||(h=!0,this._remove(L))}}shift(){if(this._first!==M.Undefined){const d=this._first.element;return this._remove(this._first),d}}pop(){if(this._last!==M.Undefined){const d=this._last.element;return this._remove(this._last),d}}_remove(d){if(d.prev!==M.Undefined&&d.next!==M.Undefined){const m=d.prev;m.next=d.next,d.next.prev=m}else d.prev===M.Undefined&&d.next===M.Undefined?(this._first=M.Undefined,this._last=M.Undefined):d.next===M.Undefined?(this._last=this._last.prev,this._last.next=M.Undefined):d.prev===M.Undefined&&(this._first=this._first.next,this._first.prev=M.Undefined);this._size-=1}*[Symbol.iterator](){let d=this._first;for(;d!==M.Undefined;)yield d.element,d=d.next}}n.LinkedList=A}),Y(X[37],J([0,1]),function(T,n){"use strict";var M,A;Object.defineProperty(n,"__esModule",{value:!0}),n.SetMap=n.BidirectionalMap=n.LRUCache=n.LinkedMap=n.ResourceMap=void 0;class i{constructor(a,c){this.uri=a,this.value=c}}function d(e){return Array.isArray(e)}class m{constructor(a,c){if(this[M]="ResourceMap",a instanceof m)this.map=new Map(a.map),this.toKey=c??m.defaultToKey;else if(d(a)){this.map=new Map,this.toKey=c??m.defaultToKey;for(const[l,f]of a)this.set(l,f)}else this.map=new Map,this.toKey=a??m.defaultToKey}set(a,c){return this.map.set(this.toKey(a),new i(a,c)),this}get(a){var c;return(c=this.map.get(this.toKey(a)))===null||c===void 0?void 0:c.value}has(a){return this.map.has(this.toKey(a))}get size(){return this.map.size}clear(){this.map.clear()}delete(a){return this.map.delete(this.toKey(a))}forEach(a,c){typeof c<"u"&&(a=a.bind(c));for(const[l,f]of this.map)a(f.value,f.uri,this)}*values(){for(const a of this.map.values())yield a.value}*keys(){for(const a of this.map.values())yield a.uri}*entries(){for(const a of this.map.values())yield[a.uri,a.value]}*[(M=Symbol.toStringTag,Symbol.iterator)](){for(const[,a]of this.map)yield[a.uri,a.value]}}n.ResourceMap=m,m.defaultToKey=e=>e.toString();class L{constructor(){this[A]="LinkedMap",this._map=new Map,this._head=void 0,this._tail=void 0,this._size=0,this._state=0}clear(){this._map.clear(),this._head=void 0,this._tail=void 0,this._size=0,this._state++}isEmpty(){return!this._head&&!this._tail}get size(){return this._size}get first(){var a;return(a=this._head)===null||a===void 0?void 0:a.value}get last(){var a;return(a=this._tail)===null||a===void 0?void 0:a.value}has(a){return this._map.has(a)}get(a,c=0){const l=this._map.get(a);if(l)return c!==0&&this.touch(l,c),l.value}set(a,c,l=0){let f=this._map.get(a);if(f)f.value=c,l!==0&&this.touch(f,l);else{switch(f={key:a,value:c,next:void 0,previous:void 0},l){case 0:this.addItemLast(f);break;case 1:this.addItemFirst(f);break;case 2:this.addItemLast(f);break;default:this.addItemLast(f);break}this._map.set(a,f),this._size++}return this}delete(a){return!!this.remove(a)}remove(a){const c=this._map.get(a);if(c)return this._map.delete(a),this.removeItem(c),this._size--,c.value}shift(){if(!this._head&&!this._tail)return;if(!this._head||!this._tail)throw new Error("Invalid list");const a=this._head;return this._map.delete(a.key),this.removeItem(a),this._size--,a.value}forEach(a,c){const l=this._state;let f=this._head;for(;f;){if(c?a.bind(c)(f.value,f.key,this):a(f.value,f.key,this),this._state!==l)throw new Error("LinkedMap got modified during iteration.");f=f.next}}keys(){const a=this,c=this._state;let l=this._head;const f={[Symbol.iterator](){return f},next(){if(a._state!==c)throw new Error("LinkedMap got modified during iteration.");if(l){const g={value:l.key,done:!1};return l=l.next,g}else return{value:void 0,done:!0}}};return f}values(){const a=this,c=this._state;let l=this._head;const f={[Symbol.iterator](){return f},next(){if(a._state!==c)throw new Error("LinkedMap got modified during iteration.");if(l){const g={value:l.value,done:!1};return l=l.next,g}else return{value:void 0,done:!0}}};return f}entries(){const a=this,c=this._state;let l=this._head;const f={[Symbol.iterator](){return f},next(){if(a._state!==c)throw new Error("LinkedMap got modified during iteration.");if(l){const g={value:[l.key,l.value],done:!1};return l=l.next,g}else return{value:void 0,done:!0}}};return f}[(A=Symbol.toStringTag,Symbol.iterator)](){return this.entries()}trimOld(a){if(a>=this.size)return;if(a===0){this.clear();return}let c=this._head,l=this.size;for(;c&&l>a;)this._map.delete(c.key),c=c.next,l--;this._head=c,this._size=l,c&&(c.previous=void 0),this._state++}addItemFirst(a){if(!this._head&&!this._tail)this._tail=a;else if(this._head)a.next=this._head,this._head.previous=a;else throw new Error("Invalid list");this._head=a,this._state++}addItemLast(a){if(!this._head&&!this._tail)this._head=a;else if(this._tail)a.previous=this._tail,this._tail.next=a;else throw new Error("Invalid list");this._tail=a,this._state++}removeItem(a){if(a===this._head&&a===this._tail)this._head=void 0,this._tail=void 0;else if(a===this._head){if(!a.next)throw new Error("Invalid list");a.next.previous=void 0,this._head=a.next}else if(a===this._tail){if(!a.previous)throw new Error("Invalid list");a.previous.next=void 0,this._tail=a.previous}else{const c=a.next,l=a.previous;if(!c||!l)throw new Error("Invalid list");c.previous=l,l.next=c}a.next=void 0,a.previous=void 0,this._state++}touch(a,c){if(!this._head||!this._tail)throw new Error("Invalid list");if(!(c!==1&&c!==2)){if(c===1){if(a===this._head)return;const l=a.next,f=a.previous;a===this._tail?(f.next=void 0,this._tail=f):(l.previous=f,f.next=l),a.previous=void 0,a.next=this._head,this._head.previous=a,this._head=a,this._state++}else if(c===2){if(a===this._tail)return;const l=a.next,f=a.previous;a===this._head?(l.previous=void 0,this._head=l):(l.previous=f,f.next=l),a.next=void 0,a.previous=this._tail,this._tail.next=a,this._tail=a,this._state++}}}toJSON(){const a=[];return this.forEach((c,l)=>{a.push([l,c])}),a}fromJSON(a){this.clear();for(const[c,l]of a)this.set(c,l)}}n.LinkedMap=L;class h extends L{constructor(a,c=1){super(),this._limit=a,this._ratio=Math.min(Math.max(0,c),1)}get limit(){return this._limit}set limit(a){this._limit=a,this.checkTrim()}get(a,c=2){return super.get(a,c)}peek(a){return super.get(a,0)}set(a,c){return super.set(a,c,2),this.checkTrim(),this}checkTrim(){this.size>this._limit&&this.trimOld(Math.round(this._limit*this._ratio))}}n.LRUCache=h;class o{constructor(a){if(this._m1=new Map,this._m2=new Map,a)for(const[c,l]of a)this.set(c,l)}clear(){this._m1.clear(),this._m2.clear()}set(a,c){this._m1.set(a,c),this._m2.set(c,a)}get(a){return this._m1.get(a)}getKey(a){return this._m2.get(a)}delete(a){const c=this._m1.get(a);return c===void 0?!1:(this._m1.delete(a),this._m2.delete(c),!0)}keys(){return this._m1.keys()}values(){return this._m1.values()}}n.BidirectionalMap=o;class w{constructor(){this.map=new Map}add(a,c){let l=this.map.get(a);l||(l=new Set,this.map.set(a,l)),l.add(c)}delete(a,c){const l=this.map.get(a);l&&(l.delete(c),l.size===0&&this.map.delete(a))}forEach(a,c){const l=this.map.get(a);l&&l.forEach(c)}get(a){const c=this.map.get(a);return c||new Set}}n.SetMap=w}),Y(X[23],J([0,1]),function(T,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.StopWatch=void 0;const M=globalThis.performance&&typeof globalThis.performance.now=="function";class A{static create(d){return new A(d)}constructor(d){this._now=M&&d===!1?Date.now:globalThis.performance.now.bind(globalThis.performance),this._startTime=this._now(),this._stopTime=-1}stop(){this._stopTime=this._now()}elapsed(){return this._stopTime!==-1?this._stopTime-this._startTime:this._now()-this._startTime}}n.StopWatch=A}),Y(X[9],J([0,1,5,20,13,22,23]),function(T,n,M,A,i,d,m){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Relay=n.EventBufferer=n.EventMultiplexer=n.MicrotaskEmitter=n.DebounceEmitter=n.PauseableEmitter=n.createEventDeliveryQueue=n.Emitter=n.EventProfiling=n.Event=void 0;const L=!1,h=!1;var o;(function(b){b.None=()=>i.Disposable.None;function C(Z){if(h){const{onDidAddListener:j}=Z,G=c.create();let Q=0;Z.onDidAddListener=()=>{++Q===2&&(console.warn("snapshotted emitter LIKELY used public and SHOULD HAVE BEEN created with DisposableStore. snapshotted here"),G.print()),j?.()}}}function N(Z,j){return F(Z,()=>{},0,void 0,!0,void 0,j)}b.defer=N;function R(Z){return(j,G=null,Q)=>{let K=!1,te;return te=Z(P=>{if(!K)return te?te.dispose():K=!0,j.call(G,P)},null,Q),K&&te.dispose(),te}}b.once=R;function D(Z,j,G){return x((Q,K=null,te)=>Z(P=>Q.call(K,j(P)),null,te),G)}b.map=D;function k(Z,j,G){return x((Q,K=null,te)=>Z(P=>{j(P),Q.call(K,P)},null,te),G)}b.forEach=k;function U(Z,j,G){return x((Q,K=null,te)=>Z(P=>j(P)&&Q.call(K,P),null,te),G)}b.filter=U;function I(Z){return Z}b.signal=I;function B(...Z){return(j,G=null,Q)=>{const K=(0,i.combinedDisposable)(...Z.map(te=>te(P=>j.call(G,P))));return O(K,Q)}}b.any=B;function z(Z,j,G,Q){let K=G;return D(Z,te=>(K=j(K,te),K),Q)}b.reduce=z;function x(Z,j){let G;const Q={onWillAddFirstListener(){G=Z(K.fire,K)},onDidRemoveLastListener(){G?.dispose()}};j||C(Q);const K=new S(Q);return j?.add(K),K.event}function O(Z,j){return j instanceof Array?j.push(Z):j&&j.add(Z),Z}function F(Z,j,G=100,Q=!1,K=!1,te,P){let q,W,$,ee=0,ie;const oe={leakWarningThreshold:te,onWillAddFirstListener(){q=Z(he=>{ee++,W=j(W,he),Q&&!$&&(ce.fire(W),W=void 0),ie=()=>{const se=W;W=void 0,$=void 0,(!Q||ee>1)&&ce.fire(se),ee=0},typeof G=="number"?(clearTimeout($),$=setTimeout(ie,G)):$===void 0&&($=0,queueMicrotask(ie))})},onWillRemoveListener(){K&&ee>0&&ie?.()},onDidRemoveLastListener(){ie=void 0,q.dispose()}};P||C(oe);const ce=new S(oe);return P?.add(ce),ce.event}b.debounce=F;function H(Z,j=0,G){return b.debounce(Z,(Q,K)=>Q?(Q.push(K),Q):[K],j,void 0,!0,void 0,G)}b.accumulate=H;function V(Z,j=(Q,K)=>Q===K,G){let Q=!0,K;return U(Z,te=>{const P=Q||!j(te,K);return Q=!1,K=te,P},G)}b.latch=V;function t(Z,j,G){return[b.filter(Z,j,G),b.filter(Z,Q=>!j(Q),G)]}b.split=t;function ne(Z,j=!1,G=[],Q){let K=G.slice(),te=Z(W=>{K?K.push(W):q.fire(W)});Q&&Q.add(te);const P=()=>{K?.forEach(W=>q.fire(W)),K=null},q=new S({onWillAddFirstListener(){te||(te=Z(W=>q.fire(W)),Q&&Q.add(te))},onDidAddFirstListener(){K&&(j?setTimeout(P):P())},onDidRemoveLastListener(){te&&te.dispose(),te=null}});return Q&&Q.add(q),q.event}b.buffer=ne;function re(Z,j){return(Q,K,te)=>{const P=j(new me);return Z(function(q){const W=P.evaluate(q);W!==le&&Q.call(K,W)},void 0,te)}}b.chain=re;const le=Symbol("HaltChainable");class me{constructor(){this.steps=[]}map(j){return this.steps.push(j),this}forEach(j){return this.steps.push(G=>(j(G),G)),this}filter(j){return this.steps.push(G=>j(G)?G:le),this}reduce(j,G){let Q=G;return this.steps.push(K=>(Q=j(Q,K),Q)),this}latch(j=(G,Q)=>G===Q){let G=!0,Q;return this.steps.push(K=>{const te=G||!j(K,Q);return G=!1,Q=K,te?K:le}),this}evaluate(j){for(const G of this.steps)if(j=G(j),j===le)break;return j}}function pe(Z,j,G=Q=>Q){const Q=(...q)=>P.fire(G(...q)),K=()=>Z.on(j,Q),te=()=>Z.removeListener(j,Q),P=new S({onWillAddFirstListener:K,onDidRemoveLastListener:te});return P.event}b.fromNodeEventEmitter=pe;function Le(Z,j,G=Q=>Q){const Q=(...q)=>P.fire(G(...q)),K=()=>Z.addEventListener(j,Q),te=()=>Z.removeEventListener(j,Q),P=new S({onWillAddFirstListener:K,onDidRemoveLastListener:te});return P.event}b.fromDOMEventEmitter=Le;function we(Z){return new Promise(j=>R(Z)(j))}b.toPromise=we;function be(Z){const j=new S;return Z.then(G=>{j.fire(G)},()=>{j.fire(void 0)}).finally(()=>{j.dispose()}),j.event}b.fromPromise=be;function Ce(Z,j){return j(void 0),Z(G=>j(G))}b.runAndSubscribe=Ce;function Se(Z,j){let G=null;function Q(te){G?.dispose(),G=new i.DisposableStore,j(te,G)}Q(void 0);const K=Z(te=>Q(te));return(0,i.toDisposable)(()=>{K.dispose(),G?.dispose()})}b.runAndSubscribeWithStore=Se;class ye{constructor(j,G){this._observable=j,this._counter=0,this._hasChanged=!1;const Q={onWillAddFirstListener:()=>{j.addObserver(this)},onDidRemoveLastListener:()=>{j.removeObserver(this)}};G||C(Q),this.emitter=new S(Q),G&&G.add(this.emitter)}beginUpdate(j){this._counter++}handlePossibleChange(j){}handleChange(j,G){this._hasChanged=!0}endUpdate(j){this._counter--,this._counter===0&&(this._observable.reportChanges(),this._hasChanged&&(this._hasChanged=!1,this.emitter.fire(this._observable.get())))}}function _e(Z,j){return new ye(Z,j).emitter.event}b.fromObservable=_e;function de(Z){return j=>{let G=0,Q=!1;const K={beginUpdate(){G++},endUpdate(){G--,G===0&&(Z.reportChanges(),Q&&(Q=!1,j()))},handlePossibleChange(){},handleChange(){Q=!0}};return Z.addObserver(K),Z.reportChanges(),{dispose(){Z.removeObserver(K)}}}}b.fromObservableLight=de})(o||(n.Event=o={}));class w{constructor(C){this.listenerCount=0,this.invocationCount=0,this.elapsedOverall=0,this.durations=[],this.name=`${C}_${w._idPool++}`,w.all.add(this)}start(C){this._stopWatch=new m.StopWatch,this.listenerCount=C}stop(){if(this._stopWatch){const C=this._stopWatch.elapsed();this.durations.push(C),this.elapsedOverall+=C,this.invocationCount+=1,this._stopWatch=void 0}}}n.EventProfiling=w,w.all=new Set,w._idPool=0;let e=-1;class a{constructor(C,N=Math.random().toString(18).slice(2,5)){this.threshold=C,this.name=N,this._warnCountdown=0}dispose(){var C;(C=this._stacks)===null||C===void 0||C.clear()}check(C,N){const R=this.threshold;if(R<=0||N<R)return;this._stacks||(this._stacks=new Map);const D=this._stacks.get(C.value)||0;if(this._stacks.set(C.value,D+1),this._warnCountdown-=1,this._warnCountdown<=0){this._warnCountdown=R*.5;let k,U=0;for(const[I,B]of this._stacks)(!k||U<B)&&(k=I,U=B);console.warn(`[${this.name}] potential listener LEAK detected, having ${N} listeners already. MOST frequent listener (${U}):`),console.warn(k)}return()=>{const k=this._stacks.get(C.value)||0;this._stacks.set(C.value,k-1)}}}class c{static create(){var C;return new c((C=new Error().stack)!==null&&C!==void 0?C:"")}constructor(C){this.value=C}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}}class l{constructor(C){this.value=C}}const f=2,g=(b,C)=>{if(b instanceof l)C(b);else for(let N=0;N<b.length;N++){const R=b[N];R&&C(R)}};class S{constructor(C){var N,R,D,k,U;this._size=0,this._options=C,this._leakageMon=e>0||!((N=this._options)===null||N===void 0)&&N.leakWarningThreshold?new a((D=(R=this._options)===null||R===void 0?void 0:R.leakWarningThreshold)!==null&&D!==void 0?D:e):void 0,this._perfMon=!((k=this._options)===null||k===void 0)&&k._profName?new w(this._options._profName):void 0,this._deliveryQueue=(U=this._options)===null||U===void 0?void 0:U.deliveryQueue}dispose(){var C,N,R,D;if(!this._disposed){if(this._disposed=!0,((C=this._deliveryQueue)===null||C===void 0?void 0:C.current)===this&&this._deliveryQueue.reset(),this._listeners){if(L){const k=this._listeners;queueMicrotask(()=>{g(k,U=>{var I;return(I=U.stack)===null||I===void 0?void 0:I.print()})})}this._listeners=void 0,this._size=0}(R=(N=this._options)===null||N===void 0?void 0:N.onDidRemoveLastListener)===null||R===void 0||R.call(N),(D=this._leakageMon)===null||D===void 0||D.dispose()}}get event(){var C;return(C=this._event)!==null&&C!==void 0||(this._event=(N,R,D)=>{var k,U,I,B,z;if(this._leakageMon&&this._size>this._leakageMon.threshold*3)return console.warn(`[${this._leakageMon.name}] REFUSES to accept new listeners because it exceeded its threshold by far`),i.Disposable.None;if(this._disposed)return i.Disposable.None;R&&(N=N.bind(R));const x=new l(N);let O,F;this._leakageMon&&this._size>=Math.ceil(this._leakageMon.threshold*.2)&&(x.stack=c.create(),O=this._leakageMon.check(x.stack,this._size+1)),L&&(x.stack=F??c.create()),this._listeners?this._listeners instanceof l?((z=this._deliveryQueue)!==null&&z!==void 0||(this._deliveryQueue=new E),this._listeners=[this._listeners,x]):this._listeners.push(x):((U=(k=this._options)===null||k===void 0?void 0:k.onWillAddFirstListener)===null||U===void 0||U.call(k,this),this._listeners=x,(B=(I=this._options)===null||I===void 0?void 0:I.onDidAddFirstListener)===null||B===void 0||B.call(I,this)),this._size++;const H=(0,i.toDisposable)(()=>{O?.(),this._removeListener(x)});return D instanceof i.DisposableStore?D.add(H):Array.isArray(D)&&D.push(H),H}),this._event}_removeListener(C){var N,R,D,k;if((R=(N=this._options)===null||N===void 0?void 0:N.onWillRemoveListener)===null||R===void 0||R.call(N,this),!this._listeners)return;if(this._size===1){this._listeners=void 0,(k=(D=this._options)===null||D===void 0?void 0:D.onDidRemoveLastListener)===null||k===void 0||k.call(D,this),this._size=0;return}const U=this._listeners,I=U.indexOf(C);if(I===-1)throw console.log("disposed?",this._disposed),console.log("size?",this._size),console.log("arr?",JSON.stringify(this._listeners)),new Error("Attempted to dispose unknown listener");this._size--,U[I]=void 0;const B=this._deliveryQueue.current===this;if(this._size*f<=U.length){let z=0;for(let x=0;x<U.length;x++)U[x]?U[z++]=U[x]:B&&(this._deliveryQueue.end--,z<this._deliveryQueue.i&&this._deliveryQueue.i--);U.length=z}}_deliver(C,N){var R;if(!C)return;const D=((R=this._options)===null||R===void 0?void 0:R.onListenerError)||M.onUnexpectedError;if(!D){C.value(N);return}try{C.value(N)}catch(k){D(k)}}_deliverQueue(C){const N=C.current._listeners;for(;C.i<C.end;)this._deliver(N[C.i++],C.value);C.reset()}fire(C){var N,R,D,k;if(!((N=this._deliveryQueue)===null||N===void 0)&&N.current&&(this._deliverQueue(this._deliveryQueue),(R=this._perfMon)===null||R===void 0||R.stop()),(D=this._perfMon)===null||D===void 0||D.start(this._size),this._listeners)if(this._listeners instanceof l)this._deliver(this._listeners,C);else{const U=this._deliveryQueue;U.enqueue(this,C,this._listeners.length),this._deliverQueue(U)}(k=this._perfMon)===null||k===void 0||k.stop()}hasListeners(){return this._size>0}}n.Emitter=S;const _=()=>new E;n.createEventDeliveryQueue=_;class E{constructor(){this.i=-1,this.end=0}enqueue(C,N,R){this.i=0,this.end=R,this.current=C,this.value=N}reset(){this.i=this.end,this.current=void 0,this.value=void 0}}class y extends S{constructor(C){super(C),this._isPaused=0,this._eventQueue=new d.LinkedList,this._mergeFn=C?.merge}pause(){this._isPaused++}resume(){if(this._isPaused!==0&&--this._isPaused===0)if(this._mergeFn){if(this._eventQueue.size>0){const C=Array.from(this._eventQueue);this._eventQueue.clear(),super.fire(this._mergeFn(C))}}else for(;!this._isPaused&&this._eventQueue.size!==0;)super.fire(this._eventQueue.shift())}fire(C){this._size&&(this._isPaused!==0?this._eventQueue.push(C):super.fire(C))}}n.PauseableEmitter=y;class v extends y{constructor(C){var N;super(C),this._delay=(N=C.delay)!==null&&N!==void 0?N:100}fire(C){this._handle||(this.pause(),this._handle=setTimeout(()=>{this._handle=void 0,this.resume()},this._delay)),super.fire(C)}}n.DebounceEmitter=v;class r extends S{constructor(C){super(C),this._queuedEvents=[],this._mergeFn=C?.merge}fire(C){this.hasListeners()&&(this._queuedEvents.push(C),this._queuedEvents.length===1&&queueMicrotask(()=>{this._mergeFn?super.fire(this._mergeFn(this._queuedEvents)):this._queuedEvents.forEach(N=>super.fire(N)),this._queuedEvents=[]}))}}n.MicrotaskEmitter=r;class s{constructor(){this.hasListeners=!1,this.events=[],this.emitter=new S({onWillAddFirstListener:()=>this.onFirstListenerAdd(),onDidRemoveLastListener:()=>this.onLastListenerRemove()})}get event(){return this.emitter.event}add(C){const N={event:C,listener:null};this.events.push(N),this.hasListeners&&this.hook(N);const R=()=>{this.hasListeners&&this.unhook(N);const D=this.events.indexOf(N);this.events.splice(D,1)};return(0,i.toDisposable)((0,A.createSingleCallFunction)(R))}onFirstListenerAdd(){this.hasListeners=!0,this.events.forEach(C=>this.hook(C))}onLastListenerRemove(){this.hasListeners=!1,this.events.forEach(C=>this.unhook(C))}hook(C){C.listener=C.event(N=>this.emitter.fire(N))}unhook(C){C.listener&&C.listener.dispose(),C.listener=null}dispose(){this.emitter.dispose()}}n.EventMultiplexer=s;class u{constructor(){this.buffers=[]}wrapEvent(C){return(N,R,D)=>C(k=>{const U=this.buffers[this.buffers.length-1];U?U.push(()=>N.call(R,k)):N.call(R,k)},void 0,D)}bufferEvents(C){const N=[];this.buffers.push(N);const R=C();return this.buffers.pop(),N.forEach(D=>D()),R}}n.EventBufferer=u;class p{constructor(){this.listening=!1,this.inputEvent=o.None,this.inputEventListener=i.Disposable.None,this.emitter=new S({onDidAddFirstListener:()=>{this.listening=!0,this.inputEventListener=this.inputEvent(this.emitter.fire,this.emitter)},onDidRemoveLastListener:()=>{this.listening=!1,this.inputEventListener.dispose()}}),this.event=this.emitter.event}set input(C){this.inputEvent=C,this.listening&&(this.inputEventListener.dispose(),this.inputEventListener=C(this.emitter.fire,this.emitter))}dispose(){this.inputEventListener.dispose(),this.emitter.dispose()}}n.Relay=p}),Y(X[38],J([0,1,9]),function(T,n,M){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.CancellationTokenSource=n.CancellationToken=void 0;const A=Object.freeze(function(L,h){const o=setTimeout(L.bind(h),0);return{dispose(){clearTimeout(o)}}});var i;(function(L){function h(o){return o===L.None||o===L.Cancelled||o instanceof d?!0:!o||typeof o!="object"?!1:typeof o.isCancellationRequested=="boolean"&&typeof o.onCancellationRequested=="function"}L.isCancellationToken=h,L.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:M.Event.None}),L.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:A})})(i||(n.CancellationToken=i={}));class d{constructor(){this._isCancelled=!1,this._emitter=null}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?A:(this._emitter||(this._emitter=new M.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=null)}}class m{constructor(h){this._token=void 0,this._parentListener=void 0,this._parentListener=h&&h.onCancellationRequested(this.cancel,this)}get token(){return this._token||(this._token=new d),this._token}cancel(){this._token?this._token instanceof d&&this._token.cancel():this._token=i.Cancelled}dispose(h=!1){var o;h&&this.cancel(),(o=this._parentListener)===null||o===void 0||o.dispose(),this._token?this._token instanceof d&&this._token.dispose():this._token=i.None}}n.CancellationTokenSource=m}),Y(X[6],J([0,1,32,36]),function(T,n,M,A){"use strict";var i;Object.defineProperty(n,"__esModule",{value:!0}),n.InvisibleCharacters=n.AmbiguousCharacters=n.noBreakWhitespace=n.getLeftDeleteOffset=n.singleLetterHash=n.containsUppercaseCharacter=n.startsWithUTF8BOM=n.UTF8_BOM_CHARACTER=n.isEmojiImprecise=n.isFullWidthCharacter=n.containsUnusualLineTerminators=n.UNUSUAL_LINE_TERMINATORS=n.isBasicASCII=n.containsRTL=n.getCharContainingOffset=n.prevCharLength=n.nextCharLength=n.GraphemeIterator=n.CodePointIterator=n.getNextCodePoint=n.computeCodePoint=n.isLowSurrogate=n.isHighSurrogate=n.commonSuffixLength=n.commonPrefixLength=n.startsWithIgnoreCase=n.equalsIgnoreCase=n.isUpperAsciiLetter=n.isLowerAsciiLetter=n.isAsciiDigit=n.compareSubstringIgnoreCase=n.compareIgnoreCase=n.compareSubstring=n.compare=n.lastNonWhitespaceIndex=n.getLeadingWhitespace=n.firstNonWhitespaceIndex=n.splitLines=n.regExpLeadsToEndlessLoop=n.createRegExp=n.stripWildcards=n.convertSimple2RegExpPattern=n.rtrim=n.ltrim=n.trim=n.escapeRegExpCharacters=n.escape=n.format=n.isFalsyOrWhitespace=void 0;function d(P){return!P||typeof P!="string"?!0:P.trim().length===0}n.isFalsyOrWhitespace=d;const m=/{(\d+)}/g;function L(P,...q){return q.length===0?P:P.replace(m,function(W,$){const ee=parseInt($,10);return isNaN(ee)||ee<0||ee>=q.length?W:q[ee]})}n.format=L;function h(P){return P.replace(/[<>&]/g,function(q){switch(q){case"<":return"&lt;";case">":return"&gt;";case"&":return"&amp;";default:return q}})}n.escape=h;function o(P){return P.replace(/[\\\{\}\*\+\?\|\^\$\.\[\]\(\)]/g,"\\$&")}n.escapeRegExpCharacters=o;function w(P,q=" "){const W=e(P,q);return a(W,q)}n.trim=w;function e(P,q){if(!P||!q)return P;const W=q.length;if(W===0||P.length===0)return P;let $=0;for(;P.indexOf(q,$)===$;)$=$+W;return P.substring($)}n.ltrim=e;function a(P,q){if(!P||!q)return P;const W=q.length,$=P.length;if(W===0||$===0)return P;let ee=$,ie=-1;for(;ie=P.lastIndexOf(q,ee-1),!(ie===-1||ie+W!==ee);){if(ie===0)return"";ee=ie}return P.substring(0,ee)}n.rtrim=a;function c(P){return P.replace(/[\-\\\{\}\+\?\|\^\$\.\,\[\]\(\)\#\s]/g,"\\$&").replace(/[\*]/g,".*")}n.convertSimple2RegExpPattern=c;function l(P){return P.replace(/\*/g,"")}n.stripWildcards=l;function f(P,q,W={}){if(!P)throw new Error("Cannot create regex from empty string");q||(P=o(P)),W.wholeWord&&(/\B/.test(P.charAt(0))||(P="\\b"+P),/\B/.test(P.charAt(P.length-1))||(P=P+"\\b"));let $="";return W.global&&($+="g"),W.matchCase||($+="i"),W.multiline&&($+="m"),W.unicode&&($+="u"),new RegExp(P,$)}n.createRegExp=f;function g(P){return P.source==="^"||P.source==="^$"||P.source==="$"||P.source==="^\\s*$"?!1:!!(P.exec("")&&P.lastIndex===0)}n.regExpLeadsToEndlessLoop=g;function S(P){return P.split(/\r\n|\r|\n/)}n.splitLines=S;function _(P){for(let q=0,W=P.length;q<W;q++){const $=P.charCodeAt(q);if($!==32&&$!==9)return q}return-1}n.firstNonWhitespaceIndex=_;function E(P,q=0,W=P.length){for(let $=q;$<W;$++){const ee=P.charCodeAt($);if(ee!==32&&ee!==9)return P.substring(q,$)}return P.substring(q,W)}n.getLeadingWhitespace=E;function y(P,q=P.length-1){for(let W=q;W>=0;W--){const $=P.charCodeAt(W);if($!==32&&$!==9)return W}return-1}n.lastNonWhitespaceIndex=y;function v(P,q){return P<q?-1:P>q?1:0}n.compare=v;function r(P,q,W=0,$=P.length,ee=0,ie=q.length){for(;W<$&&ee<ie;W++,ee++){const he=P.charCodeAt(W),se=q.charCodeAt(ee);if(he<se)return-1;if(he>se)return 1}const oe=$-W,ce=ie-ee;return oe<ce?-1:oe>ce?1:0}n.compareSubstring=r;function s(P,q){return u(P,q,0,P.length,0,q.length)}n.compareIgnoreCase=s;function u(P,q,W=0,$=P.length,ee=0,ie=q.length){for(;W<$&&ee<ie;W++,ee++){let he=P.charCodeAt(W),se=q.charCodeAt(ee);if(he===se)continue;if(he>=128||se>=128)return r(P.toLowerCase(),q.toLowerCase(),W,$,ee,ie);b(he)&&(he-=32),b(se)&&(se-=32);const fe=he-se;if(fe!==0)return fe}const oe=$-W,ce=ie-ee;return oe<ce?-1:oe>ce?1:0}n.compareSubstringIgnoreCase=u;function p(P){return P>=48&&P<=57}n.isAsciiDigit=p;function b(P){return P>=97&&P<=122}n.isLowerAsciiLetter=b;function C(P){return P>=65&&P<=90}n.isUpperAsciiLetter=C;function N(P,q){return P.length===q.length&&u(P,q)===0}n.equalsIgnoreCase=N;function R(P,q){const W=q.length;return q.length>P.length?!1:u(P,q,0,W)===0}n.startsWithIgnoreCase=R;function D(P,q){const W=Math.min(P.length,q.length);let $;for($=0;$<W;$++)if(P.charCodeAt($)!==q.charCodeAt($))return $;return W}n.commonPrefixLength=D;function k(P,q){const W=Math.min(P.length,q.length);let $;const ee=P.length-1,ie=q.length-1;for($=0;$<W;$++)if(P.charCodeAt(ee-$)!==q.charCodeAt(ie-$))return $;return W}n.commonSuffixLength=k;function U(P){return 55296<=P&&P<=56319}n.isHighSurrogate=U;function I(P){return 56320<=P&&P<=57343}n.isLowSurrogate=I;function B(P,q){return(P-55296<<10)+(q-56320)+65536}n.computeCodePoint=B;function z(P,q,W){const $=P.charCodeAt(W);if(U($)&&W+1<q){const ee=P.charCodeAt(W+1);if(I(ee))return B($,ee)}return $}n.getNextCodePoint=z;function x(P,q){const W=P.charCodeAt(q-1);if(I(W)&&q>1){const $=P.charCodeAt(q-2);if(U($))return B($,W)}return W}class O{get offset(){return this._offset}constructor(q,W=0){this._str=q,this._len=q.length,this._offset=W}setOffset(q){this._offset=q}prevCodePoint(){const q=x(this._str,this._offset);return this._offset-=q>=65536?2:1,q}nextCodePoint(){const q=z(this._str,this._len,this._offset);return this._offset+=q>=65536?2:1,q}eol(){return this._offset>=this._len}}n.CodePointIterator=O;class F{get offset(){return this._iterator.offset}constructor(q,W=0){this._iterator=new O(q,W)}nextGraphemeLength(){const q=de.getInstance(),W=this._iterator,$=W.offset;let ee=q.getGraphemeBreakType(W.nextCodePoint());for(;!W.eol();){const ie=W.offset,oe=q.getGraphemeBreakType(W.nextCodePoint());if(_e(ee,oe)){W.setOffset(ie);break}ee=oe}return W.offset-$}prevGraphemeLength(){const q=de.getInstance(),W=this._iterator,$=W.offset;let ee=q.getGraphemeBreakType(W.prevCodePoint());for(;W.offset>0;){const ie=W.offset,oe=q.getGraphemeBreakType(W.prevCodePoint());if(_e(oe,ee)){W.setOffset(ie);break}ee=oe}return $-W.offset}eol(){return this._iterator.eol()}}n.GraphemeIterator=F;function H(P,q){return new F(P,q).nextGraphemeLength()}n.nextCharLength=H;function V(P,q){return new F(P,q).prevGraphemeLength()}n.prevCharLength=V;function t(P,q){q>0&&I(P.charCodeAt(q))&&q--;const W=q+H(P,q);return[W-V(P,W),W]}n.getCharContainingOffset=t;let ne;function re(){return/(?:[\u05BE\u05C0\u05C3\u05C6\u05D0-\u05F4\u0608\u060B\u060D\u061B-\u064A\u066D-\u066F\u0671-\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u0710\u0712-\u072F\u074D-\u07A5\u07B1-\u07EA\u07F4\u07F5\u07FA\u07FE-\u0815\u081A\u0824\u0828\u0830-\u0858\u085E-\u088E\u08A0-\u08C9\u200F\uFB1D\uFB1F-\uFB28\uFB2A-\uFD3D\uFD50-\uFDC7\uFDF0-\uFDFC\uFE70-\uFEFC]|\uD802[\uDC00-\uDD1B\uDD20-\uDE00\uDE10-\uDE35\uDE40-\uDEE4\uDEEB-\uDF35\uDF40-\uDFFF]|\uD803[\uDC00-\uDD23\uDE80-\uDEA9\uDEAD-\uDF45\uDF51-\uDF81\uDF86-\uDFF6]|\uD83A[\uDC00-\uDCCF\uDD00-\uDD43\uDD4B-\uDFFF]|\uD83B[\uDC00-\uDEBB])/}function le(P){return ne||(ne=re()),ne.test(P)}n.containsRTL=le;const me=/^[\t\n\r\x20-\x7E]*$/;function pe(P){return me.test(P)}n.isBasicASCII=pe,n.UNUSUAL_LINE_TERMINATORS=/[\u2028\u2029]/;function Le(P){return n.UNUSUAL_LINE_TERMINATORS.test(P)}n.containsUnusualLineTerminators=Le;function we(P){return P>=11904&&P<=55215||P>=63744&&P<=64255||P>=65281&&P<=65374}n.isFullWidthCharacter=we;function be(P){return P>=127462&&P<=127487||P===8986||P===8987||P===9200||P===9203||P>=9728&&P<=10175||P===11088||P===11093||P>=127744&&P<=128591||P>=128640&&P<=128764||P>=128992&&P<=129008||P>=129280&&P<=129535||P>=129648&&P<=129782}n.isEmojiImprecise=be,n.UTF8_BOM_CHARACTER=String.fromCharCode(65279);function Ce(P){return!!(P&&P.length>0&&P.charCodeAt(0)===65279)}n.startsWithUTF8BOM=Ce;function Se(P,q=!1){return P?(q&&(P=P.replace(/\\./g,"")),P.toLowerCase()!==P):!1}n.containsUppercaseCharacter=Se;function ye(P){return P=P%(2*26),P<26?String.fromCharCode(97+P):String.fromCharCode(65+P-26)}n.singleLetterHash=ye;function _e(P,q){return P===0?q!==5&&q!==7:P===2&&q===3?!1:P===4||P===2||P===3||q===4||q===2||q===3?!0:!(P===8&&(q===8||q===9||q===11||q===12)||(P===11||P===9)&&(q===9||q===10)||(P===12||P===10)&&q===10||q===5||q===13||q===7||P===1||P===13&&q===14||P===6&&q===6)}class de{static getInstance(){return de._INSTANCE||(de._INSTANCE=new de),de._INSTANCE}constructor(){this._data=Z()}getGraphemeBreakType(q){if(q<32)return q===10?3:q===13?2:4;if(q<127)return 0;const W=this._data,$=W.length/3;let ee=1;for(;ee<=$;)if(q<W[3*ee])ee=2*ee;else if(q>W[3*ee+1])ee=2*ee+1;else return W[3*ee+2];return 0}}de._INSTANCE=null;function Z(){return JSON.parse("[0,0,0,51229,51255,12,44061,44087,12,127462,127487,6,7083,7085,5,47645,47671,12,54813,54839,12,128678,128678,14,3270,3270,5,9919,9923,14,45853,45879,12,49437,49463,12,53021,53047,12,71216,71218,7,128398,128399,14,129360,129374,14,2519,2519,5,4448,4519,9,9742,9742,14,12336,12336,14,44957,44983,12,46749,46775,12,48541,48567,12,50333,50359,12,52125,52151,12,53917,53943,12,69888,69890,5,73018,73018,5,127990,127990,14,128558,128559,14,128759,128760,14,129653,129655,14,2027,2035,5,2891,2892,7,3761,3761,5,6683,6683,5,8293,8293,4,9825,9826,14,9999,9999,14,43452,43453,5,44509,44535,12,45405,45431,12,46301,46327,12,47197,47223,12,48093,48119,12,48989,49015,12,49885,49911,12,50781,50807,12,51677,51703,12,52573,52599,12,53469,53495,12,54365,54391,12,65279,65279,4,70471,70472,7,72145,72147,7,119173,119179,5,127799,127818,14,128240,128244,14,128512,128512,14,128652,128652,14,128721,128722,14,129292,129292,14,129445,129450,14,129734,129743,14,1476,1477,5,2366,2368,7,2750,2752,7,3076,3076,5,3415,3415,5,4141,4144,5,6109,6109,5,6964,6964,5,7394,7400,5,9197,9198,14,9770,9770,14,9877,9877,14,9968,9969,14,10084,10084,14,43052,43052,5,43713,43713,5,44285,44311,12,44733,44759,12,45181,45207,12,45629,45655,12,46077,46103,12,46525,46551,12,46973,46999,12,47421,47447,12,47869,47895,12,48317,48343,12,48765,48791,12,49213,49239,12,49661,49687,12,50109,50135,12,50557,50583,12,51005,51031,12,51453,51479,12,51901,51927,12,52349,52375,12,52797,52823,12,53245,53271,12,53693,53719,12,54141,54167,12,54589,54615,12,55037,55063,12,69506,69509,5,70191,70193,5,70841,70841,7,71463,71467,5,72330,72342,5,94031,94031,5,123628,123631,5,127763,127765,14,127941,127941,14,128043,128062,14,128302,128317,14,128465,128467,14,128539,128539,14,128640,128640,14,128662,128662,14,128703,128703,14,128745,128745,14,129004,129007,14,129329,129330,14,129402,129402,14,129483,129483,14,129686,129704,14,130048,131069,14,173,173,4,1757,1757,1,2200,2207,5,2434,2435,7,2631,2632,5,2817,2817,5,3008,3008,5,3201,3201,5,3387,3388,5,3542,3542,5,3902,3903,7,4190,4192,5,6002,6003,5,6439,6440,5,6765,6770,7,7019,7027,5,7154,7155,7,8205,8205,13,8505,8505,14,9654,9654,14,9757,9757,14,9792,9792,14,9852,9853,14,9890,9894,14,9937,9937,14,9981,9981,14,10035,10036,14,11035,11036,14,42654,42655,5,43346,43347,7,43587,43587,5,44006,44007,7,44173,44199,12,44397,44423,12,44621,44647,12,44845,44871,12,45069,45095,12,45293,45319,12,45517,45543,12,45741,45767,12,45965,45991,12,46189,46215,12,46413,46439,12,46637,46663,12,46861,46887,12,47085,47111,12,47309,47335,12,47533,47559,12,47757,47783,12,47981,48007,12,48205,48231,12,48429,48455,12,48653,48679,12,48877,48903,12,49101,49127,12,49325,49351,12,49549,49575,12,49773,49799,12,49997,50023,12,50221,50247,12,50445,50471,12,50669,50695,12,50893,50919,12,51117,51143,12,51341,51367,12,51565,51591,12,51789,51815,12,52013,52039,12,52237,52263,12,52461,52487,12,52685,52711,12,52909,52935,12,53133,53159,12,53357,53383,12,53581,53607,12,53805,53831,12,54029,54055,12,54253,54279,12,54477,54503,12,54701,54727,12,54925,54951,12,55149,55175,12,68101,68102,5,69762,69762,7,70067,70069,7,70371,70378,5,70720,70721,7,71087,71087,5,71341,71341,5,71995,71996,5,72249,72249,7,72850,72871,5,73109,73109,5,118576,118598,5,121505,121519,5,127245,127247,14,127568,127569,14,127777,127777,14,127872,127891,14,127956,127967,14,128015,128016,14,128110,128172,14,128259,128259,14,128367,128368,14,128424,128424,14,128488,128488,14,128530,128532,14,128550,128551,14,128566,128566,14,128647,128647,14,128656,128656,14,128667,128673,14,128691,128693,14,128715,128715,14,128728,128732,14,128752,128752,14,128765,128767,14,129096,129103,14,129311,129311,14,129344,129349,14,129394,129394,14,129413,129425,14,129466,129471,14,129511,129535,14,129664,129666,14,129719,129722,14,129760,129767,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2307,2307,7,2382,2383,7,2497,2500,5,2563,2563,7,2677,2677,5,2763,2764,7,2879,2879,5,2914,2915,5,3021,3021,5,3142,3144,5,3263,3263,5,3285,3286,5,3398,3400,7,3530,3530,5,3633,3633,5,3864,3865,5,3974,3975,5,4155,4156,7,4229,4230,5,5909,5909,7,6078,6085,7,6277,6278,5,6451,6456,7,6744,6750,5,6846,6846,5,6972,6972,5,7074,7077,5,7146,7148,7,7222,7223,5,7416,7417,5,8234,8238,4,8417,8417,5,9000,9000,14,9203,9203,14,9730,9731,14,9748,9749,14,9762,9763,14,9776,9783,14,9800,9811,14,9831,9831,14,9872,9873,14,9882,9882,14,9900,9903,14,9929,9933,14,9941,9960,14,9974,9974,14,9989,9989,14,10006,10006,14,10062,10062,14,10160,10160,14,11647,11647,5,12953,12953,14,43019,43019,5,43232,43249,5,43443,43443,5,43567,43568,7,43696,43696,5,43765,43765,7,44013,44013,5,44117,44143,12,44229,44255,12,44341,44367,12,44453,44479,12,44565,44591,12,44677,44703,12,44789,44815,12,44901,44927,12,45013,45039,12,45125,45151,12,45237,45263,12,45349,45375,12,45461,45487,12,45573,45599,12,45685,45711,12,45797,45823,12,45909,45935,12,46021,46047,12,46133,46159,12,46245,46271,12,46357,46383,12,46469,46495,12,46581,46607,12,46693,46719,12,46805,46831,12,46917,46943,12,47029,47055,12,47141,47167,12,47253,47279,12,47365,47391,12,47477,47503,12,47589,47615,12,47701,47727,12,47813,47839,12,47925,47951,12,48037,48063,12,48149,48175,12,48261,48287,12,48373,48399,12,48485,48511,12,48597,48623,12,48709,48735,12,48821,48847,12,48933,48959,12,49045,49071,12,49157,49183,12,49269,49295,12,49381,49407,12,49493,49519,12,49605,49631,12,49717,49743,12,49829,49855,12,49941,49967,12,50053,50079,12,50165,50191,12,50277,50303,12,50389,50415,12,50501,50527,12,50613,50639,12,50725,50751,12,50837,50863,12,50949,50975,12,51061,51087,12,51173,51199,12,51285,51311,12,51397,51423,12,51509,51535,12,51621,51647,12,51733,51759,12,51845,51871,12,51957,51983,12,52069,52095,12,52181,52207,12,52293,52319,12,52405,52431,12,52517,52543,12,52629,52655,12,52741,52767,12,52853,52879,12,52965,52991,12,53077,53103,12,53189,53215,12,53301,53327,12,53413,53439,12,53525,53551,12,53637,53663,12,53749,53775,12,53861,53887,12,53973,53999,12,54085,54111,12,54197,54223,12,54309,54335,12,54421,54447,12,54533,54559,12,54645,54671,12,54757,54783,12,54869,54895,12,54981,55007,12,55093,55119,12,55243,55291,10,66045,66045,5,68325,68326,5,69688,69702,5,69817,69818,5,69957,69958,7,70089,70092,5,70198,70199,5,70462,70462,5,70502,70508,5,70750,70750,5,70846,70846,7,71100,71101,5,71230,71230,7,71351,71351,5,71737,71738,5,72000,72000,7,72160,72160,5,72273,72278,5,72752,72758,5,72882,72883,5,73031,73031,5,73461,73462,7,94192,94193,7,119149,119149,7,121403,121452,5,122915,122916,5,126980,126980,14,127358,127359,14,127535,127535,14,127759,127759,14,127771,127771,14,127792,127793,14,127825,127867,14,127897,127899,14,127945,127945,14,127985,127986,14,128000,128007,14,128021,128021,14,128066,128100,14,128184,128235,14,128249,128252,14,128266,128276,14,128335,128335,14,128379,128390,14,128407,128419,14,128444,128444,14,128481,128481,14,128499,128499,14,128526,128526,14,128536,128536,14,128543,128543,14,128556,128556,14,128564,128564,14,128577,128580,14,128643,128645,14,128649,128649,14,128654,128654,14,128660,128660,14,128664,128664,14,128675,128675,14,128686,128689,14,128695,128696,14,128705,128709,14,128717,128719,14,128725,128725,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129009,129023,14,129160,129167,14,129296,129304,14,129320,129327,14,129340,129342,14,129356,129356,14,129388,129392,14,129399,129400,14,129404,129407,14,129432,129442,14,129454,129455,14,129473,129474,14,129485,129487,14,129648,129651,14,129659,129660,14,129671,129679,14,129709,129711,14,129728,129730,14,129751,129753,14,129776,129782,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2274,2274,1,2363,2363,7,2377,2380,7,2402,2403,5,2494,2494,5,2507,2508,7,2558,2558,5,2622,2624,7,2641,2641,5,2691,2691,7,2759,2760,5,2786,2787,5,2876,2876,5,2881,2884,5,2901,2902,5,3006,3006,5,3014,3016,7,3072,3072,5,3134,3136,5,3157,3158,5,3260,3260,5,3266,3266,5,3274,3275,7,3328,3329,5,3391,3392,7,3405,3405,5,3457,3457,5,3536,3537,7,3551,3551,5,3636,3642,5,3764,3772,5,3895,3895,5,3967,3967,7,3993,4028,5,4146,4151,5,4182,4183,7,4226,4226,5,4253,4253,5,4957,4959,5,5940,5940,7,6070,6070,7,6087,6088,7,6158,6158,4,6432,6434,5,6448,6449,7,6679,6680,5,6742,6742,5,6754,6754,5,6783,6783,5,6912,6915,5,6966,6970,5,6978,6978,5,7042,7042,7,7080,7081,5,7143,7143,7,7150,7150,7,7212,7219,5,7380,7392,5,7412,7412,5,8203,8203,4,8232,8232,4,8265,8265,14,8400,8412,5,8421,8432,5,8617,8618,14,9167,9167,14,9200,9200,14,9410,9410,14,9723,9726,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9774,14,9786,9786,14,9794,9794,14,9823,9823,14,9828,9828,14,9833,9850,14,9855,9855,14,9875,9875,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9935,9935,14,9939,9939,14,9962,9962,14,9972,9972,14,9978,9978,14,9986,9986,14,9997,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10133,10135,14,10548,10549,14,11093,11093,14,12330,12333,5,12441,12442,5,42608,42610,5,43010,43010,5,43045,43046,5,43188,43203,7,43302,43309,5,43392,43394,5,43446,43449,5,43493,43493,5,43571,43572,7,43597,43597,7,43703,43704,5,43756,43757,5,44003,44004,7,44009,44010,7,44033,44059,12,44089,44115,12,44145,44171,12,44201,44227,12,44257,44283,12,44313,44339,12,44369,44395,12,44425,44451,12,44481,44507,12,44537,44563,12,44593,44619,12,44649,44675,12,44705,44731,12,44761,44787,12,44817,44843,12,44873,44899,12,44929,44955,12,44985,45011,12,45041,45067,12,45097,45123,12,45153,45179,12,45209,45235,12,45265,45291,12,45321,45347,12,45377,45403,12,45433,45459,12,45489,45515,12,45545,45571,12,45601,45627,12,45657,45683,12,45713,45739,12,45769,45795,12,45825,45851,12,45881,45907,12,45937,45963,12,45993,46019,12,46049,46075,12,46105,46131,12,46161,46187,12,46217,46243,12,46273,46299,12,46329,46355,12,46385,46411,12,46441,46467,12,46497,46523,12,46553,46579,12,46609,46635,12,46665,46691,12,46721,46747,12,46777,46803,12,46833,46859,12,46889,46915,12,46945,46971,12,47001,47027,12,47057,47083,12,47113,47139,12,47169,47195,12,47225,47251,12,47281,47307,12,47337,47363,12,47393,47419,12,47449,47475,12,47505,47531,12,47561,47587,12,47617,47643,12,47673,47699,12,47729,47755,12,47785,47811,12,47841,47867,12,47897,47923,12,47953,47979,12,48009,48035,12,48065,48091,12,48121,48147,12,48177,48203,12,48233,48259,12,48289,48315,12,48345,48371,12,48401,48427,12,48457,48483,12,48513,48539,12,48569,48595,12,48625,48651,12,48681,48707,12,48737,48763,12,48793,48819,12,48849,48875,12,48905,48931,12,48961,48987,12,49017,49043,12,49073,49099,12,49129,49155,12,49185,49211,12,49241,49267,12,49297,49323,12,49353,49379,12,49409,49435,12,49465,49491,12,49521,49547,12,49577,49603,12,49633,49659,12,49689,49715,12,49745,49771,12,49801,49827,12,49857,49883,12,49913,49939,12,49969,49995,12,50025,50051,12,50081,50107,12,50137,50163,12,50193,50219,12,50249,50275,12,50305,50331,12,50361,50387,12,50417,50443,12,50473,50499,12,50529,50555,12,50585,50611,12,50641,50667,12,50697,50723,12,50753,50779,12,50809,50835,12,50865,50891,12,50921,50947,12,50977,51003,12,51033,51059,12,51089,51115,12,51145,51171,12,51201,51227,12,51257,51283,12,51313,51339,12,51369,51395,12,51425,51451,12,51481,51507,12,51537,51563,12,51593,51619,12,51649,51675,12,51705,51731,12,51761,51787,12,51817,51843,12,51873,51899,12,51929,51955,12,51985,52011,12,52041,52067,12,52097,52123,12,52153,52179,12,52209,52235,12,52265,52291,12,52321,52347,12,52377,52403,12,52433,52459,12,52489,52515,12,52545,52571,12,52601,52627,12,52657,52683,12,52713,52739,12,52769,52795,12,52825,52851,12,52881,52907,12,52937,52963,12,52993,53019,12,53049,53075,12,53105,53131,12,53161,53187,12,53217,53243,12,53273,53299,12,53329,53355,12,53385,53411,12,53441,53467,12,53497,53523,12,53553,53579,12,53609,53635,12,53665,53691,12,53721,53747,12,53777,53803,12,53833,53859,12,53889,53915,12,53945,53971,12,54001,54027,12,54057,54083,12,54113,54139,12,54169,54195,12,54225,54251,12,54281,54307,12,54337,54363,12,54393,54419,12,54449,54475,12,54505,54531,12,54561,54587,12,54617,54643,12,54673,54699,12,54729,54755,12,54785,54811,12,54841,54867,12,54897,54923,12,54953,54979,12,55009,55035,12,55065,55091,12,55121,55147,12,55177,55203,12,65024,65039,5,65520,65528,4,66422,66426,5,68152,68154,5,69291,69292,5,69633,69633,5,69747,69748,5,69811,69814,5,69826,69826,5,69932,69932,7,70016,70017,5,70079,70080,7,70095,70095,5,70196,70196,5,70367,70367,5,70402,70403,7,70464,70464,5,70487,70487,5,70709,70711,7,70725,70725,7,70833,70834,7,70843,70844,7,70849,70849,7,71090,71093,5,71103,71104,5,71227,71228,7,71339,71339,5,71344,71349,5,71458,71461,5,71727,71735,5,71985,71989,7,71998,71998,5,72002,72002,7,72154,72155,5,72193,72202,5,72251,72254,5,72281,72283,5,72344,72345,5,72766,72766,7,72874,72880,5,72885,72886,5,73023,73029,5,73104,73105,5,73111,73111,5,92912,92916,5,94095,94098,5,113824,113827,4,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,125252,125258,5,127183,127183,14,127340,127343,14,127377,127386,14,127491,127503,14,127548,127551,14,127744,127756,14,127761,127761,14,127769,127769,14,127773,127774,14,127780,127788,14,127796,127797,14,127820,127823,14,127869,127869,14,127894,127895,14,127902,127903,14,127943,127943,14,127947,127950,14,127972,127972,14,127988,127988,14,127992,127994,14,128009,128011,14,128019,128019,14,128023,128041,14,128064,128064,14,128102,128107,14,128174,128181,14,128238,128238,14,128246,128247,14,128254,128254,14,128264,128264,14,128278,128299,14,128329,128330,14,128348,128359,14,128371,128377,14,128392,128393,14,128401,128404,14,128421,128421,14,128433,128434,14,128450,128452,14,128476,128478,14,128483,128483,14,128495,128495,14,128506,128506,14,128519,128520,14,128528,128528,14,128534,128534,14,128538,128538,14,128540,128542,14,128544,128549,14,128552,128555,14,128557,128557,14,128560,128563,14,128565,128565,14,128567,128576,14,128581,128591,14,128641,128642,14,128646,128646,14,128648,128648,14,128650,128651,14,128653,128653,14,128655,128655,14,128657,128659,14,128661,128661,14,128663,128663,14,128665,128666,14,128674,128674,14,128676,128677,14,128679,128685,14,128690,128690,14,128694,128694,14,128697,128702,14,128704,128704,14,128710,128714,14,128716,128716,14,128720,128720,14,128723,128724,14,128726,128727,14,128733,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129008,129008,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129661,129663,14,129667,129670,14,129680,129685,14,129705,129708,14,129712,129718,14,129723,129727,14,129731,129733,14,129744,129750,14,129754,129759,14,129768,129775,14,129783,129791,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2192,2193,1,2250,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3132,3132,5,3137,3140,7,3146,3149,5,3170,3171,5,3202,3203,7,3262,3262,7,3264,3265,7,3267,3268,7,3271,3272,7,3276,3277,5,3298,3299,5,3330,3331,7,3390,3390,5,3393,3396,5,3402,3404,7,3406,3406,1,3426,3427,5,3458,3459,7,3535,3535,5,3538,3540,5,3544,3550,7,3570,3571,7,3635,3635,7,3655,3662,5,3763,3763,7,3784,3789,5,3893,3893,5,3897,3897,5,3953,3966,5,3968,3972,5,3981,3991,5,4038,4038,5,4145,4145,7,4153,4154,5,4157,4158,5,4184,4185,5,4209,4212,5,4228,4228,7,4237,4237,5,4352,4447,8,4520,4607,10,5906,5908,5,5938,5939,5,5970,5971,5,6068,6069,5,6071,6077,5,6086,6086,5,6089,6099,5,6155,6157,5,6159,6159,5,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6862,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7679,5,8204,8204,5,8206,8207,4,8233,8233,4,8252,8252,14,8288,8292,4,8294,8303,4,8413,8416,5,8418,8420,5,8482,8482,14,8596,8601,14,8986,8987,14,9096,9096,14,9193,9196,14,9199,9199,14,9201,9202,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9729,14,9732,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9775,9775,14,9784,9785,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9874,14,9876,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9934,14,9936,9936,14,9938,9938,14,9940,9940,14,9961,9961,14,9963,9967,14,9970,9971,14,9973,9973,14,9975,9977,14,9979,9980,14,9982,9985,14,9987,9988,14,9992,9996,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10083,14,10085,10087,14,10145,10145,14,10175,10175,14,11013,11015,14,11088,11088,14,11503,11505,5,11744,11775,5,12334,12335,5,12349,12349,14,12951,12951,14,42607,42607,5,42612,42621,5,42736,42737,5,43014,43014,5,43043,43044,7,43047,43047,7,43136,43137,7,43204,43205,5,43263,43263,5,43335,43345,5,43360,43388,8,43395,43395,7,43444,43445,7,43450,43451,7,43454,43456,7,43561,43566,5,43569,43570,5,43573,43574,5,43596,43596,5,43644,43644,5,43698,43700,5,43710,43711,5,43755,43755,7,43758,43759,7,43766,43766,5,44005,44005,5,44008,44008,5,44012,44012,7,44032,44032,11,44060,44060,11,44088,44088,11,44116,44116,11,44144,44144,11,44172,44172,11,44200,44200,11,44228,44228,11,44256,44256,11,44284,44284,11,44312,44312,11,44340,44340,11,44368,44368,11,44396,44396,11,44424,44424,11,44452,44452,11,44480,44480,11,44508,44508,11,44536,44536,11,44564,44564,11,44592,44592,11,44620,44620,11,44648,44648,11,44676,44676,11,44704,44704,11,44732,44732,11,44760,44760,11,44788,44788,11,44816,44816,11,44844,44844,11,44872,44872,11,44900,44900,11,44928,44928,11,44956,44956,11,44984,44984,11,45012,45012,11,45040,45040,11,45068,45068,11,45096,45096,11,45124,45124,11,45152,45152,11,45180,45180,11,45208,45208,11,45236,45236,11,45264,45264,11,45292,45292,11,45320,45320,11,45348,45348,11,45376,45376,11,45404,45404,11,45432,45432,11,45460,45460,11,45488,45488,11,45516,45516,11,45544,45544,11,45572,45572,11,45600,45600,11,45628,45628,11,45656,45656,11,45684,45684,11,45712,45712,11,45740,45740,11,45768,45768,11,45796,45796,11,45824,45824,11,45852,45852,11,45880,45880,11,45908,45908,11,45936,45936,11,45964,45964,11,45992,45992,11,46020,46020,11,46048,46048,11,46076,46076,11,46104,46104,11,46132,46132,11,46160,46160,11,46188,46188,11,46216,46216,11,46244,46244,11,46272,46272,11,46300,46300,11,46328,46328,11,46356,46356,11,46384,46384,11,46412,46412,11,46440,46440,11,46468,46468,11,46496,46496,11,46524,46524,11,46552,46552,11,46580,46580,11,46608,46608,11,46636,46636,11,46664,46664,11,46692,46692,11,46720,46720,11,46748,46748,11,46776,46776,11,46804,46804,11,46832,46832,11,46860,46860,11,46888,46888,11,46916,46916,11,46944,46944,11,46972,46972,11,47000,47000,11,47028,47028,11,47056,47056,11,47084,47084,11,47112,47112,11,47140,47140,11,47168,47168,11,47196,47196,11,47224,47224,11,47252,47252,11,47280,47280,11,47308,47308,11,47336,47336,11,47364,47364,11,47392,47392,11,47420,47420,11,47448,47448,11,47476,47476,11,47504,47504,11,47532,47532,11,47560,47560,11,47588,47588,11,47616,47616,11,47644,47644,11,47672,47672,11,47700,47700,11,47728,47728,11,47756,47756,11,47784,47784,11,47812,47812,11,47840,47840,11,47868,47868,11,47896,47896,11,47924,47924,11,47952,47952,11,47980,47980,11,48008,48008,11,48036,48036,11,48064,48064,11,48092,48092,11,48120,48120,11,48148,48148,11,48176,48176,11,48204,48204,11,48232,48232,11,48260,48260,11,48288,48288,11,48316,48316,11,48344,48344,11,48372,48372,11,48400,48400,11,48428,48428,11,48456,48456,11,48484,48484,11,48512,48512,11,48540,48540,11,48568,48568,11,48596,48596,11,48624,48624,11,48652,48652,11,48680,48680,11,48708,48708,11,48736,48736,11,48764,48764,11,48792,48792,11,48820,48820,11,48848,48848,11,48876,48876,11,48904,48904,11,48932,48932,11,48960,48960,11,48988,48988,11,49016,49016,11,49044,49044,11,49072,49072,11,49100,49100,11,49128,49128,11,49156,49156,11,49184,49184,11,49212,49212,11,49240,49240,11,49268,49268,11,49296,49296,11,49324,49324,11,49352,49352,11,49380,49380,11,49408,49408,11,49436,49436,11,49464,49464,11,49492,49492,11,49520,49520,11,49548,49548,11,49576,49576,11,49604,49604,11,49632,49632,11,49660,49660,11,49688,49688,11,49716,49716,11,49744,49744,11,49772,49772,11,49800,49800,11,49828,49828,11,49856,49856,11,49884,49884,11,49912,49912,11,49940,49940,11,49968,49968,11,49996,49996,11,50024,50024,11,50052,50052,11,50080,50080,11,50108,50108,11,50136,50136,11,50164,50164,11,50192,50192,11,50220,50220,11,50248,50248,11,50276,50276,11,50304,50304,11,50332,50332,11,50360,50360,11,50388,50388,11,50416,50416,11,50444,50444,11,50472,50472,11,50500,50500,11,50528,50528,11,50556,50556,11,50584,50584,11,50612,50612,11,50640,50640,11,50668,50668,11,50696,50696,11,50724,50724,11,50752,50752,11,50780,50780,11,50808,50808,11,50836,50836,11,50864,50864,11,50892,50892,11,50920,50920,11,50948,50948,11,50976,50976,11,51004,51004,11,51032,51032,11,51060,51060,11,51088,51088,11,51116,51116,11,51144,51144,11,51172,51172,11,51200,51200,11,51228,51228,11,51256,51256,11,51284,51284,11,51312,51312,11,51340,51340,11,51368,51368,11,51396,51396,11,51424,51424,11,51452,51452,11,51480,51480,11,51508,51508,11,51536,51536,11,51564,51564,11,51592,51592,11,51620,51620,11,51648,51648,11,51676,51676,11,51704,51704,11,51732,51732,11,51760,51760,11,51788,51788,11,51816,51816,11,51844,51844,11,51872,51872,11,51900,51900,11,51928,51928,11,51956,51956,11,51984,51984,11,52012,52012,11,52040,52040,11,52068,52068,11,52096,52096,11,52124,52124,11,52152,52152,11,52180,52180,11,52208,52208,11,52236,52236,11,52264,52264,11,52292,52292,11,52320,52320,11,52348,52348,11,52376,52376,11,52404,52404,11,52432,52432,11,52460,52460,11,52488,52488,11,52516,52516,11,52544,52544,11,52572,52572,11,52600,52600,11,52628,52628,11,52656,52656,11,52684,52684,11,52712,52712,11,52740,52740,11,52768,52768,11,52796,52796,11,52824,52824,11,52852,52852,11,52880,52880,11,52908,52908,11,52936,52936,11,52964,52964,11,52992,52992,11,53020,53020,11,53048,53048,11,53076,53076,11,53104,53104,11,53132,53132,11,53160,53160,11,53188,53188,11,53216,53216,11,53244,53244,11,53272,53272,11,53300,53300,11,53328,53328,11,53356,53356,11,53384,53384,11,53412,53412,11,53440,53440,11,53468,53468,11,53496,53496,11,53524,53524,11,53552,53552,11,53580,53580,11,53608,53608,11,53636,53636,11,53664,53664,11,53692,53692,11,53720,53720,11,53748,53748,11,53776,53776,11,53804,53804,11,53832,53832,11,53860,53860,11,53888,53888,11,53916,53916,11,53944,53944,11,53972,53972,11,54000,54000,11,54028,54028,11,54056,54056,11,54084,54084,11,54112,54112,11,54140,54140,11,54168,54168,11,54196,54196,11,54224,54224,11,54252,54252,11,54280,54280,11,54308,54308,11,54336,54336,11,54364,54364,11,54392,54392,11,54420,54420,11,54448,54448,11,54476,54476,11,54504,54504,11,54532,54532,11,54560,54560,11,54588,54588,11,54616,54616,11,54644,54644,11,54672,54672,11,54700,54700,11,54728,54728,11,54756,54756,11,54784,54784,11,54812,54812,11,54840,54840,11,54868,54868,11,54896,54896,11,54924,54924,11,54952,54952,11,54980,54980,11,55008,55008,11,55036,55036,11,55064,55064,11,55092,55092,11,55120,55120,11,55148,55148,11,55176,55176,11,55216,55238,9,64286,64286,5,65056,65071,5,65438,65439,5,65529,65531,4,66272,66272,5,68097,68099,5,68108,68111,5,68159,68159,5,68900,68903,5,69446,69456,5,69632,69632,7,69634,69634,7,69744,69744,5,69759,69761,5,69808,69810,7,69815,69816,7,69821,69821,1,69837,69837,1,69927,69931,5,69933,69940,5,70003,70003,5,70018,70018,7,70070,70078,5,70082,70083,1,70094,70094,7,70188,70190,7,70194,70195,7,70197,70197,7,70206,70206,5,70368,70370,7,70400,70401,5,70459,70460,5,70463,70463,7,70465,70468,7,70475,70477,7,70498,70499,7,70512,70516,5,70712,70719,5,70722,70724,5,70726,70726,5,70832,70832,5,70835,70840,5,70842,70842,5,70845,70845,5,70847,70848,5,70850,70851,5,71088,71089,7,71096,71099,7,71102,71102,7,71132,71133,5,71219,71226,5,71229,71229,5,71231,71232,5,71340,71340,7,71342,71343,7,71350,71350,7,71453,71455,5,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,118528,118573,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123566,123566,5,125136,125142,5,126976,126979,14,126981,127182,14,127184,127231,14,127279,127279,14,127344,127345,14,127374,127374,14,127405,127461,14,127489,127490,14,127514,127514,14,127538,127546,14,127561,127567,14,127570,127743,14,127757,127758,14,127760,127760,14,127762,127762,14,127766,127768,14,127770,127770,14,127772,127772,14,127775,127776,14,127778,127779,14,127789,127791,14,127794,127795,14,127798,127798,14,127819,127819,14,127824,127824,14,127868,127868,14,127870,127871,14,127892,127893,14,127896,127896,14,127900,127901,14,127904,127940,14,127942,127942,14,127944,127944,14,127946,127946,14,127951,127955,14,127968,127971,14,127973,127984,14,127987,127987,14,127989,127989,14,127991,127991,14,127995,127999,5,128008,128008,14,128012,128014,14,128017,128018,14,128020,128020,14,128022,128022,14,128042,128042,14,128063,128063,14,128065,128065,14,128101,128101,14,128108,128109,14,128173,128173,14,128182,128183,14,128236,128237,14,128239,128239,14,128245,128245,14,128248,128248,14,128253,128253,14,128255,128258,14,128260,128263,14,128265,128265,14,128277,128277,14,128300,128301,14,128326,128328,14,128331,128334,14,128336,128347,14,128360,128366,14,128369,128370,14,128378,128378,14,128391,128391,14,128394,128397,14,128400,128400,14,128405,128406,14,128420,128420,14,128422,128423,14,128425,128432,14,128435,128443,14,128445,128449,14,128453,128464,14,128468,128475,14,128479,128480,14,128482,128482,14,128484,128487,14,128489,128494,14,128496,128498,14,128500,128505,14,128507,128511,14,128513,128518,14,128521,128525,14,128527,128527,14,128529,128529,14,128533,128533,14,128535,128535,14,128537,128537,14]")}function j(P,q){if(P===0)return 0;const W=G(P,q);if(W!==void 0)return W;const $=new O(q,P);return $.prevCodePoint(),$.offset}n.getLeftDeleteOffset=j;function G(P,q){const W=new O(q,P);let $=W.prevCodePoint();for(;Q($)||$===65039||$===8419;){if(W.offset===0)return;$=W.prevCodePoint()}if(!be($))return;let ee=W.offset;return ee>0&&W.prevCodePoint()===8205&&(ee=W.offset),ee}function Q(P){return 127995<=P&&P<=127999}n.noBreakWhitespace="\xA0";class K{static getInstance(q){return i.cache.get(Array.from(q))}static getLocales(){return i._locales.value}constructor(q){this.confusableDictionary=q}isAmbiguous(q){return this.confusableDictionary.has(q)}getPrimaryConfusable(q){return this.confusableDictionary.get(q)}getConfusableCodePoints(){return new Set(this.confusableDictionary.keys())}}n.AmbiguousCharacters=K,i=K,K.ambiguousCharacterData=new A.Lazy(()=>JSON.parse('{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,8218,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,8242,96,1370,96,1523,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71922,67,71913,67,65315,67,8557,67,8450,67,8493,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71919,87,71910,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,66293,90,71909,90,65338,90,8484,90,8488,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65297,49,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125,119846,109],"_default":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"cs":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"es":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"fr":[65374,126,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"it":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ja":[8211,45,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65292,44,65307,59],"ko":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pt-BR":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ru":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"zh-hans":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41],"zh-hant":[8211,45,65374,126,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65307,59]}')),K.cache=new M.LRUCachedFunction(P=>{function q(se){const fe=new Map;for(let ge=0;ge<se.length;ge+=2)fe.set(se[ge],se[ge+1]);return fe}function W(se,fe){const ge=new Map(se);for(const[ve,Ee]of fe)ge.set(ve,Ee);return ge}function $(se,fe){if(!se)return fe;const ge=new Map;for(const[ve,Ee]of se)fe.has(ve)&&ge.set(ve,Ee);return ge}const ee=i.ambiguousCharacterData.value;let ie=P.filter(se=>!se.startsWith("_")&&se in ee);ie.length===0&&(ie=["_default"]);let oe;for(const se of ie){const fe=q(ee[se]);oe=$(oe,fe)}const ce=q(ee._common),he=W(ce,oe);return new i(he)}),K._locales=new A.Lazy(()=>Object.keys(i.ambiguousCharacterData.value).filter(P=>!P.startsWith("_")));class te{static getRawData(){return JSON.parse("[9,10,11,12,13,32,127,160,173,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8203,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12288,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999]")}static getData(){return this._data||(this._data=new Set(te.getRawData())),this._data}static isInvisibleCharacter(q){return te.getData().has(q)}static get codePoints(){return te.getData()}}n.InvisibleCharacters=te,te._data=void 0}),Y(X[39],J([0,1,6]),function(T,n,M){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.StringSHA1=n.toHexString=n.stringHash=n.numberHash=n.doHash=n.hash=void 0;function A(f){return i(f,0)}n.hash=A;function i(f,g){switch(typeof f){case"object":return f===null?d(349,g):Array.isArray(f)?h(f,g):o(f,g);case"string":return L(f,g);case"boolean":return m(f,g);case"number":return d(f,g);case"undefined":return d(937,g);default:return d(617,g)}}n.doHash=i;function d(f,g){return(g<<5)-g+f|0}n.numberHash=d;function m(f,g){return d(f?433:863,g)}function L(f,g){g=d(149417,g);for(let S=0,_=f.length;S<_;S++)g=d(f.charCodeAt(S),g);return g}n.stringHash=L;function h(f,g){return g=d(104579,g),f.reduce((S,_)=>i(_,S),g)}function o(f,g){return g=d(181387,g),Object.keys(f).sort().reduce((S,_)=>(S=L(_,S),i(f[_],S)),g)}function w(f,g,S=32){const _=S-g,E=~((1<<_)-1);return(f<<g|(E&f)>>>_)>>>0}function e(f,g=0,S=f.byteLength,_=0){for(let E=0;E<S;E++)f[g+E]=_}function a(f,g,S="0"){for(;f.length<g;)f=S+f;return f}function c(f,g=32){return f instanceof ArrayBuffer?Array.from(new Uint8Array(f)).map(S=>S.toString(16).padStart(2,"0")).join(""):a((f>>>0).toString(16),g/4)}n.toHexString=c;class l{constructor(){this._h0=1732584193,this._h1=4023233417,this._h2=2562383102,this._h3=271733878,this._h4=3285377520,this._buff=new Uint8Array(64+3),this._buffDV=new DataView(this._buff.buffer),this._buffLen=0,this._totalLen=0,this._leftoverHighSurrogate=0,this._finished=!1}update(g){const S=g.length;if(S===0)return;const _=this._buff;let E=this._buffLen,y=this._leftoverHighSurrogate,v,r;for(y!==0?(v=y,r=-1,y=0):(v=g.charCodeAt(0),r=0);;){let s=v;if(M.isHighSurrogate(v))if(r+1<S){const u=g.charCodeAt(r+1);M.isLowSurrogate(u)?(r++,s=M.computeCodePoint(v,u)):s=65533}else{y=v;break}else M.isLowSurrogate(v)&&(s=65533);if(E=this._push(_,E,s),r++,r<S)v=g.charCodeAt(r);else break}this._buffLen=E,this._leftoverHighSurrogate=y}_push(g,S,_){return _<128?g[S++]=_:_<2048?(g[S++]=192|(_&1984)>>>6,g[S++]=128|(_&63)>>>0):_<65536?(g[S++]=224|(_&61440)>>>12,g[S++]=128|(_&4032)>>>6,g[S++]=128|(_&63)>>>0):(g[S++]=240|(_&1835008)>>>18,g[S++]=128|(_&258048)>>>12,g[S++]=128|(_&4032)>>>6,g[S++]=128|(_&63)>>>0),S>=64&&(this._step(),S-=64,this._totalLen+=64,g[0]=g[64+0],g[1]=g[64+1],g[2]=g[64+2]),S}digest(){return this._finished||(this._finished=!0,this._leftoverHighSurrogate&&(this._leftoverHighSurrogate=0,this._buffLen=this._push(this._buff,this._buffLen,65533)),this._totalLen+=this._buffLen,this._wrapUp()),c(this._h0)+c(this._h1)+c(this._h2)+c(this._h3)+c(this._h4)}_wrapUp(){this._buff[this._buffLen++]=128,e(this._buff,this._buffLen),this._buffLen>56&&(this._step(),e(this._buff));const g=8*this._totalLen;this._buffDV.setUint32(56,Math.floor(g/4294967296),!1),this._buffDV.setUint32(60,g%4294967296,!1),this._step()}_step(){const g=l._bigBlock32,S=this._buffDV;for(let b=0;b<64;b+=4)g.setUint32(b,S.getUint32(b,!1),!1);for(let b=64;b<320;b+=4)g.setUint32(b,w(g.getUint32(b-12,!1)^g.getUint32(b-32,!1)^g.getUint32(b-56,!1)^g.getUint32(b-64,!1),1),!1);let _=this._h0,E=this._h1,y=this._h2,v=this._h3,r=this._h4,s,u,p;for(let b=0;b<80;b++)b<20?(s=E&y|~E&v,u=1518500249):b<40?(s=E^y^v,u=1859775393):b<60?(s=E&y|E&v|y&v,u=2400959708):(s=E^y^v,u=3395469782),p=w(_,5)+s+r+u+g.getUint32(b*4,!1)&4294967295,r=v,v=y,y=w(E,30),E=_,_=p;this._h0=this._h0+_&4294967295,this._h1=this._h1+E&4294967295,this._h2=this._h2+y&4294967295,this._h3=this._h3+v&4294967295,this._h4=this._h4+r&4294967295}}n.StringSHA1=l,l._bigBlock32=new DataView(new ArrayBuffer(320))}),Y(X[24],J([0,1,34,39]),function(T,n,M,A){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.LcsDiff=n.stringDiff=n.StringDiffSequence=void 0;class i{constructor(e){this.source=e}getElements(){const e=this.source,a=new Int32Array(e.length);for(let c=0,l=e.length;c<l;c++)a[c]=e.charCodeAt(c);return a}}n.StringDiffSequence=i;function d(w,e,a){return new o(new i(w),new i(e)).ComputeDiff(a).changes}n.stringDiff=d;class m{static Assert(e,a){if(!e)throw new Error(a)}}class L{static Copy(e,a,c,l,f){for(let g=0;g<f;g++)c[l+g]=e[a+g]}static Copy2(e,a,c,l,f){for(let g=0;g<f;g++)c[l+g]=e[a+g]}}class h{constructor(){this.m_changes=[],this.m_originalStart=1073741824,this.m_modifiedStart=1073741824,this.m_originalCount=0,this.m_modifiedCount=0}MarkNextChange(){(this.m_originalCount>0||this.m_modifiedCount>0)&&this.m_changes.push(new M.DiffChange(this.m_originalStart,this.m_originalCount,this.m_modifiedStart,this.m_modifiedCount)),this.m_originalCount=0,this.m_modifiedCount=0,this.m_originalStart=1073741824,this.m_modifiedStart=1073741824}AddOriginalElement(e,a){this.m_originalStart=Math.min(this.m_originalStart,e),this.m_modifiedStart=Math.min(this.m_modifiedStart,a),this.m_originalCount++}AddModifiedElement(e,a){this.m_originalStart=Math.min(this.m_originalStart,e),this.m_modifiedStart=Math.min(this.m_modifiedStart,a),this.m_modifiedCount++}getChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes}getReverseChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes.reverse(),this.m_changes}}class o{constructor(e,a,c=null){this.ContinueProcessingPredicate=c,this._originalSequence=e,this._modifiedSequence=a;const[l,f,g]=o._getElements(e),[S,_,E]=o._getElements(a);this._hasStrings=g&&E,this._originalStringElements=l,this._originalElementsOrHash=f,this._modifiedStringElements=S,this._modifiedElementsOrHash=_,this.m_forwardHistory=[],this.m_reverseHistory=[]}static _isStringArray(e){return e.length>0&&typeof e[0]=="string"}static _getElements(e){const a=e.getElements();if(o._isStringArray(a)){const c=new Int32Array(a.length);for(let l=0,f=a.length;l<f;l++)c[l]=(0,A.stringHash)(a[l],0);return[a,c,!0]}return a instanceof Int32Array?[[],a,!1]:[[],new Int32Array(a),!1]}ElementsAreEqual(e,a){return this._originalElementsOrHash[e]!==this._modifiedElementsOrHash[a]?!1:this._hasStrings?this._originalStringElements[e]===this._modifiedStringElements[a]:!0}ElementsAreStrictEqual(e,a){if(!this.ElementsAreEqual(e,a))return!1;const c=o._getStrictElement(this._originalSequence,e),l=o._getStrictElement(this._modifiedSequence,a);return c===l}static _getStrictElement(e,a){return typeof e.getStrictElement=="function"?e.getStrictElement(a):null}OriginalElementsAreEqual(e,a){return this._originalElementsOrHash[e]!==this._originalElementsOrHash[a]?!1:this._hasStrings?this._originalStringElements[e]===this._originalStringElements[a]:!0}ModifiedElementsAreEqual(e,a){return this._modifiedElementsOrHash[e]!==this._modifiedElementsOrHash[a]?!1:this._hasStrings?this._modifiedStringElements[e]===this._modifiedStringElements[a]:!0}ComputeDiff(e){return this._ComputeDiff(0,this._originalElementsOrHash.length-1,0,this._modifiedElementsOrHash.length-1,e)}_ComputeDiff(e,a,c,l,f){const g=[!1];let S=this.ComputeDiffRecursive(e,a,c,l,g);return f&&(S=this.PrettifyChanges(S)),{quitEarly:g[0],changes:S}}ComputeDiffRecursive(e,a,c,l,f){for(f[0]=!1;e<=a&&c<=l&&this.ElementsAreEqual(e,c);)e++,c++;for(;a>=e&&l>=c&&this.ElementsAreEqual(a,l);)a--,l--;if(e>a||c>l){let v;return c<=l?(m.Assert(e===a+1,"originalStart should only be one more than originalEnd"),v=[new M.DiffChange(e,0,c,l-c+1)]):e<=a?(m.Assert(c===l+1,"modifiedStart should only be one more than modifiedEnd"),v=[new M.DiffChange(e,a-e+1,c,0)]):(m.Assert(e===a+1,"originalStart should only be one more than originalEnd"),m.Assert(c===l+1,"modifiedStart should only be one more than modifiedEnd"),v=[]),v}const g=[0],S=[0],_=this.ComputeRecursionPoint(e,a,c,l,g,S,f),E=g[0],y=S[0];if(_!==null)return _;if(!f[0]){const v=this.ComputeDiffRecursive(e,E,c,y,f);let r=[];return f[0]?r=[new M.DiffChange(E+1,a-(E+1)+1,y+1,l-(y+1)+1)]:r=this.ComputeDiffRecursive(E+1,a,y+1,l,f),this.ConcatenateChanges(v,r)}return[new M.DiffChange(e,a-e+1,c,l-c+1)]}WALKTRACE(e,a,c,l,f,g,S,_,E,y,v,r,s,u,p,b,C,N){let R=null,D=null,k=new h,U=a,I=c,B=s[0]-b[0]-l,z=-1073741824,x=this.m_forwardHistory.length-1;do{const O=B+e;O===U||O<I&&E[O-1]<E[O+1]?(v=E[O+1],u=v-B-l,v<z&&k.MarkNextChange(),z=v,k.AddModifiedElement(v+1,u),B=O+1-e):(v=E[O-1]+1,u=v-B-l,v<z&&k.MarkNextChange(),z=v-1,k.AddOriginalElement(v,u+1),B=O-1-e),x>=0&&(E=this.m_forwardHistory[x],e=E[0],U=1,I=E.length-1)}while(--x>=-1);if(R=k.getReverseChanges(),N[0]){let O=s[0]+1,F=b[0]+1;if(R!==null&&R.length>0){const H=R[R.length-1];O=Math.max(O,H.getOriginalEnd()),F=Math.max(F,H.getModifiedEnd())}D=[new M.DiffChange(O,r-O+1,F,p-F+1)]}else{k=new h,U=g,I=S,B=s[0]-b[0]-_,z=1073741824,x=C?this.m_reverseHistory.length-1:this.m_reverseHistory.length-2;do{const O=B+f;O===U||O<I&&y[O-1]>=y[O+1]?(v=y[O+1]-1,u=v-B-_,v>z&&k.MarkNextChange(),z=v+1,k.AddOriginalElement(v+1,u+1),B=O+1-f):(v=y[O-1],u=v-B-_,v>z&&k.MarkNextChange(),z=v,k.AddModifiedElement(v+1,u+1),B=O-1-f),x>=0&&(y=this.m_reverseHistory[x],f=y[0],U=1,I=y.length-1)}while(--x>=-1);D=k.getChanges()}return this.ConcatenateChanges(R,D)}ComputeRecursionPoint(e,a,c,l,f,g,S){let _=0,E=0,y=0,v=0,r=0,s=0;e--,c--,f[0]=0,g[0]=0,this.m_forwardHistory=[],this.m_reverseHistory=[];const u=a-e+(l-c),p=u+1,b=new Int32Array(p),C=new Int32Array(p),N=l-c,R=a-e,D=e-c,k=a-l,I=(R-N)%2===0;b[N]=e,C[R]=a,S[0]=!1;for(let B=1;B<=u/2+1;B++){let z=0,x=0;y=this.ClipDiagonalBound(N-B,B,N,p),v=this.ClipDiagonalBound(N+B,B,N,p);for(let F=y;F<=v;F+=2){F===y||F<v&&b[F-1]<b[F+1]?_=b[F+1]:_=b[F-1]+1,E=_-(F-N)-D;const H=_;for(;_<a&&E<l&&this.ElementsAreEqual(_+1,E+1);)_++,E++;if(b[F]=_,_+E>z+x&&(z=_,x=E),!I&&Math.abs(F-R)<=B-1&&_>=C[F])return f[0]=_,g[0]=E,H<=C[F]&&1447>0&&B<=1447+1?this.WALKTRACE(N,y,v,D,R,r,s,k,b,C,_,a,f,E,l,g,I,S):null}const O=(z-e+(x-c)-B)/2;if(this.ContinueProcessingPredicate!==null&&!this.ContinueProcessingPredicate(z,O))return S[0]=!0,f[0]=z,g[0]=x,O>0&&1447>0&&B<=1447+1?this.WALKTRACE(N,y,v,D,R,r,s,k,b,C,_,a,f,E,l,g,I,S):(e++,c++,[new M.DiffChange(e,a-e+1,c,l-c+1)]);r=this.ClipDiagonalBound(R-B,B,R,p),s=this.ClipDiagonalBound(R+B,B,R,p);for(let F=r;F<=s;F+=2){F===r||F<s&&C[F-1]>=C[F+1]?_=C[F+1]-1:_=C[F-1],E=_-(F-R)-k;const H=_;for(;_>e&&E>c&&this.ElementsAreEqual(_,E);)_--,E--;if(C[F]=_,I&&Math.abs(F-N)<=B&&_<=b[F])return f[0]=_,g[0]=E,H>=b[F]&&1447>0&&B<=1447+1?this.WALKTRACE(N,y,v,D,R,r,s,k,b,C,_,a,f,E,l,g,I,S):null}if(B<=1447){let F=new Int32Array(v-y+2);F[0]=N-y+1,L.Copy2(b,y,F,1,v-y+1),this.m_forwardHistory.push(F),F=new Int32Array(s-r+2),F[0]=R-r+1,L.Copy2(C,r,F,1,s-r+1),this.m_reverseHistory.push(F)}}return this.WALKTRACE(N,y,v,D,R,r,s,k,b,C,_,a,f,E,l,g,I,S)}PrettifyChanges(e){for(let a=0;a<e.length;a++){const c=e[a],l=a<e.length-1?e[a+1].originalStart:this._originalElementsOrHash.length,f=a<e.length-1?e[a+1].modifiedStart:this._modifiedElementsOrHash.length,g=c.originalLength>0,S=c.modifiedLength>0;for(;c.originalStart+c.originalLength<l&&c.modifiedStart+c.modifiedLength<f&&(!g||this.OriginalElementsAreEqual(c.originalStart,c.originalStart+c.originalLength))&&(!S||this.ModifiedElementsAreEqual(c.modifiedStart,c.modifiedStart+c.modifiedLength));){const E=this.ElementsAreStrictEqual(c.originalStart,c.modifiedStart);if(this.ElementsAreStrictEqual(c.originalStart+c.originalLength,c.modifiedStart+c.modifiedLength)&&!E)break;c.originalStart++,c.modifiedStart++}const _=[null];if(a<e.length-1&&this.ChangesOverlap(e[a],e[a+1],_)){e[a]=_[0],e.splice(a+1,1),a--;continue}}for(let a=e.length-1;a>=0;a--){const c=e[a];let l=0,f=0;if(a>0){const v=e[a-1];l=v.originalStart+v.originalLength,f=v.modifiedStart+v.modifiedLength}const g=c.originalLength>0,S=c.modifiedLength>0;let _=0,E=this._boundaryScore(c.originalStart,c.originalLength,c.modifiedStart,c.modifiedLength);for(let v=1;;v++){const r=c.originalStart-v,s=c.modifiedStart-v;if(r<l||s<f||g&&!this.OriginalElementsAreEqual(r,r+c.originalLength)||S&&!this.ModifiedElementsAreEqual(s,s+c.modifiedLength))break;const p=(r===l&&s===f?5:0)+this._boundaryScore(r,c.originalLength,s,c.modifiedLength);p>E&&(E=p,_=v)}c.originalStart-=_,c.modifiedStart-=_;const y=[null];if(a>0&&this.ChangesOverlap(e[a-1],e[a],y)){e[a-1]=y[0],e.splice(a,1),a++;continue}}if(this._hasStrings)for(let a=1,c=e.length;a<c;a++){const l=e[a-1],f=e[a],g=f.originalStart-l.originalStart-l.originalLength,S=l.originalStart,_=f.originalStart+f.originalLength,E=_-S,y=l.modifiedStart,v=f.modifiedStart+f.modifiedLength,r=v-y;if(g<5&&E<20&&r<20){const s=this._findBetterContiguousSequence(S,E,y,r,g);if(s){const[u,p]=s;(u!==l.originalStart+l.originalLength||p!==l.modifiedStart+l.modifiedLength)&&(l.originalLength=u-l.originalStart,l.modifiedLength=p-l.modifiedStart,f.originalStart=u+g,f.modifiedStart=p+g,f.originalLength=_-f.originalStart,f.modifiedLength=v-f.modifiedStart)}}}return e}_findBetterContiguousSequence(e,a,c,l,f){if(a<f||l<f)return null;const g=e+a-f+1,S=c+l-f+1;let _=0,E=0,y=0;for(let v=e;v<g;v++)for(let r=c;r<S;r++){const s=this._contiguousSequenceScore(v,r,f);s>0&&s>_&&(_=s,E=v,y=r)}return _>0?[E,y]:null}_contiguousSequenceScore(e,a,c){let l=0;for(let f=0;f<c;f++){if(!this.ElementsAreEqual(e+f,a+f))return 0;l+=this._originalStringElements[e+f].length}return l}_OriginalIsBoundary(e){return e<=0||e>=this._originalElementsOrHash.length-1?!0:this._hasStrings&&/^\s*$/.test(this._originalStringElements[e])}_OriginalRegionIsBoundary(e,a){if(this._OriginalIsBoundary(e)||this._OriginalIsBoundary(e-1))return!0;if(a>0){const c=e+a;if(this._OriginalIsBoundary(c-1)||this._OriginalIsBoundary(c))return!0}return!1}_ModifiedIsBoundary(e){return e<=0||e>=this._modifiedElementsOrHash.length-1?!0:this._hasStrings&&/^\s*$/.test(this._modifiedStringElements[e])}_ModifiedRegionIsBoundary(e,a){if(this._ModifiedIsBoundary(e)||this._ModifiedIsBoundary(e-1))return!0;if(a>0){const c=e+a;if(this._ModifiedIsBoundary(c-1)||this._ModifiedIsBoundary(c))return!0}return!1}_boundaryScore(e,a,c,l){const f=this._OriginalRegionIsBoundary(e,a)?1:0,g=this._ModifiedRegionIsBoundary(c,l)?1:0;return f+g}ConcatenateChanges(e,a){const c=[];if(e.length===0||a.length===0)return a.length>0?a:e;if(this.ChangesOverlap(e[e.length-1],a[0],c)){const l=new Array(e.length+a.length-1);return L.Copy(e,0,l,0,e.length-1),l[e.length-1]=c[0],L.Copy(a,1,l,e.length,a.length-1),l}else{const l=new Array(e.length+a.length);return L.Copy(e,0,l,0,e.length),L.Copy(a,0,l,e.length,a.length),l}}ChangesOverlap(e,a,c){if(m.Assert(e.originalStart<=a.originalStart,"Left change is not less than or equal to right change"),m.Assert(e.modifiedStart<=a.modifiedStart,"Left change is not less than or equal to right change"),e.originalStart+e.originalLength>=a.originalStart||e.modifiedStart+e.modifiedLength>=a.modifiedStart){const l=e.originalStart;let f=e.originalLength;const g=e.modifiedStart;let S=e.modifiedLength;return e.originalStart+e.originalLength>=a.originalStart&&(f=a.originalStart+a.originalLength-e.originalStart),e.modifiedStart+e.modifiedLength>=a.modifiedStart&&(S=a.modifiedStart+a.modifiedLength-e.modifiedStart),c[0]=new M.DiffChange(l,f,g,S),!0}else return c[0]=null,!1}ClipDiagonalBound(e,a,c,l){if(e>=0&&e<l)return e;const f=c,g=l-c-1,S=a%2===0;if(e<0){const _=f%2===0;return S===_?0:1}else{const _=g%2===0;return S===_?l-1:l-2}}}n.LcsDiff=o}),Y(X[25],J([0,1]),function(T,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.validateConstraint=n.validateConstraints=n.isFunction=n.assertIsDefined=n.assertType=n.isUndefinedOrNull=n.isDefined=n.isUndefined=n.isBoolean=n.isIterable=n.isNumber=n.isTypedArray=n.isObject=n.isString=void 0;function M(g){return typeof g=="string"}n.isString=M;function A(g){return typeof g=="object"&&g!==null&&!Array.isArray(g)&&!(g instanceof RegExp)&&!(g instanceof Date)}n.isObject=A;function i(g){const S=Object.getPrototypeOf(Uint8Array);return typeof g=="object"&&g instanceof S}n.isTypedArray=i;function d(g){return typeof g=="number"&&!isNaN(g)}n.isNumber=d;function m(g){return!!g&&typeof g[Symbol.iterator]=="function"}n.isIterable=m;function L(g){return g===!0||g===!1}n.isBoolean=L;function h(g){return typeof g>"u"}n.isUndefined=h;function o(g){return!w(g)}n.isDefined=o;function w(g){return h(g)||g===null}n.isUndefinedOrNull=w;function e(g,S){if(!g)throw new Error(S?`Unexpected type, expected '${S}'`:"Unexpected type")}n.assertType=e;function a(g){if(w(g))throw new Error("Assertion Failed: argument is undefined or null");return g}n.assertIsDefined=a;function c(g){return typeof g=="function"}n.isFunction=c;function l(g,S){const _=Math.min(g.length,S.length);for(let E=0;E<_;E++)f(g[E],S[E])}n.validateConstraints=l;function f(g,S){if(M(S)){if(typeof g!==S)throw new Error(`argument does not match constraint: typeof ${S}`)}else if(c(S)){try{if(g instanceof S)return}catch{}if(!w(g)&&g.constructor===S||S.length===1&&S.call(void 0,g)===!0)return;throw new Error("argument does not match one of these constraints: arg instanceof constraint, arg.constructor === constraint, nor constraint(arg) === true")}}n.validateConstraint=f}),Y(X[40],J([0,1,25]),function(T,n,M){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Codicon=n.getCodiconFontCharacters=void 0;const A=Object.create(null);function i(m,L){if((0,M.isString)(L)){const h=A[L];if(h===void 0)throw new Error(`${m} references an unknown codicon: ${L}`);L=h}return A[m]=L,{id:m}}function d(){return A}n.getCodiconFontCharacters=d,n.Codicon={add:i("add",6e4),plus:i("plus",6e4),gistNew:i("gist-new",6e4),repoCreate:i("repo-create",6e4),lightbulb:i("lightbulb",60001),lightBulb:i("light-bulb",60001),repo:i("repo",60002),repoDelete:i("repo-delete",60002),gistFork:i("gist-fork",60003),repoForked:i("repo-forked",60003),gitPullRequest:i("git-pull-request",60004),gitPullRequestAbandoned:i("git-pull-request-abandoned",60004),recordKeys:i("record-keys",60005),keyboard:i("keyboard",60005),tag:i("tag",60006),tagAdd:i("tag-add",60006),tagRemove:i("tag-remove",60006),gitPullRequestLabel:i("git-pull-request-label",60006),person:i("person",60007),personFollow:i("person-follow",60007),personOutline:i("person-outline",60007),personFilled:i("person-filled",60007),gitBranch:i("git-branch",60008),gitBranchCreate:i("git-branch-create",60008),gitBranchDelete:i("git-branch-delete",60008),sourceControl:i("source-control",60008),mirror:i("mirror",60009),mirrorPublic:i("mirror-public",60009),star:i("star",60010),starAdd:i("star-add",60010),starDelete:i("star-delete",60010),starEmpty:i("star-empty",60010),comment:i("comment",60011),commentAdd:i("comment-add",60011),alert:i("alert",60012),warning:i("warning",60012),search:i("search",60013),searchSave:i("search-save",60013),logOut:i("log-out",60014),signOut:i("sign-out",60014),logIn:i("log-in",60015),signIn:i("sign-in",60015),eye:i("eye",60016),eyeUnwatch:i("eye-unwatch",60016),eyeWatch:i("eye-watch",60016),circleFilled:i("circle-filled",60017),primitiveDot:i("primitive-dot",60017),closeDirty:i("close-dirty",60017),debugBreakpoint:i("debug-breakpoint",60017),debugBreakpointDisabled:i("debug-breakpoint-disabled",60017),debugHint:i("debug-hint",60017),primitiveSquare:i("primitive-square",60018),edit:i("edit",60019),pencil:i("pencil",60019),info:i("info",60020),issueOpened:i("issue-opened",60020),gistPrivate:i("gist-private",60021),gitForkPrivate:i("git-fork-private",60021),lock:i("lock",60021),mirrorPrivate:i("mirror-private",60021),close:i("close",60022),removeClose:i("remove-close",60022),x:i("x",60022),repoSync:i("repo-sync",60023),sync:i("sync",60023),clone:i("clone",60024),desktopDownload:i("desktop-download",60024),beaker:i("beaker",60025),microscope:i("microscope",60025),vm:i("vm",60026),deviceDesktop:i("device-desktop",60026),file:i("file",60027),fileText:i("file-text",60027),more:i("more",60028),ellipsis:i("ellipsis",60028),kebabHorizontal:i("kebab-horizontal",60028),mailReply:i("mail-reply",60029),reply:i("reply",60029),organization:i("organization",60030),organizationFilled:i("organization-filled",60030),organizationOutline:i("organization-outline",60030),newFile:i("new-file",60031),fileAdd:i("file-add",60031),newFolder:i("new-folder",60032),fileDirectoryCreate:i("file-directory-create",60032),trash:i("trash",60033),trashcan:i("trashcan",60033),history:i("history",60034),clock:i("clock",60034),folder:i("folder",60035),fileDirectory:i("file-directory",60035),symbolFolder:i("symbol-folder",60035),logoGithub:i("logo-github",60036),markGithub:i("mark-github",60036),github:i("github",60036),terminal:i("terminal",60037),console:i("console",60037),repl:i("repl",60037),zap:i("zap",60038),symbolEvent:i("symbol-event",60038),error:i("error",60039),stop:i("stop",60039),variable:i("variable",60040),symbolVariable:i("symbol-variable",60040),array:i("array",60042),symbolArray:i("symbol-array",60042),symbolModule:i("symbol-module",60043),symbolPackage:i("symbol-package",60043),symbolNamespace:i("symbol-namespace",60043),symbolObject:i("symbol-object",60043),symbolMethod:i("symbol-method",60044),symbolFunction:i("symbol-function",60044),symbolConstructor:i("symbol-constructor",60044),symbolBoolean:i("symbol-boolean",60047),symbolNull:i("symbol-null",60047),symbolNumeric:i("symbol-numeric",60048),symbolNumber:i("symbol-number",60048),symbolStructure:i("symbol-structure",60049),symbolStruct:i("symbol-struct",60049),symbolParameter:i("symbol-parameter",60050),symbolTypeParameter:i("symbol-type-parameter",60050),symbolKey:i("symbol-key",60051),symbolText:i("symbol-text",60051),symbolReference:i("symbol-reference",60052),goToFile:i("go-to-file",60052),symbolEnum:i("symbol-enum",60053),symbolValue:i("symbol-value",60053),symbolRuler:i("symbol-ruler",60054),symbolUnit:i("symbol-unit",60054),activateBreakpoints:i("activate-breakpoints",60055),archive:i("archive",60056),arrowBoth:i("arrow-both",60057),arrowDown:i("arrow-down",60058),arrowLeft:i("arrow-left",60059),arrowRight:i("arrow-right",60060),arrowSmallDown:i("arrow-small-down",60061),arrowSmallLeft:i("arrow-small-left",60062),arrowSmallRight:i("arrow-small-right",60063),arrowSmallUp:i("arrow-small-up",60064),arrowUp:i("arrow-up",60065),bell:i("bell",60066),bold:i("bold",60067),book:i("book",60068),bookmark:i("bookmark",60069),debugBreakpointConditionalUnverified:i("debug-breakpoint-conditional-unverified",60070),debugBreakpointConditional:i("debug-breakpoint-conditional",60071),debugBreakpointConditionalDisabled:i("debug-breakpoint-conditional-disabled",60071),debugBreakpointDataUnverified:i("debug-breakpoint-data-unverified",60072),debugBreakpointData:i("debug-breakpoint-data",60073),debugBreakpointDataDisabled:i("debug-breakpoint-data-disabled",60073),debugBreakpointLogUnverified:i("debug-breakpoint-log-unverified",60074),debugBreakpointLog:i("debug-breakpoint-log",60075),debugBreakpointLogDisabled:i("debug-breakpoint-log-disabled",60075),briefcase:i("briefcase",60076),broadcast:i("broadcast",60077),browser:i("browser",60078),bug:i("bug",60079),calendar:i("calendar",60080),caseSensitive:i("case-sensitive",60081),check:i("check",60082),checklist:i("checklist",60083),chevronDown:i("chevron-down",60084),dropDownButton:i("drop-down-button",60084),chevronLeft:i("chevron-left",60085),chevronRight:i("chevron-right",60086),chevronUp:i("chevron-up",60087),chromeClose:i("chrome-close",60088),chromeMaximize:i("chrome-maximize",60089),chromeMinimize:i("chrome-minimize",60090),chromeRestore:i("chrome-restore",60091),circle:i("circle",60092),circleOutline:i("circle-outline",60092),debugBreakpointUnverified:i("debug-breakpoint-unverified",60092),circleSlash:i("circle-slash",60093),circuitBoard:i("circuit-board",60094),clearAll:i("clear-all",60095),clippy:i("clippy",60096),closeAll:i("close-all",60097),cloudDownload:i("cloud-download",60098),cloudUpload:i("cloud-upload",60099),code:i("code",60100),collapseAll:i("collapse-all",60101),colorMode:i("color-mode",60102),commentDiscussion:i("comment-discussion",60103),compareChanges:i("compare-changes",60157),creditCard:i("credit-card",60105),dash:i("dash",60108),dashboard:i("dashboard",60109),database:i("database",60110),debugContinue:i("debug-continue",60111),debugDisconnect:i("debug-disconnect",60112),debugPause:i("debug-pause",60113),debugRestart:i("debug-restart",60114),debugStart:i("debug-start",60115),debugStepInto:i("debug-step-into",60116),debugStepOut:i("debug-step-out",60117),debugStepOver:i("debug-step-over",60118),debugStop:i("debug-stop",60119),debug:i("debug",60120),deviceCameraVideo:i("device-camera-video",60121),deviceCamera:i("device-camera",60122),deviceMobile:i("device-mobile",60123),diffAdded:i("diff-added",60124),diffIgnored:i("diff-ignored",60125),diffModified:i("diff-modified",60126),diffRemoved:i("diff-removed",60127),diffRenamed:i("diff-renamed",60128),diff:i("diff",60129),discard:i("discard",60130),editorLayout:i("editor-layout",60131),emptyWindow:i("empty-window",60132),exclude:i("exclude",60133),extensions:i("extensions",60134),eyeClosed:i("eye-closed",60135),fileBinary:i("file-binary",60136),fileCode:i("file-code",60137),fileMedia:i("file-media",60138),filePdf:i("file-pdf",60139),fileSubmodule:i("file-submodule",60140),fileSymlinkDirectory:i("file-symlink-directory",60141),fileSymlinkFile:i("file-symlink-file",60142),fileZip:i("file-zip",60143),files:i("files",60144),filter:i("filter",60145),flame:i("flame",60146),foldDown:i("fold-down",60147),foldUp:i("fold-up",60148),fold:i("fold",60149),folderActive:i("folder-active",60150),folderOpened:i("folder-opened",60151),gear:i("gear",60152),gift:i("gift",60153),gistSecret:i("gist-secret",60154),gist:i("gist",60155),gitCommit:i("git-commit",60156),gitCompare:i("git-compare",60157),gitMerge:i("git-merge",60158),githubAction:i("github-action",60159),githubAlt:i("github-alt",60160),globe:i("globe",60161),grabber:i("grabber",60162),graph:i("graph",60163),gripper:i("gripper",60164),heart:i("heart",60165),home:i("home",60166),horizontalRule:i("horizontal-rule",60167),hubot:i("hubot",60168),inbox:i("inbox",60169),issueClosed:i("issue-closed",60324),issueReopened:i("issue-reopened",60171),issues:i("issues",60172),italic:i("italic",60173),jersey:i("jersey",60174),json:i("json",60175),bracket:i("bracket",60175),kebabVertical:i("kebab-vertical",60176),key:i("key",60177),law:i("law",60178),lightbulbAutofix:i("lightbulb-autofix",60179),linkExternal:i("link-external",60180),link:i("link",60181),listOrdered:i("list-ordered",60182),listUnordered:i("list-unordered",60183),liveShare:i("live-share",60184),loading:i("loading",60185),location:i("location",60186),mailRead:i("mail-read",60187),mail:i("mail",60188),markdown:i("markdown",60189),megaphone:i("megaphone",60190),mention:i("mention",60191),milestone:i("milestone",60192),gitPullRequestMilestone:i("git-pull-request-milestone",60192),mortarBoard:i("mortar-board",60193),move:i("move",60194),multipleWindows:i("multiple-windows",60195),mute:i("mute",60196),noNewline:i("no-newline",60197),note:i("note",60198),octoface:i("octoface",60199),openPreview:i("open-preview",60200),package:i("package",60201),paintcan:i("paintcan",60202),pin:i("pin",60203),play:i("play",60204),run:i("run",60204),plug:i("plug",60205),preserveCase:i("preserve-case",60206),preview:i("preview",60207),project:i("project",60208),pulse:i("pulse",60209),question:i("question",60210),quote:i("quote",60211),radioTower:i("radio-tower",60212),reactions:i("reactions",60213),references:i("references",60214),refresh:i("refresh",60215),regex:i("regex",60216),remoteExplorer:i("remote-explorer",60217),remote:i("remote",60218),remove:i("remove",60219),replaceAll:i("replace-all",60220),replace:i("replace",60221),repoClone:i("repo-clone",60222),repoForcePush:i("repo-force-push",60223),repoPull:i("repo-pull",60224),repoPush:i("repo-push",60225),report:i("report",60226),requestChanges:i("request-changes",60227),rocket:i("rocket",60228),rootFolderOpened:i("root-folder-opened",60229),rootFolder:i("root-folder",60230),rss:i("rss",60231),ruby:i("ruby",60232),saveAll:i("save-all",60233),saveAs:i("save-as",60234),save:i("save",60235),screenFull:i("screen-full",60236),screenNormal:i("screen-normal",60237),searchStop:i("search-stop",60238),server:i("server",60240),settingsGear:i("settings-gear",60241),settings:i("settings",60242),shield:i("shield",60243),smiley:i("smiley",60244),sortPrecedence:i("sort-precedence",60245),splitHorizontal:i("split-horizontal",60246),splitVertical:i("split-vertical",60247),squirrel:i("squirrel",60248),starFull:i("star-full",60249),starHalf:i("star-half",60250),symbolClass:i("symbol-class",60251),symbolColor:i("symbol-color",60252),symbolCustomColor:i("symbol-customcolor",60252),symbolConstant:i("symbol-constant",60253),symbolEnumMember:i("symbol-enum-member",60254),symbolField:i("symbol-field",60255),symbolFile:i("symbol-file",60256),symbolInterface:i("symbol-interface",60257),symbolKeyword:i("symbol-keyword",60258),symbolMisc:i("symbol-misc",60259),symbolOperator:i("symbol-operator",60260),symbolProperty:i("symbol-property",60261),wrench:i("wrench",60261),wrenchSubaction:i("wrench-subaction",60261),symbolSnippet:i("symbol-snippet",60262),tasklist:i("tasklist",60263),telescope:i("telescope",60264),textSize:i("text-size",60265),threeBars:i("three-bars",60266),thumbsdown:i("thumbsdown",60267),thumbsup:i("thumbsup",60268),tools:i("tools",60269),triangleDown:i("triangle-down",60270),triangleLeft:i("triangle-left",60271),triangleRight:i("triangle-right",60272),triangleUp:i("triangle-up",60273),twitter:i("twitter",60274),unfold:i("unfold",60275),unlock:i("unlock",60276),unmute:i("unmute",60277),unverified:i("unverified",60278),verified:i("verified",60279),versions:i("versions",60280),vmActive:i("vm-active",60281),vmOutline:i("vm-outline",60282),vmRunning:i("vm-running",60283),watch:i("watch",60284),whitespace:i("whitespace",60285),wholeWord:i("whole-word",60286),window:i("window",60287),wordWrap:i("word-wrap",60288),zoomIn:i("zoom-in",60289),zoomOut:i("zoom-out",60290),listFilter:i("list-filter",60291),listFlat:i("list-flat",60292),listSelection:i("list-selection",60293),selection:i("selection",60293),listTree:i("list-tree",60294),debugBreakpointFunctionUnverified:i("debug-breakpoint-function-unverified",60295),debugBreakpointFunction:i("debug-breakpoint-function",60296),debugBreakpointFunctionDisabled:i("debug-breakpoint-function-disabled",60296),debugStackframeActive:i("debug-stackframe-active",60297),circleSmallFilled:i("circle-small-filled",60298),debugStackframeDot:i("debug-stackframe-dot",60298),debugStackframe:i("debug-stackframe",60299),debugStackframeFocused:i("debug-stackframe-focused",60299),debugBreakpointUnsupported:i("debug-breakpoint-unsupported",60300),symbolString:i("symbol-string",60301),debugReverseContinue:i("debug-reverse-continue",60302),debugStepBack:i("debug-step-back",60303),debugRestartFrame:i("debug-restart-frame",60304),callIncoming:i("call-incoming",60306),callOutgoing:i("call-outgoing",60307),menu:i("menu",60308),expandAll:i("expand-all",60309),feedback:i("feedback",60310),gitPullRequestReviewer:i("git-pull-request-reviewer",60310),groupByRefType:i("group-by-ref-type",60311),ungroupByRefType:i("ungroup-by-ref-type",60312),account:i("account",60313),gitPullRequestAssignee:i("git-pull-request-assignee",60313),bellDot:i("bell-dot",60314),debugConsole:i("debug-console",60315),library:i("library",60316),output:i("output",60317),runAll:i("run-all",60318),syncIgnored:i("sync-ignored",60319),pinned:i("pinned",60320),githubInverted:i("github-inverted",60321),debugAlt:i("debug-alt",60305),serverProcess:i("server-process",60322),serverEnvironment:i("server-environment",60323),pass:i("pass",60324),stopCircle:i("stop-circle",60325),playCircle:i("play-circle",60326),record:i("record",60327),debugAltSmall:i("debug-alt-small",60328),vmConnect:i("vm-connect",60329),cloud:i("cloud",60330),merge:i("merge",60331),exportIcon:i("export",60332),graphLeft:i("graph-left",60333),magnet:i("magnet",60334),notebook:i("notebook",60335),redo:i("redo",60336),checkAll:i("check-all",60337),pinnedDirty:i("pinned-dirty",60338),passFilled:i("pass-filled",60339),circleLargeFilled:i("circle-large-filled",60340),circleLarge:i("circle-large",60341),circleLargeOutline:i("circle-large-outline",60341),combine:i("combine",60342),gather:i("gather",60342),table:i("table",60343),variableGroup:i("variable-group",60344),typeHierarchy:i("type-hierarchy",60345),typeHierarchySub:i("type-hierarchy-sub",60346),typeHierarchySuper:i("type-hierarchy-super",60347),gitPullRequestCreate:i("git-pull-request-create",60348),runAbove:i("run-above",60349),runBelow:i("run-below",60350),notebookTemplate:i("notebook-template",60351),debugRerun:i("debug-rerun",60352),workspaceTrusted:i("workspace-trusted",60353),workspaceUntrusted:i("workspace-untrusted",60354),workspaceUnspecified:i("workspace-unspecified",60355),terminalCmd:i("terminal-cmd",60356),terminalDebian:i("terminal-debian",60357),terminalLinux:i("terminal-linux",60358),terminalPowershell:i("terminal-powershell",60359),terminalTmux:i("terminal-tmux",60360),terminalUbuntu:i("terminal-ubuntu",60361),terminalBash:i("terminal-bash",60362),arrowSwap:i("arrow-swap",60363),copy:i("copy",60364),personAdd:i("person-add",60365),filterFilled:i("filter-filled",60366),wand:i("wand",60367),debugLineByLine:i("debug-line-by-line",60368),inspect:i("inspect",60369),layers:i("layers",60370),layersDot:i("layers-dot",60371),layersActive:i("layers-active",60372),compass:i("compass",60373),compassDot:i("compass-dot",60374),compassActive:i("compass-active",60375),azure:i("azure",60376),issueDraft:i("issue-draft",60377),gitPullRequestClosed:i("git-pull-request-closed",60378),gitPullRequestDraft:i("git-pull-request-draft",60379),debugAll:i("debug-all",60380),debugCoverage:i("debug-coverage",60381),runErrors:i("run-errors",60382),folderLibrary:i("folder-library",60383),debugContinueSmall:i("debug-continue-small",60384),beakerStop:i("beaker-stop",60385),graphLine:i("graph-line",60386),graphScatter:i("graph-scatter",60387),pieChart:i("pie-chart",60388),bracketDot:i("bracket-dot",60389),bracketError:i("bracket-error",60390),lockSmall:i("lock-small",60391),azureDevops:i("azure-devops",60392),verifiedFilled:i("verified-filled",60393),newLine:i("newline",60394),layout:i("layout",60395),layoutActivitybarLeft:i("layout-activitybar-left",60396),layoutActivitybarRight:i("layout-activitybar-right",60397),layoutPanelLeft:i("layout-panel-left",60398),layoutPanelCenter:i("layout-panel-center",60399),layoutPanelJustify:i("layout-panel-justify",60400),layoutPanelRight:i("layout-panel-right",60401),layoutPanel:i("layout-panel",60402),layoutSidebarLeft:i("layout-sidebar-left",60403),layoutSidebarRight:i("layout-sidebar-right",60404),layoutStatusbar:i("layout-statusbar",60405),layoutMenubar:i("layout-menubar",60406),layoutCentered:i("layout-centered",60407),layoutSidebarRightOff:i("layout-sidebar-right-off",60416),layoutPanelOff:i("layout-panel-off",60417),layoutSidebarLeftOff:i("layout-sidebar-left-off",60418),target:i("target",60408),indent:i("indent",60409),recordSmall:i("record-small",60410),errorSmall:i("error-small",60411),arrowCircleDown:i("arrow-circle-down",60412),arrowCircleLeft:i("arrow-circle-left",60413),arrowCircleRight:i("arrow-circle-right",60414),arrowCircleUp:i("arrow-circle-up",60415),heartFilled:i("heart-filled",60420),map:i("map",60421),mapFilled:i("map-filled",60422),circleSmall:i("circle-small",60423),bellSlash:i("bell-slash",60424),bellSlashDot:i("bell-slash-dot",60425),commentUnresolved:i("comment-unresolved",60426),gitPullRequestGoToChanges:i("git-pull-request-go-to-changes",60427),gitPullRequestNewChanges:i("git-pull-request-new-changes",60428),searchFuzzy:i("search-fuzzy",60429),commentDraft:i("comment-draft",60430),send:i("send",60431),sparkle:i("sparkle",60432),insert:i("insert",60433),mic:i("mic",60434),dialogError:i("dialog-error","error"),dialogWarning:i("dialog-warning","warning"),dialogInfo:i("dialog-info","info"),dialogClose:i("dialog-close","close"),treeItemExpanded:i("tree-item-expanded","chevron-down"),treeFilterOnTypeOn:i("tree-filter-on-type-on","list-filter"),treeFilterOnTypeOff:i("tree-filter-on-type-off","list-selection"),treeFilterClear:i("tree-filter-clear","close"),treeItemLoading:i("tree-item-loading","loading"),menuSelection:i("menu-selection","check"),menuSubmenu:i("menu-submenu","chevron-right"),menuBarMore:i("menubar-more","more"),scrollbarButtonLeft:i("scrollbar-button-left","triangle-left"),scrollbarButtonRight:i("scrollbar-button-right","triangle-right"),scrollbarButtonUp:i("scrollbar-button-up","triangle-up"),scrollbarButtonDown:i("scrollbar-button-down","triangle-down"),toolBarMore:i("toolbar-more","more"),quickInputBack:i("quick-input-back","arrow-left")}}),Y(X[14],J([0,1,25]),function(T,n,M){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.createProxyObject=n.getAllMethodNames=n.getAllPropertyNames=n.equals=n.mixin=n.cloneAndChange=n.deepFreeze=n.deepClone=void 0;function A(c){if(!c||typeof c!="object"||c instanceof RegExp)return c;const l=Array.isArray(c)?[]:{};return Object.entries(c).forEach(([f,g])=>{l[f]=g&&typeof g=="object"?A(g):g}),l}n.deepClone=A;function i(c){if(!c||typeof c!="object")return c;const l=[c];for(;l.length>0;){const f=l.shift();Object.freeze(f);for(const g in f)if(d.call(f,g)){const S=f[g];typeof S=="object"&&!Object.isFrozen(S)&&!(0,M.isTypedArray)(S)&&l.push(S)}}return c}n.deepFreeze=i;const d=Object.prototype.hasOwnProperty;function m(c,l){return L(c,l,new Set)}n.cloneAndChange=m;function L(c,l,f){if((0,M.isUndefinedOrNull)(c))return c;const g=l(c);if(typeof g<"u")return g;if(Array.isArray(c)){const S=[];for(const _ of c)S.push(L(_,l,f));return S}if((0,M.isObject)(c)){if(f.has(c))throw new Error("Cannot clone recursive data-structure");f.add(c);const S={};for(const _ in c)d.call(c,_)&&(S[_]=L(c[_],l,f));return f.delete(c),S}return c}function h(c,l,f=!0){return(0,M.isObject)(c)?((0,M.isObject)(l)&&Object.keys(l).forEach(g=>{g in c?f&&((0,M.isObject)(c[g])&&(0,M.isObject)(l[g])?h(c[g],l[g],f):c[g]=l[g]):c[g]=l[g]}),c):l}n.mixin=h;function o(c,l){if(c===l)return!0;if(c==null||l===null||l===void 0||typeof c!=typeof l||typeof c!="object"||Array.isArray(c)!==Array.isArray(l))return!1;let f,g;if(Array.isArray(c)){if(c.length!==l.length)return!1;for(f=0;f<c.length;f++)if(!o(c[f],l[f]))return!1}else{const S=[];for(g in c)S.push(g);S.sort();const _=[];for(g in l)_.push(g);if(_.sort(),!o(S,_))return!1;for(f=0;f<S.length;f++)if(!o(c[S[f]],l[S[f]]))return!1}return!0}n.equals=o;function w(c){let l=[];for(;Object.prototype!==c;)l=l.concat(Object.getOwnPropertyNames(c)),c=Object.getPrototypeOf(c);return l}n.getAllPropertyNames=w;function e(c){const l=[];for(const f of w(c))typeof c[f]=="function"&&l.push(f);return l}n.getAllMethodNames=e;function a(c,l){const f=S=>function(){const _=Array.prototype.slice.call(arguments,0);return l(S,_)},g={};for(const S of c)g[S]=f(S);return g}n.createProxyObject=a}),Y(X[26],J([0,1]),function(T,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.toUint32=n.toUint8=void 0;function M(i){return i<0?0:i>255?255:i|0}n.toUint8=M;function A(i){return i<0?0:i>4294967295?4294967295:i|0}n.toUint32=A}),Y(X[27],J([0,1,26]),function(T,n,M){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.CharacterSet=n.CharacterClassifier=void 0;class A{constructor(m){const L=(0,M.toUint8)(m);this._defaultValue=L,this._asciiMap=A._createAsciiMap(L),this._map=new Map}static _createAsciiMap(m){const L=new Uint8Array(256);return L.fill(m),L}set(m,L){const h=(0,M.toUint8)(L);m>=0&&m<256?this._asciiMap[m]=h:this._map.set(m,h)}get(m){return m>=0&&m<256?this._asciiMap[m]:this._map.get(m)||this._defaultValue}clear(){this._asciiMap.fill(this._defaultValue),this._map.clear()}}n.CharacterClassifier=A;class i{constructor(){this._actual=new A(0)}add(m){this._actual.set(m,1)}has(m){return this._actual.get(m)===1}clear(){return this._actual.clear()}}n.CharacterSet=i}),Y(X[3],J([0,1,5]),function(T,n,M){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.OffsetRangeSet=n.OffsetRange=void 0;class A{static addRange(m,L){let h=0;for(;h<L.length&&L[h].endExclusive<m.start;)h++;let o=h;for(;o<L.length&&L[o].start<=m.endExclusive;)o++;if(h===o)L.splice(h,0,m);else{const w=Math.min(m.start,L[h].start),e=Math.max(m.endExclusive,L[o-1].endExclusive);L.splice(h,o-h,new A(w,e))}}static tryCreate(m,L){if(!(m>L))return new A(m,L)}static ofLength(m){return new A(0,m)}constructor(m,L){if(this.start=m,this.endExclusive=L,m>L)throw new M.BugIndicatingError(`Invalid range: ${this.toString()}`)}get isEmpty(){return this.start===this.endExclusive}delta(m){return new A(this.start+m,this.endExclusive+m)}deltaStart(m){return new A(this.start+m,this.endExclusive)}deltaEnd(m){return new A(this.start,this.endExclusive+m)}get length(){return this.endExclusive-this.start}toString(){return`[${this.start}, ${this.endExclusive})`}equals(m){return this.start===m.start&&this.endExclusive===m.endExclusive}containsRange(m){return this.start<=m.start&&m.endExclusive<=this.endExclusive}contains(m){return this.start<=m&&m<this.endExclusive}join(m){return new A(Math.min(this.start,m.start),Math.max(this.endExclusive,m.endExclusive))}intersect(m){const L=Math.max(this.start,m.start),h=Math.min(this.endExclusive,m.endExclusive);if(L<=h)return new A(L,h)}slice(m){return m.slice(this.start,this.endExclusive)}clip(m){if(this.isEmpty)throw new M.BugIndicatingError(`Invalid clipping range: ${this.toString()}`);return Math.max(this.start,Math.min(this.endExclusive-1,m))}clipCyclic(m){if(this.isEmpty)throw new M.BugIndicatingError(`Invalid clipping range: ${this.toString()}`);return m<this.start?this.endExclusive-(this.start-m)%this.length:m>=this.endExclusive?this.start+(m-this.start)%this.length:m}forEach(m){for(let L=this.start;L<this.endExclusive;L++)m(L)}}n.OffsetRange=A;class i{constructor(){this._sortedRanges=[]}addRange(m){let L=0;for(;L<this._sortedRanges.length&&this._sortedRanges[L].endExclusive<m.start;)L++;let h=L;for(;h<this._sortedRanges.length&&this._sortedRanges[h].start<=m.endExclusive;)h++;if(L===h)this._sortedRanges.splice(L,0,m);else{const o=Math.min(m.start,this._sortedRanges[L].start),w=Math.max(m.endExclusive,this._sortedRanges[h-1].endExclusive);this._sortedRanges.splice(L,h-L,new A(o,w))}}toString(){return this._sortedRanges.map(m=>m.toString()).join(", ")}intersectsStrict(m){let L=0;for(;L<this._sortedRanges.length&&this._sortedRanges[L].endExclusive<=m.start;)L++;return L<this._sortedRanges.length&&this._sortedRanges[L].start<m.endExclusive}intersectWithRange(m){const L=new i;for(const h of this._sortedRanges){const o=h.intersect(m);o&&L.addRange(o)}return L}intersectWithRangeLength(m){return this.intersectWithRange(m).length}get length(){return this._sortedRanges.reduce((m,L)=>m+L.length,0)}}n.OffsetRangeSet=i}),Y(X[4],J([0,1]),function(T,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Position=void 0;class M{constructor(i,d){this.lineNumber=i,this.column=d}with(i=this.lineNumber,d=this.column){return i===this.lineNumber&&d===this.column?this:new M(i,d)}delta(i=0,d=0){return this.with(this.lineNumber+i,this.column+d)}equals(i){return M.equals(this,i)}static equals(i,d){return!i&&!d?!0:!!i&&!!d&&i.lineNumber===d.lineNumber&&i.column===d.column}isBefore(i){return M.isBefore(this,i)}static isBefore(i,d){return i.lineNumber<d.lineNumber?!0:d.lineNumber<i.lineNumber?!1:i.column<d.column}isBeforeOrEqual(i){return M.isBeforeOrEqual(this,i)}static isBeforeOrEqual(i,d){return i.lineNumber<d.lineNumber?!0:d.lineNumber<i.lineNumber?!1:i.column<=d.column}static compare(i,d){const m=i.lineNumber|0,L=d.lineNumber|0;if(m===L){const h=i.column|0,o=d.column|0;return h-o}return m-L}clone(){return new M(this.lineNumber,this.column)}toString(){return"("+this.lineNumber+","+this.column+")"}static lift(i){return new M(i.lineNumber,i.column)}static isIPosition(i){return i&&typeof i.lineNumber=="number"&&typeof i.column=="number"}}n.Position=M}),Y(X[2],J([0,1,4]),function(T,n,M){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Range=void 0;class A{constructor(d,m,L,h){d>L||d===L&&m>h?(this.startLineNumber=L,this.startColumn=h,this.endLineNumber=d,this.endColumn=m):(this.startLineNumber=d,this.startColumn=m,this.endLineNumber=L,this.endColumn=h)}isEmpty(){return A.isEmpty(this)}static isEmpty(d){return d.startLineNumber===d.endLineNumber&&d.startColumn===d.endColumn}containsPosition(d){return A.containsPosition(this,d)}static containsPosition(d,m){return!(m.lineNumber<d.startLineNumber||m.lineNumber>d.endLineNumber||m.lineNumber===d.startLineNumber&&m.column<d.startColumn||m.lineNumber===d.endLineNumber&&m.column>d.endColumn)}static strictContainsPosition(d,m){return!(m.lineNumber<d.startLineNumber||m.lineNumber>d.endLineNumber||m.lineNumber===d.startLineNumber&&m.column<=d.startColumn||m.lineNumber===d.endLineNumber&&m.column>=d.endColumn)}containsRange(d){return A.containsRange(this,d)}static containsRange(d,m){return!(m.startLineNumber<d.startLineNumber||m.endLineNumber<d.startLineNumber||m.startLineNumber>d.endLineNumber||m.endLineNumber>d.endLineNumber||m.startLineNumber===d.startLineNumber&&m.startColumn<d.startColumn||m.endLineNumber===d.endLineNumber&&m.endColumn>d.endColumn)}strictContainsRange(d){return A.strictContainsRange(this,d)}static strictContainsRange(d,m){return!(m.startLineNumber<d.startLineNumber||m.endLineNumber<d.startLineNumber||m.startLineNumber>d.endLineNumber||m.endLineNumber>d.endLineNumber||m.startLineNumber===d.startLineNumber&&m.startColumn<=d.startColumn||m.endLineNumber===d.endLineNumber&&m.endColumn>=d.endColumn)}plusRange(d){return A.plusRange(this,d)}static plusRange(d,m){let L,h,o,w;return m.startLineNumber<d.startLineNumber?(L=m.startLineNumber,h=m.startColumn):m.startLineNumber===d.startLineNumber?(L=m.startLineNumber,h=Math.min(m.startColumn,d.startColumn)):(L=d.startLineNumber,h=d.startColumn),m.endLineNumber>d.endLineNumber?(o=m.endLineNumber,w=m.endColumn):m.endLineNumber===d.endLineNumber?(o=m.endLineNumber,w=Math.max(m.endColumn,d.endColumn)):(o=d.endLineNumber,w=d.endColumn),new A(L,h,o,w)}intersectRanges(d){return A.intersectRanges(this,d)}static intersectRanges(d,m){let L=d.startLineNumber,h=d.startColumn,o=d.endLineNumber,w=d.endColumn;const e=m.startLineNumber,a=m.startColumn,c=m.endLineNumber,l=m.endColumn;return L<e?(L=e,h=a):L===e&&(h=Math.max(h,a)),o>c?(o=c,w=l):o===c&&(w=Math.min(w,l)),L>o||L===o&&h>w?null:new A(L,h,o,w)}equalsRange(d){return A.equalsRange(this,d)}static equalsRange(d,m){return!d&&!m?!0:!!d&&!!m&&d.startLineNumber===m.startLineNumber&&d.startColumn===m.startColumn&&d.endLineNumber===m.endLineNumber&&d.endColumn===m.endColumn}getEndPosition(){return A.getEndPosition(this)}static getEndPosition(d){return new M.Position(d.endLineNumber,d.endColumn)}getStartPosition(){return A.getStartPosition(this)}static getStartPosition(d){return new M.Position(d.startLineNumber,d.startColumn)}toString(){return"["+this.startLineNumber+","+this.startColumn+" -> "+this.endLineNumber+","+this.endColumn+"]"}setEndPosition(d,m){return new A(this.startLineNumber,this.startColumn,d,m)}setStartPosition(d,m){return new A(d,m,this.endLineNumber,this.endColumn)}collapseToStart(){return A.collapseToStart(this)}static collapseToStart(d){return new A(d.startLineNumber,d.startColumn,d.startLineNumber,d.startColumn)}collapseToEnd(){return A.collapseToEnd(this)}static collapseToEnd(d){return new A(d.endLineNumber,d.endColumn,d.endLineNumber,d.endColumn)}delta(d){return new A(this.startLineNumber+d,this.startColumn,this.endLineNumber+d,this.endColumn)}static fromPositions(d,m=d){return new A(d.lineNumber,d.column,m.lineNumber,m.column)}static lift(d){return d?new A(d.startLineNumber,d.startColumn,d.endLineNumber,d.endColumn):null}static isIRange(d){return d&&typeof d.startLineNumber=="number"&&typeof d.startColumn=="number"&&typeof d.endLineNumber=="number"&&typeof d.endColumn=="number"}static areIntersectingOrTouching(d,m){return!(d.endLineNumber<m.startLineNumber||d.endLineNumber===m.startLineNumber&&d.endColumn<m.startColumn||m.endLineNumber<d.startLineNumber||m.endLineNumber===d.startLineNumber&&m.endColumn<d.startColumn)}static areIntersecting(d,m){return!(d.endLineNumber<m.startLineNumber||d.endLineNumber===m.startLineNumber&&d.endColumn<=m.startColumn||m.endLineNumber<d.startLineNumber||m.endLineNumber===d.startLineNumber&&m.endColumn<=d.startColumn)}static compareRangesUsingStarts(d,m){if(d&&m){const o=d.startLineNumber|0,w=m.startLineNumber|0;if(o===w){const e=d.startColumn|0,a=m.startColumn|0;if(e===a){const c=d.endLineNumber|0,l=m.endLineNumber|0;if(c===l){const f=d.endColumn|0,g=m.endColumn|0;return f-g}return c-l}return e-a}return o-w}return(d?1:0)-(m?1:0)}static compareRangesUsingEnds(d,m){return d.endLineNumber===m.endLineNumber?d.endColumn===m.endColumn?d.startLineNumber===m.startLineNumber?d.startColumn-m.startColumn:d.startLineNumber-m.startLineNumber:d.endColumn-m.endColumn:d.endLineNumber-m.endLineNumber}static spansMultipleLines(d){return d.endLineNumber>d.startLineNumber}toJSON(){return this}}n.Range=A}),Y(X[10],J([0,1,5,3,2,11]),function(T,n,M,A,i,d){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.LineRangeSet=n.LineRange=void 0;class m{static fromRange(o){return new m(o.startLineNumber,o.endLineNumber)}static joinMany(o){if(o.length===0)return[];let w=new L(o[0].slice());for(let e=1;e<o.length;e++)w=w.getUnion(new L(o[e].slice()));return w.ranges}static ofLength(o,w){return new m(o,o+w)}static deserialize(o){return new m(o[0],o[1])}constructor(o,w){if(o>w)throw new M.BugIndicatingError(`startLineNumber ${o} cannot be after endLineNumberExclusive ${w}`);this.startLineNumber=o,this.endLineNumberExclusive=w}contains(o){return this.startLineNumber<=o&&o<this.endLineNumberExclusive}get isEmpty(){return this.startLineNumber===this.endLineNumberExclusive}delta(o){return new m(this.startLineNumber+o,this.endLineNumberExclusive+o)}deltaLength(o){return new m(this.startLineNumber,this.endLineNumberExclusive+o)}get length(){return this.endLineNumberExclusive-this.startLineNumber}join(o){return new m(Math.min(this.startLineNumber,o.startLineNumber),Math.max(this.endLineNumberExclusive,o.endLineNumberExclusive))}toString(){return`[${this.startLineNumber},${this.endLineNumberExclusive})`}intersect(o){const w=Math.max(this.startLineNumber,o.startLineNumber),e=Math.min(this.endLineNumberExclusive,o.endLineNumberExclusive);if(w<=e)return new m(w,e)}intersectsStrict(o){return this.startLineNumber<o.endLineNumberExclusive&&o.startLineNumber<this.endLineNumberExclusive}overlapOrTouch(o){return this.startLineNumber<=o.endLineNumberExclusive&&o.startLineNumber<=this.endLineNumberExclusive}equals(o){return this.startLineNumber===o.startLineNumber&&this.endLineNumberExclusive===o.endLineNumberExclusive}toInclusiveRange(){return this.isEmpty?null:new i.Range(this.startLineNumber,1,this.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER)}toExclusiveRange(){return new i.Range(this.startLineNumber,1,this.endLineNumberExclusive,1)}mapToLineArray(o){const w=[];for(let e=this.startLineNumber;e<this.endLineNumberExclusive;e++)w.push(o(e));return w}forEach(o){for(let w=this.startLineNumber;w<this.endLineNumberExclusive;w++)o(w)}serialize(){return[this.startLineNumber,this.endLineNumberExclusive]}includes(o){return this.startLineNumber<=o&&o<this.endLineNumberExclusive}toOffsetRange(){return new A.OffsetRange(this.startLineNumber-1,this.endLineNumberExclusive-1)}}n.LineRange=m;class L{constructor(o=[]){this._normalizedRanges=o}get ranges(){return this._normalizedRanges}addRange(o){if(o.length===0)return;const w=(0,d.findFirstIdxMonotonousOrArrLen)(this._normalizedRanges,a=>a.endLineNumberExclusive>=o.startLineNumber),e=(0,d.findLastIdxMonotonous)(this._normalizedRanges,a=>a.startLineNumber<=o.endLineNumberExclusive)+1;if(w===e)this._normalizedRanges.splice(w,0,o);else if(w===e-1){const a=this._normalizedRanges[w];this._normalizedRanges[w]=a.join(o)}else{const a=this._normalizedRanges[w].join(this._normalizedRanges[e-1]).join(o);this._normalizedRanges.splice(w,e-w,a)}}contains(o){const w=(0,d.findLastMonotonous)(this._normalizedRanges,e=>e.startLineNumber<=o);return!!w&&w.endLineNumberExclusive>o}getUnion(o){if(this._normalizedRanges.length===0)return o;if(o._normalizedRanges.length===0)return this;const w=[];let e=0,a=0,c=null;for(;e<this._normalizedRanges.length||a<o._normalizedRanges.length;){let l=null;if(e<this._normalizedRanges.length&&a<o._normalizedRanges.length){const f=this._normalizedRanges[e],g=o._normalizedRanges[a];f.startLineNumber<g.startLineNumber?(l=f,e++):(l=g,a++)}else e<this._normalizedRanges.length?(l=this._normalizedRanges[e],e++):(l=o._normalizedRanges[a],a++);c===null?c=l:c.endLineNumberExclusive>=l.startLineNumber?c=new m(c.startLineNumber,Math.max(c.endLineNumberExclusive,l.endLineNumberExclusive)):(w.push(c),c=l)}return c!==null&&w.push(c),new L(w)}subtractFrom(o){const w=(0,d.findFirstIdxMonotonousOrArrLen)(this._normalizedRanges,l=>l.endLineNumberExclusive>=o.startLineNumber),e=(0,d.findLastIdxMonotonous)(this._normalizedRanges,l=>l.startLineNumber<=o.endLineNumberExclusive)+1;if(w===e)return new L([o]);const a=[];let c=o.startLineNumber;for(let l=w;l<e;l++){const f=this._normalizedRanges[l];f.startLineNumber>c&&a.push(new m(c,f.startLineNumber)),c=f.endLineNumberExclusive}return c<o.endLineNumberExclusive&&a.push(new m(c,o.endLineNumberExclusive)),new L(a)}toString(){return this._normalizedRanges.map(o=>o.toString()).join(", ")}getIntersection(o){const w=[];let e=0,a=0;for(;e<this._normalizedRanges.length&&a<o._normalizedRanges.length;){const c=this._normalizedRanges[e],l=o._normalizedRanges[a],f=c.intersect(l);f&&!f.isEmpty&&w.push(f),c.endLineNumberExclusive<l.endLineNumberExclusive?e++:a++}return new L(w)}getWithDelta(o){return new L(this._normalizedRanges.map(w=>w.delta(o)))}}n.LineRangeSet=L}),Y(X[41],J([0,1,4,2]),function(T,n,M,A){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Selection=void 0;class i extends A.Range{constructor(m,L,h,o){super(m,L,h,o),this.selectionStartLineNumber=m,this.selectionStartColumn=L,this.positionLineNumber=h,this.positionColumn=o}toString(){return"["+this.selectionStartLineNumber+","+this.selectionStartColumn+" -> "+this.positionLineNumber+","+this.positionColumn+"]"}equalsSelection(m){return i.selectionsEqual(this,m)}static selectionsEqual(m,L){return m.selectionStartLineNumber===L.selectionStartLineNumber&&m.selectionStartColumn===L.selectionStartColumn&&m.positionLineNumber===L.positionLineNumber&&m.positionColumn===L.positionColumn}getDirection(){return this.selectionStartLineNumber===this.startLineNumber&&this.selectionStartColumn===this.startColumn?0:1}setEndPosition(m,L){return this.getDirection()===0?new i(this.startLineNumber,this.startColumn,m,L):new i(m,L,this.startLineNumber,this.startColumn)}getPosition(){return new M.Position(this.positionLineNumber,this.positionColumn)}getSelectionStart(){return new M.Position(this.selectionStartLineNumber,this.selectionStartColumn)}setStartPosition(m,L){return this.getDirection()===0?new i(m,L,this.endLineNumber,this.endColumn):new i(this.endLineNumber,this.endColumn,m,L)}static fromPositions(m,L=m){return new i(m.lineNumber,m.column,L.lineNumber,L.column)}static fromRange(m,L){return L===0?new i(m.startLineNumber,m.startColumn,m.endLineNumber,m.endColumn):new i(m.endLineNumber,m.endColumn,m.startLineNumber,m.startColumn)}static liftSelection(m){return new i(m.selectionStartLineNumber,m.selectionStartColumn,m.positionLineNumber,m.positionColumn)}static selectionsArrEqual(m,L){if(m&&!L||!m&&L)return!1;if(!m&&!L)return!0;if(m.length!==L.length)return!1;for(let h=0,o=m.length;h<o;h++)if(!this.selectionsEqual(m[h],L[h]))return!1;return!0}static isISelection(m){return m&&typeof m.selectionStartLineNumber=="number"&&typeof m.selectionStartColumn=="number"&&typeof m.positionLineNumber=="number"&&typeof m.positionColumn=="number"}static createWithDirection(m,L,h,o,w){return w===0?new i(m,L,h,o):new i(h,o,m,L)}}n.Selection=i}),Y(X[42],J([0,1,27]),function(T,n,M){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.getMapForWordSeparators=n.WordCharacterClassifier=void 0;class A extends M.CharacterClassifier{constructor(m){super(0);for(let L=0,h=m.length;L<h;L++)this.set(m.charCodeAt(L),2);this.set(32,1),this.set(9,1)}}n.WordCharacterClassifier=A;function i(d){const m={};return L=>(m.hasOwnProperty(L)||(m[L]=d(L)),m[L])}n.getMapForWordSeparators=i(d=>new A(d))}),Y(X[28],J([0,1,21,22]),function(T,n,M,A){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.getWordAtText=n.ensureValidWordDefinition=n.DEFAULT_WORD_REGEXP=n.USUAL_WORD_SEPARATORS=void 0,n.USUAL_WORD_SEPARATORS="`~!@#$%^&*()-=+[{]}\\|;:'\",.<>/?";function i(o=""){let w="(-?\\d*\\.\\d\\w*)|([^";for(const e of n.USUAL_WORD_SEPARATORS)o.indexOf(e)>=0||(w+="\\"+e);return w+="\\s]+)",new RegExp(w,"g")}n.DEFAULT_WORD_REGEXP=i();function d(o){let w=n.DEFAULT_WORD_REGEXP;if(o&&o instanceof RegExp)if(o.global)w=o;else{let e="g";o.ignoreCase&&(e+="i"),o.multiline&&(e+="m"),o.unicode&&(e+="u"),w=new RegExp(o.source,e)}return w.lastIndex=0,w}n.ensureValidWordDefinition=d;const m=new A.LinkedList;m.unshift({maxLen:1e3,windowSize:15,timeBudget:150});function L(o,w,e,a,c){if(c||(c=M.Iterable.first(m)),e.length>c.maxLen){let _=o-c.maxLen/2;return _<0?_=0:a+=_,e=e.substring(_,o+c.maxLen/2),L(o,w,e,a,c)}const l=Date.now(),f=o-1-a;let g=-1,S=null;for(let _=1;!(Date.now()-l>=c.timeBudget);_++){const E=f-c.windowSize*_;w.lastIndex=Math.max(0,E);const y=h(w,e,f,g);if(!y&&S||(S=y,E<=0))break;g=E}if(S){const _={word:S[0],startColumn:a+1+S.index,endColumn:a+1+S.index+S[0].length};return w.lastIndex=0,_}return null}n.getWordAtText=L;function h(o,w,e,a){let c;for(;c=o.exec(w);){const l=c.index||0;if(l<=e&&o.lastIndex>=e)return c;if(a>0&&l>a)return null}return null}}),Y(X[8],J([0,1,7,5,3]),function(T,n,M,A,i){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.DateTimeout=n.InfiniteTimeout=n.OffsetPair=n.SequenceDiff=n.DiffAlgorithmResult=void 0;class d{static trivial(e,a){return new d([new m(i.OffsetRange.ofLength(e.length),i.OffsetRange.ofLength(a.length))],!1)}static trivialTimedOut(e,a){return new d([new m(i.OffsetRange.ofLength(e.length),i.OffsetRange.ofLength(a.length))],!0)}constructor(e,a){this.diffs=e,this.hitTimeout=a}}n.DiffAlgorithmResult=d;class m{static invert(e,a){const c=[];return(0,M.forEachAdjacent)(e,(l,f)=>{c.push(m.fromOffsetPairs(l?l.getEndExclusives():L.zero,f?f.getStarts():new L(a,(l?l.seq2Range.endExclusive-l.seq1Range.endExclusive:0)+a)))}),c}static fromOffsetPairs(e,a){return new m(new i.OffsetRange(e.offset1,a.offset1),new i.OffsetRange(e.offset2,a.offset2))}constructor(e,a){this.seq1Range=e,this.seq2Range=a}swap(){return new m(this.seq2Range,this.seq1Range)}toString(){return`${this.seq1Range} <-> ${this.seq2Range}`}join(e){return new m(this.seq1Range.join(e.seq1Range),this.seq2Range.join(e.seq2Range))}delta(e){return e===0?this:new m(this.seq1Range.delta(e),this.seq2Range.delta(e))}deltaStart(e){return e===0?this:new m(this.seq1Range.deltaStart(e),this.seq2Range.deltaStart(e))}deltaEnd(e){return e===0?this:new m(this.seq1Range.deltaEnd(e),this.seq2Range.deltaEnd(e))}intersect(e){const a=this.seq1Range.intersect(e.seq1Range),c=this.seq2Range.intersect(e.seq2Range);if(!(!a||!c))return new m(a,c)}getStarts(){return new L(this.seq1Range.start,this.seq2Range.start)}getEndExclusives(){return new L(this.seq1Range.endExclusive,this.seq2Range.endExclusive)}}n.SequenceDiff=m;class L{constructor(e,a){this.offset1=e,this.offset2=a}toString(){return`${this.offset1} <-> ${this.offset2}`}}n.OffsetPair=L,L.zero=new L(0,0),L.max=new L(Number.MAX_SAFE_INTEGER,Number.MAX_SAFE_INTEGER);class h{isValid(){return!0}}n.InfiniteTimeout=h,h.instance=new h;class o{constructor(e){if(this.timeout=e,this.startTime=Date.now(),this.valid=!0,e<=0)throw new A.BugIndicatingError("timeout must be positive")}isValid(){if(!(Date.now()-this.startTime<this.timeout)&&this.valid){this.valid=!1;debugger}return this.valid}}n.DateTimeout=o}),Y(X[29],J([0,1,3,8]),function(T,n,M,A){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.MyersDiffAlgorithm=void 0;class i{compute(o,w,e=A.InfiniteTimeout.instance){if(o.length===0||w.length===0)return A.DiffAlgorithmResult.trivial(o,w);const a=o,c=w;function l(s,u){for(;s<a.length&&u<c.length&&a.getElement(s)===c.getElement(u);)s++,u++;return s}let f=0;const g=new m;g.set(0,l(0,0));const S=new L;S.set(0,g.get(0)===0?null:new d(null,0,0,g.get(0)));let _=0;e:for(;;){if(f++,!e.isValid())return A.DiffAlgorithmResult.trivialTimedOut(a,c);const s=-Math.min(f,c.length+f%2),u=Math.min(f,a.length+f%2);for(_=s;_<=u;_+=2){let p=0;const b=_===u?-1:g.get(_+1),C=_===s?-1:g.get(_-1)+1;p++;const N=Math.min(Math.max(b,C),a.length),R=N-_;if(p++,N>a.length||R>c.length)continue;const D=l(N,R);g.set(_,D);const k=N===b?S.get(_+1):S.get(_-1);if(S.set(_,D!==N?new d(k,N,R,D-N):k),g.get(_)===a.length&&g.get(_)-_===c.length)break e}}let E=S.get(_);const y=[];let v=a.length,r=c.length;for(;;){const s=E?E.x+E.length:0,u=E?E.y+E.length:0;if((s!==v||u!==r)&&y.push(new A.SequenceDiff(new M.OffsetRange(s,v),new M.OffsetRange(u,r))),!E)break;v=E.x,r=E.y,E=E.prev}return y.reverse(),new A.DiffAlgorithmResult(y,!1)}}n.MyersDiffAlgorithm=i;class d{constructor(o,w,e,a){this.prev=o,this.x=w,this.y=e,this.length=a}}class m{constructor(){this.positiveArr=new Int32Array(10),this.negativeArr=new Int32Array(10)}get(o){return o<0?(o=-o-1,this.negativeArr[o]):this.positiveArr[o]}set(o,w){if(o<0){if(o=-o-1,o>=this.negativeArr.length){const e=this.negativeArr;this.negativeArr=new Int32Array(e.length*2),this.negativeArr.set(e)}this.negativeArr[o]=w}else{if(o>=this.positiveArr.length){const e=this.positiveArr;this.positiveArr=new Int32Array(e.length*2),this.positiveArr.set(e)}this.positiveArr[o]=w}}}class L{constructor(){this.positiveArr=[],this.negativeArr=[]}get(o){return o<0?(o=-o-1,this.negativeArr[o]):this.positiveArr[o]}set(o,w){o<0?(o=-o-1,this.negativeArr[o]=w):this.positiveArr[o]=w}}}),Y(X[43],J([0,1,7,3,8]),function(T,n,M,A,i){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.removeVeryShortMatchingTextBetweenLongDiffs=n.removeVeryShortMatchingLinesBetweenDiffs=n.extendDiffsToEntireWordIfAppropriate=n.removeShortMatches=n.optimizeSequenceDiffs=void 0;function d(l,f,g){let S=g;return S=m(l,f,S),S=L(l,f,S),S}n.optimizeSequenceDiffs=d;function m(l,f,g){if(g.length===0)return g;const S=[];S.push(g[0]);for(let E=1;E<g.length;E++){const y=S[S.length-1];let v=g[E];if(v.seq1Range.isEmpty||v.seq2Range.isEmpty){const r=v.seq1Range.start-y.seq1Range.endExclusive;let s;for(s=1;s<=r&&!(l.getElement(v.seq1Range.start-s)!==l.getElement(v.seq1Range.endExclusive-s)||f.getElement(v.seq2Range.start-s)!==f.getElement(v.seq2Range.endExclusive-s));s++);if(s--,s===r){S[S.length-1]=new i.SequenceDiff(new A.OffsetRange(y.seq1Range.start,v.seq1Range.endExclusive-r),new A.OffsetRange(y.seq2Range.start,v.seq2Range.endExclusive-r));continue}v=v.delta(-s)}S.push(v)}const _=[];for(let E=0;E<S.length-1;E++){const y=S[E+1];let v=S[E];if(v.seq1Range.isEmpty||v.seq2Range.isEmpty){const r=y.seq1Range.start-v.seq1Range.endExclusive;let s;for(s=0;s<r&&!(!l.isStronglyEqual(v.seq1Range.start+s,v.seq1Range.endExclusive+s)||!f.isStronglyEqual(v.seq2Range.start+s,v.seq2Range.endExclusive+s));s++);if(s===r){S[E+1]=new i.SequenceDiff(new A.OffsetRange(v.seq1Range.start+r,y.seq1Range.endExclusive),new A.OffsetRange(v.seq2Range.start+r,y.seq2Range.endExclusive));continue}s>0&&(v=v.delta(s))}_.push(v)}return S.length>0&&_.push(S[S.length-1]),_}function L(l,f,g){if(!l.getBoundaryScore||!f.getBoundaryScore)return g;for(let S=0;S<g.length;S++){const _=S>0?g[S-1]:void 0,E=g[S],y=S+1<g.length?g[S+1]:void 0,v=new A.OffsetRange(_?_.seq1Range.start+1:0,y?y.seq1Range.endExclusive-1:l.length),r=new A.OffsetRange(_?_.seq2Range.start+1:0,y?y.seq2Range.endExclusive-1:f.length);E.seq1Range.isEmpty?g[S]=h(E,l,f,v,r):E.seq2Range.isEmpty&&(g[S]=h(E.swap(),f,l,r,v).swap())}return g}function h(l,f,g,S,_){let y=1;for(;l.seq1Range.start-y>=S.start&&l.seq2Range.start-y>=_.start&&g.isStronglyEqual(l.seq2Range.start-y,l.seq2Range.endExclusive-y)&&y<100;)y++;y--;let v=0;for(;l.seq1Range.start+v<S.endExclusive&&l.seq2Range.endExclusive+v<_.endExclusive&&g.isStronglyEqual(l.seq2Range.start+v,l.seq2Range.endExclusive+v)&&v<100;)v++;if(y===0&&v===0)return l;let r=0,s=-1;for(let u=-y;u<=v;u++){const p=l.seq2Range.start+u,b=l.seq2Range.endExclusive+u,C=l.seq1Range.start+u,N=f.getBoundaryScore(C)+g.getBoundaryScore(p)+g.getBoundaryScore(b);N>s&&(s=N,r=u)}return l.delta(r)}function o(l,f,g){const S=[];for(const _ of g){const E=S[S.length-1];if(!E){S.push(_);continue}_.seq1Range.start-E.seq1Range.endExclusive<=2||_.seq2Range.start-E.seq2Range.endExclusive<=2?S[S.length-1]=new i.SequenceDiff(E.seq1Range.join(_.seq1Range),E.seq2Range.join(_.seq2Range)):S.push(_)}return S}n.removeShortMatches=o;function w(l,f,g){const S=[];let _;function E(){if(!_)return;const v=_.s1Range.length-_.deleted,r=_.s2Range.length-_.added;Math.max(_.deleted,_.added)+(_.count-1)>v&&S.push(new i.SequenceDiff(_.s1Range,_.s2Range)),_=void 0}for(const v of g){let r=function(C,N){var R,D,k,U;if(!_||!_.s1Range.containsRange(C)||!_.s2Range.containsRange(N))if(_&&!(_.s1Range.endExclusive<C.start&&_.s2Range.endExclusive<N.start)){const z=A.OffsetRange.tryCreate(_.s1Range.endExclusive,C.start),x=A.OffsetRange.tryCreate(_.s2Range.endExclusive,N.start);_.deleted+=(R=z?.length)!==null&&R!==void 0?R:0,_.added+=(D=x?.length)!==null&&D!==void 0?D:0,_.s1Range=_.s1Range.join(C),_.s2Range=_.s2Range.join(N)}else E(),_={added:0,deleted:0,count:0,s1Range:C,s2Range:N};const I=C.intersect(v.seq1Range),B=N.intersect(v.seq2Range);_.count++,_.deleted+=(k=I?.length)!==null&&k!==void 0?k:0,_.added+=(U=B?.length)!==null&&U!==void 0?U:0};const s=l.findWordContaining(v.seq1Range.start-1),u=f.findWordContaining(v.seq2Range.start-1),p=l.findWordContaining(v.seq1Range.endExclusive),b=f.findWordContaining(v.seq2Range.endExclusive);s&&p&&u&&b&&s.equals(p)&&u.equals(b)?r(s,u):(s&&u&&r(s,u),p&&b&&r(p,b))}return E(),e(g,S)}n.extendDiffsToEntireWordIfAppropriate=w;function e(l,f){const g=[];for(;l.length>0||f.length>0;){const S=l[0],_=f[0];let E;S&&(!_||S.seq1Range.start<_.seq1Range.start)?E=l.shift():E=f.shift(),g.length>0&&g[g.length-1].seq1Range.endExclusive>=E.seq1Range.start?g[g.length-1]=g[g.length-1].join(E):g.push(E)}return g}function a(l,f,g){let S=g;if(S.length===0)return S;let _=0,E;do{E=!1;const y=[S[0]];for(let v=1;v<S.length;v++){let u=function(b,C){const N=new A.OffsetRange(s.seq1Range.endExclusive,r.seq1Range.start);return l.getText(N).replace(/\s/g,"").length<=4&&(b.seq1Range.length+b.seq2Range.length>5||C.seq1Range.length+C.seq2Range.length>5)};const r=S[v],s=y[y.length-1];u(s,r)?(E=!0,y[y.length-1]=y[y.length-1].join(r)):y.push(r)}S=y}while(_++<10&&E);return S}n.removeVeryShortMatchingLinesBetweenDiffs=a;function c(l,f,g){let S=g;if(S.length===0)return S;let _=0,E;do{E=!1;const v=[S[0]];for(let r=1;r<S.length;r++){let p=function(C,N){const R=new A.OffsetRange(u.seq1Range.endExclusive,s.seq1Range.start);if(l.countLinesIn(R)>5||R.length>500)return!1;const k=l.getText(R).trim();if(k.length>20||k.split(/\r\n|\r|\n/).length>1)return!1;const U=l.countLinesIn(C.seq1Range),I=C.seq1Range.length,B=f.countLinesIn(C.seq2Range),z=C.seq2Range.length,x=l.countLinesIn(N.seq1Range),O=N.seq1Range.length,F=f.countLinesIn(N.seq2Range),H=N.seq2Range.length,V=2*40+50;function t(ne){return Math.min(ne,V)}return Math.pow(Math.pow(t(U*40+I),1.5)+Math.pow(t(B*40+z),1.5),1.5)+Math.pow(Math.pow(t(x*40+O),1.5)+Math.pow(t(F*40+H),1.5),1.5)>Math.pow(Math.pow(V,1.5),1.5)*1.3};const s=S[r],u=v[v.length-1];p(u,s)?(E=!0,v[v.length-1]=v[v.length-1].join(s)):v.push(s)}S=v}while(_++<10&&E);const y=[];return(0,M.forEachWithNeighbors)(S,(v,r,s)=>{let u=r;function p(k){return k.length>0&&k.trim().length<=3&&r.seq1Range.length+r.seq2Range.length>100}const b=l.extendToFullLines(r.seq1Range),C=l.getText(new A.OffsetRange(b.start,r.seq1Range.start));p(C)&&(u=u.deltaStart(-C.length));const N=l.getText(new A.OffsetRange(r.seq1Range.endExclusive,b.endExclusive));p(N)&&(u=u.deltaEnd(N.length));const R=i.SequenceDiff.fromOffsetPairs(v?v.getEndExclusives():i.OffsetPair.zero,s?s.getStarts():i.OffsetPair.max),D=u.intersect(R);y.push(D)}),y}n.removeVeryShortMatchingTextBetweenLongDiffs=c}),Y(X[44],J([0,1]),function(T,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.LineSequence=void 0;class M{constructor(d,m){this.trimmedHash=d,this.lines=m}getElement(d){return this.trimmedHash[d]}get length(){return this.trimmedHash.length}getBoundaryScore(d){const m=d===0?0:A(this.lines[d-1]),L=d===this.lines.length?0:A(this.lines[d]);return 1e3-(m+L)}getText(d){return this.lines.slice(d.start,d.endExclusive).join(`
`)}isStronglyEqual(d,m){return this.lines[d]===this.lines[m]}}n.LineSequence=M;function A(i){let d=0;for(;d<i.length&&(i.charCodeAt(d)===32||i.charCodeAt(d)===9);)d++;return d}}),Y(X[15],J([0,1]),function(T,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.LineRangeFragment=n.isSpace=n.Array2D=void 0;class M{constructor(m,L){this.width=m,this.height=L,this.array=[],this.array=new Array(m*L)}get(m,L){return this.array[m+L*this.width]}set(m,L,h){this.array[m+L*this.width]=h}}n.Array2D=M;function A(d){return d===32||d===9}n.isSpace=A;class i{static getKey(m){let L=this.chrKeys.get(m);return L===void 0&&(L=this.chrKeys.size,this.chrKeys.set(m,L)),L}constructor(m,L,h){this.range=m,this.lines=L,this.source=h,this.histogram=[];let o=0;for(let w=m.startLineNumber-1;w<m.endLineNumberExclusive-1;w++){const e=L[w];for(let c=0;c<e.length;c++){o++;const l=e[c],f=i.getKey(l);this.histogram[f]=(this.histogram[f]||0)+1}o++;const a=i.getKey(`
`);this.histogram[a]=(this.histogram[a]||0)+1}this.totalCount=o}computeSimilarity(m){var L,h;let o=0;const w=Math.max(this.histogram.length,m.histogram.length);for(let e=0;e<w;e++)o+=Math.abs(((L=this.histogram[e])!==null&&L!==void 0?L:0)-((h=m.histogram[e])!==null&&h!==void 0?h:0));return 1-o/(this.totalCount+m.totalCount)}}n.LineRangeFragment=i,i.chrKeys=new Map}),Y(X[45],J([0,1,3,8,15]),function(T,n,M,A,i){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.DynamicProgrammingDiffing=void 0;class d{compute(L,h,o=A.InfiniteTimeout.instance,w){if(L.length===0||h.length===0)return A.DiffAlgorithmResult.trivial(L,h);const e=new i.Array2D(L.length,h.length),a=new i.Array2D(L.length,h.length),c=new i.Array2D(L.length,h.length);for(let y=0;y<L.length;y++)for(let v=0;v<h.length;v++){if(!o.isValid())return A.DiffAlgorithmResult.trivialTimedOut(L,h);const r=y===0?0:e.get(y-1,v),s=v===0?0:e.get(y,v-1);let u;L.getElement(y)===h.getElement(v)?(y===0||v===0?u=0:u=e.get(y-1,v-1),y>0&&v>0&&a.get(y-1,v-1)===3&&(u+=c.get(y-1,v-1)),u+=w?w(y,v):1):u=-1;const p=Math.max(r,s,u);if(p===u){const b=y>0&&v>0?c.get(y-1,v-1):0;c.set(y,v,b+1),a.set(y,v,3)}else p===r?(c.set(y,v,0),a.set(y,v,1)):p===s&&(c.set(y,v,0),a.set(y,v,2));e.set(y,v,p)}const l=[];let f=L.length,g=h.length;function S(y,v){(y+1!==f||v+1!==g)&&l.push(new A.SequenceDiff(new M.OffsetRange(y+1,f),new M.OffsetRange(v+1,g))),f=y,g=v}let _=L.length-1,E=h.length-1;for(;_>=0&&E>=0;)a.get(_,E)===3?(S(_,E),_--,E--):a.get(_,E)===1?_--:E--;return S(-1,-1),l.reverse(),new A.DiffAlgorithmResult(l,!1)}}n.DynamicProgrammingDiffing=d}),Y(X[30],J([0,1,11,3,4,2,15]),function(T,n,M,A,i,d,m){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.LinesSliceCharSequence=void 0;class L{constructor(c,l,f){this.lines=c,this.considerWhitespaceChanges=f,this.elements=[],this.firstCharOffsetByLine=[],this.additionalOffsetByLine=[];let g=!1;l.start>0&&l.endExclusive>=c.length&&(l=new A.OffsetRange(l.start-1,l.endExclusive),g=!0),this.lineRange=l,this.firstCharOffsetByLine[0]=0;for(let S=this.lineRange.start;S<this.lineRange.endExclusive;S++){let _=c[S],E=0;if(g)E=_.length,_="",g=!1;else if(!f){const y=_.trimStart();E=_.length-y.length,_=y.trimEnd()}this.additionalOffsetByLine.push(E);for(let y=0;y<_.length;y++)this.elements.push(_.charCodeAt(y));S<c.length-1&&(this.elements.push(`
`.charCodeAt(0)),this.firstCharOffsetByLine[S-this.lineRange.start+1]=this.elements.length)}this.additionalOffsetByLine.push(0)}toString(){return`Slice: "${this.text}"`}get text(){return this.getText(new A.OffsetRange(0,this.length))}getText(c){return this.elements.slice(c.start,c.endExclusive).map(l=>String.fromCharCode(l)).join("")}getElement(c){return this.elements[c]}get length(){return this.elements.length}getBoundaryScore(c){const l=e(c>0?this.elements[c-1]:-1),f=e(c<this.elements.length?this.elements[c]:-1);if(l===6&&f===7)return 0;let g=0;return l!==f&&(g+=10,l===0&&f===1&&(g+=1)),g+=w(l),g+=w(f),g}translateOffset(c){if(this.lineRange.isEmpty)return new i.Position(this.lineRange.start+1,1);const l=(0,M.findLastIdxMonotonous)(this.firstCharOffsetByLine,f=>f<=c);return new i.Position(this.lineRange.start+l+1,c-this.firstCharOffsetByLine[l]+this.additionalOffsetByLine[l]+1)}translateRange(c){return d.Range.fromPositions(this.translateOffset(c.start),this.translateOffset(c.endExclusive))}findWordContaining(c){if(c<0||c>=this.elements.length||!h(this.elements[c]))return;let l=c;for(;l>0&&h(this.elements[l-1]);)l--;let f=c;for(;f<this.elements.length&&h(this.elements[f]);)f++;return new A.OffsetRange(l,f)}countLinesIn(c){return this.translateOffset(c.endExclusive).lineNumber-this.translateOffset(c.start).lineNumber}isStronglyEqual(c,l){return this.elements[c]===this.elements[l]}extendToFullLines(c){var l,f;const g=(l=(0,M.findLastMonotonous)(this.firstCharOffsetByLine,_=>_<=c.start))!==null&&l!==void 0?l:0,S=(f=(0,M.findFirstMonotonous)(this.firstCharOffsetByLine,_=>c.endExclusive<=_))!==null&&f!==void 0?f:this.elements.length;return new A.OffsetRange(g,S)}}n.LinesSliceCharSequence=L;function h(a){return a>=97&&a<=122||a>=65&&a<=90||a>=48&&a<=57}const o={[0]:0,[1]:0,[2]:0,[3]:10,[4]:2,[5]:3,[6]:10,[7]:10};function w(a){return o[a]}function e(a){return a===10?7:a===13?6:(0,m.isSpace)(a)?5:a>=97&&a<=122?0:a>=65&&a<=90?1:a>=48&&a<=57?2:a===-1?3:4}}),Y(X[31],J([0,1]),function(T,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.MovedText=n.LinesDiff=void 0;class M{constructor(d,m,L){this.changes=d,this.moves=m,this.hitTimeout=L}}n.LinesDiff=M;class A{constructor(d,m){this.lineRangeMapping=d,this.changes=m}}n.MovedText=A}),Y(X[16],J([0,1,10]),function(T,n,M){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.RangeMapping=n.DetailedLineRangeMapping=n.LineRangeMapping=void 0;class A{static inverse(L,h,o){const w=[];let e=1,a=1;for(const l of L){const f=new i(new M.LineRange(e,l.original.startLineNumber),new M.LineRange(a,l.modified.startLineNumber),void 0);f.modified.isEmpty||w.push(f),e=l.original.endLineNumberExclusive,a=l.modified.endLineNumberExclusive}const c=new i(new M.LineRange(e,h+1),new M.LineRange(a,o+1),void 0);return c.modified.isEmpty||w.push(c),w}constructor(L,h){this.original=L,this.modified=h}toString(){return`{${this.original.toString()}->${this.modified.toString()}}`}flip(){return new A(this.modified,this.original)}join(L){return new A(this.original.join(L.original),this.modified.join(L.modified))}}n.LineRangeMapping=A;class i extends A{constructor(L,h,o){super(L,h),this.innerChanges=o}flip(){var L;return new i(this.modified,this.original,(L=this.innerChanges)===null||L===void 0?void 0:L.map(h=>h.flip()))}}n.DetailedLineRangeMapping=i;class d{constructor(L,h){this.originalRange=L,this.modifiedRange=h}toString(){return`{${this.originalRange.toString()}->${this.modifiedRange.toString()}}`}flip(){return new d(this.modifiedRange,this.originalRange)}}n.RangeMapping=d}),Y(X[46],J([0,1,8,16,7,11,37,10,3,30,15,29]),function(T,n,M,A,i,d,m,L,h,o,w,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.computeMovedLines=void 0;function a(_,E,y,v,r,s){let{moves:u,excludedChanges:p}=c(_,E,y,s);if(!s.isValid())return[];const b=_.filter(N=>!p.has(N)),C=l(b,v,r,E,y,s);return(0,i.pushMany)(u,C),u=g(u),u=u.filter(N=>N.original.toOffsetRange().slice(E).map(D=>D.trim()).join(`
`).length>=10),u=S(_,u),u}n.computeMovedLines=a;function c(_,E,y,v){const r=[],s=_.filter(b=>b.modified.isEmpty&&b.original.length>=3).map(b=>new w.LineRangeFragment(b.original,E,b)),u=new Set(_.filter(b=>b.original.isEmpty&&b.modified.length>=3).map(b=>new w.LineRangeFragment(b.modified,y,b))),p=new Set;for(const b of s){let C=-1,N;for(const R of u){const D=b.computeSimilarity(R);D>C&&(C=D,N=R)}if(C>.9&&N&&(u.delete(N),r.push(new A.LineRangeMapping(b.range,N.range)),p.add(b.source),p.add(N.source)),!v.isValid())return{moves:r,excludedChanges:p}}return{moves:r,excludedChanges:p}}function l(_,E,y,v,r,s){const u=[],p=new m.SetMap;for(const D of _)for(let k=D.original.startLineNumber;k<D.original.endLineNumberExclusive-2;k++){const U=`${E[k-1]}:${E[k+1-1]}:${E[k+2-1]}`;p.add(U,{range:new L.LineRange(k,k+3)})}const b=[];_.sort((0,i.compareBy)(D=>D.modified.startLineNumber,i.numberComparator));for(const D of _){let k=[];for(let U=D.modified.startLineNumber;U<D.modified.endLineNumberExclusive-2;U++){const I=`${y[U-1]}:${y[U+1-1]}:${y[U+2-1]}`,B=new L.LineRange(U,U+3),z=[];p.forEach(I,({range:x})=>{for(const F of k)if(F.originalLineRange.endLineNumberExclusive+1===x.endLineNumberExclusive&&F.modifiedLineRange.endLineNumberExclusive+1===B.endLineNumberExclusive){F.originalLineRange=new L.LineRange(F.originalLineRange.startLineNumber,x.endLineNumberExclusive),F.modifiedLineRange=new L.LineRange(F.modifiedLineRange.startLineNumber,B.endLineNumberExclusive),z.push(F);return}const O={modifiedLineRange:B,originalLineRange:x};b.push(O),z.push(O)}),k=z}if(!s.isValid())return[]}b.sort((0,i.reverseOrder)((0,i.compareBy)(D=>D.modifiedLineRange.length,i.numberComparator)));const C=new L.LineRangeSet,N=new L.LineRangeSet;for(const D of b){const k=D.modifiedLineRange.startLineNumber-D.originalLineRange.startLineNumber,U=C.subtractFrom(D.modifiedLineRange),I=N.subtractFrom(D.originalLineRange).getWithDelta(k),B=U.getIntersection(I);for(const z of B.ranges){if(z.length<3)continue;const x=z,O=z.delta(-k);u.push(new A.LineRangeMapping(O,x)),C.addRange(x),N.addRange(O)}}u.sort((0,i.compareBy)(D=>D.original.startLineNumber,i.numberComparator));const R=new d.MonotonousArray(_);for(let D=0;D<u.length;D++){const k=u[D],U=R.findLastMonotonous(V=>V.original.startLineNumber<=k.original.startLineNumber),I=(0,d.findLastMonotonous)(_,V=>V.modified.startLineNumber<=k.modified.startLineNumber),B=Math.max(k.original.startLineNumber-U.original.startLineNumber,k.modified.startLineNumber-I.modified.startLineNumber),z=R.findLastMonotonous(V=>V.original.startLineNumber<k.original.endLineNumberExclusive),x=(0,d.findLastMonotonous)(_,V=>V.modified.startLineNumber<k.modified.endLineNumberExclusive),O=Math.max(z.original.endLineNumberExclusive-k.original.endLineNumberExclusive,x.modified.endLineNumberExclusive-k.modified.endLineNumberExclusive);let F;for(F=0;F<B;F++){const V=k.original.startLineNumber-F-1,t=k.modified.startLineNumber-F-1;if(V>v.length||t>r.length||C.contains(t)||N.contains(V)||!f(v[V-1],r[t-1],s))break}F>0&&(N.addRange(new L.LineRange(k.original.startLineNumber-F,k.original.startLineNumber)),C.addRange(new L.LineRange(k.modified.startLineNumber-F,k.modified.startLineNumber)));let H;for(H=0;H<O;H++){const V=k.original.endLineNumberExclusive+H,t=k.modified.endLineNumberExclusive+H;if(V>v.length||t>r.length||C.contains(t)||N.contains(V)||!f(v[V-1],r[t-1],s))break}H>0&&(N.addRange(new L.LineRange(k.original.endLineNumberExclusive,k.original.endLineNumberExclusive+H)),C.addRange(new L.LineRange(k.modified.endLineNumberExclusive,k.modified.endLineNumberExclusive+H))),(F>0||H>0)&&(u[D]=new A.LineRangeMapping(new L.LineRange(k.original.startLineNumber-F,k.original.endLineNumberExclusive+H),new L.LineRange(k.modified.startLineNumber-F,k.modified.endLineNumberExclusive+H)))}return u}function f(_,E,y){if(_.trim()===E.trim())return!0;if(_.length>300&&E.length>300)return!1;const r=new e.MyersDiffAlgorithm().compute(new o.LinesSliceCharSequence([_],new h.OffsetRange(0,1),!1),new o.LinesSliceCharSequence([E],new h.OffsetRange(0,1),!1),y);let s=0;const u=M.SequenceDiff.invert(r.diffs,_.length);for(const N of u)N.seq1Range.forEach(R=>{(0,w.isSpace)(_.charCodeAt(R))||s++});function p(N){let R=0;for(let D=0;D<_.length;D++)(0,w.isSpace)(N.charCodeAt(D))||R++;return R}const b=p(_.length>E.length?_:E);return s/b>.6&&b>10}function g(_){if(_.length===0)return _;_.sort((0,i.compareBy)(y=>y.original.startLineNumber,i.numberComparator));const E=[_[0]];for(let y=1;y<_.length;y++){const v=E[E.length-1],r=_[y],s=r.original.startLineNumber-v.original.endLineNumberExclusive,u=r.modified.startLineNumber-v.modified.endLineNumberExclusive;if(s>=0&&u>=0&&s+u<=2){E[E.length-1]=v.join(r);continue}E.push(r)}return E}function S(_,E){const y=new d.MonotonousArray(_);return E=E.filter(v=>{const r=y.findLastMonotonous(p=>p.original.endLineNumberExclusive<v.original.endLineNumberExclusive)||new A.LineRangeMapping(new L.LineRange(1,1),new L.LineRange(1,1)),s=(0,d.findLastMonotonous)(_,p=>p.modified.endLineNumberExclusive<v.modified.endLineNumberExclusive);return r!==s}),E}}),Y(X[47],J([0,1,7,12,10,3,2,8,45,29,46,43,31,16,30,44]),function(T,n,M,A,i,d,m,L,h,o,w,e,a,c,l,f){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.getLineRangeMapping=n.lineRangeMappingFromRangeMappings=n.DefaultLinesDiffComputer=void 0;class g{constructor(){this.dynamicProgrammingDiffing=new h.DynamicProgrammingDiffing,this.myersDiffingAlgorithm=new o.MyersDiffAlgorithm}computeDiff(y,v,r){if(y.length<=1&&(0,M.equals)(y,v,(V,t)=>V===t))return new a.LinesDiff([],[],!1);if(y.length===1&&y[0].length===0||v.length===1&&v[0].length===0)return new a.LinesDiff([new c.DetailedLineRangeMapping(new i.LineRange(1,y.length+1),new i.LineRange(1,v.length+1),[new c.RangeMapping(new m.Range(1,1,y.length,y[0].length+1),new m.Range(1,1,v.length,v[0].length+1))])],[],!1);const s=r.maxComputationTimeMs===0?L.InfiniteTimeout.instance:new L.DateTimeout(r.maxComputationTimeMs),u=!r.ignoreTrimWhitespace,p=new Map;function b(V){let t=p.get(V);return t===void 0&&(t=p.size,p.set(V,t)),t}const C=y.map(V=>b(V.trim())),N=v.map(V=>b(V.trim())),R=new f.LineSequence(C,y),D=new f.LineSequence(N,v),k=(()=>R.length+D.length<1700?this.dynamicProgrammingDiffing.compute(R,D,s,(V,t)=>y[V]===v[t]?v[t].length===0?.1:1+Math.log(1+v[t].length):.99):this.myersDiffingAlgorithm.compute(R,D))();let U=k.diffs,I=k.hitTimeout;U=(0,e.optimizeSequenceDiffs)(R,D,U),U=(0,e.removeVeryShortMatchingLinesBetweenDiffs)(R,D,U);const B=[],z=V=>{if(u)for(let t=0;t<V;t++){const ne=x+t,re=O+t;if(y[ne]!==v[re]){const le=this.refineDiff(y,v,new L.SequenceDiff(new d.OffsetRange(ne,ne+1),new d.OffsetRange(re,re+1)),s,u);for(const me of le.mappings)B.push(me);le.hitTimeout&&(I=!0)}}};let x=0,O=0;for(const V of U){(0,A.assertFn)(()=>V.seq1Range.start-x===V.seq2Range.start-O);const t=V.seq1Range.start-x;z(t),x=V.seq1Range.endExclusive,O=V.seq2Range.endExclusive;const ne=this.refineDiff(y,v,V,s,u);ne.hitTimeout&&(I=!0);for(const re of ne.mappings)B.push(re)}z(y.length-x);const F=S(B,y,v);let H=[];return r.computeMoves&&(H=this.computeMoves(F,y,v,C,N,s,u)),(0,A.assertFn)(()=>{function V(ne,re){if(ne.lineNumber<1||ne.lineNumber>re.length)return!1;const le=re[ne.lineNumber-1];return!(ne.column<1||ne.column>le.length+1)}function t(ne,re){return!(ne.startLineNumber<1||ne.startLineNumber>re.length+1||ne.endLineNumberExclusive<1||ne.endLineNumberExclusive>re.length+1)}for(const ne of F){if(!ne.innerChanges)return!1;for(const re of ne.innerChanges)if(!(V(re.modifiedRange.getStartPosition(),v)&&V(re.modifiedRange.getEndPosition(),v)&&V(re.originalRange.getStartPosition(),y)&&V(re.originalRange.getEndPosition(),y)))return!1;if(!t(ne.modified,v)||!t(ne.original,y))return!1}return!0}),new a.LinesDiff(F,H,I)}computeMoves(y,v,r,s,u,p,b){return(0,w.computeMovedLines)(y,v,r,s,u,p).map(R=>{const D=this.refineDiff(v,r,new L.SequenceDiff(R.original.toOffsetRange(),R.modified.toOffsetRange()),p,b),k=S(D.mappings,v,r,!0);return new a.MovedText(R,k)})}refineDiff(y,v,r,s,u){const p=new l.LinesSliceCharSequence(y,r.seq1Range,u),b=new l.LinesSliceCharSequence(v,r.seq2Range,u),C=p.length+b.length<500?this.dynamicProgrammingDiffing.compute(p,b,s):this.myersDiffingAlgorithm.compute(p,b,s);let N=C.diffs;return N=(0,e.optimizeSequenceDiffs)(p,b,N),N=(0,e.extendDiffsToEntireWordIfAppropriate)(p,b,N),N=(0,e.removeShortMatches)(p,b,N),N=(0,e.removeVeryShortMatchingTextBetweenLongDiffs)(p,b,N),{mappings:N.map(D=>new c.RangeMapping(p.translateRange(D.seq1Range),b.translateRange(D.seq2Range))),hitTimeout:C.hitTimeout}}}n.DefaultLinesDiffComputer=g;function S(E,y,v,r=!1){const s=[];for(const u of(0,M.groupAdjacentBy)(E.map(p=>_(p,y,v)),(p,b)=>p.original.overlapOrTouch(b.original)||p.modified.overlapOrTouch(b.modified))){const p=u[0],b=u[u.length-1];s.push(new c.DetailedLineRangeMapping(p.original.join(b.original),p.modified.join(b.modified),u.map(C=>C.innerChanges[0])))}return(0,A.assertFn)(()=>!r&&s.length>0&&s[0].original.startLineNumber!==s[0].modified.startLineNumber?!1:(0,A.checkAdjacentItems)(s,(u,p)=>p.original.startLineNumber-u.original.endLineNumberExclusive===p.modified.startLineNumber-u.modified.endLineNumberExclusive&&u.original.endLineNumberExclusive<p.original.startLineNumber&&u.modified.endLineNumberExclusive<p.modified.startLineNumber)),s}n.lineRangeMappingFromRangeMappings=S;function _(E,y,v){let r=0,s=0;E.modifiedRange.endColumn===1&&E.originalRange.endColumn===1&&E.originalRange.startLineNumber+r<=E.originalRange.endLineNumber&&E.modifiedRange.startLineNumber+r<=E.modifiedRange.endLineNumber&&(s=-1),E.modifiedRange.startColumn-1>=v[E.modifiedRange.startLineNumber-1].length&&E.originalRange.startColumn-1>=y[E.originalRange.startLineNumber-1].length&&E.originalRange.startLineNumber<=E.originalRange.endLineNumber+s&&E.modifiedRange.startLineNumber<=E.modifiedRange.endLineNumber+s&&(r=1);const u=new i.LineRange(E.originalRange.startLineNumber+r,E.originalRange.endLineNumber+1+s),p=new i.LineRange(E.modifiedRange.startLineNumber+r,E.modifiedRange.endLineNumber+1+s);return new c.DetailedLineRangeMapping(u,p,[E])}n.getLineRangeMapping=_}),Y(X[48],J([0,1,24,31,16,6,2,12,10]),function(T,n,M,A,i,d,m,L,h){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.DiffComputer=n.LegacyLinesDiffComputer=void 0;const o=3;class w{computeDiff(r,s,u){var p;const C=new S(r,s,{maxComputationTime:u.maxComputationTimeMs,shouldIgnoreTrimWhitespace:u.ignoreTrimWhitespace,shouldComputeCharChanges:!0,shouldMakePrettyDiff:!0,shouldPostProcessCharChanges:!0}).computeDiff(),N=[];let R=null;for(const D of C.changes){let k;D.originalEndLineNumber===0?k=new h.LineRange(D.originalStartLineNumber+1,D.originalStartLineNumber+1):k=new h.LineRange(D.originalStartLineNumber,D.originalEndLineNumber+1);let U;D.modifiedEndLineNumber===0?U=new h.LineRange(D.modifiedStartLineNumber+1,D.modifiedStartLineNumber+1):U=new h.LineRange(D.modifiedStartLineNumber,D.modifiedEndLineNumber+1);let I=new i.DetailedLineRangeMapping(k,U,(p=D.charChanges)===null||p===void 0?void 0:p.map(B=>new i.RangeMapping(new m.Range(B.originalStartLineNumber,B.originalStartColumn,B.originalEndLineNumber,B.originalEndColumn),new m.Range(B.modifiedStartLineNumber,B.modifiedStartColumn,B.modifiedEndLineNumber,B.modifiedEndColumn))));R&&(R.modified.endLineNumberExclusive===I.modified.startLineNumber||R.original.endLineNumberExclusive===I.original.startLineNumber)&&(I=new i.DetailedLineRangeMapping(R.original.join(I.original),R.modified.join(I.modified),R.innerChanges&&I.innerChanges?R.innerChanges.concat(I.innerChanges):void 0),N.pop()),N.push(I),R=I}return(0,L.assertFn)(()=>(0,L.checkAdjacentItems)(N,(D,k)=>k.original.startLineNumber-D.original.endLineNumberExclusive===k.modified.startLineNumber-D.modified.endLineNumberExclusive&&D.original.endLineNumberExclusive<k.original.startLineNumber&&D.modified.endLineNumberExclusive<k.modified.startLineNumber)),new A.LinesDiff(N,[],C.quitEarly)}}n.LegacyLinesDiffComputer=w;function e(v,r,s,u){return new M.LcsDiff(v,r,s).ComputeDiff(u)}class a{constructor(r){const s=[],u=[];for(let p=0,b=r.length;p<b;p++)s[p]=_(r[p],1),u[p]=E(r[p],1);this.lines=r,this._startColumns=s,this._endColumns=u}getElements(){const r=[];for(let s=0,u=this.lines.length;s<u;s++)r[s]=this.lines[s].substring(this._startColumns[s]-1,this._endColumns[s]-1);return r}getStrictElement(r){return this.lines[r]}getStartLineNumber(r){return r+1}getEndLineNumber(r){return r+1}createCharSequence(r,s,u){const p=[],b=[],C=[];let N=0;for(let R=s;R<=u;R++){const D=this.lines[R],k=r?this._startColumns[R]:1,U=r?this._endColumns[R]:D.length+1;for(let I=k;I<U;I++)p[N]=D.charCodeAt(I-1),b[N]=R+1,C[N]=I,N++;!r&&R<u&&(p[N]=10,b[N]=R+1,C[N]=D.length+1,N++)}return new c(p,b,C)}}class c{constructor(r,s,u){this._charCodes=r,this._lineNumbers=s,this._columns=u}toString(){return"["+this._charCodes.map((r,s)=>(r===10?"\\n":String.fromCharCode(r))+`-(${this._lineNumbers[s]},${this._columns[s]})`).join(", ")+"]"}_assertIndex(r,s){if(r<0||r>=s.length)throw new Error("Illegal index")}getElements(){return this._charCodes}getStartLineNumber(r){return r>0&&r===this._lineNumbers.length?this.getEndLineNumber(r-1):(this._assertIndex(r,this._lineNumbers),this._lineNumbers[r])}getEndLineNumber(r){return r===-1?this.getStartLineNumber(r+1):(this._assertIndex(r,this._lineNumbers),this._charCodes[r]===10?this._lineNumbers[r]+1:this._lineNumbers[r])}getStartColumn(r){return r>0&&r===this._columns.length?this.getEndColumn(r-1):(this._assertIndex(r,this._columns),this._columns[r])}getEndColumn(r){return r===-1?this.getStartColumn(r+1):(this._assertIndex(r,this._columns),this._charCodes[r]===10?1:this._columns[r]+1)}}class l{constructor(r,s,u,p,b,C,N,R){this.originalStartLineNumber=r,this.originalStartColumn=s,this.originalEndLineNumber=u,this.originalEndColumn=p,this.modifiedStartLineNumber=b,this.modifiedStartColumn=C,this.modifiedEndLineNumber=N,this.modifiedEndColumn=R}static createFromDiffChange(r,s,u){const p=s.getStartLineNumber(r.originalStart),b=s.getStartColumn(r.originalStart),C=s.getEndLineNumber(r.originalStart+r.originalLength-1),N=s.getEndColumn(r.originalStart+r.originalLength-1),R=u.getStartLineNumber(r.modifiedStart),D=u.getStartColumn(r.modifiedStart),k=u.getEndLineNumber(r.modifiedStart+r.modifiedLength-1),U=u.getEndColumn(r.modifiedStart+r.modifiedLength-1);return new l(p,b,C,N,R,D,k,U)}}function f(v){if(v.length<=1)return v;const r=[v[0]];let s=r[0];for(let u=1,p=v.length;u<p;u++){const b=v[u],C=b.originalStart-(s.originalStart+s.originalLength),N=b.modifiedStart-(s.modifiedStart+s.modifiedLength);Math.min(C,N)<o?(s.originalLength=b.originalStart+b.originalLength-s.originalStart,s.modifiedLength=b.modifiedStart+b.modifiedLength-s.modifiedStart):(r.push(b),s=b)}return r}class g{constructor(r,s,u,p,b){this.originalStartLineNumber=r,this.originalEndLineNumber=s,this.modifiedStartLineNumber=u,this.modifiedEndLineNumber=p,this.charChanges=b}static createFromDiffResult(r,s,u,p,b,C,N){let R,D,k,U,I;if(s.originalLength===0?(R=u.getStartLineNumber(s.originalStart)-1,D=0):(R=u.getStartLineNumber(s.originalStart),D=u.getEndLineNumber(s.originalStart+s.originalLength-1)),s.modifiedLength===0?(k=p.getStartLineNumber(s.modifiedStart)-1,U=0):(k=p.getStartLineNumber(s.modifiedStart),U=p.getEndLineNumber(s.modifiedStart+s.modifiedLength-1)),C&&s.originalLength>0&&s.originalLength<20&&s.modifiedLength>0&&s.modifiedLength<20&&b()){const B=u.createCharSequence(r,s.originalStart,s.originalStart+s.originalLength-1),z=p.createCharSequence(r,s.modifiedStart,s.modifiedStart+s.modifiedLength-1);if(B.getElements().length>0&&z.getElements().length>0){let x=e(B,z,b,!0).changes;N&&(x=f(x)),I=[];for(let O=0,F=x.length;O<F;O++)I.push(l.createFromDiffChange(x[O],B,z))}}return new g(R,D,k,U,I)}}class S{constructor(r,s,u){this.shouldComputeCharChanges=u.shouldComputeCharChanges,this.shouldPostProcessCharChanges=u.shouldPostProcessCharChanges,this.shouldIgnoreTrimWhitespace=u.shouldIgnoreTrimWhitespace,this.shouldMakePrettyDiff=u.shouldMakePrettyDiff,this.originalLines=r,this.modifiedLines=s,this.original=new a(r),this.modified=new a(s),this.continueLineDiff=y(u.maxComputationTime),this.continueCharDiff=y(u.maxComputationTime===0?0:Math.min(u.maxComputationTime,5e3))}computeDiff(){if(this.original.lines.length===1&&this.original.lines[0].length===0)return this.modified.lines.length===1&&this.modified.lines[0].length===0?{quitEarly:!1,changes:[]}:{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:1,modifiedStartLineNumber:1,modifiedEndLineNumber:this.modified.lines.length,charChanges:void 0}]};if(this.modified.lines.length===1&&this.modified.lines[0].length===0)return{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:this.original.lines.length,modifiedStartLineNumber:1,modifiedEndLineNumber:1,charChanges:void 0}]};const r=e(this.original,this.modified,this.continueLineDiff,this.shouldMakePrettyDiff),s=r.changes,u=r.quitEarly;if(this.shouldIgnoreTrimWhitespace){const N=[];for(let R=0,D=s.length;R<D;R++)N.push(g.createFromDiffResult(this.shouldIgnoreTrimWhitespace,s[R],this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges));return{quitEarly:u,changes:N}}const p=[];let b=0,C=0;for(let N=-1,R=s.length;N<R;N++){const D=N+1<R?s[N+1]:null,k=D?D.originalStart:this.originalLines.length,U=D?D.modifiedStart:this.modifiedLines.length;for(;b<k&&C<U;){const I=this.originalLines[b],B=this.modifiedLines[C];if(I!==B){{let z=_(I,1),x=_(B,1);for(;z>1&&x>1;){const O=I.charCodeAt(z-2),F=B.charCodeAt(x-2);if(O!==F)break;z--,x--}(z>1||x>1)&&this._pushTrimWhitespaceCharChange(p,b+1,1,z,C+1,1,x)}{let z=E(I,1),x=E(B,1);const O=I.length+1,F=B.length+1;for(;z<O&&x<F;){const H=I.charCodeAt(z-1),V=I.charCodeAt(x-1);if(H!==V)break;z++,x++}(z<O||x<F)&&this._pushTrimWhitespaceCharChange(p,b+1,z,O,C+1,x,F)}}b++,C++}D&&(p.push(g.createFromDiffResult(this.shouldIgnoreTrimWhitespace,D,this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges)),b+=D.originalLength,C+=D.modifiedLength)}return{quitEarly:u,changes:p}}_pushTrimWhitespaceCharChange(r,s,u,p,b,C,N){if(this._mergeTrimWhitespaceCharChange(r,s,u,p,b,C,N))return;let R;this.shouldComputeCharChanges&&(R=[new l(s,u,s,p,b,C,b,N)]),r.push(new g(s,s,b,b,R))}_mergeTrimWhitespaceCharChange(r,s,u,p,b,C,N){const R=r.length;if(R===0)return!1;const D=r[R-1];return D.originalEndLineNumber===0||D.modifiedEndLineNumber===0?!1:D.originalEndLineNumber===s&&D.modifiedEndLineNumber===b?(this.shouldComputeCharChanges&&D.charChanges&&D.charChanges.push(new l(s,u,s,p,b,C,b,N)),!0):D.originalEndLineNumber+1===s&&D.modifiedEndLineNumber+1===b?(D.originalEndLineNumber=s,D.modifiedEndLineNumber=b,this.shouldComputeCharChanges&&D.charChanges&&D.charChanges.push(new l(s,u,s,p,b,C,b,N)),!0):!1}}n.DiffComputer=S;function _(v,r){const s=d.firstNonWhitespaceIndex(v);return s===-1?r:s+1}function E(v,r){const s=d.lastNonWhitespaceIndex(v);return s===-1?r:s+2}function y(v){if(v===0)return()=>!0;const r=Date.now();return()=>Date.now()-r<v}}),Y(X[49],J([0,1,48,47]),function(T,n,M,A){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.linesDiffComputers=void 0,n.linesDiffComputers={getLegacy:()=>new M.LegacyLinesDiffComputer,getDefault:()=>new A.DefaultLinesDiffComputer}}),Y(X[50],J([0,1,33]),function(T,n,M){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.computeDefaultDocumentColors=void 0;function A(a){const c=[];for(const l of a){const f=Number(l);(f||f===0&&l.replace(/\s/g,"")!=="")&&c.push(f)}return c}function i(a,c,l,f){return{red:a/255,blue:l/255,green:c/255,alpha:f}}function d(a,c){const l=c.index,f=c[0].length;if(!l)return;const g=a.positionAt(l);return{startLineNumber:g.lineNumber,startColumn:g.column,endLineNumber:g.lineNumber,endColumn:g.column+f}}function m(a,c){if(!a)return;const l=M.Color.Format.CSS.parseHex(c);if(l)return{range:a,color:i(l.rgba.r,l.rgba.g,l.rgba.b,l.rgba.a)}}function L(a,c,l){if(!a||c.length!==1)return;const g=c[0].values(),S=A(g);return{range:a,color:i(S[0],S[1],S[2],l?S[3]:1)}}function h(a,c,l){if(!a||c.length!==1)return;const g=c[0].values(),S=A(g),_=new M.Color(new M.HSLA(S[0],S[1]/100,S[2]/100,l?S[3]:1));return{range:a,color:i(_.rgba.r,_.rgba.g,_.rgba.b,_.rgba.a)}}function o(a,c){return typeof a=="string"?[...a.matchAll(c)]:a.findMatches(c)}function w(a){const c=[],f=o(a,/\b(rgb|rgba|hsl|hsla)(\([0-9\s,.\%]*\))|(#)([A-Fa-f0-9]{3})\b|(#)([A-Fa-f0-9]{4})\b|(#)([A-Fa-f0-9]{6})\b|(#)([A-Fa-f0-9]{8})\b/gm);if(f.length>0)for(const g of f){const S=g.filter(v=>v!==void 0),_=S[1],E=S[2];if(!E)continue;let y;if(_==="rgb"){const v=/^\(\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*\)$/gm;y=L(d(a,g),o(E,v),!1)}else if(_==="rgba"){const v=/^\(\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\s*\)$/gm;y=L(d(a,g),o(E,v),!0)}else if(_==="hsl"){const v=/^\(\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*\)$/gm;y=h(d(a,g),o(E,v),!1)}else if(_==="hsla"){const v=/^\(\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\s*\)$/gm;y=h(d(a,g),o(E,v),!0)}else _==="#"&&(y=m(d(a,g),_+E));y&&c.push(y)}return c}function e(a){return!a||typeof a.getValue!="function"||typeof a.positionAt!="function"?[]:w(a)}n.computeDefaultDocumentColors=e}),Y(X[51],J([0,1,27]),function(T,n,M){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.computeLinks=n.LinkComputer=n.StateMachine=void 0;class A{constructor(a,c,l){const f=new Uint8Array(a*c);for(let g=0,S=a*c;g<S;g++)f[g]=l;this._data=f,this.rows=a,this.cols=c}get(a,c){return this._data[a*this.cols+c]}set(a,c,l){this._data[a*this.cols+c]=l}}class i{constructor(a){let c=0,l=0;for(let g=0,S=a.length;g<S;g++){const[_,E,y]=a[g];E>c&&(c=E),_>l&&(l=_),y>l&&(l=y)}c++,l++;const f=new A(l,c,0);for(let g=0,S=a.length;g<S;g++){const[_,E,y]=a[g];f.set(_,E,y)}this._states=f,this._maxCharCode=c}nextState(a,c){return c<0||c>=this._maxCharCode?0:this._states.get(a,c)}}n.StateMachine=i;let d=null;function m(){return d===null&&(d=new i([[1,104,2],[1,72,2],[1,102,6],[1,70,6],[2,116,3],[2,84,3],[3,116,4],[3,84,4],[4,112,5],[4,80,5],[5,115,9],[5,83,9],[5,58,10],[6,105,7],[6,73,7],[7,108,8],[7,76,8],[8,101,9],[8,69,9],[9,58,10],[10,47,11],[11,47,12]])),d}let L=null;function h(){if(L===null){L=new M.CharacterClassifier(0);const e=` 	<>'"\u3001\u3002\uFF61\uFF64\uFF0C\uFF0E\uFF1A\uFF1B\u2018\u3008\u300C\u300E\u3014\uFF08\uFF3B\uFF5B\uFF62\uFF63\uFF5D\uFF3D\uFF09\u3015\u300F\u300D\u3009\u2019\uFF40\uFF5E\u2026`;for(let c=0;c<e.length;c++)L.set(e.charCodeAt(c),1);const a=".,;:";for(let c=0;c<a.length;c++)L.set(a.charCodeAt(c),2)}return L}class o{static _createLink(a,c,l,f,g){let S=g-1;do{const _=c.charCodeAt(S);if(a.get(_)!==2)break;S--}while(S>f);if(f>0){const _=c.charCodeAt(f-1),E=c.charCodeAt(S);(_===40&&E===41||_===91&&E===93||_===123&&E===125)&&S--}return{range:{startLineNumber:l,startColumn:f+1,endLineNumber:l,endColumn:S+2},url:c.substring(f,S+1)}}static computeLinks(a,c=m()){const l=h(),f=[];for(let g=1,S=a.getLineCount();g<=S;g++){const _=a.getLineContent(g),E=_.length;let y=0,v=0,r=0,s=1,u=!1,p=!1,b=!1,C=!1;for(;y<E;){let N=!1;const R=_.charCodeAt(y);if(s===13){let D;switch(R){case 40:u=!0,D=0;break;case 41:D=u?0:1;break;case 91:b=!0,p=!0,D=0;break;case 93:b=!1,D=p?0:1;break;case 123:C=!0,D=0;break;case 125:D=C?0:1;break;case 39:case 34:case 96:r===R?D=1:r===39||r===34||r===96?D=0:D=1;break;case 42:D=r===42?1:0;break;case 124:D=r===124?1:0;break;case 32:D=b?0:1;break;default:D=l.get(R)}D===1&&(f.push(o._createLink(l,_,g,v,y)),N=!0)}else if(s===12){let D;R===91?(p=!0,D=0):D=l.get(R),D===1?N=!0:s=13}else s=c.nextState(s,R),s===0&&(N=!0);N&&(s=1,u=!1,p=!1,C=!1,v=y+1,r=R),y++}s===13&&f.push(o._createLink(l,_,g,v,E))}return f}}n.LinkComputer=o;function w(e){return!e||typeof e.getLineCount!="function"||typeof e.getLineContent!="function"?[]:o.computeLinks(e)}n.computeLinks=w}),Y(X[52],J([0,1]),function(T,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.BasicInplaceReplace=void 0;class M{constructor(){this._defaultValueSet=[["true","false"],["True","False"],["Private","Public","Friend","ReadOnly","Partial","Protected","WriteOnly"],["public","protected","private"]]}navigateValueSet(i,d,m,L,h){if(i&&d){const o=this.doNavigateValueSet(d,h);if(o)return{range:i,value:o}}if(m&&L){const o=this.doNavigateValueSet(L,h);if(o)return{range:m,value:o}}return null}doNavigateValueSet(i,d){const m=this.numberReplace(i,d);return m!==null?m:this.textReplace(i,d)}numberReplace(i,d){const m=Math.pow(10,i.length-(i.lastIndexOf(".")+1));let L=Number(i);const h=parseFloat(i);return!isNaN(L)&&!isNaN(h)&&L===h?L===0&&!d?null:(L=Math.floor(L*m),L+=d?m:-m,String(L/m)):null}textReplace(i,d){return this.valueSetsReplace(this._defaultValueSet,i,d)}valueSetsReplace(i,d,m){let L=null;for(let h=0,o=i.length;L===null&&h<o;h++)L=this.valueSetReplace(i[h],d,m);return L}valueSetReplace(i,d,m){let L=i.indexOf(d);return L>=0?(L+=m?1:-1,L<0?L=i.length-1:L%=i.length,i[L]):null}}n.BasicInplaceReplace=M,M.INSTANCE=new M}),Y(X[53],J([0,1,14]),function(T,n,M){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.shouldSynchronizeModel=n.ApplyEditsResult=n.SearchData=n.ValidAnnotatedEditOperation=n.isITextSnapshot=n.FindMatch=n.TextModelResolvedOptions=n.InjectedTextCursorStops=n.MinimapPosition=n.GlyphMarginLane=n.OverviewRulerLane=void 0;var A;(function(l){l[l.Left=1]="Left",l[l.Center=2]="Center",l[l.Right=4]="Right",l[l.Full=7]="Full"})(A||(n.OverviewRulerLane=A={}));var i;(function(l){l[l.Left=1]="Left",l[l.Right=2]="Right"})(i||(n.GlyphMarginLane=i={}));var d;(function(l){l[l.Inline=1]="Inline",l[l.Gutter=2]="Gutter"})(d||(n.MinimapPosition=d={}));var m;(function(l){l[l.Both=0]="Both",l[l.Right=1]="Right",l[l.Left=2]="Left",l[l.None=3]="None"})(m||(n.InjectedTextCursorStops=m={}));class L{get originalIndentSize(){return this._indentSizeIsTabSize?"tabSize":this.indentSize}constructor(f){this._textModelResolvedOptionsBrand=void 0,this.tabSize=Math.max(1,f.tabSize|0),f.indentSize==="tabSize"?(this.indentSize=this.tabSize,this._indentSizeIsTabSize=!0):(this.indentSize=Math.max(1,f.indentSize|0),this._indentSizeIsTabSize=!1),this.insertSpaces=!!f.insertSpaces,this.defaultEOL=f.defaultEOL|0,this.trimAutoWhitespace=!!f.trimAutoWhitespace,this.bracketPairColorizationOptions=f.bracketPairColorizationOptions}equals(f){return this.tabSize===f.tabSize&&this._indentSizeIsTabSize===f._indentSizeIsTabSize&&this.indentSize===f.indentSize&&this.insertSpaces===f.insertSpaces&&this.defaultEOL===f.defaultEOL&&this.trimAutoWhitespace===f.trimAutoWhitespace&&(0,M.equals)(this.bracketPairColorizationOptions,f.bracketPairColorizationOptions)}createChangeEvent(f){return{tabSize:this.tabSize!==f.tabSize,indentSize:this.indentSize!==f.indentSize,insertSpaces:this.insertSpaces!==f.insertSpaces,trimAutoWhitespace:this.trimAutoWhitespace!==f.trimAutoWhitespace}}}n.TextModelResolvedOptions=L;class h{constructor(f,g){this._findMatchBrand=void 0,this.range=f,this.matches=g}}n.FindMatch=h;function o(l){return l&&typeof l.read=="function"}n.isITextSnapshot=o;class w{constructor(f,g,S,_,E,y){this.identifier=f,this.range=g,this.text=S,this.forceMoveMarkers=_,this.isAutoWhitespaceEdit=E,this._isTracked=y}}n.ValidAnnotatedEditOperation=w;class e{constructor(f,g,S){this.regex=f,this.wordSeparators=g,this.simpleSearch=S}}n.SearchData=e;class a{constructor(f,g,S){this.reverseEdits=f,this.changes=g,this.trimAutoWhitespaceLineNumbers=S}}n.ApplyEditsResult=a;function c(l){return!l.isTooLargeForSyncing()&&!l.isForSimpleWidget}n.shouldSynchronizeModel=c}),Y(X[54],J([0,1,7,26]),function(T,n,M,A){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.PrefixSumIndexOfResult=n.ConstantTimePrefixSumComputer=n.PrefixSumComputer=void 0;class i{constructor(h){this.values=h,this.prefixSum=new Uint32Array(h.length),this.prefixSumValidIndex=new Int32Array(1),this.prefixSumValidIndex[0]=-1}insertValues(h,o){h=(0,A.toUint32)(h);const w=this.values,e=this.prefixSum,a=o.length;return a===0?!1:(this.values=new Uint32Array(w.length+a),this.values.set(w.subarray(0,h),0),this.values.set(w.subarray(h),h+a),this.values.set(o,h),h-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=h-1),this.prefixSum=new Uint32Array(this.values.length),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(e.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}setValue(h,o){return h=(0,A.toUint32)(h),o=(0,A.toUint32)(o),this.values[h]===o?!1:(this.values[h]=o,h-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=h-1),!0)}removeValues(h,o){h=(0,A.toUint32)(h),o=(0,A.toUint32)(o);const w=this.values,e=this.prefixSum;if(h>=w.length)return!1;const a=w.length-h;return o>=a&&(o=a),o===0?!1:(this.values=new Uint32Array(w.length-o),this.values.set(w.subarray(0,h),0),this.values.set(w.subarray(h+o),h),this.prefixSum=new Uint32Array(this.values.length),h-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=h-1),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(e.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}getTotalSum(){return this.values.length===0?0:this._getPrefixSum(this.values.length-1)}getPrefixSum(h){return h<0?0:(h=(0,A.toUint32)(h),this._getPrefixSum(h))}_getPrefixSum(h){if(h<=this.prefixSumValidIndex[0])return this.prefixSum[h];let o=this.prefixSumValidIndex[0]+1;o===0&&(this.prefixSum[0]=this.values[0],o++),h>=this.values.length&&(h=this.values.length-1);for(let w=o;w<=h;w++)this.prefixSum[w]=this.prefixSum[w-1]+this.values[w];return this.prefixSumValidIndex[0]=Math.max(this.prefixSumValidIndex[0],h),this.prefixSum[h]}getIndexOf(h){h=Math.floor(h),this.getTotalSum();let o=0,w=this.values.length-1,e=0,a=0,c=0;for(;o<=w;)if(e=o+(w-o)/2|0,a=this.prefixSum[e],c=a-this.values[e],h<c)w=e-1;else if(h>=a)o=e+1;else break;return new m(e,h-c)}}n.PrefixSumComputer=i;class d{constructor(h){this._values=h,this._isValid=!1,this._validEndIndex=-1,this._prefixSum=[],this._indexBySum=[]}getTotalSum(){return this._ensureValid(),this._indexBySum.length}getPrefixSum(h){return this._ensureValid(),h===0?0:this._prefixSum[h-1]}getIndexOf(h){this._ensureValid();const o=this._indexBySum[h],w=o>0?this._prefixSum[o-1]:0;return new m(o,h-w)}removeValues(h,o){this._values.splice(h,o),this._invalidate(h)}insertValues(h,o){this._values=(0,M.arrayInsert)(this._values,h,o),this._invalidate(h)}_invalidate(h){this._isValid=!1,this._validEndIndex=Math.min(this._validEndIndex,h-1)}_ensureValid(){if(!this._isValid){for(let h=this._validEndIndex+1,o=this._values.length;h<o;h++){const w=this._values[h],e=h>0?this._prefixSum[h-1]:0;this._prefixSum[h]=e+w;for(let a=0;a<w;a++)this._indexBySum[e+a]=h}this._prefixSum.length=this._values.length,this._indexBySum.length=this._prefixSum[this._prefixSum.length-1],this._isValid=!0,this._validEndIndex=this._values.length-1}}setValue(h,o){this._values[h]!==o&&(this._values[h]=o,this._invalidate(h))}}n.ConstantTimePrefixSumComputer=d;class m{constructor(h,o){this.index=h,this.remainder=o,this._prefixSumIndexOfResultBrand=void 0,this.index=h,this.remainder=o}}n.PrefixSumIndexOfResult=m}),Y(X[55],J([0,1,6,4,54]),function(T,n,M,A,i){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.MirrorTextModel=void 0;class d{constructor(L,h,o,w){this._uri=L,this._lines=h,this._eol=o,this._versionId=w,this._lineStarts=null,this._cachedTextValue=null}dispose(){this._lines.length=0}get version(){return this._versionId}getText(){return this._cachedTextValue===null&&(this._cachedTextValue=this._lines.join(this._eol)),this._cachedTextValue}onEvents(L){L.eol&&L.eol!==this._eol&&(this._eol=L.eol,this._lineStarts=null);const h=L.changes;for(const o of h)this._acceptDeleteRange(o.range),this._acceptInsertText(new A.Position(o.range.startLineNumber,o.range.startColumn),o.text);this._versionId=L.versionId,this._cachedTextValue=null}_ensureLineStarts(){if(!this._lineStarts){const L=this._eol.length,h=this._lines.length,o=new Uint32Array(h);for(let w=0;w<h;w++)o[w]=this._lines[w].length+L;this._lineStarts=new i.PrefixSumComputer(o)}}_setLineText(L,h){this._lines[L]=h,this._lineStarts&&this._lineStarts.setValue(L,this._lines[L].length+this._eol.length)}_acceptDeleteRange(L){if(L.startLineNumber===L.endLineNumber){if(L.startColumn===L.endColumn)return;this._setLineText(L.startLineNumber-1,this._lines[L.startLineNumber-1].substring(0,L.startColumn-1)+this._lines[L.startLineNumber-1].substring(L.endColumn-1));return}this._setLineText(L.startLineNumber-1,this._lines[L.startLineNumber-1].substring(0,L.startColumn-1)+this._lines[L.endLineNumber-1].substring(L.endColumn-1)),this._lines.splice(L.startLineNumber,L.endLineNumber-L.startLineNumber),this._lineStarts&&this._lineStarts.removeValues(L.startLineNumber,L.endLineNumber-L.startLineNumber)}_acceptInsertText(L,h){if(h.length===0)return;const o=(0,M.splitLines)(h);if(o.length===1){this._setLineText(L.lineNumber-1,this._lines[L.lineNumber-1].substring(0,L.column-1)+o[0]+this._lines[L.lineNumber-1].substring(L.column-1));return}o[o.length-1]+=this._lines[L.lineNumber-1].substring(L.column-1),this._setLineText(L.lineNumber-1,this._lines[L.lineNumber-1].substring(0,L.column-1)+o[0]);const w=new Uint32Array(o.length-1);for(let e=1;e<o.length;e++)this._lines.splice(L.lineNumber+e-1,0,o[e]),w[e-1]=o[e].length+this._eol.length;this._lineStarts&&this._lineStarts.insertValues(L.lineNumber,w)}}n.MirrorTextModel=d}),Y(X[56],J([0,1,6,42,4,2,53]),function(T,n,M,A,i,d,m){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Searcher=n.isValidMatch=n.TextModelSearch=n.createFindMatch=n.isMultilineRegexSource=n.SearchParams=void 0;const L=999;class h{constructor(_,E,y,v){this.searchString=_,this.isRegex=E,this.matchCase=y,this.wordSeparators=v}parseSearchRequest(){if(this.searchString==="")return null;let _;this.isRegex?_=o(this.searchString):_=this.searchString.indexOf(`
`)>=0;let E=null;try{E=M.createRegExp(this.searchString,this.isRegex,{matchCase:this.matchCase,wholeWord:!1,multiline:_,global:!0,unicode:!0})}catch{return null}if(!E)return null;let y=!this.isRegex&&!_;return y&&this.searchString.toLowerCase()!==this.searchString.toUpperCase()&&(y=this.matchCase),new m.SearchData(E,this.wordSeparators?(0,A.getMapForWordSeparators)(this.wordSeparators):null,y?this.searchString:null)}}n.SearchParams=h;function o(S){if(!S||S.length===0)return!1;for(let _=0,E=S.length;_<E;_++){const y=S.charCodeAt(_);if(y===10)return!0;if(y===92){if(_++,_>=E)break;const v=S.charCodeAt(_);if(v===110||v===114||v===87)return!0}}return!1}n.isMultilineRegexSource=o;function w(S,_,E){if(!E)return new m.FindMatch(S,null);const y=[];for(let v=0,r=_.length;v<r;v++)y[v]=_[v];return new m.FindMatch(S,y)}n.createFindMatch=w;class e{constructor(_){const E=[];let y=0;for(let v=0,r=_.length;v<r;v++)_.charCodeAt(v)===10&&(E[y++]=v);this._lineFeedsOffsets=E}findLineFeedCountBeforeOffset(_){const E=this._lineFeedsOffsets;let y=0,v=E.length-1;if(v===-1||_<=E[0])return 0;for(;y<v;){const r=y+((v-y)/2>>0);E[r]>=_?v=r-1:E[r+1]>=_?(y=r,v=r):y=r+1}return y+1}}class a{static findMatches(_,E,y,v,r){const s=E.parseSearchRequest();return s?s.regex.multiline?this._doFindMatchesMultiline(_,y,new g(s.wordSeparators,s.regex),v,r):this._doFindMatchesLineByLine(_,y,s,v,r):[]}static _getMultilineMatchRange(_,E,y,v,r,s){let u,p=0;v?(p=v.findLineFeedCountBeforeOffset(r),u=E+r+p):u=E+r;let b;if(v){const D=v.findLineFeedCountBeforeOffset(r+s.length)-p;b=u+s.length+D}else b=u+s.length;const C=_.getPositionAt(u),N=_.getPositionAt(b);return new d.Range(C.lineNumber,C.column,N.lineNumber,N.column)}static _doFindMatchesMultiline(_,E,y,v,r){const s=_.getOffsetAt(E.getStartPosition()),u=_.getValueInRange(E,1),p=_.getEOL()===`\r
`?new e(u):null,b=[];let C=0,N;for(y.reset(0);N=y.next(u);)if(b[C++]=w(this._getMultilineMatchRange(_,s,u,p,N.index,N[0]),N,v),C>=r)return b;return b}static _doFindMatchesLineByLine(_,E,y,v,r){const s=[];let u=0;if(E.startLineNumber===E.endLineNumber){const b=_.getLineContent(E.startLineNumber).substring(E.startColumn-1,E.endColumn-1);return u=this._findMatchesInLine(y,b,E.startLineNumber,E.startColumn-1,u,s,v,r),s}const p=_.getLineContent(E.startLineNumber).substring(E.startColumn-1);u=this._findMatchesInLine(y,p,E.startLineNumber,E.startColumn-1,u,s,v,r);for(let b=E.startLineNumber+1;b<E.endLineNumber&&u<r;b++)u=this._findMatchesInLine(y,_.getLineContent(b),b,0,u,s,v,r);if(u<r){const b=_.getLineContent(E.endLineNumber).substring(0,E.endColumn-1);u=this._findMatchesInLine(y,b,E.endLineNumber,0,u,s,v,r)}return s}static _findMatchesInLine(_,E,y,v,r,s,u,p){const b=_.wordSeparators;if(!u&&_.simpleSearch){const R=_.simpleSearch,D=R.length,k=E.length;let U=-D;for(;(U=E.indexOf(R,U+D))!==-1;)if((!b||f(b,E,k,U,D))&&(s[r++]=new m.FindMatch(new d.Range(y,U+1+v,y,U+1+D+v),null),r>=p))return r;return r}const C=new g(_.wordSeparators,_.regex);let N;C.reset(0);do if(N=C.next(E),N&&(s[r++]=w(new d.Range(y,N.index+1+v,y,N.index+1+N[0].length+v),N,u),r>=p))return r;while(N);return r}static findNextMatch(_,E,y,v){const r=E.parseSearchRequest();if(!r)return null;const s=new g(r.wordSeparators,r.regex);return r.regex.multiline?this._doFindNextMatchMultiline(_,y,s,v):this._doFindNextMatchLineByLine(_,y,s,v)}static _doFindNextMatchMultiline(_,E,y,v){const r=new i.Position(E.lineNumber,1),s=_.getOffsetAt(r),u=_.getLineCount(),p=_.getValueInRange(new d.Range(r.lineNumber,r.column,u,_.getLineMaxColumn(u)),1),b=_.getEOL()===`\r
`?new e(p):null;y.reset(E.column-1);const C=y.next(p);return C?w(this._getMultilineMatchRange(_,s,p,b,C.index,C[0]),C,v):E.lineNumber!==1||E.column!==1?this._doFindNextMatchMultiline(_,new i.Position(1,1),y,v):null}static _doFindNextMatchLineByLine(_,E,y,v){const r=_.getLineCount(),s=E.lineNumber,u=_.getLineContent(s),p=this._findFirstMatchInLine(y,u,s,E.column,v);if(p)return p;for(let b=1;b<=r;b++){const C=(s+b-1)%r,N=_.getLineContent(C+1),R=this._findFirstMatchInLine(y,N,C+1,1,v);if(R)return R}return null}static _findFirstMatchInLine(_,E,y,v,r){_.reset(v-1);const s=_.next(E);return s?w(new d.Range(y,s.index+1,y,s.index+1+s[0].length),s,r):null}static findPreviousMatch(_,E,y,v){const r=E.parseSearchRequest();if(!r)return null;const s=new g(r.wordSeparators,r.regex);return r.regex.multiline?this._doFindPreviousMatchMultiline(_,y,s,v):this._doFindPreviousMatchLineByLine(_,y,s,v)}static _doFindPreviousMatchMultiline(_,E,y,v){const r=this._doFindMatchesMultiline(_,new d.Range(1,1,E.lineNumber,E.column),y,v,10*L);if(r.length>0)return r[r.length-1];const s=_.getLineCount();return E.lineNumber!==s||E.column!==_.getLineMaxColumn(s)?this._doFindPreviousMatchMultiline(_,new i.Position(s,_.getLineMaxColumn(s)),y,v):null}static _doFindPreviousMatchLineByLine(_,E,y,v){const r=_.getLineCount(),s=E.lineNumber,u=_.getLineContent(s).substring(0,E.column-1),p=this._findLastMatchInLine(y,u,s,v);if(p)return p;for(let b=1;b<=r;b++){const C=(r+s-b-1)%r,N=_.getLineContent(C+1),R=this._findLastMatchInLine(y,N,C+1,v);if(R)return R}return null}static _findLastMatchInLine(_,E,y,v){let r=null,s;for(_.reset(0);s=_.next(E);)r=w(new d.Range(y,s.index+1,y,s.index+1+s[0].length),s,v);return r}}n.TextModelSearch=a;function c(S,_,E,y,v){if(y===0)return!0;const r=_.charCodeAt(y-1);if(S.get(r)!==0||r===13||r===10)return!0;if(v>0){const s=_.charCodeAt(y);if(S.get(s)!==0)return!0}return!1}function l(S,_,E,y,v){if(y+v===E)return!0;const r=_.charCodeAt(y+v);if(S.get(r)!==0||r===13||r===10)return!0;if(v>0){const s=_.charCodeAt(y+v-1);if(S.get(s)!==0)return!0}return!1}function f(S,_,E,y,v){return c(S,_,E,y,v)&&l(S,_,E,y,v)}n.isValidMatch=f;class g{constructor(_,E){this._wordSeparators=_,this._searchRegex=E,this._prevMatchStartIndex=-1,this._prevMatchLength=0}reset(_){this._searchRegex.lastIndex=_,this._prevMatchStartIndex=-1,this._prevMatchLength=0}next(_){const E=_.length;let y;do{if(this._prevMatchStartIndex+this._prevMatchLength===E||(y=this._searchRegex.exec(_),!y))return null;const v=y.index,r=y[0].length;if(v===this._prevMatchStartIndex&&r===this._prevMatchLength){if(r===0){M.getNextCodePoint(_,E,this._searchRegex.lastIndex)>65535?this._searchRegex.lastIndex+=2:this._searchRegex.lastIndex+=1;continue}return null}if(this._prevMatchStartIndex=v,this._prevMatchLength=r,!this._wordSeparators||f(this._wordSeparators,_,E,v,r))return y}while(y);return null}}n.Searcher=g}),Y(X[57],J([0,1,2,56,6,12,28]),function(T,n,M,A,i,d,m){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.UnicodeTextModelHighlighter=void 0;class L{static computeUnicodeHighlights(a,c,l){const f=l?l.startLineNumber:1,g=l?l.endLineNumber:a.getLineCount(),S=new o(c),_=S.getCandidateCodePoints();let E;_==="allNonBasicAscii"?E=new RegExp("[^\\t\\n\\r\\x20-\\x7E]","g"):E=new RegExp(`${h(Array.from(_))}`,"g");const y=new A.Searcher(null,E),v=[];let r=!1,s,u=0,p=0,b=0;e:for(let C=f,N=g;C<=N;C++){const R=a.getLineContent(C),D=R.length;y.reset(0);do if(s=y.next(R),s){let k=s.index,U=s.index+s[0].length;if(k>0){const x=R.charCodeAt(k-1);i.isHighSurrogate(x)&&k--}if(U+1<D){const x=R.charCodeAt(U-1);i.isHighSurrogate(x)&&U++}const I=R.substring(k,U);let B=(0,m.getWordAtText)(k+1,m.DEFAULT_WORD_REGEXP,R,0);B&&B.endColumn<=k+1&&(B=null);const z=S.shouldHighlightNonBasicASCII(I,B?B.word:null);if(z!==0){z===3?u++:z===2?p++:z===1?b++:(0,d.assertNever)(z);const x=1e3;if(v.length>=x){r=!0;break e}v.push(new M.Range(C,k+1,C,U+1))}}while(s)}return{ranges:v,hasMore:r,ambiguousCharacterCount:u,invisibleCharacterCount:p,nonBasicAsciiCharacterCount:b}}static computeUnicodeHighlightReason(a,c){const l=new o(c);switch(l.shouldHighlightNonBasicASCII(a,null)){case 0:return null;case 2:return{kind:1};case 3:{const g=a.codePointAt(0),S=l.ambiguousCharacters.getPrimaryConfusable(g),_=i.AmbiguousCharacters.getLocales().filter(E=>!i.AmbiguousCharacters.getInstance(new Set([...c.allowedLocales,E])).isAmbiguous(g));return{kind:0,confusableWith:String.fromCodePoint(S),notAmbiguousInLocales:_}}case 1:return{kind:2}}}}n.UnicodeTextModelHighlighter=L;function h(e,a){return`[${i.escapeRegExpCharacters(e.map(l=>String.fromCodePoint(l)).join(""))}]`}class o{constructor(a){this.options=a,this.allowedCodePoints=new Set(a.allowedCodePoints),this.ambiguousCharacters=i.AmbiguousCharacters.getInstance(new Set(a.allowedLocales))}getCandidateCodePoints(){if(this.options.nonBasicASCII)return"allNonBasicAscii";const a=new Set;if(this.options.invisibleCharacters)for(const c of i.InvisibleCharacters.codePoints)w(String.fromCodePoint(c))||a.add(c);if(this.options.ambiguousCharacters)for(const c of this.ambiguousCharacters.getConfusableCodePoints())a.add(c);for(const c of this.allowedCodePoints)a.delete(c);return a}shouldHighlightNonBasicASCII(a,c){const l=a.codePointAt(0);if(this.allowedCodePoints.has(l))return 0;if(this.options.nonBasicASCII)return 1;let f=!1,g=!1;if(c)for(const S of c){const _=S.codePointAt(0),E=i.isBasicASCII(S);f=f||E,!E&&!this.ambiguousCharacters.isAmbiguous(_)&&!i.InvisibleCharacters.isInvisibleCharacter(_)&&(g=!0)}return!f&&g?0:this.options.invisibleCharacters&&!w(a)&&i.InvisibleCharacters.isInvisibleCharacter(l)?2:this.options.ambiguousCharacters&&this.ambiguousCharacters.isAmbiguous(l)?3:0}}function w(e){return e===" "||e===`
`||e==="	"}}),Y(X[58],J([0,1]),function(T,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.WrappingIndent=n.TrackedRangeStickiness=n.TextEditorCursorStyle=n.TextEditorCursorBlinkingStyle=n.SymbolTag=n.SymbolKind=n.SignatureHelpTriggerKind=n.SelectionDirection=n.ScrollbarVisibility=n.ScrollType=n.RenderMinimap=n.RenderLineNumbersType=n.PositionAffinity=n.OverviewRulerLane=n.OverlayWidgetPositionPreference=n.MouseTargetType=n.MinimapPosition=n.MarkerTag=n.MarkerSeverity=n.KeyCode=n.InlineCompletionTriggerKind=n.InlayHintKind=n.InjectedTextCursorStops=n.IndentAction=n.GlyphMarginLane=n.EndOfLineSequence=n.EndOfLinePreference=n.EditorOption=n.EditorAutoIndentStrategy=n.DocumentHighlightKind=n.DefaultEndOfLine=n.CursorChangeReason=n.ContentWidgetPositionPreference=n.CompletionTriggerKind=n.CompletionItemTag=n.CompletionItemKind=n.CompletionItemInsertTextRule=n.CodeActionTriggerType=n.AccessibilitySupport=void 0;var M;(function(t){t[t.Unknown=0]="Unknown",t[t.Disabled=1]="Disabled",t[t.Enabled=2]="Enabled"})(M||(n.AccessibilitySupport=M={}));var A;(function(t){t[t.Invoke=1]="Invoke",t[t.Auto=2]="Auto"})(A||(n.CodeActionTriggerType=A={}));var i;(function(t){t[t.None=0]="None",t[t.KeepWhitespace=1]="KeepWhitespace",t[t.InsertAsSnippet=4]="InsertAsSnippet"})(i||(n.CompletionItemInsertTextRule=i={}));var d;(function(t){t[t.Method=0]="Method",t[t.Function=1]="Function",t[t.Constructor=2]="Constructor",t[t.Field=3]="Field",t[t.Variable=4]="Variable",t[t.Class=5]="Class",t[t.Struct=6]="Struct",t[t.Interface=7]="Interface",t[t.Module=8]="Module",t[t.Property=9]="Property",t[t.Event=10]="Event",t[t.Operator=11]="Operator",t[t.Unit=12]="Unit",t[t.Value=13]="Value",t[t.Constant=14]="Constant",t[t.Enum=15]="Enum",t[t.EnumMember=16]="EnumMember",t[t.Keyword=17]="Keyword",t[t.Text=18]="Text",t[t.Color=19]="Color",t[t.File=20]="File",t[t.Reference=21]="Reference",t[t.Customcolor=22]="Customcolor",t[t.Folder=23]="Folder",t[t.TypeParameter=24]="TypeParameter",t[t.User=25]="User",t[t.Issue=26]="Issue",t[t.Snippet=27]="Snippet"})(d||(n.CompletionItemKind=d={}));var m;(function(t){t[t.Deprecated=1]="Deprecated"})(m||(n.CompletionItemTag=m={}));var L;(function(t){t[t.Invoke=0]="Invoke",t[t.TriggerCharacter=1]="TriggerCharacter",t[t.TriggerForIncompleteCompletions=2]="TriggerForIncompleteCompletions"})(L||(n.CompletionTriggerKind=L={}));var h;(function(t){t[t.EXACT=0]="EXACT",t[t.ABOVE=1]="ABOVE",t[t.BELOW=2]="BELOW"})(h||(n.ContentWidgetPositionPreference=h={}));var o;(function(t){t[t.NotSet=0]="NotSet",t[t.ContentFlush=1]="ContentFlush",t[t.RecoverFromMarkers=2]="RecoverFromMarkers",t[t.Explicit=3]="Explicit",t[t.Paste=4]="Paste",t[t.Undo=5]="Undo",t[t.Redo=6]="Redo"})(o||(n.CursorChangeReason=o={}));var w;(function(t){t[t.LF=1]="LF",t[t.CRLF=2]="CRLF"})(w||(n.DefaultEndOfLine=w={}));var e;(function(t){t[t.Text=0]="Text",t[t.Read=1]="Read",t[t.Write=2]="Write"})(e||(n.DocumentHighlightKind=e={}));var a;(function(t){t[t.None=0]="None",t[t.Keep=1]="Keep",t[t.Brackets=2]="Brackets",t[t.Advanced=3]="Advanced",t[t.Full=4]="Full"})(a||(n.EditorAutoIndentStrategy=a={}));var c;(function(t){t[t.acceptSuggestionOnCommitCharacter=0]="acceptSuggestionOnCommitCharacter",t[t.acceptSuggestionOnEnter=1]="acceptSuggestionOnEnter",t[t.accessibilitySupport=2]="accessibilitySupport",t[t.accessibilityPageSize=3]="accessibilityPageSize",t[t.ariaLabel=4]="ariaLabel",t[t.ariaRequired=5]="ariaRequired",t[t.autoClosingBrackets=6]="autoClosingBrackets",t[t.autoClosingComments=7]="autoClosingComments",t[t.screenReaderAnnounceInlineSuggestion=8]="screenReaderAnnounceInlineSuggestion",t[t.autoClosingDelete=9]="autoClosingDelete",t[t.autoClosingOvertype=10]="autoClosingOvertype",t[t.autoClosingQuotes=11]="autoClosingQuotes",t[t.autoIndent=12]="autoIndent",t[t.automaticLayout=13]="automaticLayout",t[t.autoSurround=14]="autoSurround",t[t.bracketPairColorization=15]="bracketPairColorization",t[t.guides=16]="guides",t[t.codeLens=17]="codeLens",t[t.codeLensFontFamily=18]="codeLensFontFamily",t[t.codeLensFontSize=19]="codeLensFontSize",t[t.colorDecorators=20]="colorDecorators",t[t.colorDecoratorsLimit=21]="colorDecoratorsLimit",t[t.columnSelection=22]="columnSelection",t[t.comments=23]="comments",t[t.contextmenu=24]="contextmenu",t[t.copyWithSyntaxHighlighting=25]="copyWithSyntaxHighlighting",t[t.cursorBlinking=26]="cursorBlinking",t[t.cursorSmoothCaretAnimation=27]="cursorSmoothCaretAnimation",t[t.cursorStyle=28]="cursorStyle",t[t.cursorSurroundingLines=29]="cursorSurroundingLines",t[t.cursorSurroundingLinesStyle=30]="cursorSurroundingLinesStyle",t[t.cursorWidth=31]="cursorWidth",t[t.disableLayerHinting=32]="disableLayerHinting",t[t.disableMonospaceOptimizations=33]="disableMonospaceOptimizations",t[t.domReadOnly=34]="domReadOnly",t[t.dragAndDrop=35]="dragAndDrop",t[t.dropIntoEditor=36]="dropIntoEditor",t[t.emptySelectionClipboard=37]="emptySelectionClipboard",t[t.experimentalWhitespaceRendering=38]="experimentalWhitespaceRendering",t[t.extraEditorClassName=39]="extraEditorClassName",t[t.fastScrollSensitivity=40]="fastScrollSensitivity",t[t.find=41]="find",t[t.fixedOverflowWidgets=42]="fixedOverflowWidgets",t[t.folding=43]="folding",t[t.foldingStrategy=44]="foldingStrategy",t[t.foldingHighlight=45]="foldingHighlight",t[t.foldingImportsByDefault=46]="foldingImportsByDefault",t[t.foldingMaximumRegions=47]="foldingMaximumRegions",t[t.unfoldOnClickAfterEndOfLine=48]="unfoldOnClickAfterEndOfLine",t[t.fontFamily=49]="fontFamily",t[t.fontInfo=50]="fontInfo",t[t.fontLigatures=51]="fontLigatures",t[t.fontSize=52]="fontSize",t[t.fontWeight=53]="fontWeight",t[t.fontVariations=54]="fontVariations",t[t.formatOnPaste=55]="formatOnPaste",t[t.formatOnType=56]="formatOnType",t[t.glyphMargin=57]="glyphMargin",t[t.gotoLocation=58]="gotoLocation",t[t.hideCursorInOverviewRuler=59]="hideCursorInOverviewRuler",t[t.hover=60]="hover",t[t.inDiffEditor=61]="inDiffEditor",t[t.inlineSuggest=62]="inlineSuggest",t[t.letterSpacing=63]="letterSpacing",t[t.lightbulb=64]="lightbulb",t[t.lineDecorationsWidth=65]="lineDecorationsWidth",t[t.lineHeight=66]="lineHeight",t[t.lineNumbers=67]="lineNumbers",t[t.lineNumbersMinChars=68]="lineNumbersMinChars",t[t.linkedEditing=69]="linkedEditing",t[t.links=70]="links",t[t.matchBrackets=71]="matchBrackets",t[t.minimap=72]="minimap",t[t.mouseStyle=73]="mouseStyle",t[t.mouseWheelScrollSensitivity=74]="mouseWheelScrollSensitivity",t[t.mouseWheelZoom=75]="mouseWheelZoom",t[t.multiCursorMergeOverlapping=76]="multiCursorMergeOverlapping",t[t.multiCursorModifier=77]="multiCursorModifier",t[t.multiCursorPaste=78]="multiCursorPaste",t[t.multiCursorLimit=79]="multiCursorLimit",t[t.occurrencesHighlight=80]="occurrencesHighlight",t[t.overviewRulerBorder=81]="overviewRulerBorder",t[t.overviewRulerLanes=82]="overviewRulerLanes",t[t.padding=83]="padding",t[t.pasteAs=84]="pasteAs",t[t.parameterHints=85]="parameterHints",t[t.peekWidgetDefaultFocus=86]="peekWidgetDefaultFocus",t[t.definitionLinkOpensInPeek=87]="definitionLinkOpensInPeek",t[t.quickSuggestions=88]="quickSuggestions",t[t.quickSuggestionsDelay=89]="quickSuggestionsDelay",t[t.readOnly=90]="readOnly",t[t.readOnlyMessage=91]="readOnlyMessage",t[t.renameOnType=92]="renameOnType",t[t.renderControlCharacters=93]="renderControlCharacters",t[t.renderFinalNewline=94]="renderFinalNewline",t[t.renderLineHighlight=95]="renderLineHighlight",t[t.renderLineHighlightOnlyWhenFocus=96]="renderLineHighlightOnlyWhenFocus",t[t.renderValidationDecorations=97]="renderValidationDecorations",t[t.renderWhitespace=98]="renderWhitespace",t[t.revealHorizontalRightPadding=99]="revealHorizontalRightPadding",t[t.roundedSelection=100]="roundedSelection",t[t.rulers=101]="rulers",t[t.scrollbar=102]="scrollbar",t[t.scrollBeyondLastColumn=103]="scrollBeyondLastColumn",t[t.scrollBeyondLastLine=104]="scrollBeyondLastLine",t[t.scrollPredominantAxis=105]="scrollPredominantAxis",t[t.selectionClipboard=106]="selectionClipboard",t[t.selectionHighlight=107]="selectionHighlight",t[t.selectOnLineNumbers=108]="selectOnLineNumbers",t[t.showFoldingControls=109]="showFoldingControls",t[t.showUnused=110]="showUnused",t[t.snippetSuggestions=111]="snippetSuggestions",t[t.smartSelect=112]="smartSelect",t[t.smoothScrolling=113]="smoothScrolling",t[t.stickyScroll=114]="stickyScroll",t[t.stickyTabStops=115]="stickyTabStops",t[t.stopRenderingLineAfter=116]="stopRenderingLineAfter",t[t.suggest=117]="suggest",t[t.suggestFontSize=118]="suggestFontSize",t[t.suggestLineHeight=119]="suggestLineHeight",t[t.suggestOnTriggerCharacters=120]="suggestOnTriggerCharacters",t[t.suggestSelection=121]="suggestSelection",t[t.tabCompletion=122]="tabCompletion",t[t.tabIndex=123]="tabIndex",t[t.unicodeHighlighting=124]="unicodeHighlighting",t[t.unusualLineTerminators=125]="unusualLineTerminators",t[t.useShadowDOM=126]="useShadowDOM",t[t.useTabStops=127]="useTabStops",t[t.wordBreak=128]="wordBreak",t[t.wordSeparators=129]="wordSeparators",t[t.wordWrap=130]="wordWrap",t[t.wordWrapBreakAfterCharacters=131]="wordWrapBreakAfterCharacters",t[t.wordWrapBreakBeforeCharacters=132]="wordWrapBreakBeforeCharacters",t[t.wordWrapColumn=133]="wordWrapColumn",t[t.wordWrapOverride1=134]="wordWrapOverride1",t[t.wordWrapOverride2=135]="wordWrapOverride2",t[t.wrappingIndent=136]="wrappingIndent",t[t.wrappingStrategy=137]="wrappingStrategy",t[t.showDeprecated=138]="showDeprecated",t[t.inlayHints=139]="inlayHints",t[t.editorClassName=140]="editorClassName",t[t.pixelRatio=141]="pixelRatio",t[t.tabFocusMode=142]="tabFocusMode",t[t.layoutInfo=143]="layoutInfo",t[t.wrappingInfo=144]="wrappingInfo",t[t.defaultColorDecorators=145]="defaultColorDecorators",t[t.colorDecoratorsActivatedOn=146]="colorDecoratorsActivatedOn",t[t.inlineCompletionsAccessibilityVerbose=147]="inlineCompletionsAccessibilityVerbose"})(c||(n.EditorOption=c={}));var l;(function(t){t[t.TextDefined=0]="TextDefined",t[t.LF=1]="LF",t[t.CRLF=2]="CRLF"})(l||(n.EndOfLinePreference=l={}));var f;(function(t){t[t.LF=0]="LF",t[t.CRLF=1]="CRLF"})(f||(n.EndOfLineSequence=f={}));var g;(function(t){t[t.Left=1]="Left",t[t.Right=2]="Right"})(g||(n.GlyphMarginLane=g={}));var S;(function(t){t[t.None=0]="None",t[t.Indent=1]="Indent",t[t.IndentOutdent=2]="IndentOutdent",t[t.Outdent=3]="Outdent"})(S||(n.IndentAction=S={}));var _;(function(t){t[t.Both=0]="Both",t[t.Right=1]="Right",t[t.Left=2]="Left",t[t.None=3]="None"})(_||(n.InjectedTextCursorStops=_={}));var E;(function(t){t[t.Type=1]="Type",t[t.Parameter=2]="Parameter"})(E||(n.InlayHintKind=E={}));var y;(function(t){t[t.Automatic=0]="Automatic",t[t.Explicit=1]="Explicit"})(y||(n.InlineCompletionTriggerKind=y={}));var v;(function(t){t[t.DependsOnKbLayout=-1]="DependsOnKbLayout",t[t.Unknown=0]="Unknown",t[t.Backspace=1]="Backspace",t[t.Tab=2]="Tab",t[t.Enter=3]="Enter",t[t.Shift=4]="Shift",t[t.Ctrl=5]="Ctrl",t[t.Alt=6]="Alt",t[t.PauseBreak=7]="PauseBreak",t[t.CapsLock=8]="CapsLock",t[t.Escape=9]="Escape",t[t.Space=10]="Space",t[t.PageUp=11]="PageUp",t[t.PageDown=12]="PageDown",t[t.End=13]="End",t[t.Home=14]="Home",t[t.LeftArrow=15]="LeftArrow",t[t.UpArrow=16]="UpArrow",t[t.RightArrow=17]="RightArrow",t[t.DownArrow=18]="DownArrow",t[t.Insert=19]="Insert",t[t.Delete=20]="Delete",t[t.Digit0=21]="Digit0",t[t.Digit1=22]="Digit1",t[t.Digit2=23]="Digit2",t[t.Digit3=24]="Digit3",t[t.Digit4=25]="Digit4",t[t.Digit5=26]="Digit5",t[t.Digit6=27]="Digit6",t[t.Digit7=28]="Digit7",t[t.Digit8=29]="Digit8",t[t.Digit9=30]="Digit9",t[t.KeyA=31]="KeyA",t[t.KeyB=32]="KeyB",t[t.KeyC=33]="KeyC",t[t.KeyD=34]="KeyD",t[t.KeyE=35]="KeyE",t[t.KeyF=36]="KeyF",t[t.KeyG=37]="KeyG",t[t.KeyH=38]="KeyH",t[t.KeyI=39]="KeyI",t[t.KeyJ=40]="KeyJ",t[t.KeyK=41]="KeyK",t[t.KeyL=42]="KeyL",t[t.KeyM=43]="KeyM",t[t.KeyN=44]="KeyN",t[t.KeyO=45]="KeyO",t[t.KeyP=46]="KeyP",t[t.KeyQ=47]="KeyQ",t[t.KeyR=48]="KeyR",t[t.KeyS=49]="KeyS",t[t.KeyT=50]="KeyT",t[t.KeyU=51]="KeyU",t[t.KeyV=52]="KeyV",t[t.KeyW=53]="KeyW",t[t.KeyX=54]="KeyX",t[t.KeyY=55]="KeyY",t[t.KeyZ=56]="KeyZ",t[t.Meta=57]="Meta",t[t.ContextMenu=58]="ContextMenu",t[t.F1=59]="F1",t[t.F2=60]="F2",t[t.F3=61]="F3",t[t.F4=62]="F4",t[t.F5=63]="F5",t[t.F6=64]="F6",t[t.F7=65]="F7",t[t.F8=66]="F8",t[t.F9=67]="F9",t[t.F10=68]="F10",t[t.F11=69]="F11",t[t.F12=70]="F12",t[t.F13=71]="F13",t[t.F14=72]="F14",t[t.F15=73]="F15",t[t.F16=74]="F16",t[t.F17=75]="F17",t[t.F18=76]="F18",t[t.F19=77]="F19",t[t.F20=78]="F20",t[t.F21=79]="F21",t[t.F22=80]="F22",t[t.F23=81]="F23",t[t.F24=82]="F24",t[t.NumLock=83]="NumLock",t[t.ScrollLock=84]="ScrollLock",t[t.Semicolon=85]="Semicolon",t[t.Equal=86]="Equal",t[t.Comma=87]="Comma",t[t.Minus=88]="Minus",t[t.Period=89]="Period",t[t.Slash=90]="Slash",t[t.Backquote=91]="Backquote",t[t.BracketLeft=92]="BracketLeft",t[t.Backslash=93]="Backslash",t[t.BracketRight=94]="BracketRight",t[t.Quote=95]="Quote",t[t.OEM_8=96]="OEM_8",t[t.IntlBackslash=97]="IntlBackslash",t[t.Numpad0=98]="Numpad0",t[t.Numpad1=99]="Numpad1",t[t.Numpad2=100]="Numpad2",t[t.Numpad3=101]="Numpad3",t[t.Numpad4=102]="Numpad4",t[t.Numpad5=103]="Numpad5",t[t.Numpad6=104]="Numpad6",t[t.Numpad7=105]="Numpad7",t[t.Numpad8=106]="Numpad8",t[t.Numpad9=107]="Numpad9",t[t.NumpadMultiply=108]="NumpadMultiply",t[t.NumpadAdd=109]="NumpadAdd",t[t.NUMPAD_SEPARATOR=110]="NUMPAD_SEPARATOR",t[t.NumpadSubtract=111]="NumpadSubtract",t[t.NumpadDecimal=112]="NumpadDecimal",t[t.NumpadDivide=113]="NumpadDivide",t[t.KEY_IN_COMPOSITION=114]="KEY_IN_COMPOSITION",t[t.ABNT_C1=115]="ABNT_C1",t[t.ABNT_C2=116]="ABNT_C2",t[t.AudioVolumeMute=117]="AudioVolumeMute",t[t.AudioVolumeUp=118]="AudioVolumeUp",t[t.AudioVolumeDown=119]="AudioVolumeDown",t[t.BrowserSearch=120]="BrowserSearch",t[t.BrowserHome=121]="BrowserHome",t[t.BrowserBack=122]="BrowserBack",t[t.BrowserForward=123]="BrowserForward",t[t.MediaTrackNext=124]="MediaTrackNext",t[t.MediaTrackPrevious=125]="MediaTrackPrevious",t[t.MediaStop=126]="MediaStop",t[t.MediaPlayPause=127]="MediaPlayPause",t[t.LaunchMediaPlayer=128]="LaunchMediaPlayer",t[t.LaunchMail=129]="LaunchMail",t[t.LaunchApp2=130]="LaunchApp2",t[t.Clear=131]="Clear",t[t.MAX_VALUE=132]="MAX_VALUE"})(v||(n.KeyCode=v={}));var r;(function(t){t[t.Hint=1]="Hint",t[t.Info=2]="Info",t[t.Warning=4]="Warning",t[t.Error=8]="Error"})(r||(n.MarkerSeverity=r={}));var s;(function(t){t[t.Unnecessary=1]="Unnecessary",t[t.Deprecated=2]="Deprecated"})(s||(n.MarkerTag=s={}));var u;(function(t){t[t.Inline=1]="Inline",t[t.Gutter=2]="Gutter"})(u||(n.MinimapPosition=u={}));var p;(function(t){t[t.UNKNOWN=0]="UNKNOWN",t[t.TEXTAREA=1]="TEXTAREA",t[t.GUTTER_GLYPH_MARGIN=2]="GUTTER_GLYPH_MARGIN",t[t.GUTTER_LINE_NUMBERS=3]="GUTTER_LINE_NUMBERS",t[t.GUTTER_LINE_DECORATIONS=4]="GUTTER_LINE_DECORATIONS",t[t.GUTTER_VIEW_ZONE=5]="GUTTER_VIEW_ZONE",t[t.CONTENT_TEXT=6]="CONTENT_TEXT",t[t.CONTENT_EMPTY=7]="CONTENT_EMPTY",t[t.CONTENT_VIEW_ZONE=8]="CONTENT_VIEW_ZONE",t[t.CONTENT_WIDGET=9]="CONTENT_WIDGET",t[t.OVERVIEW_RULER=10]="OVERVIEW_RULER",t[t.SCROLLBAR=11]="SCROLLBAR",t[t.OVERLAY_WIDGET=12]="OVERLAY_WIDGET",t[t.OUTSIDE_EDITOR=13]="OUTSIDE_EDITOR"})(p||(n.MouseTargetType=p={}));var b;(function(t){t[t.TOP_RIGHT_CORNER=0]="TOP_RIGHT_CORNER",t[t.BOTTOM_RIGHT_CORNER=1]="BOTTOM_RIGHT_CORNER",t[t.TOP_CENTER=2]="TOP_CENTER"})(b||(n.OverlayWidgetPositionPreference=b={}));var C;(function(t){t[t.Left=1]="Left",t[t.Center=2]="Center",t[t.Right=4]="Right",t[t.Full=7]="Full"})(C||(n.OverviewRulerLane=C={}));var N;(function(t){t[t.Left=0]="Left",t[t.Right=1]="Right",t[t.None=2]="None",t[t.LeftOfInjectedText=3]="LeftOfInjectedText",t[t.RightOfInjectedText=4]="RightOfInjectedText"})(N||(n.PositionAffinity=N={}));var R;(function(t){t[t.Off=0]="Off",t[t.On=1]="On",t[t.Relative=2]="Relative",t[t.Interval=3]="Interval",t[t.Custom=4]="Custom"})(R||(n.RenderLineNumbersType=R={}));var D;(function(t){t[t.None=0]="None",t[t.Text=1]="Text",t[t.Blocks=2]="Blocks"})(D||(n.RenderMinimap=D={}));var k;(function(t){t[t.Smooth=0]="Smooth",t[t.Immediate=1]="Immediate"})(k||(n.ScrollType=k={}));var U;(function(t){t[t.Auto=1]="Auto",t[t.Hidden=2]="Hidden",t[t.Visible=3]="Visible"})(U||(n.ScrollbarVisibility=U={}));var I;(function(t){t[t.LTR=0]="LTR",t[t.RTL=1]="RTL"})(I||(n.SelectionDirection=I={}));var B;(function(t){t[t.Invoke=1]="Invoke",t[t.TriggerCharacter=2]="TriggerCharacter",t[t.ContentChange=3]="ContentChange"})(B||(n.SignatureHelpTriggerKind=B={}));var z;(function(t){t[t.File=0]="File",t[t.Module=1]="Module",t[t.Namespace=2]="Namespace",t[t.Package=3]="Package",t[t.Class=4]="Class",t[t.Method=5]="Method",t[t.Property=6]="Property",t[t.Field=7]="Field",t[t.Constructor=8]="Constructor",t[t.Enum=9]="Enum",t[t.Interface=10]="Interface",t[t.Function=11]="Function",t[t.Variable=12]="Variable",t[t.Constant=13]="Constant",t[t.String=14]="String",t[t.Number=15]="Number",t[t.Boolean=16]="Boolean",t[t.Array=17]="Array",t[t.Object=18]="Object",t[t.Key=19]="Key",t[t.Null=20]="Null",t[t.EnumMember=21]="EnumMember",t[t.Struct=22]="Struct",t[t.Event=23]="Event",t[t.Operator=24]="Operator",t[t.TypeParameter=25]="TypeParameter"})(z||(n.SymbolKind=z={}));var x;(function(t){t[t.Deprecated=1]="Deprecated"})(x||(n.SymbolTag=x={}));var O;(function(t){t[t.Hidden=0]="Hidden",t[t.Blink=1]="Blink",t[t.Smooth=2]="Smooth",t[t.Phase=3]="Phase",t[t.Expand=4]="Expand",t[t.Solid=5]="Solid"})(O||(n.TextEditorCursorBlinkingStyle=O={}));var F;(function(t){t[t.Line=1]="Line",t[t.Block=2]="Block",t[t.Underline=3]="Underline",t[t.LineThin=4]="LineThin",t[t.BlockOutline=5]="BlockOutline",t[t.UnderlineThin=6]="UnderlineThin"})(F||(n.TextEditorCursorStyle=F={}));var H;(function(t){t[t.AlwaysGrowsWhenTypingAtEdges=0]="AlwaysGrowsWhenTypingAtEdges",t[t.NeverGrowsWhenTypingAtEdges=1]="NeverGrowsWhenTypingAtEdges",t[t.GrowsOnlyWhenTypingBefore=2]="GrowsOnlyWhenTypingBefore",t[t.GrowsOnlyWhenTypingAfter=3]="GrowsOnlyWhenTypingAfter"})(H||(n.TrackedRangeStickiness=H={}));var V;(function(t){t[t.None=0]="None",t[t.Same=1]="Same",t[t.Indent=2]="Indent",t[t.DeepIndent=3]="DeepIndent"})(V||(n.WrappingIndent=V={}))}),Y(X[59],J([0,1,9,13]),function(T,n,M,A){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.TokenizationRegistry=void 0;class i{constructor(){this._tokenizationSupports=new Map,this._factories=new Map,this._onDidChange=new M.Emitter,this.onDidChange=this._onDidChange.event,this._colorMap=null}handleChange(L){this._onDidChange.fire({changedLanguages:L,changedColorMap:!1})}register(L,h){return this._tokenizationSupports.set(L,h),this.handleChange([L]),(0,A.toDisposable)(()=>{this._tokenizationSupports.get(L)===h&&(this._tokenizationSupports.delete(L),this.handleChange([L]))})}get(L){return this._tokenizationSupports.get(L)||null}registerFactory(L,h){var o;(o=this._factories.get(L))===null||o===void 0||o.dispose();const w=new d(this,L,h);return this._factories.set(L,w),(0,A.toDisposable)(()=>{const e=this._factories.get(L);!e||e!==w||(this._factories.delete(L),e.dispose())})}getOrCreate(L){return ue(this,void 0,void 0,function*(){const h=this.get(L);if(h)return h;const o=this._factories.get(L);return!o||o.isResolved?null:(yield o.resolve(),this.get(L))})}isResolved(L){if(this.get(L))return!0;const o=this._factories.get(L);return!!(!o||o.isResolved)}setColorMap(L){this._colorMap=L,this._onDidChange.fire({changedLanguages:Array.from(this._tokenizationSupports.keys()),changedColorMap:!0})}getColorMap(){return this._colorMap}getDefaultBackground(){return this._colorMap&&this._colorMap.length>2?this._colorMap[2]:null}}n.TokenizationRegistry=i;class d extends A.Disposable{get isResolved(){return this._isResolved}constructor(L,h,o){super(),this._registry=L,this._languageId=h,this._factory=o,this._isDisposed=!1,this._resolvePromise=null,this._isResolved=!1}dispose(){this._isDisposed=!0,super.dispose()}resolve(){return ue(this,void 0,void 0,function*(){return this._resolvePromise||(this._resolvePromise=this._create()),this._resolvePromise})}_create(){return ue(this,void 0,void 0,function*(){const L=yield this._factory.tokenizationSupport;this._isResolved=!0,L&&!this._isDisposed&&this._register(this._registry.register(this._languageId,L))})}}}),Y(X[60],J([19,61]),function(T,n){return T.create("vs/base/common/platform",n)}),Y(X[17],J([0,1,60]),function(T,n,M){"use strict";var A;Object.defineProperty(n,"__esModule",{value:!0}),n.isAndroid=n.isEdge=n.isSafari=n.isFirefox=n.isChrome=n.isLittleEndian=n.OS=n.setTimeout0=n.setTimeout0IsFaster=n.language=n.userAgent=n.isMobile=n.isIOS=n.isWebWorker=n.isWeb=n.isNative=n.isLinux=n.isMacintosh=n.isWindows=n.globals=n.LANGUAGE_DEFAULT=void 0,n.LANGUAGE_DEFAULT="en";let i=!1,d=!1,m=!1,L=!1,h=!1,o=!1,w=!1,e=!1,a=!1,c=!1,l,f=n.LANGUAGE_DEFAULT,g=n.LANGUAGE_DEFAULT,S,_;n.globals=typeof self=="object"?self:typeof global=="object"?global:{};let E;typeof n.globals.vscode<"u"&&typeof n.globals.vscode.process<"u"?E=n.globals.vscode.process:typeof process<"u"&&(E=process);const y=typeof((A=E?.versions)===null||A===void 0?void 0:A.electron)=="string",v=y&&E?.type==="renderer";if(typeof navigator=="object"&&!v)_=navigator.userAgent,i=_.indexOf("Windows")>=0,d=_.indexOf("Macintosh")>=0,e=(_.indexOf("Macintosh")>=0||_.indexOf("iPad")>=0||_.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,m=_.indexOf("Linux")>=0,c=_?.indexOf("Mobi")>=0,o=!0,l=M.getConfiguredDefaultLocale(M.localize(0,null))||n.LANGUAGE_DEFAULT,f=l,g=navigator.language;else if(typeof E=="object"){i=E.platform==="win32",d=E.platform==="darwin",m=E.platform==="linux",L=m&&!!E.env.SNAP&&!!E.env.SNAP_REVISION,w=y,a=!!E.env.CI||!!E.env.BUILD_ARTIFACTSTAGINGDIRECTORY,l=n.LANGUAGE_DEFAULT,f=n.LANGUAGE_DEFAULT;const b=E.env.VSCODE_NLS_CONFIG;if(b)try{const C=JSON.parse(b),N=C.availableLanguages["*"];l=C.locale,g=C.osLocale,f=N||n.LANGUAGE_DEFAULT,S=C._translationsConfigFile}catch{}h=!0}else console.error("Unable to resolve platform.");let r=0;d?r=1:i?r=3:m&&(r=2),n.isWindows=i,n.isMacintosh=d,n.isLinux=m,n.isNative=h,n.isWeb=o,n.isWebWorker=o&&typeof n.globals.importScripts=="function",n.isIOS=e,n.isMobile=c,n.userAgent=_,n.language=f,n.setTimeout0IsFaster=typeof n.globals.postMessage=="function"&&!n.globals.importScripts,n.setTimeout0=(()=>{if(n.setTimeout0IsFaster){const b=[];n.globals.addEventListener("message",N=>{if(N.data&&N.data.vscodeScheduleAsyncWork)for(let R=0,D=b.length;R<D;R++){const k=b[R];if(k.id===N.data.vscodeScheduleAsyncWork){b.splice(R,1),k.callback();return}}});let C=0;return N=>{const R=++C;b.push({id:R,callback:N}),n.globals.postMessage({vscodeScheduleAsyncWork:R},"*")}}return b=>setTimeout(b)})(),n.OS=d||e?2:i?1:3;let s=!0,u=!1;function p(){if(!u){u=!0;const b=new Uint8Array(2);b[0]=1,b[1]=2,s=new Uint16Array(b.buffer)[0]===(2<<8)+1}return s}n.isLittleEndian=p,n.isChrome=!!(n.userAgent&&n.userAgent.indexOf("Chrome")>=0),n.isFirefox=!!(n.userAgent&&n.userAgent.indexOf("Firefox")>=0),n.isSafari=!!(!n.isChrome&&n.userAgent&&n.userAgent.indexOf("Safari")>=0),n.isEdge=!!(n.userAgent&&n.userAgent.indexOf("Edg/")>=0),n.isAndroid=!!(n.userAgent&&n.userAgent.indexOf("Android")>=0)}),Y(X[62],J([0,1,17]),function(T,n,M){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.platform=n.env=n.cwd=void 0;let A;if(typeof M.globals.vscode<"u"&&typeof M.globals.vscode.process<"u"){const i=M.globals.vscode.process;A={get platform(){return i.platform},get arch(){return i.arch},get env(){return i.env},cwd(){return i.cwd()}}}else typeof process<"u"?A={get platform(){return process.platform},get arch(){return process.arch},get env(){return process.env},cwd(){return process.env.VSCODE_CWD||process.cwd()}}:A={get platform(){return M.isWindows?"win32":M.isMacintosh?"darwin":"linux"},get arch(){},get env(){return{}},cwd(){return"/"}};n.cwd=A.cwd,n.env=A.env,n.platform=A.platform}),Y(X[63],J([0,1,62]),function(T,n,M){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.sep=n.extname=n.basename=n.dirname=n.relative=n.resolve=n.normalize=n.posix=n.win32=void 0;const A=65,i=97,d=90,m=122,L=46,h=47,o=92,w=58,e=63;class a extends Error{constructor(s,u,p){let b;typeof u=="string"&&u.indexOf("not ")===0?(b="must not be",u=u.replace(/^not /,"")):b="must be";const C=s.indexOf(".")!==-1?"property":"argument";let N=`The "${s}" ${C} ${b} of type ${u}`;N+=`. Received type ${typeof p}`,super(N),this.code="ERR_INVALID_ARG_TYPE"}}function c(r,s){if(r===null||typeof r!="object")throw new a(s,"Object",r)}function l(r,s){if(typeof r!="string")throw new a(s,"string",r)}const f=M.platform==="win32";function g(r){return r===h||r===o}function S(r){return r===h}function _(r){return r>=A&&r<=d||r>=i&&r<=m}function E(r,s,u,p){let b="",C=0,N=-1,R=0,D=0;for(let k=0;k<=r.length;++k){if(k<r.length)D=r.charCodeAt(k);else{if(p(D))break;D=h}if(p(D)){if(!(N===k-1||R===1))if(R===2){if(b.length<2||C!==2||b.charCodeAt(b.length-1)!==L||b.charCodeAt(b.length-2)!==L){if(b.length>2){const U=b.lastIndexOf(u);U===-1?(b="",C=0):(b=b.slice(0,U),C=b.length-1-b.lastIndexOf(u)),N=k,R=0;continue}else if(b.length!==0){b="",C=0,N=k,R=0;continue}}s&&(b+=b.length>0?`${u}..`:"..",C=2)}else b.length>0?b+=`${u}${r.slice(N+1,k)}`:b=r.slice(N+1,k),C=k-N-1;N=k,R=0}else D===L&&R!==-1?++R:R=-1}return b}function y(r,s){c(s,"pathObject");const u=s.dir||s.root,p=s.base||`${s.name||""}${s.ext||""}`;return u?u===s.root?`${u}${p}`:`${u}${r}${p}`:p}n.win32={resolve(...r){let s="",u="",p=!1;for(let b=r.length-1;b>=-1;b--){let C;if(b>=0){if(C=r[b],l(C,"path"),C.length===0)continue}else s.length===0?C=M.cwd():(C=M.env[`=${s}`]||M.cwd(),(C===void 0||C.slice(0,2).toLowerCase()!==s.toLowerCase()&&C.charCodeAt(2)===o)&&(C=`${s}\\`));const N=C.length;let R=0,D="",k=!1;const U=C.charCodeAt(0);if(N===1)g(U)&&(R=1,k=!0);else if(g(U))if(k=!0,g(C.charCodeAt(1))){let I=2,B=I;for(;I<N&&!g(C.charCodeAt(I));)I++;if(I<N&&I!==B){const z=C.slice(B,I);for(B=I;I<N&&g(C.charCodeAt(I));)I++;if(I<N&&I!==B){for(B=I;I<N&&!g(C.charCodeAt(I));)I++;(I===N||I!==B)&&(D=`\\\\${z}\\${C.slice(B,I)}`,R=I)}}}else R=1;else _(U)&&C.charCodeAt(1)===w&&(D=C.slice(0,2),R=2,N>2&&g(C.charCodeAt(2))&&(k=!0,R=3));if(D.length>0)if(s.length>0){if(D.toLowerCase()!==s.toLowerCase())continue}else s=D;if(p){if(s.length>0)break}else if(u=`${C.slice(R)}\\${u}`,p=k,k&&s.length>0)break}return u=E(u,!p,"\\",g),p?`${s}\\${u}`:`${s}${u}`||"."},normalize(r){l(r,"path");const s=r.length;if(s===0)return".";let u=0,p,b=!1;const C=r.charCodeAt(0);if(s===1)return S(C)?"\\":r;if(g(C))if(b=!0,g(r.charCodeAt(1))){let R=2,D=R;for(;R<s&&!g(r.charCodeAt(R));)R++;if(R<s&&R!==D){const k=r.slice(D,R);for(D=R;R<s&&g(r.charCodeAt(R));)R++;if(R<s&&R!==D){for(D=R;R<s&&!g(r.charCodeAt(R));)R++;if(R===s)return`\\\\${k}\\${r.slice(D)}\\`;R!==D&&(p=`\\\\${k}\\${r.slice(D,R)}`,u=R)}}}else u=1;else _(C)&&r.charCodeAt(1)===w&&(p=r.slice(0,2),u=2,s>2&&g(r.charCodeAt(2))&&(b=!0,u=3));let N=u<s?E(r.slice(u),!b,"\\",g):"";return N.length===0&&!b&&(N="."),N.length>0&&g(r.charCodeAt(s-1))&&(N+="\\"),p===void 0?b?`\\${N}`:N:b?`${p}\\${N}`:`${p}${N}`},isAbsolute(r){l(r,"path");const s=r.length;if(s===0)return!1;const u=r.charCodeAt(0);return g(u)||s>2&&_(u)&&r.charCodeAt(1)===w&&g(r.charCodeAt(2))},join(...r){if(r.length===0)return".";let s,u;for(let C=0;C<r.length;++C){const N=r[C];l(N,"path"),N.length>0&&(s===void 0?s=u=N:s+=`\\${N}`)}if(s===void 0)return".";let p=!0,b=0;if(typeof u=="string"&&g(u.charCodeAt(0))){++b;const C=u.length;C>1&&g(u.charCodeAt(1))&&(++b,C>2&&(g(u.charCodeAt(2))?++b:p=!1))}if(p){for(;b<s.length&&g(s.charCodeAt(b));)b++;b>=2&&(s=`\\${s.slice(b)}`)}return n.win32.normalize(s)},relative(r,s){if(l(r,"from"),l(s,"to"),r===s)return"";const u=n.win32.resolve(r),p=n.win32.resolve(s);if(u===p||(r=u.toLowerCase(),s=p.toLowerCase(),r===s))return"";let b=0;for(;b<r.length&&r.charCodeAt(b)===o;)b++;let C=r.length;for(;C-1>b&&r.charCodeAt(C-1)===o;)C--;const N=C-b;let R=0;for(;R<s.length&&s.charCodeAt(R)===o;)R++;let D=s.length;for(;D-1>R&&s.charCodeAt(D-1)===o;)D--;const k=D-R,U=N<k?N:k;let I=-1,B=0;for(;B<U;B++){const x=r.charCodeAt(b+B);if(x!==s.charCodeAt(R+B))break;x===o&&(I=B)}if(B!==U){if(I===-1)return p}else{if(k>U){if(s.charCodeAt(R+B)===o)return p.slice(R+B+1);if(B===2)return p.slice(R+B)}N>U&&(r.charCodeAt(b+B)===o?I=B:B===2&&(I=3)),I===-1&&(I=0)}let z="";for(B=b+I+1;B<=C;++B)(B===C||r.charCodeAt(B)===o)&&(z+=z.length===0?"..":"\\..");return R+=I,z.length>0?`${z}${p.slice(R,D)}`:(p.charCodeAt(R)===o&&++R,p.slice(R,D))},toNamespacedPath(r){if(typeof r!="string"||r.length===0)return r;const s=n.win32.resolve(r);if(s.length<=2)return r;if(s.charCodeAt(0)===o){if(s.charCodeAt(1)===o){const u=s.charCodeAt(2);if(u!==e&&u!==L)return`\\\\?\\UNC\\${s.slice(2)}`}}else if(_(s.charCodeAt(0))&&s.charCodeAt(1)===w&&s.charCodeAt(2)===o)return`\\\\?\\${s}`;return r},dirname(r){l(r,"path");const s=r.length;if(s===0)return".";let u=-1,p=0;const b=r.charCodeAt(0);if(s===1)return g(b)?r:".";if(g(b)){if(u=p=1,g(r.charCodeAt(1))){let R=2,D=R;for(;R<s&&!g(r.charCodeAt(R));)R++;if(R<s&&R!==D){for(D=R;R<s&&g(r.charCodeAt(R));)R++;if(R<s&&R!==D){for(D=R;R<s&&!g(r.charCodeAt(R));)R++;if(R===s)return r;R!==D&&(u=p=R+1)}}}}else _(b)&&r.charCodeAt(1)===w&&(u=s>2&&g(r.charCodeAt(2))?3:2,p=u);let C=-1,N=!0;for(let R=s-1;R>=p;--R)if(g(r.charCodeAt(R))){if(!N){C=R;break}}else N=!1;if(C===-1){if(u===-1)return".";C=u}return r.slice(0,C)},basename(r,s){s!==void 0&&l(s,"ext"),l(r,"path");let u=0,p=-1,b=!0,C;if(r.length>=2&&_(r.charCodeAt(0))&&r.charCodeAt(1)===w&&(u=2),s!==void 0&&s.length>0&&s.length<=r.length){if(s===r)return"";let N=s.length-1,R=-1;for(C=r.length-1;C>=u;--C){const D=r.charCodeAt(C);if(g(D)){if(!b){u=C+1;break}}else R===-1&&(b=!1,R=C+1),N>=0&&(D===s.charCodeAt(N)?--N===-1&&(p=C):(N=-1,p=R))}return u===p?p=R:p===-1&&(p=r.length),r.slice(u,p)}for(C=r.length-1;C>=u;--C)if(g(r.charCodeAt(C))){if(!b){u=C+1;break}}else p===-1&&(b=!1,p=C+1);return p===-1?"":r.slice(u,p)},extname(r){l(r,"path");let s=0,u=-1,p=0,b=-1,C=!0,N=0;r.length>=2&&r.charCodeAt(1)===w&&_(r.charCodeAt(0))&&(s=p=2);for(let R=r.length-1;R>=s;--R){const D=r.charCodeAt(R);if(g(D)){if(!C){p=R+1;break}continue}b===-1&&(C=!1,b=R+1),D===L?u===-1?u=R:N!==1&&(N=1):u!==-1&&(N=-1)}return u===-1||b===-1||N===0||N===1&&u===b-1&&u===p+1?"":r.slice(u,b)},format:y.bind(null,"\\"),parse(r){l(r,"path");const s={root:"",dir:"",base:"",ext:"",name:""};if(r.length===0)return s;const u=r.length;let p=0,b=r.charCodeAt(0);if(u===1)return g(b)?(s.root=s.dir=r,s):(s.base=s.name=r,s);if(g(b)){if(p=1,g(r.charCodeAt(1))){let I=2,B=I;for(;I<u&&!g(r.charCodeAt(I));)I++;if(I<u&&I!==B){for(B=I;I<u&&g(r.charCodeAt(I));)I++;if(I<u&&I!==B){for(B=I;I<u&&!g(r.charCodeAt(I));)I++;I===u?p=I:I!==B&&(p=I+1)}}}}else if(_(b)&&r.charCodeAt(1)===w){if(u<=2)return s.root=s.dir=r,s;if(p=2,g(r.charCodeAt(2))){if(u===3)return s.root=s.dir=r,s;p=3}}p>0&&(s.root=r.slice(0,p));let C=-1,N=p,R=-1,D=!0,k=r.length-1,U=0;for(;k>=p;--k){if(b=r.charCodeAt(k),g(b)){if(!D){N=k+1;break}continue}R===-1&&(D=!1,R=k+1),b===L?C===-1?C=k:U!==1&&(U=1):C!==-1&&(U=-1)}return R!==-1&&(C===-1||U===0||U===1&&C===R-1&&C===N+1?s.base=s.name=r.slice(N,R):(s.name=r.slice(N,C),s.base=r.slice(N,R),s.ext=r.slice(C,R))),N>0&&N!==p?s.dir=r.slice(0,N-1):s.dir=s.root,s},sep:"\\",delimiter:";",win32:null,posix:null};const v=(()=>{if(f){const r=/\\/g;return()=>{const s=M.cwd().replace(r,"/");return s.slice(s.indexOf("/"))}}return()=>M.cwd()})();n.posix={resolve(...r){let s="",u=!1;for(let p=r.length-1;p>=-1&&!u;p--){const b=p>=0?r[p]:v();l(b,"path"),b.length!==0&&(s=`${b}/${s}`,u=b.charCodeAt(0)===h)}return s=E(s,!u,"/",S),u?`/${s}`:s.length>0?s:"."},normalize(r){if(l(r,"path"),r.length===0)return".";const s=r.charCodeAt(0)===h,u=r.charCodeAt(r.length-1)===h;return r=E(r,!s,"/",S),r.length===0?s?"/":u?"./":".":(u&&(r+="/"),s?`/${r}`:r)},isAbsolute(r){return l(r,"path"),r.length>0&&r.charCodeAt(0)===h},join(...r){if(r.length===0)return".";let s;for(let u=0;u<r.length;++u){const p=r[u];l(p,"path"),p.length>0&&(s===void 0?s=p:s+=`/${p}`)}return s===void 0?".":n.posix.normalize(s)},relative(r,s){if(l(r,"from"),l(s,"to"),r===s||(r=n.posix.resolve(r),s=n.posix.resolve(s),r===s))return"";const u=1,p=r.length,b=p-u,C=1,N=s.length-C,R=b<N?b:N;let D=-1,k=0;for(;k<R;k++){const I=r.charCodeAt(u+k);if(I!==s.charCodeAt(C+k))break;I===h&&(D=k)}if(k===R)if(N>R){if(s.charCodeAt(C+k)===h)return s.slice(C+k+1);if(k===0)return s.slice(C+k)}else b>R&&(r.charCodeAt(u+k)===h?D=k:k===0&&(D=0));let U="";for(k=u+D+1;k<=p;++k)(k===p||r.charCodeAt(k)===h)&&(U+=U.length===0?"..":"/..");return`${U}${s.slice(C+D)}`},toNamespacedPath(r){return r},dirname(r){if(l(r,"path"),r.length===0)return".";const s=r.charCodeAt(0)===h;let u=-1,p=!0;for(let b=r.length-1;b>=1;--b)if(r.charCodeAt(b)===h){if(!p){u=b;break}}else p=!1;return u===-1?s?"/":".":s&&u===1?"//":r.slice(0,u)},basename(r,s){s!==void 0&&l(s,"ext"),l(r,"path");let u=0,p=-1,b=!0,C;if(s!==void 0&&s.length>0&&s.length<=r.length){if(s===r)return"";let N=s.length-1,R=-1;for(C=r.length-1;C>=0;--C){const D=r.charCodeAt(C);if(D===h){if(!b){u=C+1;break}}else R===-1&&(b=!1,R=C+1),N>=0&&(D===s.charCodeAt(N)?--N===-1&&(p=C):(N=-1,p=R))}return u===p?p=R:p===-1&&(p=r.length),r.slice(u,p)}for(C=r.length-1;C>=0;--C)if(r.charCodeAt(C)===h){if(!b){u=C+1;break}}else p===-1&&(b=!1,p=C+1);return p===-1?"":r.slice(u,p)},extname(r){l(r,"path");let s=-1,u=0,p=-1,b=!0,C=0;for(let N=r.length-1;N>=0;--N){const R=r.charCodeAt(N);if(R===h){if(!b){u=N+1;break}continue}p===-1&&(b=!1,p=N+1),R===L?s===-1?s=N:C!==1&&(C=1):s!==-1&&(C=-1)}return s===-1||p===-1||C===0||C===1&&s===p-1&&s===u+1?"":r.slice(s,p)},format:y.bind(null,"/"),parse(r){l(r,"path");const s={root:"",dir:"",base:"",ext:"",name:""};if(r.length===0)return s;const u=r.charCodeAt(0)===h;let p;u?(s.root="/",p=1):p=0;let b=-1,C=0,N=-1,R=!0,D=r.length-1,k=0;for(;D>=p;--D){const U=r.charCodeAt(D);if(U===h){if(!R){C=D+1;break}continue}N===-1&&(R=!1,N=D+1),U===L?b===-1?b=D:k!==1&&(k=1):b!==-1&&(k=-1)}if(N!==-1){const U=C===0&&u?1:C;b===-1||k===0||k===1&&b===N-1&&b===C+1?s.base=s.name=r.slice(U,N):(s.name=r.slice(U,b),s.base=r.slice(U,N),s.ext=r.slice(b,N))}return C>0?s.dir=r.slice(0,C-1):u&&(s.dir="/"),s},sep:"/",delimiter:":",win32:null,posix:null},n.posix.win32=n.win32.win32=n.win32,n.posix.posix=n.win32.posix=n.posix,n.normalize=f?n.win32.normalize:n.posix.normalize,n.resolve=f?n.win32.resolve:n.posix.resolve,n.relative=f?n.win32.relative:n.posix.relative,n.dirname=f?n.win32.dirname:n.posix.dirname,n.basename=f?n.win32.basename:n.posix.basename,n.extname=f?n.win32.extname:n.posix.extname,n.sep=f?n.win32.sep:n.posix.sep}),Y(X[18],J([0,1,63,17]),function(T,n,M,A){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.uriToFsPath=n.URI=void 0;const i=/^\w[\w\d+.-]*$/,d=/^\//,m=/^\/\//;function L(u,p){if(!u.scheme&&p)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${u.authority}", path: "${u.path}", query: "${u.query}", fragment: "${u.fragment}"}`);if(u.scheme&&!i.test(u.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(u.path){if(u.authority){if(!d.test(u.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(m.test(u.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}function h(u,p){return!u&&!p?"file":u}function o(u,p){switch(u){case"https":case"http":case"file":p?p[0]!==e&&(p=e+p):p=e;break}return p}const w="",e="/",a=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class c{static isUri(p){return p instanceof c?!0:p?typeof p.authority=="string"&&typeof p.fragment=="string"&&typeof p.path=="string"&&typeof p.query=="string"&&typeof p.scheme=="string"&&typeof p.fsPath=="string"&&typeof p.with=="function"&&typeof p.toString=="function":!1}constructor(p,b,C,N,R,D=!1){typeof p=="object"?(this.scheme=p.scheme||w,this.authority=p.authority||w,this.path=p.path||w,this.query=p.query||w,this.fragment=p.fragment||w):(this.scheme=h(p,D),this.authority=b||w,this.path=o(this.scheme,C||w),this.query=N||w,this.fragment=R||w,L(this,D))}get fsPath(){return E(this,!1)}with(p){if(!p)return this;let{scheme:b,authority:C,path:N,query:R,fragment:D}=p;return b===void 0?b=this.scheme:b===null&&(b=w),C===void 0?C=this.authority:C===null&&(C=w),N===void 0?N=this.path:N===null&&(N=w),R===void 0?R=this.query:R===null&&(R=w),D===void 0?D=this.fragment:D===null&&(D=w),b===this.scheme&&C===this.authority&&N===this.path&&R===this.query&&D===this.fragment?this:new f(b,C,N,R,D)}static parse(p,b=!1){const C=a.exec(p);return C?new f(C[2]||w,s(C[4]||w),s(C[5]||w),s(C[7]||w),s(C[9]||w),b):new f(w,w,w,w,w)}static file(p){let b=w;if(A.isWindows&&(p=p.replace(/\\/g,e)),p[0]===e&&p[1]===e){const C=p.indexOf(e,2);C===-1?(b=p.substring(2),p=e):(b=p.substring(2,C),p=p.substring(C)||e)}return new f("file",b,p,w,w)}static from(p,b){return new f(p.scheme,p.authority,p.path,p.query,p.fragment,b)}static joinPath(p,...b){if(!p.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let C;return A.isWindows&&p.scheme==="file"?C=c.file(M.win32.join(E(p,!0),...b)).path:C=M.posix.join(p.path,...b),p.with({path:C})}toString(p=!1){return y(this,p)}toJSON(){return this}static revive(p){var b,C;if(p){if(p instanceof c)return p;{const N=new f(p);return N._formatted=(b=p.external)!==null&&b!==void 0?b:null,N._fsPath=p._sep===l&&(C=p.fsPath)!==null&&C!==void 0?C:null,N}}else return p}}n.URI=c;const l=A.isWindows?1:void 0;class f extends c{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=E(this,!1)),this._fsPath}toString(p=!1){return p?y(this,!0):(this._formatted||(this._formatted=y(this,!1)),this._formatted)}toJSON(){const p={$mid:1};return this._fsPath&&(p.fsPath=this._fsPath,p._sep=l),this._formatted&&(p.external=this._formatted),this.path&&(p.path=this.path),this.scheme&&(p.scheme=this.scheme),this.authority&&(p.authority=this.authority),this.query&&(p.query=this.query),this.fragment&&(p.fragment=this.fragment),p}}const g={[58]:"%3A",[47]:"%2F",[63]:"%3F",[35]:"%23",[91]:"%5B",[93]:"%5D",[64]:"%40",[33]:"%21",[36]:"%24",[38]:"%26",[39]:"%27",[40]:"%28",[41]:"%29",[42]:"%2A",[43]:"%2B",[44]:"%2C",[59]:"%3B",[61]:"%3D",[32]:"%20"};function S(u,p,b){let C,N=-1;for(let R=0;R<u.length;R++){const D=u.charCodeAt(R);if(D>=97&&D<=122||D>=65&&D<=90||D>=48&&D<=57||D===45||D===46||D===95||D===126||p&&D===47||b&&D===91||b&&D===93||b&&D===58)N!==-1&&(C+=encodeURIComponent(u.substring(N,R)),N=-1),C!==void 0&&(C+=u.charAt(R));else{C===void 0&&(C=u.substr(0,R));const k=g[D];k!==void 0?(N!==-1&&(C+=encodeURIComponent(u.substring(N,R)),N=-1),C+=k):N===-1&&(N=R)}}return N!==-1&&(C+=encodeURIComponent(u.substring(N))),C!==void 0?C:u}function _(u){let p;for(let b=0;b<u.length;b++){const C=u.charCodeAt(b);C===35||C===63?(p===void 0&&(p=u.substr(0,b)),p+=g[C]):p!==void 0&&(p+=u[b])}return p!==void 0?p:u}function E(u,p){let b;return u.authority&&u.path.length>1&&u.scheme==="file"?b=`//${u.authority}${u.path}`:u.path.charCodeAt(0)===47&&(u.path.charCodeAt(1)>=65&&u.path.charCodeAt(1)<=90||u.path.charCodeAt(1)>=97&&u.path.charCodeAt(1)<=122)&&u.path.charCodeAt(2)===58?p?b=u.path.substr(1):b=u.path[1].toLowerCase()+u.path.substr(2):b=u.path,A.isWindows&&(b=b.replace(/\//g,"\\")),b}n.uriToFsPath=E;function y(u,p){const b=p?_:S;let C="",{scheme:N,authority:R,path:D,query:k,fragment:U}=u;if(N&&(C+=N,C+=":"),(R||N==="file")&&(C+=e,C+=e),R){let I=R.indexOf("@");if(I!==-1){const B=R.substr(0,I);R=R.substr(I+1),I=B.lastIndexOf(":"),I===-1?C+=b(B,!1,!1):(C+=b(B.substr(0,I),!1,!1),C+=":",C+=b(B.substr(I+1),!1,!0)),C+="@"}R=R.toLowerCase(),I=R.lastIndexOf(":"),I===-1?C+=b(R,!1,!0):(C+=b(R.substr(0,I),!1,!0),C+=R.substr(I))}if(D){if(D.length>=3&&D.charCodeAt(0)===47&&D.charCodeAt(2)===58){const I=D.charCodeAt(1);I>=65&&I<=90&&(D=`/${String.fromCharCode(I+32)}:${D.substr(3)}`)}else if(D.length>=2&&D.charCodeAt(1)===58){const I=D.charCodeAt(0);I>=65&&I<=90&&(D=`${String.fromCharCode(I+32)}:${D.substr(2)}`)}C+=b(D,!0,!1)}return k&&(C+="?",C+=b(k,!1,!1)),U&&(C+="#",C+=p?U:S(U,!1,!1)),C}function v(u){try{return decodeURIComponent(u)}catch{return u.length>3?u.substr(0,3)+v(u.substr(3)):u}}const r=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function s(u){return u.match(r)?u.replace(r,p=>v(p)):u}}),Y(X[67],J([0,1,5,9,13,14,17,6]),function(T,n,M,A,i,d,m,L){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.create=n.SimpleWorkerServer=n.SimpleWorkerClient=n.logOnceWebWorkerWarning=void 0;const h="$initialize";let o=!1;function w(s){m.isWeb&&(o||(o=!0,console.warn("Could not create web worker(s). Falling back to loading web worker code in main thread, which might cause UI freezes. Please see https://github.com/microsoft/monaco-editor#faq")),console.warn(s.message))}n.logOnceWebWorkerWarning=w;class e{constructor(u,p,b,C){this.vsWorker=u,this.req=p,this.method=b,this.args=C,this.type=0}}class a{constructor(u,p,b,C){this.vsWorker=u,this.seq=p,this.res=b,this.err=C,this.type=1}}class c{constructor(u,p,b,C){this.vsWorker=u,this.req=p,this.eventName=b,this.arg=C,this.type=2}}class l{constructor(u,p,b){this.vsWorker=u,this.req=p,this.event=b,this.type=3}}class f{constructor(u,p){this.vsWorker=u,this.req=p,this.type=4}}class g{constructor(u){this._workerId=-1,this._handler=u,this._lastSentReq=0,this._pendingReplies=Object.create(null),this._pendingEmitters=new Map,this._pendingEvents=new Map}setWorkerId(u){this._workerId=u}sendMessage(u,p){const b=String(++this._lastSentReq);return new Promise((C,N)=>{this._pendingReplies[b]={resolve:C,reject:N},this._send(new e(this._workerId,b,u,p))})}listen(u,p){let b=null;const C=new A.Emitter({onWillAddFirstListener:()=>{b=String(++this._lastSentReq),this._pendingEmitters.set(b,C),this._send(new c(this._workerId,b,u,p))},onDidRemoveLastListener:()=>{this._pendingEmitters.delete(b),this._send(new f(this._workerId,b)),b=null}});return C.event}handleMessage(u){!u||!u.vsWorker||this._workerId!==-1&&u.vsWorker!==this._workerId||this._handleMessage(u)}_handleMessage(u){switch(u.type){case 1:return this._handleReplyMessage(u);case 0:return this._handleRequestMessage(u);case 2:return this._handleSubscribeEventMessage(u);case 3:return this._handleEventMessage(u);case 4:return this._handleUnsubscribeEventMessage(u)}}_handleReplyMessage(u){if(!this._pendingReplies[u.seq]){console.warn("Got reply to unknown seq");return}const p=this._pendingReplies[u.seq];if(delete this._pendingReplies[u.seq],u.err){let b=u.err;u.err.$isError&&(b=new Error,b.name=u.err.name,b.message=u.err.message,b.stack=u.err.stack),p.reject(b);return}p.resolve(u.res)}_handleRequestMessage(u){const p=u.req;this._handler.handleMessage(u.method,u.args).then(C=>{this._send(new a(this._workerId,p,C,void 0))},C=>{C.detail instanceof Error&&(C.detail=(0,M.transformErrorForSerialization)(C.detail)),this._send(new a(this._workerId,p,void 0,(0,M.transformErrorForSerialization)(C)))})}_handleSubscribeEventMessage(u){const p=u.req,b=this._handler.handleEvent(u.eventName,u.arg)(C=>{this._send(new l(this._workerId,p,C))});this._pendingEvents.set(p,b)}_handleEventMessage(u){if(!this._pendingEmitters.has(u.req)){console.warn("Got event for unknown req");return}this._pendingEmitters.get(u.req).fire(u.event)}_handleUnsubscribeEventMessage(u){if(!this._pendingEvents.has(u.req)){console.warn("Got unsubscribe for unknown req");return}this._pendingEvents.get(u.req).dispose(),this._pendingEvents.delete(u.req)}_send(u){const p=[];if(u.type===0)for(let b=0;b<u.args.length;b++)u.args[b]instanceof ArrayBuffer&&p.push(u.args[b]);else u.type===1&&u.res instanceof ArrayBuffer&&p.push(u.res);this._handler.sendMessage(u,p)}}class S extends i.Disposable{constructor(u,p,b){super();let C=null;this._worker=this._register(u.create("vs/base/common/worker/simpleWorker",I=>{this._protocol.handleMessage(I)},I=>{C?.(I)})),this._protocol=new g({sendMessage:(I,B)=>{this._worker.postMessage(I,B)},handleMessage:(I,B)=>{if(typeof b[I]!="function")return Promise.reject(new Error("Missing method "+I+" on main thread host."));try{return Promise.resolve(b[I].apply(b,B))}catch(z){return Promise.reject(z)}},handleEvent:(I,B)=>{if(E(I)){const z=b[I].call(b,B);if(typeof z!="function")throw new Error(`Missing dynamic event ${I} on main thread host.`);return z}if(_(I)){const z=b[I];if(typeof z!="function")throw new Error(`Missing event ${I} on main thread host.`);return z}throw new Error(`Malformed event name ${I}`)}}),this._protocol.setWorkerId(this._worker.getId());let N=null;const R=globalThis.require;typeof R<"u"&&typeof R.getConfig=="function"?N=R.getConfig():typeof globalThis.requirejs<"u"&&(N=globalThis.requirejs.s.contexts._.config);const D=(0,d.getAllMethodNames)(b);this._onModuleLoaded=this._protocol.sendMessage(h,[this._worker.getId(),JSON.parse(JSON.stringify(N)),p,D]);const k=(I,B)=>this._request(I,B),U=(I,B)=>this._protocol.listen(I,B);this._lazyProxy=new Promise((I,B)=>{C=B,this._onModuleLoaded.then(z=>{I(y(z,k,U))},z=>{B(z),this._onError("Worker failed to load "+p,z)})})}getProxyObject(){return this._lazyProxy}_request(u,p){return new Promise((b,C)=>{this._onModuleLoaded.then(()=>{this._protocol.sendMessage(u,p).then(b,C)},C)})}_onError(u,p){console.error(u),console.info(p)}}n.SimpleWorkerClient=S;function _(s){return s[0]==="o"&&s[1]==="n"&&L.isUpperAsciiLetter(s.charCodeAt(2))}function E(s){return/^onDynamic/.test(s)&&L.isUpperAsciiLetter(s.charCodeAt(9))}function y(s,u,p){const b=R=>function(){const D=Array.prototype.slice.call(arguments,0);return u(R,D)},C=R=>function(D){return p(R,D)},N={};for(const R of s){if(E(R)){N[R]=C(R);continue}if(_(R)){N[R]=p(R,void 0);continue}N[R]=b(R)}return N}class v{constructor(u,p){this._requestHandlerFactory=p,this._requestHandler=null,this._protocol=new g({sendMessage:(b,C)=>{u(b,C)},handleMessage:(b,C)=>this._handleMessage(b,C),handleEvent:(b,C)=>this._handleEvent(b,C)})}onmessage(u){this._protocol.handleMessage(u)}_handleMessage(u,p){if(u===h)return this.initialize(p[0],p[1],p[2],p[3]);if(!this._requestHandler||typeof this._requestHandler[u]!="function")return Promise.reject(new Error("Missing requestHandler or method: "+u));try{return Promise.resolve(this._requestHandler[u].apply(this._requestHandler,p))}catch(b){return Promise.reject(b)}}_handleEvent(u,p){if(!this._requestHandler)throw new Error("Missing requestHandler");if(E(u)){const b=this._requestHandler[u].call(this._requestHandler,p);if(typeof b!="function")throw new Error(`Missing dynamic event ${u} on request handler.`);return b}if(_(u)){const b=this._requestHandler[u];if(typeof b!="function")throw new Error(`Missing event ${u} on request handler.`);return b}throw new Error(`Malformed event name ${u}`)}initialize(u,p,b,C){this._protocol.setWorkerId(u);const D=y(C,(k,U)=>this._protocol.sendMessage(k,U),(k,U)=>this._protocol.listen(k,U));return this._requestHandlerFactory?(this._requestHandler=this._requestHandlerFactory(D),Promise.resolve((0,d.getAllMethodNames)(this._requestHandler))):(p&&(typeof p.baseUrl<"u"&&delete p.baseUrl,typeof p.paths<"u"&&typeof p.paths.vs<"u"&&delete p.paths.vs,typeof p.trustedTypesPolicy!==void 0&&delete p.trustedTypesPolicy,p.catchError=!0,globalThis.require.config(p)),new Promise((k,U)=>{(globalThis.require||T)([b],B=>{if(this._requestHandler=B.create(D),!this._requestHandler){U(new Error("No RequestHandler!"));return}k((0,d.getAllMethodNames)(this._requestHandler))},U)}))}}n.SimpleWorkerServer=v;function r(s){return new v(s,null)}n.create=r}),Y(X[64],J([19,61]),function(T,n){return T.create("vs/editor/common/languages",n)}),Y(X[65],J([0,1,40,18,2,59,64]),function(T,n,M,A,i,d,m){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.TokenizationRegistry=n.LazyTokenizationSupport=n.InlayHintKind=n.Command=n.FoldingRangeKind=n.TextEdit=n.SymbolKinds=n.getAriaLabelForSymbol=n.symbolKindNames=n.isLocationLink=n.DocumentHighlightKind=n.SignatureHelpTriggerKind=n.SelectedSuggestionInfo=n.InlineCompletionTriggerKind=n.CompletionItemKinds=n.EncodedTokenizationResult=n.TokenizationResult=n.Token=void 0;class L{constructor(u,p,b){this.offset=u,this.type=p,this.language=b,this._tokenBrand=void 0}toString(){return"("+this.offset+", "+this.type+")"}}n.Token=L;class h{constructor(u,p){this.tokens=u,this.endState=p,this._tokenizationResultBrand=void 0}}n.TokenizationResult=h;class o{constructor(u,p){this.tokens=u,this.endState=p,this._encodedTokenizationResultBrand=void 0}}n.EncodedTokenizationResult=o;var w;(function(s){const u=new Map;u.set(0,M.Codicon.symbolMethod),u.set(1,M.Codicon.symbolFunction),u.set(2,M.Codicon.symbolConstructor),u.set(3,M.Codicon.symbolField),u.set(4,M.Codicon.symbolVariable),u.set(5,M.Codicon.symbolClass),u.set(6,M.Codicon.symbolStruct),u.set(7,M.Codicon.symbolInterface),u.set(8,M.Codicon.symbolModule),u.set(9,M.Codicon.symbolProperty),u.set(10,M.Codicon.symbolEvent),u.set(11,M.Codicon.symbolOperator),u.set(12,M.Codicon.symbolUnit),u.set(13,M.Codicon.symbolValue),u.set(15,M.Codicon.symbolEnum),u.set(14,M.Codicon.symbolConstant),u.set(15,M.Codicon.symbolEnum),u.set(16,M.Codicon.symbolEnumMember),u.set(17,M.Codicon.symbolKeyword),u.set(27,M.Codicon.symbolSnippet),u.set(18,M.Codicon.symbolText),u.set(19,M.Codicon.symbolColor),u.set(20,M.Codicon.symbolFile),u.set(21,M.Codicon.symbolReference),u.set(22,M.Codicon.symbolCustomColor),u.set(23,M.Codicon.symbolFolder),u.set(24,M.Codicon.symbolTypeParameter),u.set(25,M.Codicon.account),u.set(26,M.Codicon.issues);function p(N){let R=u.get(N);return R||(console.info("No codicon found for CompletionItemKind "+N),R=M.Codicon.symbolProperty),R}s.toIcon=p;const b=new Map;b.set("method",0),b.set("function",1),b.set("constructor",2),b.set("field",3),b.set("variable",4),b.set("class",5),b.set("struct",6),b.set("interface",7),b.set("module",8),b.set("property",9),b.set("event",10),b.set("operator",11),b.set("unit",12),b.set("value",13),b.set("constant",14),b.set("enum",15),b.set("enum-member",16),b.set("enumMember",16),b.set("keyword",17),b.set("snippet",27),b.set("text",18),b.set("color",19),b.set("file",20),b.set("reference",21),b.set("customcolor",22),b.set("folder",23),b.set("type-parameter",24),b.set("typeParameter",24),b.set("account",25),b.set("issue",26);function C(N,R){let D=b.get(N);return typeof D>"u"&&!R&&(D=9),D}s.fromString=C})(w||(n.CompletionItemKinds=w={}));var e;(function(s){s[s.Automatic=0]="Automatic",s[s.Explicit=1]="Explicit"})(e||(n.InlineCompletionTriggerKind=e={}));class a{constructor(u,p,b,C){this.range=u,this.text=p,this.completionKind=b,this.isSnippetText=C}equals(u){return i.Range.lift(this.range).equalsRange(u.range)&&this.text===u.text&&this.completionKind===u.completionKind&&this.isSnippetText===u.isSnippetText}}n.SelectedSuggestionInfo=a;var c;(function(s){s[s.Invoke=1]="Invoke",s[s.TriggerCharacter=2]="TriggerCharacter",s[s.ContentChange=3]="ContentChange"})(c||(n.SignatureHelpTriggerKind=c={}));var l;(function(s){s[s.Text=0]="Text",s[s.Read=1]="Read",s[s.Write=2]="Write"})(l||(n.DocumentHighlightKind=l={}));function f(s){return s&&A.URI.isUri(s.uri)&&i.Range.isIRange(s.range)&&(i.Range.isIRange(s.originSelectionRange)||i.Range.isIRange(s.targetSelectionRange))}n.isLocationLink=f,n.symbolKindNames={[17]:(0,m.localize)(0,null),[16]:(0,m.localize)(1,null),[4]:(0,m.localize)(2,null),[13]:(0,m.localize)(3,null),[8]:(0,m.localize)(4,null),[9]:(0,m.localize)(5,null),[21]:(0,m.localize)(6,null),[23]:(0,m.localize)(7,null),[7]:(0,m.localize)(8,null),[0]:(0,m.localize)(9,null),[11]:(0,m.localize)(10,null),[10]:(0,m.localize)(11,null),[19]:(0,m.localize)(12,null),[5]:(0,m.localize)(13,null),[1]:(0,m.localize)(14,null),[2]:(0,m.localize)(15,null),[20]:(0,m.localize)(16,null),[15]:(0,m.localize)(17,null),[18]:(0,m.localize)(18,null),[24]:(0,m.localize)(19,null),[3]:(0,m.localize)(20,null),[6]:(0,m.localize)(21,null),[14]:(0,m.localize)(22,null),[22]:(0,m.localize)(23,null),[25]:(0,m.localize)(24,null),[12]:(0,m.localize)(25,null)};function g(s,u){return(0,m.localize)(26,null,s,n.symbolKindNames[u])}n.getAriaLabelForSymbol=g;var S;(function(s){const u=new Map;u.set(0,M.Codicon.symbolFile),u.set(1,M.Codicon.symbolModule),u.set(2,M.Codicon.symbolNamespace),u.set(3,M.Codicon.symbolPackage),u.set(4,M.Codicon.symbolClass),u.set(5,M.Codicon.symbolMethod),u.set(6,M.Codicon.symbolProperty),u.set(7,M.Codicon.symbolField),u.set(8,M.Codicon.symbolConstructor),u.set(9,M.Codicon.symbolEnum),u.set(10,M.Codicon.symbolInterface),u.set(11,M.Codicon.symbolFunction),u.set(12,M.Codicon.symbolVariable),u.set(13,M.Codicon.symbolConstant),u.set(14,M.Codicon.symbolString),u.set(15,M.Codicon.symbolNumber),u.set(16,M.Codicon.symbolBoolean),u.set(17,M.Codicon.symbolArray),u.set(18,M.Codicon.symbolObject),u.set(19,M.Codicon.symbolKey),u.set(20,M.Codicon.symbolNull),u.set(21,M.Codicon.symbolEnumMember),u.set(22,M.Codicon.symbolStruct),u.set(23,M.Codicon.symbolEvent),u.set(24,M.Codicon.symbolOperator),u.set(25,M.Codicon.symbolTypeParameter);function p(b){let C=u.get(b);return C||(console.info("No codicon found for SymbolKind "+b),C=M.Codicon.symbolProperty),C}s.toIcon=p})(S||(n.SymbolKinds=S={}));class _{}n.TextEdit=_;class E{static fromValue(u){switch(u){case"comment":return E.Comment;case"imports":return E.Imports;case"region":return E.Region}return new E(u)}constructor(u){this.value=u}}n.FoldingRangeKind=E,E.Comment=new E("comment"),E.Imports=new E("imports"),E.Region=new E("region");var y;(function(s){function u(p){return!p||typeof p!="object"?!1:typeof p.id=="string"&&typeof p.title=="string"}s.is=u})(y||(n.Command=y={}));var v;(function(s){s[s.Type=1]="Type",s[s.Parameter=2]="Parameter"})(v||(n.InlayHintKind=v={}));class r{constructor(u){this.createSupport=u,this._tokenizationSupport=null}dispose(){this._tokenizationSupport&&this._tokenizationSupport.then(u=>{u&&u.dispose()})}get tokenizationSupport(){return this._tokenizationSupport||(this._tokenizationSupport=this.createSupport()),this._tokenizationSupport}}n.LazyTokenizationSupport=r,n.TokenizationRegistry=new d.TokenizationRegistry}),Y(X[66],J([0,1,38,9,35,18,4,2,41,65,58]),function(T,n,M,A,i,d,m,L,h,o,w){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.createMonacoBaseAPI=n.KeyMod=void 0;class e{static chord(l,f){return(0,i.KeyChord)(l,f)}}n.KeyMod=e,e.CtrlCmd=2048,e.Shift=1024,e.Alt=512,e.WinCtrl=256;function a(){return{editor:void 0,languages:void 0,CancellationTokenSource:M.CancellationTokenSource,Emitter:A.Emitter,KeyCode:w.KeyCode,KeyMod:e,Position:m.Position,Range:L.Range,Selection:h.Selection,SelectionDirection:w.SelectionDirection,MarkerSeverity:w.MarkerSeverity,MarkerTag:w.MarkerTag,Uri:d.URI,Token:o.Token}}n.createMonacoBaseAPI=a}),Y(X[68],J([0,1,24,18,4,2,55,28,51,52,66,23,57,49,14,50]),function(T,n,M,A,i,d,m,L,h,o,w,e,a,c,l,f){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.create=n.EditorSimpleWorker=void 0;class g extends m.MirrorTextModel{get uri(){return this._uri}get eol(){return this._eol}getValue(){return this.getText()}findMatches(y){const v=[];for(let r=0;r<this._lines.length;r++){const s=this._lines[r],u=this.offsetAt(new i.Position(r+1,1)),p=s.matchAll(y);for(const b of p)(b.index||b.index===0)&&(b.index=b.index+u),v.push(b)}return v}getLinesContent(){return this._lines.slice(0)}getLineCount(){return this._lines.length}getLineContent(y){return this._lines[y-1]}getWordAtPosition(y,v){const r=(0,L.getWordAtText)(y.column,(0,L.ensureValidWordDefinition)(v),this._lines[y.lineNumber-1],0);return r?new d.Range(y.lineNumber,r.startColumn,y.lineNumber,r.endColumn):null}words(y){const v=this._lines,r=this._wordenize.bind(this);let s=0,u="",p=0,b=[];return{*[Symbol.iterator](){for(;;)if(p<b.length){const C=u.substring(b[p].start,b[p].end);p+=1,yield C}else if(s<v.length)u=v[s],b=r(u,y),p=0,s+=1;else break}}}getLineWords(y,v){const r=this._lines[y-1],s=this._wordenize(r,v),u=[];for(const p of s)u.push({word:r.substring(p.start,p.end),startColumn:p.start+1,endColumn:p.end+1});return u}_wordenize(y,v){const r=[];let s;for(v.lastIndex=0;(s=v.exec(y))&&s[0].length!==0;)r.push({start:s.index,end:s.index+s[0].length});return r}getValueInRange(y){if(y=this._validateRange(y),y.startLineNumber===y.endLineNumber)return this._lines[y.startLineNumber-1].substring(y.startColumn-1,y.endColumn-1);const v=this._eol,r=y.startLineNumber-1,s=y.endLineNumber-1,u=[];u.push(this._lines[r].substring(y.startColumn-1));for(let p=r+1;p<s;p++)u.push(this._lines[p]);return u.push(this._lines[s].substring(0,y.endColumn-1)),u.join(v)}offsetAt(y){return y=this._validatePosition(y),this._ensureLineStarts(),this._lineStarts.getPrefixSum(y.lineNumber-2)+(y.column-1)}positionAt(y){y=Math.floor(y),y=Math.max(0,y),this._ensureLineStarts();const v=this._lineStarts.getIndexOf(y),r=this._lines[v.index].length;return{lineNumber:1+v.index,column:1+Math.min(v.remainder,r)}}_validateRange(y){const v=this._validatePosition({lineNumber:y.startLineNumber,column:y.startColumn}),r=this._validatePosition({lineNumber:y.endLineNumber,column:y.endColumn});return v.lineNumber!==y.startLineNumber||v.column!==y.startColumn||r.lineNumber!==y.endLineNumber||r.column!==y.endColumn?{startLineNumber:v.lineNumber,startColumn:v.column,endLineNumber:r.lineNumber,endColumn:r.column}:y}_validatePosition(y){if(!i.Position.isIPosition(y))throw new Error("bad position");let{lineNumber:v,column:r}=y,s=!1;if(v<1)v=1,r=1,s=!0;else if(v>this._lines.length)v=this._lines.length,r=this._lines[v-1].length+1,s=!0;else{const u=this._lines[v-1].length+1;r<1?(r=1,s=!0):r>u&&(r=u,s=!0)}return s?{lineNumber:v,column:r}:y}}class S{constructor(y,v){this._host=y,this._models=Object.create(null),this._foreignModuleFactory=v,this._foreignModule=null}dispose(){this._models=Object.create(null)}_getModel(y){return this._models[y]}_getModels(){const y=[];return Object.keys(this._models).forEach(v=>y.push(this._models[v])),y}acceptNewModel(y){this._models[y.url]=new g(A.URI.parse(y.url),y.lines,y.EOL,y.versionId)}acceptModelChanged(y,v){if(!this._models[y])return;this._models[y].onEvents(v)}acceptRemovedModel(y){this._models[y]&&delete this._models[y]}computeUnicodeHighlights(y,v,r){return ue(this,void 0,void 0,function*(){const s=this._getModel(y);return s?a.UnicodeTextModelHighlighter.computeUnicodeHighlights(s,v,r):{ranges:[],hasMore:!1,ambiguousCharacterCount:0,invisibleCharacterCount:0,nonBasicAsciiCharacterCount:0}})}computeDiff(y,v,r,s){return ue(this,void 0,void 0,function*(){const u=this._getModel(y),p=this._getModel(v);return!u||!p?null:S.computeDiff(u,p,r,s)})}static computeDiff(y,v,r,s){const u=s==="advanced"?c.linesDiffComputers.getDefault():c.linesDiffComputers.getLegacy(),p=y.getLinesContent(),b=v.getLinesContent(),C=u.computeDiff(p,b,r),N=C.changes.length>0?!1:this._modelsAreIdentical(y,v);function R(D){return D.map(k=>{var U;return[k.original.startLineNumber,k.original.endLineNumberExclusive,k.modified.startLineNumber,k.modified.endLineNumberExclusive,(U=k.innerChanges)===null||U===void 0?void 0:U.map(I=>[I.originalRange.startLineNumber,I.originalRange.startColumn,I.originalRange.endLineNumber,I.originalRange.endColumn,I.modifiedRange.startLineNumber,I.modifiedRange.startColumn,I.modifiedRange.endLineNumber,I.modifiedRange.endColumn])]})}return{identical:N,quitEarly:C.hitTimeout,changes:R(C.changes),moves:C.moves.map(D=>[D.lineRangeMapping.original.startLineNumber,D.lineRangeMapping.original.endLineNumberExclusive,D.lineRangeMapping.modified.startLineNumber,D.lineRangeMapping.modified.endLineNumberExclusive,R(D.changes)])}}static _modelsAreIdentical(y,v){const r=y.getLineCount(),s=v.getLineCount();if(r!==s)return!1;for(let u=1;u<=r;u++){const p=y.getLineContent(u),b=v.getLineContent(u);if(p!==b)return!1}return!0}computeMoreMinimalEdits(y,v,r){return ue(this,void 0,void 0,function*(){const s=this._getModel(y);if(!s)return v;const u=[];let p;v=v.slice(0).sort((C,N)=>{if(C.range&&N.range)return d.Range.compareRangesUsingStarts(C.range,N.range);const R=C.range?0:1,D=N.range?0:1;return R-D});let b=0;for(let C=1;C<v.length;C++)d.Range.getEndPosition(v[b].range).equals(d.Range.getStartPosition(v[C].range))?(v[b].range=d.Range.fromPositions(d.Range.getStartPosition(v[b].range),d.Range.getEndPosition(v[C].range)),v[b].text+=v[C].text):(b++,v[b]=v[C]);v.length=b+1;for(let{range:C,text:N,eol:R}of v){if(typeof R=="number"&&(p=R),d.Range.isEmpty(C)&&!N)continue;const D=s.getValueInRange(C);if(N=N.replace(/\r\n|\n|\r/g,s.eol),D===N)continue;if(Math.max(N.length,D.length)>S._diffLimit){u.push({range:C,text:N});continue}const k=(0,M.stringDiff)(D,N,r),U=s.offsetAt(d.Range.lift(C).getStartPosition());for(const I of k){const B=s.positionAt(U+I.originalStart),z=s.positionAt(U+I.originalStart+I.originalLength),x={text:N.substr(I.modifiedStart,I.modifiedLength),range:{startLineNumber:B.lineNumber,startColumn:B.column,endLineNumber:z.lineNumber,endColumn:z.column}};s.getValueInRange(x.range)!==x.text&&u.push(x)}}return typeof p=="number"&&u.push({eol:p,text:"",range:{startLineNumber:0,startColumn:0,endLineNumber:0,endColumn:0}}),u})}computeLinks(y){return ue(this,void 0,void 0,function*(){const v=this._getModel(y);return v?(0,h.computeLinks)(v):null})}computeDefaultDocumentColors(y){return ue(this,void 0,void 0,function*(){const v=this._getModel(y);return v?(0,f.computeDefaultDocumentColors)(v):null})}textualSuggest(y,v,r,s){return ue(this,void 0,void 0,function*(){const u=new e.StopWatch,p=new RegExp(r,s),b=new Set;e:for(const C of y){const N=this._getModel(C);if(N){for(const R of N.words(p))if(!(R===v||!isNaN(Number(R)))&&(b.add(R),b.size>S._suggestionsLimit))break e}}return{words:Array.from(b),duration:u.elapsed()}})}computeWordRanges(y,v,r,s){return ue(this,void 0,void 0,function*(){const u=this._getModel(y);if(!u)return Object.create(null);const p=new RegExp(r,s),b=Object.create(null);for(let C=v.startLineNumber;C<v.endLineNumber;C++){const N=u.getLineWords(C,p);for(const R of N){if(!isNaN(Number(R.word)))continue;let D=b[R.word];D||(D=[],b[R.word]=D),D.push({startLineNumber:C,startColumn:R.startColumn,endLineNumber:C,endColumn:R.endColumn})}}return b})}navigateValueSet(y,v,r,s,u){return ue(this,void 0,void 0,function*(){const p=this._getModel(y);if(!p)return null;const b=new RegExp(s,u);v.startColumn===v.endColumn&&(v={startLineNumber:v.startLineNumber,startColumn:v.startColumn,endLineNumber:v.endLineNumber,endColumn:v.endColumn+1});const C=p.getValueInRange(v),N=p.getWordAtPosition({lineNumber:v.startLineNumber,column:v.startColumn},b);if(!N)return null;const R=p.getValueInRange(N);return o.BasicInplaceReplace.INSTANCE.navigateValueSet(v,C,N,R,r)})}loadForeignModule(y,v,r){const s=(b,C)=>this._host.fhr(b,C),p={host:(0,l.createProxyObject)(r,s),getMirrorModels:()=>this._getModels()};return this._foreignModuleFactory?(this._foreignModule=this._foreignModuleFactory(p,v),Promise.resolve((0,l.getAllMethodNames)(this._foreignModule))):new Promise((b,C)=>{T([y],N=>{this._foreignModule=N.create(p,v),b((0,l.getAllMethodNames)(this._foreignModule))},C)})}fmr(y,v){if(!this._foreignModule||typeof this._foreignModule[y]!="function")return Promise.reject(new Error("Missing requestHandler or method: "+y));try{return Promise.resolve(this._foreignModule[y].apply(this._foreignModule,v))}catch(r){return Promise.reject(r)}}}n.EditorSimpleWorker=S,S._diffLimit=1e5,S._suggestionsLimit=1e4;function _(E){return new S(E,null)}n.create=_,typeof importScripts=="function"&&(globalThis.monaco=(0,w.createMonacoBaseAPI)())})}).call(this);

//# sourceMappingURL=../../../../min-maps/vs/base/worker/workerMain.js.map
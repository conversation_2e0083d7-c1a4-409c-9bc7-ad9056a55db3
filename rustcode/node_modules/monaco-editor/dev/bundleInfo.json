{"graph": {"===anonymous1===": ["require"], "===anonymous2===": ["vs/editor/editor.main", "vs/css", "vs/nls", "vs/base/common/worker/simpleWorker", "vs/editor/common/services/editorSimpleWorker"], "vs/editor/editor.main": ["require", "exports", "vs/editor/editor.api", "vs/editor/editor.all", "vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard", "vs/editor/standalone/browser/inspectTokens/inspectTokens", "vs/editor/standalone/browser/quickAccess/standaloneHelpQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneGotoLineQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneGotoSymbolQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneCommandsQuickAccess", "vs/editor/standalone/browser/referenceSearch/standaloneReferenceSearch", "vs/editor/standalone/browser/toggleHighContrast/toggleHighContrast"], "vs/css": ["require", "exports"], "vs/nls": ["require", "exports"], "vs/base/common/worker/simpleWorker": ["require", "exports", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/base/common/platform", "vs/base/common/strings"], "vs/editor/common/services/editorSimpleWorker": ["require", "exports", "vs/base/common/diff/diff", "vs/base/common/uri", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/model/mirrorTextModel", "vs/editor/common/core/wordHelper", "vs/editor/common/languages/linkComputer", "vs/editor/common/languages/supports/inplaceReplaceSupport", "vs/editor/common/services/editorBaseApi", "vs/base/common/stopwatch", "vs/editor/common/services/unicodeTextModelHighlighter", "vs/editor/common/diff/linesDiffComputers", "vs/base/common/objects", "vs/editor/common/languages/defaultDocumentColorsComputer"], "vs/editor/editor.api": ["require", "exports", "vs/editor/common/config/editorOptions", "vs/editor/common/services/editorBaseApi", "vs/editor/standalone/browser/standaloneEditor", "vs/editor/standalone/browser/standaloneLanguages", "vs/editor/contrib/format/browser/format"], "vs/editor/editor.all": ["require", "exports", "vs/editor/browser/coreCommands", "vs/editor/browser/widget/codeEditorWidget", "vs/editor/browser/widget/diffEditor/diffEditor.contribution", "vs/editor/contrib/anchorSelect/browser/anchorSelect", "vs/editor/contrib/bracketMatching/browser/bracketMatching", "vs/editor/contrib/caretOperations/browser/caretOperations", "vs/editor/contrib/caretOperations/browser/transpose", "vs/editor/contrib/clipboard/browser/clipboard", "vs/editor/contrib/codeAction/browser/codeActionContributions", "vs/editor/contrib/codelens/browser/codelensController", "vs/editor/contrib/colorPicker/browser/colorContributions", "vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions", "vs/editor/contrib/comment/browser/comment", "vs/editor/contrib/contextmenu/browser/contextmenu", "vs/editor/contrib/cursorUndo/browser/cursorUndo", "vs/editor/contrib/dnd/browser/dnd", "vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution", "vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution", "vs/editor/contrib/find/browser/findController", "vs/editor/contrib/folding/browser/folding", "vs/editor/contrib/fontZoom/browser/fontZoom", "vs/editor/contrib/format/browser/formatActions", "vs/editor/contrib/documentSymbols/browser/documentSymbols", "vs/editor/contrib/inlineCompletions/browser/inlineCompletions.contribution", "vs/editor/contrib/inlineProgress/browser/inlineProgress", "vs/editor/contrib/gotoSymbol/browser/goToCommands", "vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition", "vs/editor/contrib/gotoError/browser/gotoError", "vs/editor/contrib/hover/browser/hover", "vs/editor/contrib/indentation/browser/indentation", "vs/editor/contrib/inlayHints/browser/inlayHintsContribution", "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace", "vs/editor/contrib/lineSelection/browser/lineSelection", "vs/editor/contrib/linesOperations/browser/linesOperations", "vs/editor/contrib/linkedEditing/browser/linkedEditing", "vs/editor/contrib/links/browser/links", "vs/editor/contrib/longLinesHelper/browser/longLinesHelper", "vs/editor/contrib/multicursor/browser/multicursor", "vs/editor/contrib/parameterHints/browser/parameterHints", "vs/editor/contrib/rename/browser/rename", "vs/editor/contrib/semanticTokens/browser/documentSemanticTokens", "vs/editor/contrib/semanticTokens/browser/viewportSemanticTokens", "vs/editor/contrib/smartSelect/browser/smartSelect", "vs/editor/contrib/snippet/browser/snippetController2", "vs/editor/contrib/stickyScroll/browser/stickyScrollContribution", "vs/editor/contrib/suggest/browser/suggestController", "vs/editor/contrib/suggest/browser/suggestInlineCompletions", "vs/editor/contrib/tokenization/browser/tokenization", "vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode", "vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter", "vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators", "vs/editor/contrib/wordHighlighter/browser/wordHighlighter", "vs/editor/contrib/wordOperations/browser/wordOperations", "vs/editor/contrib/wordPartOperations/browser/wordPartOperations", "vs/editor/contrib/readOnlyMessage/browser/contribution", "vs/editor/common/standaloneStrings", "vs/base/browser/ui/codicons/codiconStyles"], "vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard": ["require", "exports", "vs/base/browser/dom", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/base/common/platform", "vs/css!vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard"], "vs/editor/standalone/browser/inspectTokens/inspectTokens": ["require", "exports", "vs/base/browser/dom", "vs/base/common/color", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/languages", "vs/editor/common/encodedTokenAttributes", "vs/editor/common/languages/nullTokenize", "vs/editor/common/languages/language", "vs/editor/standalone/common/standaloneTheme", "vs/editor/common/standaloneStrings", "vs/css!vs/editor/standalone/browser/inspectTokens/inspectTokens"], "vs/editor/standalone/browser/quickAccess/standaloneHelpQuickAccess": ["require", "exports", "vs/platform/registry/common/platform", "vs/platform/quickinput/common/quickAccess", "vs/editor/common/standaloneStrings", "vs/platform/quickinput/browser/helpQuickAccess"], "vs/editor/standalone/browser/quickAccess/standaloneGotoLineQuickAccess": ["require", "exports", "vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess", "vs/platform/registry/common/platform", "vs/platform/quickinput/common/quickAccess", "vs/editor/browser/services/codeEditorService", "vs/editor/common/standaloneStrings", "vs/base/common/event", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/quickinput/common/quickInput"], "vs/editor/standalone/browser/quickAccess/standaloneGotoSymbolQuickAccess": ["require", "exports", "vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess", "vs/platform/registry/common/platform", "vs/platform/quickinput/common/quickAccess", "vs/editor/browser/services/codeEditorService", "vs/editor/common/standaloneStrings", "vs/base/common/event", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/quickinput/common/quickInput", "vs/editor/contrib/documentSymbols/browser/outlineModel", "vs/editor/common/services/languageFeatures", "vs/base/browser/ui/codicons/codiconStyles", "vs/editor/contrib/symbolIcons/browser/symbolIcons"], "vs/editor/standalone/browser/quickAccess/standaloneCommandsQuickAccess": ["require", "exports", "vs/platform/registry/common/platform", "vs/platform/quickinput/common/quickAccess", "vs/editor/common/standaloneStrings", "vs/editor/browser/services/codeEditorService", "vs/editor/contrib/quickAccess/browser/commandsQuickAccess", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/commands/common/commands", "vs/platform/telemetry/common/telemetry", "vs/platform/dialogs/common/dialogs", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/quickinput/common/quickInput"], "vs/editor/standalone/browser/referenceSearch/standaloneReferenceSearch": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/contrib/gotoSymbol/browser/peek/referencesController", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/notification/common/notification", "vs/platform/storage/common/storage"], "vs/editor/standalone/browser/toggleHighContrast/toggleHighContrast": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/standalone/common/standaloneTheme", "vs/editor/common/standaloneStrings", "vs/platform/theme/common/theme", "vs/editor/standalone/browser/standaloneThemeService"], "vs/base/common/errors": ["require", "exports"], "vs/base/common/event": ["require", "exports", "vs/base/common/errors", "vs/base/common/functional", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/base/common/stopwatch"], "vs/base/common/lifecycle": ["require", "exports", "vs/base/common/functional", "vs/base/common/iterator"], "vs/base/common/objects": ["require", "exports", "vs/base/common/types"], "vs/base/common/platform": ["require", "exports", "vs/nls!vs/base/common/platform"], "vs/base/common/strings": ["require", "exports", "vs/base/common/cache", "vs/base/common/lazy"], "vs/base/common/diff/diff": ["require", "exports", "vs/base/common/diff/diffChange", "vs/base/common/hash"], "vs/base/common/uri": ["require", "exports", "vs/base/common/path", "vs/base/common/platform"], "vs/editor/common/core/position": ["require", "exports"], "vs/editor/common/core/range": ["require", "exports", "vs/editor/common/core/position"], "vs/editor/common/model/mirrorTextModel": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/position", "vs/editor/common/model/prefixSumComputer"], "vs/editor/common/core/wordHelper": ["require", "exports", "vs/base/common/iterator", "vs/base/common/linkedList"], "vs/editor/common/languages/linkComputer": ["require", "exports", "vs/editor/common/core/characterClassifier"], "vs/editor/common/languages/supports/inplaceReplaceSupport": ["require", "exports"], "vs/editor/common/services/editorBaseApi": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/event", "vs/base/common/keyCodes", "vs/base/common/uri", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/languages", "vs/editor/common/standalone/standaloneEnums"], "vs/base/common/stopwatch": ["require", "exports"], "vs/editor/common/services/unicodeTextModelHighlighter": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/model/textModelSearch", "vs/base/common/strings", "vs/base/common/assert", "vs/editor/common/core/wordHelper"], "vs/editor/common/diff/linesDiffComputers": ["require", "exports", "vs/editor/common/diff/legacyLinesDiffComputer", "vs/editor/common/diff/defaultLinesDiffComputer/defaultLinesDiffComputer"], "vs/editor/common/languages/defaultDocumentColorsComputer": ["require", "exports", "vs/base/common/color"], "vs/editor/common/config/editorOptions": ["require", "exports", "vs/base/common/arrays", "vs/base/common/objects", "vs/base/common/platform", "vs/editor/common/core/textModelDefaults", "vs/editor/common/core/wordHelper", "vs/nls!vs/editor/common/config/editorOptions"], "vs/editor/standalone/browser/standaloneEditor": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/base/common/uri", "vs/editor/browser/config/fontMeasurements", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/services/webWorker", "vs/editor/common/config/editorOptions", "vs/editor/common/config/editorZoom", "vs/editor/common/config/fontInfo", "vs/editor/common/editor<PERSON><PERSON><PERSON>", "vs/editor/common/languages", "vs/editor/common/languages/language", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/languages/modesRegistry", "vs/editor/common/languages/nullTokenize", "vs/editor/common/model", "vs/editor/common/services/model", "vs/editor/common/standalone/standaloneEnums", "vs/editor/standalone/browser/colorizer", "vs/editor/standalone/browser/standaloneCodeEditor", "vs/editor/standalone/browser/standaloneServices", "vs/editor/standalone/common/standaloneTheme", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/keybinding/common/keybinding", "vs/platform/markers/common/markers", "vs/platform/opener/common/opener", "vs/css!vs/editor/standalone/browser/standalone-tokens"], "vs/editor/standalone/browser/standaloneLanguages": ["require", "exports", "vs/base/common/color", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/languages/modesRegistry", "vs/editor/common/languages/language", "vs/editor/common/standalone/standaloneEnums", "vs/editor/standalone/browser/standaloneServices", "vs/editor/standalone/common/monarch/monarchCompile", "vs/editor/standalone/common/monarch/monarchLexer", "vs/editor/standalone/common/standaloneTheme", "vs/platform/markers/common/markers", "vs/editor/common/services/languageFeatures", "vs/platform/configuration/common/configuration"], "vs/editor/contrib/format/browser/format": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/iterator", "vs/base/common/linkedList", "vs/base/common/types", "vs/base/common/uri", "vs/editor/contrib/editorState/browser/editorState", "vs/editor/browser/editorBrowser", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/services/editorW<PERSON>ker", "vs/editor/common/services/resolverService", "vs/editor/contrib/format/browser/formattingEdit", "vs/nls!vs/editor/contrib/format/browser/format", "vs/platform/commands/common/commands", "vs/platform/extensions/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/editor/common/services/languageFeatures", "vs/platform/log/common/log"], "vs/base/browser/dom": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/canIUse", "vs/base/browser/keyboardEvent", "vs/base/browser/mouseEvent", "vs/base/common/errors", "vs/base/common/event", "vs/base/browser/dompurify/dompurify", "vs/base/common/lifecycle", "vs/base/common/network", "vs/base/common/platform"], "vs/editor/browser/editorExtensions": ["require", "exports", "vs/nls!vs/editor/browser/editorExtensions", "vs/base/common/uri", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/position", "vs/editor/common/services/model", "vs/editor/common/services/resolverService", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybindingsRegistry", "vs/platform/registry/common/platform", "vs/platform/telemetry/common/telemetry", "vs/base/common/types", "vs/platform/log/common/log", "vs/base/browser/dom"], "vs/css!vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard": [], "vs/editor/browser/coreCommands": ["require", "exports", "vs/nls!vs/editor/browser/coreCommands", "vs/base/browser/browser", "vs/base/common/types", "vs/base/browser/ui/aria/aria", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/cursor/cursorColumnSelection", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/cursor/cursorDeleteOperations", "vs/editor/common/cursor/cursorMoveCommands", "vs/editor/common/cursor/cursorTypeOperations", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/contextkey/common/contextkey", "vs/platform/keybinding/common/keybindingsRegistry", "vs/base/browser/dom"], "vs/editor/browser/widget/codeEditorWidget": ["require", "exports", "vs/nls!vs/editor/browser/widget/codeEditorWidget", "vs/base/browser/dom", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/network", "vs/editor/browser/config/editorConfiguration", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/view", "vs/editor/browser/view/viewUserInputEvents", "vs/editor/common/config/editorOptions", "vs/editor/common/core/cursorColumns", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorAction", "vs/editor/common/editor<PERSON><PERSON><PERSON>", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/model/textModel", "vs/editor/common/core/editorColorRegistry", "vs/platform/theme/common/colorRegistry", "vs/editor/common/viewModel/viewModelImpl", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/instantiation/common/serviceCollection", "vs/platform/notification/common/notification", "vs/platform/theme/common/themeService", "vs/platform/accessibility/common/accessibility", "vs/editor/common/viewModel/monospaceLineBreaksComputer", "vs/editor/browser/view/domLineBreaksComputer", "vs/editor/common/cursor/cursorWordOperations", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/browser/config/domFontInfo", "vs/editor/common/services/languageFeatures", "vs/editor/browser/widget/codeEditorContributions", "vs/editor/browser/config/tabFocus", "vs/editor/browser/services/markerDecorations", "vs/css!vs/editor/browser/widget/media/editor"], "vs/editor/browser/widget/diffEditor/diffEditor.contribution": ["require", "exports", "vs/base/browser/dom", "vs/base/common/codicons", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/widget/diffEditor/diffEditorWidget", "vs/editor/common/editorContext<PERSON>eys", "vs/nls!vs/editor/browser/widget/diffEditor/diffEditor.contribution", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey"], "vs/editor/contrib/anchorSelect/browser/anchorSelect": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/htmlContent", "vs/base/common/keyCodes", "vs/editor/browser/editorExtensions", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/nls!vs/editor/contrib/anchorSelect/browser/anchorSelect", "vs/platform/contextkey/common/contextkey", "vs/css!vs/editor/contrib/anchorSelect/browser/anchorSelect"], "vs/editor/contrib/bracketMatching/browser/bracketMatching": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/model", "vs/editor/common/model/textModel", "vs/nls!vs/editor/contrib/bracketMatching/browser/bracketMatching", "vs/platform/actions/common/actions", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/bracketMatching/browser/bracketMatching"], "vs/editor/contrib/caretOperations/browser/caretOperations": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/caretOperations/browser/moveCaretCommand", "vs/nls!vs/editor/contrib/caretOperations/browser/caretOperations"], "vs/editor/contrib/caretOperations/browser/transpose": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/commands/replaceCommand", "vs/editor/common/cursor/cursorMoveOperations", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/nls!vs/editor/contrib/caretOperations/browser/transpose"], "vs/editor/contrib/clipboard/browser/clipboard": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dom", "vs/base/common/platform", "vs/editor/browser/controller/textAreaInput", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/editorContext<PERSON>eys", "vs/nls!vs/editor/contrib/clipboard/browser/clipboard", "vs/platform/actions/common/actions", "vs/platform/clipboard/common/clipboardService", "vs/platform/contextkey/common/contextkey"], "vs/editor/contrib/codeAction/browser/codeActionContributions": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/config/editorConfigurationSchema", "vs/editor/contrib/codeAction/browser/codeActionCommands", "vs/editor/contrib/codeAction/browser/codeActionController", "vs/editor/contrib/codeAction/browser/lightBulbWidget", "vs/nls!vs/editor/contrib/codeAction/browser/codeActionContributions", "vs/platform/configuration/common/configurationRegistry", "vs/platform/registry/common/platform"], "vs/editor/contrib/codelens/browser/codelensController": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/editor/browser/stableEditorScroll", "vs/editor/browser/editorExtensions", "vs/editor/common/config/editorOptions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/codelens/browser/codelens", "vs/editor/contrib/codelens/browser/codeLensCache", "vs/editor/contrib/codelens/browser/codelensWidget", "vs/nls!vs/editor/contrib/codelens/browser/codelensController", "vs/platform/commands/common/commands", "vs/platform/notification/common/notification", "vs/platform/quickinput/common/quickInput", "vs/editor/common/services/languageFeatureDebounce", "vs/editor/common/services/languageFeatures"], "vs/editor/contrib/colorPicker/browser/colorContributions": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/contrib/colorPicker/browser/colorDetector", "vs/editor/contrib/colorPicker/browser/colorHoverParticipant", "vs/editor/contrib/hover/browser/hover", "vs/editor/contrib/hover/browser/hoverTypes"], "vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/nls!vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions", "vs/editor/contrib/colorPicker/browser/standaloneColorPickerWidget", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/actions/common/actions", "vs/css!vs/editor/contrib/colorPicker/browser/colorPicker"], "vs/editor/contrib/comment/browser/comment": ["require", "exports", "vs/base/common/keyCodes", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/contrib/comment/browser/blockCommentCommand", "vs/editor/contrib/comment/browser/lineCommentCommand", "vs/nls!vs/editor/contrib/comment/browser/comment", "vs/platform/actions/common/actions"], "vs/editor/contrib/contextmenu/browser/contextmenu": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/common/actions", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/nls!vs/editor/contrib/contextmenu/browser/contextmenu", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/keybinding/common/keybinding", "vs/platform/configuration/common/configuration", "vs/platform/workspace/common/workspace"], "vs/editor/contrib/cursorUndo/browser/cursorUndo": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/nls!vs/editor/contrib/cursorUndo/browser/cursorUndo"], "vs/editor/contrib/dnd/browser/dnd": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/editor/browser/editorExtensions", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/model/textModel", "vs/editor/contrib/dnd/browser/dragAndDropCommand", "vs/css!vs/editor/contrib/dnd/browser/dnd"], "vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/editorFeatures", "vs/editor/contrib/dropOrPasteInto/browser/copyPasteController", "vs/editor/contrib/dropOrPasteInto/browser/defaultProviders", "vs/nls!vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution"], "vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/config/editorConfigurationSchema", "vs/editor/common/editorFeatures", "vs/editor/contrib/dropOrPasteInto/browser/defaultProviders", "vs/nls!vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution", "vs/platform/configuration/common/configurationRegistry", "vs/platform/registry/common/platform", "vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController"], "vs/editor/contrib/find/browser/findController": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/browser/editorExtensions", "vs/editor/common/core/editorColorRegistry", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/model", "vs/editor/contrib/find/browser/findModel", "vs/editor/contrib/find/browser/findOptionsWidget", "vs/editor/contrib/find/browser/findState", "vs/editor/contrib/find/browser/findWidget", "vs/nls!vs/editor/contrib/find/browser/findController", "vs/platform/actions/common/actions", "vs/platform/clipboard/common/clipboardService", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/keybinding/common/keybinding", "vs/platform/notification/common/notification", "vs/platform/quickinput/common/quickInput", "vs/platform/storage/common/storage", "vs/platform/theme/common/themeService"], "vs/editor/contrib/folding/browser/folding": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/keyCodes", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/base/common/types", "vs/editor/browser/stableEditorScroll", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/contrib/folding/browser/foldingModel", "vs/editor/contrib/folding/browser/hiddenRangeModel", "vs/editor/contrib/folding/browser/indentRangeProvider", "vs/nls!vs/editor/contrib/folding/browser/folding", "vs/platform/contextkey/common/contextkey", "vs/editor/contrib/folding/browser/foldingDecorations", "vs/editor/contrib/folding/browser/foldingRanges", "vs/editor/contrib/folding/browser/syntaxRangeProvider", "vs/platform/notification/common/notification", "vs/editor/common/services/languageFeatureDebounce", "vs/base/common/stopwatch", "vs/editor/common/services/languageFeatures", "vs/base/common/event", "vs/platform/commands/common/commands", "vs/base/common/uri", "vs/editor/common/services/model", "vs/platform/configuration/common/configuration", "vs/css!vs/editor/contrib/folding/browser/folding"], "vs/editor/contrib/fontZoom/browser/fontZoom": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/config/editorZoom", "vs/nls!vs/editor/contrib/fontZoom/browser/fontZoom"], "vs/editor/contrib/format/browser/formatActions": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/keyCodes", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/characterClassifier", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/services/editorW<PERSON>ker", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/format/browser/format", "vs/editor/contrib/format/browser/formattingEdit", "vs/nls!vs/editor/contrib/format/browser/formatActions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/progress/common/progress"], "vs/editor/contrib/documentSymbols/browser/documentSymbols": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/types", "vs/base/common/uri", "vs/editor/common/services/resolverService", "vs/editor/contrib/documentSymbols/browser/outlineModel", "vs/platform/commands/common/commands"], "vs/editor/contrib/inlineCompletions/browser/inlineCompletions.contribution": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/contrib/hover/browser/hoverTypes", "vs/editor/contrib/inlineCompletions/browser/commands", "vs/editor/contrib/inlineCompletions/browser/hoverParticipant", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsController", "vs/platform/actions/common/actions"], "vs/editor/contrib/inlineProgress/browser/inlineProgress": ["require", "exports", "vs/base/browser/dom", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/base/common/themables", "vs/editor/common/core/range", "vs/editor/common/model/textModel", "vs/platform/instantiation/common/instantiation", "vs/css!vs/editor/contrib/inlineProgress/browser/inlineProgressWidget"], "vs/editor/contrib/gotoSymbol/browser/goToCommands": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/async", "vs/base/common/keyCodes", "vs/base/common/types", "vs/base/common/uri", "vs/editor/contrib/editorState/browser/editorState", "vs/editor/browser/editorBrowser", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/widget/embeddedCodeEditorWidget", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages", "vs/editor/contrib/gotoSymbol/browser/peek/referencesController", "vs/editor/contrib/gotoSymbol/browser/referencesModel", "vs/editor/contrib/gotoSymbol/browser/symbolNavigation", "vs/editor/contrib/message/browser/messageController", "vs/editor/contrib/peekView/browser/peekView", "vs/nls!vs/editor/contrib/gotoSymbol/browser/goToCommands", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/notification/common/notification", "vs/platform/progress/common/progress", "vs/editor/contrib/gotoSymbol/browser/goToSymbol", "vs/editor/common/services/languageFeatures", "vs/base/common/iterator", "vs/platform/contextkey/common/contextkeys"], "vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/editor/contrib/editorState/browser/editorState", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/languages/language", "vs/editor/common/services/resolverService", "vs/editor/contrib/gotoSymbol/browser/link/clickLinkGesture", "vs/editor/contrib/peekView/browser/peekView", "vs/nls!vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition", "vs/platform/contextkey/common/contextkey", "vs/editor/contrib/gotoSymbol/browser/goToCommands", "vs/editor/contrib/gotoSymbol/browser/goToSymbol", "vs/editor/common/services/languageFeatures", "vs/editor/common/model/textModel", "vs/css!vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition"], "vs/editor/contrib/gotoError/browser/gotoError": ["require", "exports", "vs/base/common/codicons", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/gotoError/browser/markerNavigationService", "vs/nls!vs/editor/contrib/gotoError/browser/gotoError", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/theme/common/iconRegistry", "vs/editor/contrib/gotoError/browser/gotoErrorWidget"], "vs/editor/contrib/hover/browser/hover": ["require", "exports", "vs/base/common/keyCodes", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages/language", "vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition", "vs/editor/contrib/hover/browser/contentHover", "vs/editor/contrib/hover/browser/marginHover", "vs/platform/instantiation/common/instantiation", "vs/platform/opener/common/opener", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/editor/contrib/hover/browser/hoverTypes", "vs/editor/contrib/hover/browser/markdownHoverParticipant", "vs/editor/contrib/hover/browser/markerHoverParticipant", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget", "vs/platform/keybinding/common/keybinding", "vs/nls!vs/editor/contrib/hover/browser/hover", "vs/base/common/async", "vs/css!vs/editor/contrib/hover/browser/hover"], "vs/editor/contrib/indentation/browser/indentation": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/browser/editorExtensions", "vs/editor/common/commands/shiftCommand", "vs/editor/common/core/editOperation", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/services/model", "vs/editor/contrib/indentation/browser/indentUtils", "vs/nls!vs/editor/contrib/indentation/browser/indentation", "vs/platform/quickinput/common/quickInput", "vs/editor/common/core/indentation", "vs/editor/common/languages/autoIndent"], "vs/editor/contrib/inlayHints/browser/inlayHintsContribution": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/contrib/hover/browser/hoverTypes", "vs/editor/contrib/inlayHints/browser/inlayHintsController", "vs/editor/contrib/inlayHints/browser/inlayHintsHover"], "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/editor/contrib/editorState/browser/editorState", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/model/textModel", "vs/editor/common/services/editorW<PERSON>ker", "vs/nls!vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace", "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplaceCommand", "vs/css!vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace"], "vs/editor/contrib/lineSelection/browser/lineSelection": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/cursor/cursorMoveCommands", "vs/editor/common/editorContext<PERSON>eys", "vs/nls!vs/editor/contrib/lineSelection/browser/lineSelection"], "vs/editor/contrib/linesOperations/browser/linesOperations": ["require", "exports", "vs/base/common/keyCodes", "vs/editor/browser/coreCommands", "vs/editor/browser/editorExtensions", "vs/editor/common/commands/replaceCommand", "vs/editor/common/commands/trimTrailingWhitespaceCommand", "vs/editor/common/cursor/cursorTypeOperations", "vs/editor/common/core/editOperation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/linesOperations/browser/copyLinesCommand", "vs/editor/contrib/linesOperations/browser/moveLinesCommand", "vs/editor/contrib/linesOperations/browser/sortLinesCommand", "vs/nls!vs/editor/contrib/linesOperations/browser/linesOperations", "vs/platform/actions/common/actions", "vs/editor/common/languages/languageConfigurationRegistry"], "vs/editor/contrib/linkedEditing/browser/linkedEditing": ["require", "exports", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/color", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/base/common/uri", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/model/textModel", "vs/editor/common/languages/languageConfigurationRegistry", "vs/nls!vs/editor/contrib/linkedEditing/browser/linkedEditing", "vs/platform/contextkey/common/contextkey", "vs/editor/common/services/languageFeatures", "vs/platform/theme/common/colorRegistry", "vs/editor/common/services/languageFeatureDebounce", "vs/base/common/stopwatch", "vs/css!vs/editor/contrib/linkedEditing/browser/linkedEditing"], "vs/editor/contrib/links/browser/links": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/base/common/network", "vs/base/common/platform", "vs/base/common/resources", "vs/base/common/stopwatch", "vs/base/common/uri", "vs/editor/browser/editorExtensions", "vs/editor/common/model/textModel", "vs/editor/common/services/languageFeatureDebounce", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/gotoSymbol/browser/link/clickLinkGesture", "vs/editor/contrib/links/browser/getLinks", "vs/nls!vs/editor/contrib/links/browser/links", "vs/platform/notification/common/notification", "vs/platform/opener/common/opener", "vs/css!vs/editor/contrib/links/browser/links"], "vs/editor/contrib/longLinesHelper/browser/longLinesHelper": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions"], "vs/editor/contrib/multicursor/browser/multicursor": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/async", "vs/base/common/keyCodes", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/cursor/cursorMoveCommands", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/find/browser/findController", "vs/nls!vs/editor/contrib/multicursor/browser/multicursor", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/wordHighlighter/browser/highlightDecorations", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/parameterHints/browser/parameterHints": ["require", "exports", "vs/base/common/lazy", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/parameterHints/browser/parameterHintsModel", "vs/editor/contrib/parameterHints/browser/provideSignatureHelp", "vs/nls!vs/editor/contrib/parameterHints/browser/parameterHints", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/editor/contrib/parameterHints/browser/parameterHintsWidget"], "vs/editor/contrib/rename/browser/rename": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/types", "vs/base/common/uri", "vs/editor/contrib/editorState/browser/editorState", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/bulkEditService", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/services/textResourceConfiguration", "vs/editor/contrib/message/browser/messageController", "vs/nls!vs/editor/contrib/rename/browser/rename", "vs/platform/configuration/common/configurationRegistry", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/log/common/log", "vs/platform/notification/common/notification", "vs/platform/progress/common/progress", "vs/platform/registry/common/platform", "vs/editor/contrib/rename/browser/renameInputField", "vs/editor/common/services/languageFeatures"], "vs/editor/contrib/semanticTokens/browser/documentSemanticTokens": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/errors", "vs/editor/common/services/model", "vs/platform/configuration/common/configuration", "vs/base/common/async", "vs/base/common/cancellation", "vs/platform/theme/common/themeService", "vs/editor/common/services/semanticTokensProviderStyling", "vs/editor/contrib/semanticTokens/common/getSemanticTokens", "vs/editor/common/services/languageFeatureDebounce", "vs/base/common/stopwatch", "vs/editor/common/services/languageFeatures", "vs/editor/common/services/semanticTokensStyling", "vs/editor/common/editorFeatures", "vs/editor/contrib/semanticTokens/common/semanticTokensConfig"], "vs/editor/contrib/semanticTokens/browser/viewportSemanticTokens": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/contrib/semanticTokens/common/getSemanticTokens", "vs/editor/contrib/semanticTokens/common/semanticTokensConfig", "vs/editor/common/services/semanticTokensProviderStyling", "vs/platform/configuration/common/configuration", "vs/platform/theme/common/themeService", "vs/editor/common/services/languageFeatureDebounce", "vs/base/common/stopwatch", "vs/editor/common/services/languageFeatures", "vs/editor/common/services/semanticTokensStyling"], "vs/editor/contrib/smartSelect/browser/smartSelect": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/editor/browser/editorExtensions", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/smartSelect/browser/bracketSelections", "vs/editor/contrib/smartSelect/browser/wordSelections", "vs/nls!vs/editor/contrib/smartSelect/browser/smartSelect", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/editor/common/services/languageFeatures", "vs/editor/common/services/resolverService", "vs/base/common/types", "vs/base/common/uri"], "vs/editor/contrib/snippet/browser/snippetController2": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/types", "vs/editor/browser/editorExtensions", "vs/editor/common/core/position", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/suggest/browser/suggest", "vs/nls!vs/editor/contrib/snippet/browser/snippetController2", "vs/platform/contextkey/common/contextkey", "vs/platform/log/common/log", "vs/editor/contrib/snippet/browser/snippetSession"], "vs/editor/contrib/stickyScroll/browser/stickyScrollContribution": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/contrib/stickyScroll/browser/stickyScrollActions", "vs/editor/contrib/stickyScroll/browser/stickyScrollController", "vs/platform/actions/common/actions"], "vs/editor/contrib/suggest/browser/suggestController": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/keybindings", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/stopwatch", "vs/base/common/types", "vs/editor/browser/stableEditorScroll", "vs/editor/browser/editorExtensions", "vs/editor/common/core/editOperation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/snippet/browser/snippetController2", "vs/editor/contrib/snippet/browser/snippetParser", "vs/editor/contrib/suggest/browser/suggestMemory", "vs/editor/contrib/suggest/browser/wordContextKey", "vs/nls!vs/editor/contrib/suggest/browser/suggestController", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/log/common/log", "vs/editor/contrib/suggest/browser/suggest", "vs/editor/contrib/suggest/browser/suggestAlternatives", "vs/editor/contrib/suggest/browser/suggestCommitCharacters", "vs/editor/contrib/suggest/browser/suggestModel", "vs/editor/contrib/suggest/browser/suggestOvertypingCapturer", "vs/editor/contrib/suggest/browser/suggestWidget", "vs/platform/telemetry/common/telemetry", "vs/base/common/resources", "vs/base/common/hash"], "vs/editor/contrib/suggest/browser/suggestInlineCompletions": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/filters", "vs/base/common/iterator", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/range", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/suggest/browser/completionModel", "vs/editor/contrib/suggest/browser/suggest", "vs/editor/contrib/suggest/browser/suggestMemory", "vs/editor/contrib/suggest/browser/wordDistance", "vs/platform/clipboard/common/clipboardService", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/tokenization/browser/tokenization": ["require", "exports", "vs/base/common/stopwatch", "vs/editor/browser/editorExtensions", "vs/nls!vs/editor/contrib/tokenization/browser/tokenization"], "vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/editor/browser/config/tabFocus", "vs/nls!vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode", "vs/platform/actions/common/actions"], "vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter": ["require", "exports", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/strings", "vs/editor/browser/editorExtensions", "vs/editor/common/config/editorOptions", "vs/editor/common/model/textModel", "vs/editor/common/services/unicodeTextModelHighlighter", "vs/editor/common/services/editorW<PERSON>ker", "vs/editor/common/languages/language", "vs/editor/common/viewModel/viewModelDecorations", "vs/editor/contrib/hover/browser/hoverTypes", "vs/editor/contrib/hover/browser/markdownHoverParticipant", "vs/editor/contrib/unicodeHighlighter/browser/bannerController", "vs/nls!vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter", "vs/platform/configuration/common/configuration", "vs/platform/instantiation/common/instantiation", "vs/platform/opener/common/opener", "vs/platform/quickinput/common/quickInput", "vs/platform/theme/common/iconRegistry", "vs/platform/workspace/common/workspaceTrust", "vs/css!vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter"], "vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/nls!vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators", "vs/platform/dialogs/common/dialogs"], "vs/editor/contrib/wordHighlighter/browser/wordHighlighter": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages", "vs/nls!vs/editor/contrib/wordHighlighter/browser/wordHighlighter", "vs/platform/contextkey/common/contextkey", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/wordHighlighter/browser/highlightDecorations", "vs/base/common/iterator"], "vs/editor/contrib/wordOperations/browser/wordOperations": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/commands/replaceCommand", "vs/editor/common/config/editorOptions", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/cursor/cursorWordOperations", "vs/editor/common/core/wordCharacterClassifier", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages/languageConfigurationRegistry", "vs/nls!vs/editor/contrib/wordOperations/browser/wordOperations", "vs/platform/accessibility/common/accessibility", "vs/platform/contextkey/common/contextkey", "vs/platform/contextkey/common/contextkeys"], "vs/editor/contrib/wordPartOperations/browser/wordPartOperations": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/cursor/cursorWordOperations", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/wordOperations/browser/wordOperations", "vs/platform/commands/common/commands"], "vs/editor/contrib/readOnlyMessage/browser/contribution": ["require", "exports", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/contrib/message/browser/messageController", "vs/nls!vs/editor/contrib/readOnlyMessage/browser/contribution"], "vs/editor/common/standaloneStrings": ["require", "exports", "vs/nls!vs/editor/common/standaloneStrings"], "vs/base/browser/ui/codicons/codiconStyles": ["require", "exports", "vs/css!vs/base/browser/ui/codicons/codicon/codicon", "vs/css!vs/base/browser/ui/codicons/codicon/codicon-modifiers"], "vs/base/common/color": ["require", "exports"], "vs/editor/common/languages": ["require", "exports", "vs/base/common/codicons", "vs/base/common/uri", "vs/editor/common/core/range", "vs/editor/common/tokenizationRegistry", "vs/nls!vs/editor/common/languages"], "vs/editor/common/encodedTokenAttributes": ["require", "exports"], "vs/editor/common/languages/nullTokenize": ["require", "exports", "vs/editor/common/languages"], "vs/editor/common/languages/language": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/standalone/common/standaloneTheme": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/css!vs/editor/standalone/browser/inspectTokens/inspectTokens": [], "vs/platform/registry/common/platform": ["require", "exports", "vs/base/common/assert", "vs/base/common/types"], "vs/platform/quickinput/common/quickAccess": ["require", "exports", "vs/base/common/arrays", "vs/base/common/lifecycle", "vs/platform/registry/common/platform"], "vs/platform/quickinput/browser/helpQuickAccess": ["require", "exports", "vs/nls!vs/platform/quickinput/browser/helpQuickAccess", "vs/platform/registry/common/platform", "vs/base/common/lifecycle", "vs/platform/keybinding/common/keybinding", "vs/platform/quickinput/common/quickAccess", "vs/platform/quickinput/common/quickInput"], "vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/browser/editorBrowser", "vs/editor/contrib/quickAccess/browser/editorNavigationQuickAccess", "vs/nls!vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess"], "vs/editor/browser/services/codeEditorService": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/common/editorContextKeys": ["require", "exports", "vs/nls!vs/editor/common/editorContextKeys", "vs/platform/contextkey/common/contextkey"], "vs/platform/quickinput/common/quickInput": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/codicons", "vs/base/common/themables", "vs/base/common/fuzzyScorer", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/editor/contrib/documentSymbols/browser/outlineModel", "vs/editor/contrib/quickAccess/browser/editorNavigationQuickAccess", "vs/nls!vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess", "vs/editor/common/services/languageFeatures", "vs/base/common/arraysFind"], "vs/editor/contrib/documentSymbols/browser/outlineModel": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/iterator", "vs/base/common/map", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/services/languageFeatureDebounce", "vs/platform/instantiation/common/instantiation", "vs/platform/instantiation/common/extensions", "vs/editor/common/services/model", "vs/base/common/lifecycle", "vs/editor/common/services/languageFeatures"], "vs/editor/common/services/languageFeatures": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/symbolIcons/browser/symbolIcons": ["require", "exports", "vs/nls!vs/editor/contrib/symbolIcons/browser/symbolIcons", "vs/platform/theme/common/colorRegistry", "vs/css!vs/editor/contrib/symbolIcons/browser/symbolIcons"], "vs/editor/contrib/quickAccess/browser/commandsQuickAccess": ["require", "exports", "vs/base/common/iconLabels", "vs/platform/quickinput/browser/commandsQuickAccess"], "vs/platform/instantiation/common/instantiation": ["require", "exports"], "vs/platform/keybinding/common/keybinding": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/commands/common/commands": ["require", "exports", "vs/base/common/event", "vs/base/common/iterator", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/base/common/types", "vs/platform/instantiation/common/instantiation"], "vs/platform/telemetry/common/telemetry": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/dialogs/common/dialogs": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/gotoSymbol/browser/peek/referencesController": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/keyCodes", "vs/base/common/lifecycle", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/contrib/peekView/browser/peekView", "vs/nls!vs/editor/contrib/gotoSymbol/browser/peek/referencesController", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybindingsRegistry", "vs/platform/list/browser/listService", "vs/platform/notification/common/notification", "vs/platform/storage/common/storage", "vs/editor/contrib/gotoSymbol/browser/referencesModel", "vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget"], "vs/platform/configuration/common/configuration": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/contextkey/common/contextkey": ["require", "exports", "vs/base/common/platform", "vs/base/common/strings", "vs/platform/contextkey/common/scanner", "vs/platform/instantiation/common/instantiation", "vs/nls!vs/platform/contextkey/common/contextkey"], "vs/platform/notification/common/notification": ["require", "exports", "vs/base/common/severity", "vs/platform/instantiation/common/instantiation"], "vs/platform/storage/common/storage": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/types", "vs/base/parts/storage/common/storage", "vs/platform/instantiation/common/instantiation"], "vs/platform/theme/common/theme": ["require", "exports"], "vs/editor/standalone/browser/standaloneThemeService": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/browser", "vs/base/common/color", "vs/base/common/event", "vs/editor/common/languages", "vs/editor/common/encodedTokenAttributes", "vs/editor/common/languages/supports/tokenization", "vs/editor/standalone/common/themes", "vs/platform/registry/common/platform", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/base/common/lifecycle", "vs/platform/theme/common/theme", "vs/platform/theme/browser/iconsStyleSheet"], "vs/base/common/functional": ["require", "exports"], "vs/base/common/linkedList": ["require", "exports"], "vs/base/common/iterator": ["require", "exports"], "vs/nls!vs/base/common/platform": [], "===anonymous3===": ["vs/base/common/platform.nls", "vs/base/common/platform.nls.keys"], "vs/base/common/platform.nls": [], "vs/base/common/platform.nls.keys": [], "vs/base/common/types": ["require", "exports"], "vs/base/common/path": ["require", "exports", "vs/base/common/process"], "vs/base/common/cache": ["require", "exports"], "vs/base/common/lazy": ["require", "exports"], "vs/base/common/diff/diffChange": ["require", "exports"], "vs/base/common/hash": ["require", "exports", "vs/base/common/strings"], "vs/editor/common/model/prefixSumComputer": ["require", "exports", "vs/base/common/arrays", "vs/base/common/uint"], "vs/editor/common/core/characterClassifier": ["require", "exports", "vs/base/common/uint"], "vs/base/common/cancellation": ["require", "exports", "vs/base/common/event"], "vs/base/common/keyCodes": ["require", "exports"], "vs/editor/common/core/selection": ["require", "exports", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/common/standalone/standaloneEnums": ["require", "exports"], "vs/editor/common/model/textModelSearch": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/wordCharacterClassifier", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/model"], "vs/base/common/assert": ["require", "exports", "vs/base/common/errors"], "vs/editor/common/diff/legacyLinesDiffComputer": ["require", "exports", "vs/base/common/diff/diff", "vs/editor/common/diff/linesDiffComputer", "vs/editor/common/diff/rangeMapping", "vs/base/common/strings", "vs/editor/common/core/range", "vs/base/common/assert", "vs/editor/common/core/lineRange"], "vs/editor/common/diff/defaultLinesDiffComputer/defaultLinesDiffComputer": ["require", "exports", "vs/base/common/arrays", "vs/base/common/assert", "vs/editor/common/core/lineRange", "vs/editor/common/core/offsetRange", "vs/editor/common/core/range", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/diffAlgorithm", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/dynamicProgrammingDiffing", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/myersDiffAlgorithm", "vs/editor/common/diff/defaultLinesDiffComputer/computeMovedLines", "vs/editor/common/diff/defaultLinesDiffComputer/heuristicSequenceOptimizations", "vs/editor/common/diff/linesDiffComputer", "vs/editor/common/diff/rangeMapping", "vs/editor/common/diff/defaultLinesDiffComputer/linesSliceCharSequence", "vs/editor/common/diff/defaultLinesDiffComputer/lineSequence"], "vs/editor/browser/config/fontMeasurements": ["require", "exports", "vs/base/browser/browser", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/browser/config/charWidthReader", "vs/editor/common/config/editorOptions", "vs/editor/common/config/fontInfo"], "vs/editor/browser/services/webWorker": ["require", "exports", "vs/base/common/objects", "vs/editor/browser/services/editorWorkerService"], "vs/editor/common/config/editorZoom": ["require", "exports", "vs/base/common/event"], "vs/editor/common/config/fontInfo": ["require", "exports", "vs/base/common/platform", "vs/editor/common/config/editorOptions", "vs/editor/common/config/editorZoom"], "vs/editor/common/editorCommon": ["require", "exports"], "vs/editor/common/languages/languageConfigurationRegistry": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/common/core/wordHelper", "vs/editor/common/languages/languageConfiguration", "vs/editor/common/languages/supports", "vs/editor/common/languages/supports/characterPair", "vs/editor/common/languages/supports/electricCharacter", "vs/editor/common/languages/supports/indentRules", "vs/editor/common/languages/supports/onEnter", "vs/editor/common/languages/supports/richEditBrackets", "vs/platform/instantiation/common/instantiation", "vs/platform/configuration/common/configuration", "vs/editor/common/languages/language", "vs/platform/instantiation/common/extensions", "vs/editor/common/languages/modesRegistry", "vs/editor/common/languages/supports/languageBracketsConfiguration"], "vs/editor/common/languages/modesRegistry": ["require", "exports", "vs/nls!vs/editor/common/languages/modesRegistry", "vs/base/common/event", "vs/platform/registry/common/platform", "vs/base/common/mime", "vs/platform/configuration/common/configurationRegistry"], "vs/editor/common/model": ["require", "exports", "vs/base/common/objects"], "vs/editor/common/services/model": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/standalone/browser/colorizer": ["require", "exports", "vs/base/browser/trustedTypes", "vs/base/common/strings", "vs/editor/common/languages", "vs/editor/common/tokens/lineTokens", "vs/editor/common/viewLayout/viewLineRenderer", "vs/editor/common/viewModel", "vs/editor/standalone/common/monarch/monarchLexer"], "vs/editor/standalone/browser/standaloneCodeEditor": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/lifecycle", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/widget/codeEditorWidget", "vs/editor/common/editorAction", "vs/editor/standalone/browser/standaloneServices", "vs/editor/standalone/common/standaloneTheme", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/notification/common/notification", "vs/platform/theme/common/themeService", "vs/platform/accessibility/common/accessibility", "vs/editor/common/standaloneStrings", "vs/platform/clipboard/common/clipboardService", "vs/platform/progress/common/progress", "vs/editor/common/services/model", "vs/editor/common/languages/language", "vs/editor/standalone/browser/standaloneCodeEditorService", "vs/editor/common/languages/modesRegistry", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/services/languageFeatures", "vs/editor/browser/widget/diffEditor/diffEditorWidget", "vs/platform/audioCues/browser/audioCueService"], "vs/editor/standalone/browser/standaloneServices": ["require", "exports", "vs/base/common/strings", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/common/event", "vs/base/common/keybindings", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/severity", "vs/base/common/uri", "vs/editor/browser/services/bulkEditService", "vs/editor/common/config/editorConfigurationSchema", "vs/editor/common/core/editOperation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/services/model", "vs/editor/common/services/resolverService", "vs/editor/common/services/textResourceConfiguration", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/configuration/common/configurationModels", "vs/platform/contextkey/common/contextkey", "vs/platform/dialogs/common/dialogs", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/abstractKeybindingService", "vs/platform/keybinding/common/keybinding", "vs/platform/keybinding/common/keybindingResolver", "vs/platform/keybinding/common/keybindingsRegistry", "vs/platform/keybinding/common/resolvedKeybindingItem", "vs/platform/keybinding/common/usLayoutResolvedKeybinding", "vs/platform/label/common/label", "vs/platform/notification/common/notification", "vs/platform/progress/common/progress", "vs/platform/telemetry/common/telemetry", "vs/platform/workspace/common/workspace", "vs/platform/layout/browser/layoutService", "vs/editor/common/standaloneStrings", "vs/base/common/resources", "vs/editor/browser/services/codeEditorService", "vs/platform/log/common/log", "vs/platform/workspace/common/workspaceTrust", "vs/platform/contextview/browser/contextView", "vs/platform/contextview/browser/contextViewService", "vs/editor/common/services/languageService", "vs/platform/contextview/browser/contextMenuService", "vs/platform/instantiation/common/extensions", "vs/editor/browser/services/openerService", "vs/editor/common/services/editorW<PERSON>ker", "vs/editor/browser/services/editorWorkerService", "vs/editor/common/languages/language", "vs/editor/common/services/markerDecorationsService", "vs/editor/common/services/markerDecorations", "vs/editor/common/services/modelService", "vs/editor/standalone/browser/quickInput/standaloneQuickInputService", "vs/editor/standalone/browser/standaloneThemeService", "vs/editor/standalone/common/standaloneTheme", "vs/platform/accessibility/browser/accessibilityService", "vs/platform/accessibility/common/accessibility", "vs/platform/actions/common/actions", "vs/platform/actions/common/menuService", "vs/platform/clipboard/browser/clipboardService", "vs/platform/clipboard/common/clipboardService", "vs/platform/contextkey/browser/contextKeyService", "vs/platform/instantiation/common/descriptors", "vs/platform/instantiation/common/instantiationService", "vs/platform/instantiation/common/serviceCollection", "vs/platform/list/browser/listService", "vs/platform/markers/common/markers", "vs/platform/markers/common/markerService", "vs/platform/opener/common/opener", "vs/platform/quickinput/common/quickInput", "vs/platform/storage/common/storage", "vs/platform/configuration/common/configurations", "vs/platform/audioCues/browser/audioCueService", "vs/platform/log/common/logService", "vs/editor/common/editorFeatures", "vs/base/common/errors", "vs/platform/environment/common/environment", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/standalone/browser/standaloneCodeEditorService", "vs/editor/standalone/browser/standaloneLayoutService", "vs/platform/undoRedo/common/undoRedoService", "vs/editor/common/services/languageFeatureDebounce", "vs/editor/common/services/semanticTokensStylingService", "vs/editor/common/services/languageFeaturesService"], "vs/platform/actions/common/actions": ["require", "exports", "vs/base/common/actions", "vs/base/common/themables", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybindingsRegistry"], "vs/platform/markers/common/markers": ["require", "exports", "vs/base/common/severity", "vs/nls!vs/platform/markers/common/markers", "vs/platform/instantiation/common/instantiation"], "vs/platform/opener/common/opener": ["require", "exports", "vs/base/common/strings", "vs/base/common/uri", "vs/platform/instantiation/common/instantiation"], "vs/css!vs/editor/standalone/browser/standalone-tokens": [], "vs/base/common/arrays": ["require", "exports"], "vs/editor/common/core/textModelDefaults": ["require", "exports"], "vs/nls!vs/editor/common/config/editorOptions": [], "===anonymous4===": ["vs/editor/common/config/editorOptions.nls", "vs/editor/common/config/editorOptions.nls.keys"], "vs/editor/common/config/editorOptions.nls": [], "vs/editor/common/config/editorOptions.nls.keys": [], "vs/editor/standalone/common/monarch/monarchCompile": ["require", "exports", "vs/editor/standalone/common/monarch/monarch<PERSON><PERSON>mon"], "vs/editor/standalone/common/monarch/monarchLexer": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/common/languages", "vs/editor/common/languages/nullTokenize", "vs/editor/standalone/common/monarch/monarch<PERSON><PERSON>mon", "vs/platform/configuration/common/configuration"], "vs/base/browser/ui/aria/aria": ["require", "exports", "vs/base/browser/dom", "vs/css!vs/base/browser/ui/aria/aria"], "vs/editor/contrib/editorState/browser/editorState": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/range", "vs/base/common/cancellation", "vs/base/common/lifecycle", "vs/editor/contrib/editorState/browser/keybindingCancellation"], "vs/editor/browser/editorBrowser": ["require", "exports", "vs/editor/common/editor<PERSON><PERSON><PERSON>"], "vs/editor/common/services/editorWorker": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/common/services/resolverService": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/format/browser/formattingEdit": ["require", "exports", "vs/editor/common/core/editOperation", "vs/editor/common/core/range", "vs/editor/browser/stableEditorScroll"], "vs/nls!vs/editor/contrib/format/browser/format": [], "vs/platform/extensions/common/extensions": ["require", "exports"], "vs/platform/log/common/log": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation"], "===anonymous5===": ["vs/editor/contrib/format/browser/format.nls", "vs/editor/contrib/format/browser/format.nls.keys"], "vs/editor/contrib/format/browser/format.nls": [], "vs/editor/contrib/format/browser/format.nls.keys": [], "vs/base/browser/browser": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/base/browser/canIUse": ["require", "exports", "vs/base/browser/browser", "vs/base/common/platform"], "vs/base/browser/keyboardEvent": ["require", "exports", "vs/base/browser/browser", "vs/base/common/keyCodes", "vs/base/common/keybindings", "vs/base/common/platform"], "vs/base/browser/mouseEvent": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/iframe", "vs/base/common/platform"], "vs/base/browser/dompurify/dompurify": ["require", "exports", "module"], "vs/base/common/network": ["require", "exports", "vs/base/common/errors", "vs/base/common/platform", "vs/base/common/uri"], "vs/nls!vs/editor/browser/editorExtensions": [], "vs/platform/keybinding/common/keybindingsRegistry": ["require", "exports", "vs/base/common/keybindings", "vs/base/common/platform", "vs/platform/commands/common/commands", "vs/platform/registry/common/platform", "vs/base/common/lifecycle", "vs/base/common/linkedList"], "===anonymous6===": ["vs/editor/browser/editorExtensions.nls", "vs/editor/browser/editorExtensions.nls.keys"], "vs/editor/browser/editorExtensions.nls": [], "vs/editor/browser/editorExtensions.nls.keys": [], "vs/nls!vs/editor/browser/coreCommands": [], "vs/editor/common/cursor/cursorColumnSelection": ["require", "exports", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/common/cursorCommon": ["require", "exports", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/languages/supports", "vs/editor/common/core/cursorColumns", "vs/editor/common/core/indentation"], "vs/editor/common/cursor/cursorDeleteOperations": ["require", "exports", "vs/base/common/strings", "vs/editor/common/commands/replaceCommand", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/core/cursorColumns", "vs/editor/common/cursor/cursorMoveOperations", "vs/editor/common/core/range", "vs/editor/common/core/position"], "vs/editor/common/cursor/cursorMoveCommands": ["require", "exports", "vs/base/common/types", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/cursor/cursorMoveOperations", "vs/editor/common/cursor/cursorWordOperations", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/common/cursor/cursorTypeOperations": ["require", "exports", "vs/base/common/errors", "vs/base/common/strings", "vs/editor/common/commands/replaceCommand", "vs/editor/common/commands/shiftCommand", "vs/editor/common/commands/surroundSelectionCommand", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/core/wordCharacterClassifier", "vs/editor/common/core/range", "vs/editor/common/core/position", "vs/editor/common/languages/languageConfiguration", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/languages/supports", "vs/editor/common/languages/autoIndent", "vs/editor/common/languages/enterAction"], "===anonymous7===": ["vs/editor/browser/coreCommands.nls", "vs/editor/browser/coreCommands.nls.keys"], "vs/editor/browser/coreCommands.nls": [], "vs/editor/browser/coreCommands.nls.keys": [], "vs/base/common/codicons": ["require", "exports", "vs/base/common/types"], "vs/editor/browser/widget/diffEditor/diffEditorWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/common/arraysFind", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/widget/codeEditorWidget", "vs/editor/browser/widget/diffEditor/accessibleDiffViewer", "vs/editor/browser/widget/diffEditor/diffEditorDecorations", "vs/editor/browser/widget/diffEditor/diffEditorSash", "vs/editor/browser/widget/diffEditor/hideUnchangedRegionsFeature", "vs/editor/browser/widget/diffEditor/lineAlignment", "vs/editor/browser/widget/diffEditor/movedBlocksLines", "vs/editor/browser/widget/diffEditor/overviewRulerPart", "vs/editor/browser/widget/diffEditor/utils", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editor<PERSON><PERSON><PERSON>", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length", "vs/platform/audioCues/browser/audioCueService", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/instantiation/common/serviceCollection", "vs/platform/progress/common/progress", "vs/editor/browser/widget/diffEditor/delegatingEditorImpl", "vs/editor/browser/widget/diffEditor/diffEditorEditors", "vs/editor/browser/widget/diffEditor/diffEditorOptions", "vs/editor/browser/widget/diffEditor/diffEditorViewModel", "vs/css!vs/editor/browser/widget/diffEditor/style", "vs/editor/browser/widget/diffEditor/colors"], "vs/nls!vs/editor/browser/widget/diffEditor/diffEditor.contribution": [], "===anonymous8===": ["vs/editor/browser/widget/diffEditor/diffEditor.contribution.nls", "vs/editor/browser/widget/diffEditor/diffEditor.contribution.nls.keys"], "vs/editor/browser/widget/diffEditor/diffEditor.contribution.nls": [], "vs/editor/browser/widget/diffEditor/diffEditor.contribution.nls.keys": [], "vs/base/common/async": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/symbols"], "vs/editor/common/model/textModel": ["require", "exports", "vs/base/common/arrays", "vs/base/common/color", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/base/common/uri", "vs/editor/common/core/eolCounter", "vs/editor/common/core/indentation", "vs/editor/common/core/lineRange", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/core/textModelDefaults", "vs/editor/common/languages/language", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/model", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsImpl", "vs/editor/common/model/bracketPairsTextModelPart/colorizedBracketPairsDecorationProvider", "vs/editor/common/model/editStack", "vs/editor/common/model/guidesTextModelPart", "vs/editor/common/model/indentationG<PERSON>ser", "vs/editor/common/model/intervalTree", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBuffer", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBufferBuilder", "vs/editor/common/model/textModelSearch", "vs/editor/common/model/tokenizationTextModelPart", "vs/editor/common/textModelEvents", "vs/platform/undoRedo/common/undoRedo"], "vs/nls!vs/editor/contrib/bracketMatching/browser/bracketMatching": [], "vs/platform/theme/common/colorRegistry": ["require", "exports", "vs/base/common/async", "vs/base/common/color", "vs/base/common/event", "vs/base/common/assert", "vs/nls!vs/platform/theme/common/colorRegistry", "vs/platform/jsonschemas/common/jsonContributionRegistry", "vs/platform/registry/common/platform"], "vs/platform/theme/common/themeService": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/instantiation/common/instantiation", "vs/platform/registry/common/platform", "vs/platform/theme/common/theme"], "vs/css!vs/editor/contrib/bracketMatching/browser/bracketMatching": [], "===anonymous9===": ["vs/editor/contrib/bracketMatching/browser/bracketMatching.nls", "vs/editor/contrib/bracketMatching/browser/bracketMatching.nls.keys"], "vs/editor/contrib/bracketMatching/browser/bracketMatching.nls": [], "vs/editor/contrib/bracketMatching/browser/bracketMatching.nls.keys": [], "vs/editor/contrib/caretOperations/browser/moveCaretCommand": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/nls!vs/editor/contrib/caretOperations/browser/caretOperations": [], "===anonymous10===": ["vs/editor/contrib/caretOperations/browser/caretOperations.nls", "vs/editor/contrib/caretOperations/browser/caretOperations.nls.keys"], "vs/editor/contrib/caretOperations/browser/caretOperations.nls": [], "vs/editor/contrib/caretOperations/browser/caretOperations.nls.keys": [], "vs/nls!vs/editor/browser/widget/codeEditorWidget": [], "vs/editor/browser/config/editorConfiguration": ["require", "exports", "vs/base/browser/browser", "vs/base/common/arrays", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/base/common/platform", "vs/editor/browser/config/elementSizeObserver", "vs/editor/browser/config/fontMeasurements", "vs/editor/browser/config/migrateOptions", "vs/editor/browser/config/tabFocus", "vs/editor/common/config/editorOptions", "vs/editor/common/config/editorZoom", "vs/editor/common/config/fontInfo", "vs/platform/accessibility/common/accessibility"], "vs/editor/browser/view": ["require", "exports", "vs/base/browser/dom", "vs/editor/common/core/selection", "vs/editor/common/core/range", "vs/base/browser/fastDomNode", "vs/base/common/errors", "vs/editor/browser/controller/pointerHandler", "vs/editor/browser/controller/text<PERSON><PERSON>Hand<PERSON>", "vs/editor/browser/view/viewController", "vs/editor/browser/view/viewUserInputEvents", "vs/editor/browser/view/viewOverlays", "vs/editor/browser/view/viewPart", "vs/editor/browser/viewParts/contentWidgets/contentWidgets", "vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight", "vs/editor/browser/viewParts/decorations/decorations", "vs/editor/browser/viewParts/editorScrollbar/editorScrollbar", "vs/editor/browser/viewParts/indentGuides/indentGuides", "vs/editor/browser/viewParts/lineNumbers/lineNumbers", "vs/editor/browser/viewParts/lines/viewLines", "vs/editor/browser/viewParts/linesDecorations/linesDecorations", "vs/editor/browser/viewParts/margin/margin", "vs/editor/browser/viewParts/marginDecorations/marginDecorations", "vs/editor/browser/viewParts/minimap/minimap", "vs/editor/browser/viewParts/overlayWidgets/overlayWidgets", "vs/editor/browser/viewParts/overviewRuler/decorationsOverviewRuler", "vs/editor/browser/viewParts/overviewRuler/overviewRuler", "vs/editor/browser/viewParts/rulers/rulers", "vs/editor/browser/viewParts/scrollDecoration/scrollDecoration", "vs/editor/browser/viewParts/selections/selections", "vs/editor/browser/viewParts/viewCursors/viewCursors", "vs/editor/browser/viewParts/viewZones/viewZones", "vs/editor/common/core/position", "vs/editor/browser/view/renderingContext", "vs/editor/common/viewModel/viewContext", "vs/editor/common/viewLayout/viewLinesViewportData", "vs/editor/common/viewEventHandler", "vs/platform/theme/common/themeService", "vs/editor/browser/controller/mouseTarget", "vs/editor/browser/viewParts/blockDecorations/blockDecorations", "vs/base/browser/performance", "vs/editor/browser/viewParts/whitespace/whitespace", "vs/editor/browser/viewParts/glyphMargin/glyphMargin", "vs/editor/common/model", "vs/platform/instantiation/common/instantiation"], "vs/editor/browser/view/viewUserInputEvents": ["require", "exports", "vs/editor/common/core/position"], "vs/editor/common/core/cursorColumns": ["require", "exports", "vs/base/common/strings"], "vs/editor/common/editorAction": ["require", "exports"], "vs/editor/common/core/editorColorRegistry": ["require", "exports", "vs/nls!vs/editor/common/core/editorColorRegistry", "vs/base/common/color", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/common/viewModel/viewModelImpl": ["require", "exports", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/color", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/strings", "vs/editor/common/config/editorOptions", "vs/editor/common/cursor/cursor", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/textModelEvents", "vs/editor/common/languages", "vs/editor/common/languages/modesRegistry", "vs/editor/common/languages/textToHtmlTokenizer", "vs/editor/common/viewEvents", "vs/editor/common/viewLayout/viewLayout", "vs/editor/common/viewModel/minimapTokensColorTracker", "vs/editor/common/viewModel", "vs/editor/common/viewModel/viewModelDecorations", "vs/editor/common/viewModelEventDispatcher", "vs/editor/common/viewModel/viewModelLines"], "vs/platform/instantiation/common/serviceCollection": ["require", "exports"], "vs/platform/accessibility/common/accessibility": ["require", "exports", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation"], "vs/editor/common/viewModel/monospaceLineBreaksComputer": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/characterClassifier", "vs/editor/common/textModelEvents", "vs/editor/common/modelLineProjectionData"], "vs/editor/browser/view/domLineBreaksComputer": ["require", "exports", "vs/base/browser/trustedTypes", "vs/base/common/strings", "vs/editor/browser/config/domFontInfo", "vs/editor/common/core/stringBuilder", "vs/editor/common/modelLineProjectionData", "vs/editor/common/textModelEvents"], "vs/editor/common/cursor/cursorWordOperations": ["require", "exports", "vs/base/common/strings", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/cursor/cursorDeleteOperations", "vs/editor/common/core/wordCharacterClassifier", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/browser/config/domFontInfo": ["require", "exports", "vs/base/browser/fastDomNode"], "vs/editor/browser/widget/codeEditorContributions": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/lifecycle"], "vs/editor/browser/config/tabFocus": ["require", "exports", "vs/base/common/event"], "vs/editor/browser/services/markerDecorations": ["require", "exports", "vs/editor/common/services/markerDecorations", "vs/editor/browser/editorExtensions"], "vs/css!vs/editor/browser/widget/media/editor": [], "===anonymous11===": ["vs/editor/browser/widget/codeEditorWidget.nls", "vs/editor/browser/widget/codeEditorWidget.nls.keys"], "vs/editor/browser/widget/codeEditorWidget.nls": [], "vs/editor/browser/widget/codeEditorWidget.nls.keys": [], "vs/editor/common/commands/replaceCommand": ["require", "exports", "vs/editor/common/core/selection"], "vs/editor/common/cursor/cursorMoveOperations": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/cursorColumns", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/cursor/cursorAtomicMoveOperations", "vs/editor/common/cursor<PERSON><PERSON>mon"], "vs/nls!vs/editor/contrib/caretOperations/browser/transpose": [], "===anonymous12===": ["vs/editor/contrib/caretOperations/browser/transpose.nls", "vs/editor/contrib/caretOperations/browser/transpose.nls.keys"], "vs/editor/contrib/caretOperations/browser/transpose.nls": [], "vs/editor/contrib/caretOperations/browser/transpose.nls.keys": [], "vs/base/common/htmlContent": ["require", "exports", "vs/base/common/errors", "vs/base/common/iconLabels", "vs/base/common/resources", "vs/base/common/strings", "vs/base/common/uri"], "vs/nls!vs/editor/contrib/anchorSelect/browser/anchorSelect": [], "vs/css!vs/editor/contrib/anchorSelect/browser/anchorSelect": [], "===anonymous13===": ["vs/editor/contrib/anchorSelect/browser/anchorSelect.nls", "vs/editor/contrib/anchorSelect/browser/anchorSelect.nls.keys"], "vs/editor/contrib/anchorSelect/browser/anchorSelect.nls": [], "vs/editor/contrib/anchorSelect/browser/anchorSelect.nls.keys": [], "vs/editor/browser/controller/textAreaInput": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/keyboardEvent", "vs/base/browser/performance", "vs/base/common/async", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/mime", "vs/base/common/strings", "vs/editor/browser/controller/textAreaState", "vs/editor/common/core/selection"], "vs/nls!vs/editor/contrib/clipboard/browser/clipboard": [], "vs/platform/clipboard/common/clipboardService": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "===anonymous14===": ["vs/editor/contrib/clipboard/browser/clipboard.nls", "vs/editor/contrib/clipboard/browser/clipboard.nls.keys"], "vs/editor/contrib/clipboard/browser/clipboard.nls": [], "vs/editor/contrib/clipboard/browser/clipboard.nls.keys": [], "vs/editor/common/config/editorConfigurationSchema": ["require", "exports", "vs/editor/common/config/diffEditor", "vs/editor/common/config/editorOptions", "vs/editor/common/core/textModelDefaults", "vs/nls!vs/editor/common/config/editorConfigurationSchema", "vs/platform/configuration/common/configurationRegistry", "vs/platform/registry/common/platform"], "vs/editor/contrib/codeAction/browser/codeActionCommands": ["require", "exports", "vs/base/common/strings", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/codeAction/browser/codeAction", "vs/nls!vs/editor/contrib/codeAction/browser/codeActionCommands", "vs/platform/contextkey/common/contextkey", "vs/editor/contrib/codeAction/common/types", "vs/editor/contrib/codeAction/browser/codeActionController", "vs/editor/contrib/codeAction/browser/codeActionModel"], "vs/editor/contrib/codeAction/browser/codeActionController": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/aria/aria", "vs/base/common/errors", "vs/base/common/lazy", "vs/base/common/lifecycle", "vs/editor/common/core/position", "vs/editor/common/model/textModel", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/codeAction/browser/codeAction", "vs/editor/contrib/codeAction/browser/codeActionKeybindingResolver", "vs/editor/contrib/codeAction/browser/codeActionMenu", "vs/editor/contrib/codeAction/browser/lightBulbWidget", "vs/editor/contrib/message/browser/messageController", "vs/nls!vs/editor/contrib/codeAction/browser/codeActionController", "vs/platform/actionWidget/browser/actionWidget", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/markers/common/markers", "vs/platform/progress/common/progress", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/theme", "vs/platform/theme/common/themeService", "vs/editor/contrib/codeAction/common/types", "vs/editor/contrib/codeAction/browser/codeActionModel"], "vs/editor/contrib/codeAction/browser/lightBulbWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/touch", "vs/base/common/codicons", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/themables", "vs/editor/common/model/utils", "vs/editor/contrib/codeAction/browser/codeAction", "vs/nls!vs/editor/contrib/codeAction/browser/lightBulbWidget", "vs/platform/keybinding/common/keybinding", "vs/css!vs/editor/contrib/codeAction/browser/lightBulbWidget"], "vs/nls!vs/editor/contrib/codeAction/browser/codeActionContributions": [], "vs/platform/configuration/common/configurationRegistry": ["require", "exports", "vs/base/common/arrays", "vs/base/common/event", "vs/base/common/types", "vs/nls!vs/platform/configuration/common/configurationRegistry", "vs/platform/configuration/common/configuration", "vs/platform/jsonschemas/common/jsonContributionRegistry", "vs/platform/registry/common/platform"], "===anonymous15===": ["vs/editor/contrib/codeAction/browser/codeActionContributions.nls", "vs/editor/contrib/codeAction/browser/codeActionContributions.nls.keys"], "vs/editor/contrib/codeAction/browser/codeActionContributions.nls": [], "vs/editor/contrib/codeAction/browser/codeActionContributions.nls.keys": [], "vs/nls!vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions": [], "vs/editor/contrib/colorPicker/browser/standaloneColorPickerWidget": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/contrib/colorPicker/browser/colorHoverParticipant", "vs/platform/instantiation/common/instantiation", "vs/editor/contrib/hover/browser/contentHover", "vs/platform/keybinding/common/keybinding", "vs/base/common/event", "vs/editor/common/services/languageFeatures", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/contextkey/common/contextkey", "vs/editor/common/services/model", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/contrib/colorPicker/browser/defaultDocumentColorProvider", "vs/base/browser/dom", "vs/css!vs/editor/contrib/colorPicker/browser/colorPicker"], "vs/css!vs/editor/contrib/colorPicker/browser/colorPicker": [], "===anonymous16===": ["vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions.nls", "vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions.nls.keys"], "vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions.nls": [], "vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions.nls.keys": [], "vs/editor/browser/stableEditorScroll": ["require", "exports"], "vs/editor/contrib/codelens/browser/codelens": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/types", "vs/base/common/uri", "vs/editor/common/services/model", "vs/platform/commands/common/commands", "vs/editor/common/services/languageFeatures"], "vs/editor/contrib/codelens/browser/codeLensCache": ["require", "exports", "vs/base/common/async", "vs/base/common/event", "vs/base/common/map", "vs/editor/common/core/range", "vs/editor/contrib/codelens/browser/codelens", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/storage/common/storage"], "vs/editor/contrib/codelens/browser/codelensWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/iconLabel/iconLabels", "vs/editor/common/core/range", "vs/editor/common/model/textModel", "vs/css!vs/editor/contrib/codelens/browser/codelensWidget"], "vs/nls!vs/editor/contrib/codelens/browser/codelensController": [], "vs/editor/common/services/languageFeatureDebounce": ["require", "exports", "vs/base/common/hash", "vs/base/common/map", "vs/base/common/numbers", "vs/platform/environment/common/environment", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/log/common/log", "vs/platform/opener/common/opener"], "===anonymous17===": ["vs/editor/contrib/codelens/browser/codelensController.nls", "vs/editor/contrib/codelens/browser/codelensController.nls.keys"], "vs/editor/contrib/codelens/browser/codelensController.nls": [], "vs/editor/contrib/codelens/browser/codelensController.nls.keys": [], "vs/editor/contrib/colorPicker/browser/colorDetector": ["require", "exports", "vs/base/common/async", "vs/base/common/color", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/stopwatch", "vs/base/common/strings", "vs/editor/browser/editorDom", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/model/textModel", "vs/editor/common/services/languageFeatureDebounce", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/colorPicker/browser/color", "vs/platform/configuration/common/configuration"], "vs/editor/contrib/colorPicker/browser/colorHoverParticipant": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/color", "vs/base/common/lifecycle", "vs/editor/common/core/range", "vs/editor/contrib/colorPicker/browser/color", "vs/editor/contrib/colorPicker/browser/colorDetector", "vs/editor/contrib/colorPicker/browser/colorPickerModel", "vs/editor/contrib/colorPicker/browser/colorPickerWidget", "vs/platform/theme/common/themeService", "vs/base/browser/dom"], "vs/editor/contrib/hover/browser/hoverTypes": ["require", "exports"], "vs/editor/contrib/comment/browser/blockCommentCommand": ["require", "exports", "vs/editor/common/core/editOperation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/editor/contrib/comment/browser/lineCommentCommand": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/editOperation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/contrib/comment/browser/blockCommentCommand"], "vs/nls!vs/editor/contrib/comment/browser/comment": [], "===anonymous18===": ["vs/editor/contrib/comment/browser/comment.nls", "vs/editor/contrib/comment/browser/comment.nls.keys"], "vs/editor/contrib/comment/browser/comment.nls": [], "vs/editor/contrib/comment/browser/comment.nls.keys": [], "vs/base/browser/ui/actionbar/actionViewItems": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dnd", "vs/base/browser/dom", "vs/base/browser/touch", "vs/base/browser/ui/iconLabel/iconLabelHover", "vs/base/browser/ui/selectBox/selectBox", "vs/base/common/actions", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/types", "vs/nls!vs/base/browser/ui/actionbar/actionViewItems", "vs/css!vs/base/browser/ui/actionbar/actionbar"], "vs/base/common/actions": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/nls!vs/base/common/actions"], "vs/nls!vs/editor/contrib/contextmenu/browser/contextmenu": [], "vs/platform/contextview/browser/contextView": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/workspace/common/workspace": ["require", "exports", "vs/nls!vs/platform/workspace/common/workspace", "vs/base/common/path", "vs/base/common/ternarySearchTree", "vs/base/common/uri", "vs/platform/instantiation/common/instantiation"], "===anonymous19===": ["vs/editor/contrib/contextmenu/browser/contextmenu.nls", "vs/editor/contrib/contextmenu/browser/contextmenu.nls.keys"], "vs/editor/contrib/contextmenu/browser/contextmenu.nls": [], "vs/editor/contrib/contextmenu/browser/contextmenu.nls.keys": [], "vs/editor/contrib/dnd/browser/dragAndDropCommand": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/css!vs/editor/contrib/dnd/browser/dnd": [], "vs/nls!vs/editor/contrib/cursorUndo/browser/cursorUndo": [], "===anonymous20===": ["vs/editor/contrib/cursorUndo/browser/cursorUndo.nls", "vs/editor/contrib/cursorUndo/browser/cursorUndo.nls.keys"], "vs/editor/contrib/cursorUndo/browser/cursorUndo.nls": [], "vs/editor/contrib/cursorUndo/browser/cursorUndo.nls.keys": [], "vs/editor/common/editorFeatures": ["require", "exports"], "vs/editor/contrib/dropOrPasteInto/browser/copyPasteController": ["require", "exports", "vs/base/browser/dom", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/dataTransfer", "vs/base/common/lifecycle", "vs/base/common/mime", "vs/base/common/platform", "vs/base/common/uuid", "vs/editor/browser/controller/textAreaInput", "vs/editor/browser/dnd", "vs/editor/browser/services/bulkEditService", "vs/editor/common/core/range", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/dropOrPasteInto/browser/edit", "vs/editor/contrib/editorState/browser/editorState", "vs/editor/contrib/inlineProgress/browser/inlineProgress", "vs/nls!vs/editor/contrib/dropOrPasteInto/browser/copyPasteController", "vs/platform/clipboard/common/clipboardService", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/progress/common/progress", "vs/platform/quickinput/common/quickInput", "vs/editor/contrib/dropOrPasteInto/browser/postEditWidget"], "vs/editor/contrib/dropOrPasteInto/browser/defaultProviders": ["require", "exports", "vs/base/common/arrays", "vs/base/common/dataTransfer", "vs/base/common/lifecycle", "vs/base/common/mime", "vs/base/common/network", "vs/base/common/resources", "vs/base/common/uri", "vs/editor/common/services/languageFeatures", "vs/nls!vs/editor/contrib/dropOrPasteInto/browser/defaultProviders", "vs/platform/workspace/common/workspace"], "vs/nls!vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution": [], "===anonymous21===": ["vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution.nls", "vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution.nls.keys"], "vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution.nls": [], "vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution.nls.keys": [], "vs/nls!vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution": [], "vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController": ["require", "exports", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/dataTransfer", "vs/base/common/lifecycle", "vs/editor/browser/dnd", "vs/editor/common/core/range", "vs/editor/common/services/languageFeatures", "vs/editor/common/services/treeViewsDnd", "vs/editor/common/services/treeViewsDndService", "vs/editor/contrib/editorState/browser/editorState", "vs/editor/contrib/inlineProgress/browser/inlineProgress", "vs/nls!vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/dnd/browser/dnd", "vs/platform/instantiation/common/instantiation", "vs/editor/contrib/dropOrPasteInto/browser/edit", "vs/editor/contrib/dropOrPasteInto/browser/postEditWidget"], "===anonymous22===": ["vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution.nls", "vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution.nls.keys"], "vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution.nls": [], "vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution.nls.keys": [], "vs/nls!vs/editor/contrib/fontZoom/browser/fontZoom": [], "===anonymous23===": ["vs/editor/contrib/fontZoom/browser/fontZoom.nls", "vs/editor/contrib/fontZoom/browser/fontZoom.nls.keys"], "vs/editor/contrib/fontZoom/browser/fontZoom.nls": [], "vs/editor/contrib/fontZoom/browser/fontZoom.nls.keys": [], "vs/editor/contrib/find/browser/findModel": ["require", "exports", "vs/base/common/arraysFind", "vs/base/common/async", "vs/base/common/lifecycle", "vs/editor/common/commands/replaceCommand", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/model/textModelSearch", "vs/editor/contrib/find/browser/findDecorations", "vs/editor/contrib/find/browser/replaceAllCommand", "vs/editor/contrib/find/browser/replacePattern", "vs/platform/contextkey/common/contextkey"], "vs/editor/contrib/find/browser/findOptionsWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/findinput/findInputToggles", "vs/base/browser/ui/widget", "vs/base/common/async", "vs/editor/contrib/find/browser/findModel", "vs/platform/theme/common/colorRegistry", "vs/css!vs/editor/contrib/find/browser/findOptionsWidget"], "vs/editor/contrib/find/browser/findState": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/range", "vs/editor/contrib/find/browser/findModel"], "vs/editor/contrib/find/browser/findWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/aria/aria", "vs/base/browser/ui/toggle/toggle", "vs/base/browser/ui/sash/sash", "vs/base/browser/ui/widget", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/strings", "vs/editor/common/core/range", "vs/editor/contrib/find/browser/findModel", "vs/nls!vs/editor/contrib/find/browser/findWidget", "vs/platform/history/browser/contextScopedHistoryWidget", "vs/platform/history/browser/historyWidgetKeybindingHint", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/iconRegistry", "vs/platform/theme/common/themeService", "vs/base/common/themables", "vs/platform/theme/common/theme", "vs/base/common/types", "vs/platform/theme/browser/defaultStyles", "vs/css!vs/editor/contrib/find/browser/findWidget"], "vs/nls!vs/editor/contrib/find/browser/findController": [], "===anonymous24===": ["vs/editor/contrib/find/browser/findController.nls", "vs/editor/contrib/find/browser/findController.nls.keys"], "vs/editor/contrib/find/browser/findController.nls": [], "vs/editor/contrib/find/browser/findController.nls.keys": [], "vs/nls!vs/editor/contrib/format/browser/formatActions": [], "vs/platform/progress/common/progress": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "===anonymous25===": ["vs/editor/contrib/format/browser/formatActions.nls", "vs/editor/contrib/format/browser/formatActions.nls.keys"], "vs/editor/contrib/format/browser/formatActions.nls": [], "vs/editor/contrib/format/browser/formatActions.nls.keys": [], "vs/editor/contrib/folding/browser/foldingModel": ["require", "exports", "vs/base/common/event", "vs/editor/contrib/folding/browser/foldingRanges", "vs/base/common/hash"], "vs/editor/contrib/folding/browser/hiddenRangeModel": ["require", "exports", "vs/base/common/arraysFind", "vs/base/common/event", "vs/editor/common/core/range", "vs/editor/common/core/eolCounter"], "vs/editor/contrib/folding/browser/indentRangeProvider": ["require", "exports", "vs/editor/common/model/utils", "vs/editor/contrib/folding/browser/foldingRanges"], "vs/nls!vs/editor/contrib/folding/browser/folding": [], "vs/editor/contrib/folding/browser/foldingDecorations": ["require", "exports", "vs/base/common/codicons", "vs/editor/common/model", "vs/editor/common/model/textModel", "vs/nls!vs/editor/contrib/folding/browser/foldingDecorations", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/iconRegistry", "vs/platform/theme/common/themeService", "vs/base/common/themables"], "vs/editor/contrib/folding/browser/foldingRanges": ["require", "exports"], "vs/editor/contrib/folding/browser/syntaxRangeProvider": ["require", "exports", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/editor/contrib/folding/browser/foldingRanges"], "vs/css!vs/editor/contrib/folding/browser/folding": [], "===anonymous26===": ["vs/editor/contrib/folding/browser/folding.nls", "vs/editor/contrib/folding/browser/folding.nls.keys"], "vs/editor/contrib/folding/browser/folding.nls": [], "vs/editor/contrib/folding/browser/folding.nls.keys": [], "vs/editor/contrib/inlineCompletions/browser/commands": ["require", "exports", "vs/base/common/observable", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/inlineCompletions/browser/commandIds", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsController", "vs/editor/contrib/suggest/browser/suggest", "vs/nls!vs/editor/contrib/inlineCompletions/browser/commands", "vs/platform/actions/common/actions", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey"], "vs/editor/contrib/inlineCompletions/browser/hoverParticipant": ["require", "exports", "vs/base/browser/dom", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/editor/common/core/range", "vs/editor/common/languages/language", "vs/editor/contrib/hover/browser/hoverTypes", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsController", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget", "vs/editor/contrib/markdownRenderer/browser/markdownRenderer", "vs/nls!vs/editor/contrib/inlineCompletions/browser/hoverParticipant", "vs/platform/accessibility/common/accessibility", "vs/platform/instantiation/common/instantiation", "vs/platform/opener/common/opener", "vs/platform/telemetry/common/telemetry"], "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsController": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/editor/browser/coreCommands", "vs/editor/common/core/position", "vs/editor/common/services/languageFeatureDebounce", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/inlineCompletions/browser/commandIds", "vs/editor/contrib/inlineCompletions/browser/ghostTextWidget", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsModel", "vs/editor/contrib/inlineCompletions/browser/suggestWidgetInlineCompletionProvider", "vs/nls!vs/editor/contrib/inlineCompletions/browser/inlineCompletionsController", "vs/platform/audioCues/browser/audioCueService", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding"], "vs/base/common/themables": ["require", "exports", "vs/base/common/codicons"], "vs/css!vs/editor/contrib/inlineProgress/browser/inlineProgressWidget": [], "vs/editor/browser/widget/embeddedCodeEditorWidget": ["require", "exports", "vs/base/common/objects", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/widget/codeEditorWidget", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/services/languageFeatures", "vs/platform/accessibility/common/accessibility", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/notification/common/notification", "vs/platform/theme/common/themeService"], "vs/editor/contrib/gotoSymbol/browser/referencesModel": ["require", "exports", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/idGenerator", "vs/base/common/lifecycle", "vs/base/common/map", "vs/base/common/resources", "vs/base/common/strings", "vs/editor/common/core/range", "vs/nls!vs/editor/contrib/gotoSymbol/browser/referencesModel"], "vs/editor/contrib/gotoSymbol/browser/symbolNavigation": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/range", "vs/nls!vs/editor/contrib/gotoSymbol/browser/symbolNavigation", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/keybinding/common/keybindingsRegistry", "vs/platform/notification/common/notification"], "vs/editor/contrib/message/browser/messageController": ["require", "exports", "vs/base/browser/markdownRenderer", "vs/base/browser/ui/aria/aria", "vs/base/common/event", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/contrib/markdownRenderer/browser/markdownRenderer", "vs/nls!vs/editor/contrib/message/browser/messageController", "vs/platform/contextkey/common/contextkey", "vs/platform/opener/common/opener", "vs/base/browser/dom", "vs/css!vs/editor/contrib/message/browser/messageController"], "vs/editor/contrib/peekView/browser/peekView": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionbar", "vs/base/common/actions", "vs/base/common/codicons", "vs/base/common/themables", "vs/base/common/color", "vs/base/common/event", "vs/base/common/objects", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/widget/embeddedCodeEditorWidget", "vs/editor/contrib/zoneWidget/browser/zoneWidget", "vs/nls!vs/editor/contrib/peekView/browser/peekView", "vs/platform/actions/browser/menuEntryActionViewItem", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/theme/common/colorRegistry", "vs/css!vs/editor/contrib/peekView/browser/media/peekViewWidget"], "vs/nls!vs/editor/contrib/gotoSymbol/browser/goToCommands": [], "vs/editor/contrib/gotoSymbol/browser/goToSymbol": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/editor/browser/editorExtensions", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/gotoSymbol/browser/referencesModel"], "vs/platform/contextkey/common/contextkeys": ["require", "exports", "vs/base/common/platform", "vs/nls!vs/platform/contextkey/common/contextkeys", "vs/platform/contextkey/common/contextkey"], "===anonymous27===": ["vs/editor/contrib/gotoSymbol/browser/goToCommands.nls", "vs/editor/contrib/gotoSymbol/browser/goToCommands.nls.keys"], "vs/editor/contrib/gotoSymbol/browser/goToCommands.nls": [], "vs/editor/contrib/gotoSymbol/browser/goToCommands.nls.keys": [], "vs/editor/contrib/gotoSymbol/browser/link/clickLinkGesture": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform"], "vs/nls!vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition": [], "vs/css!vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition": [], "===anonymous28===": ["vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition.nls", "vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition.nls.keys"], "vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition.nls": [], "vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition.nls.keys": [], "vs/editor/contrib/gotoError/browser/markerNavigationService": ["require", "exports", "vs/base/common/arrays", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/base/common/strings", "vs/base/common/uri", "vs/editor/common/core/range", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/markers/common/markers", "vs/platform/configuration/common/configuration"], "vs/nls!vs/editor/contrib/gotoError/browser/gotoError": [], "vs/platform/theme/common/iconRegistry": ["require", "exports", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/themables", "vs/base/common/event", "vs/base/common/types", "vs/base/common/uri", "vs/nls!vs/platform/theme/common/iconRegistry", "vs/platform/jsonschemas/common/jsonContributionRegistry", "vs/platform/registry/common/platform"], "vs/editor/contrib/gotoError/browser/gotoErrorWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/arrays", "vs/base/common/color", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/base/common/strings", "vs/editor/common/core/range", "vs/editor/contrib/peekView/browser/peekView", "vs/nls!vs/editor/contrib/gotoError/browser/gotoErrorWidget", "vs/platform/actions/browser/menuEntryActionViewItem", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/label/common/label", "vs/platform/markers/common/markers", "vs/platform/opener/common/opener", "vs/platform/severityIcon/browser/severityIcon", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/gotoError/browser/media/gotoErrorWidget"], "===anonymous29===": ["vs/editor/contrib/gotoError/browser/gotoError.nls", "vs/editor/contrib/gotoError/browser/gotoError.nls.keys"], "vs/editor/contrib/gotoError/browser/gotoError.nls": [], "vs/editor/contrib/gotoError/browser/gotoError.nls.keys": [], "vs/editor/contrib/hover/browser/contentHover": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/hover/hoverWidget", "vs/base/common/arrays", "vs/base/common/lifecycle", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/model/textModel", "vs/editor/common/languages", "vs/editor/contrib/hover/browser/hoverOperation", "vs/editor/contrib/hover/browser/hoverTypes", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/base/common/async", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/contextkey/common/contextkey", "vs/editor/contrib/hover/browser/resizableContentWidget", "vs/platform/configuration/common/configuration", "vs/platform/accessibility/common/accessibility"], "vs/editor/contrib/hover/browser/marginHover": ["require", "exports", "vs/base/browser/dom", "vs/base/common/arrays", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/editor/contrib/markdownRenderer/browser/markdownRenderer", "vs/editor/contrib/hover/browser/hoverOperation", "vs/base/browser/ui/hover/hoverWidget"], "vs/editor/contrib/hover/browser/markdownHoverParticipant": ["require", "exports", "vs/base/browser/dom", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/editor/contrib/markdownRenderer/browser/markdownRenderer", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/languages/language", "vs/editor/contrib/hover/browser/getHover", "vs/nls!vs/editor/contrib/hover/browser/markdownHoverParticipant", "vs/platform/configuration/common/configuration", "vs/platform/opener/common/opener", "vs/editor/common/services/languageFeatures"], "vs/editor/contrib/hover/browser/markerHoverParticipant": ["require", "exports", "vs/base/browser/dom", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/editor/common/core/range", "vs/editor/common/services/languageFeatures", "vs/editor/common/services/markerDecorations", "vs/editor/contrib/codeAction/browser/codeAction", "vs/editor/contrib/codeAction/browser/codeActionController", "vs/editor/contrib/codeAction/common/types", "vs/editor/contrib/gotoError/browser/gotoError", "vs/nls!vs/editor/contrib/hover/browser/markerHoverParticipant", "vs/platform/markers/common/markers", "vs/platform/opener/common/opener", "vs/platform/progress/common/progress"], "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/browser/ui/keybindingLabel/keybindingLabel", "vs/base/common/actions", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/platform", "vs/base/common/themables", "vs/editor/common/core/position", "vs/editor/common/languages", "vs/editor/contrib/inlineCompletions/browser/commandIds", "vs/nls!vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget", "vs/platform/actions/browser/menuEntryActionViewItem", "vs/platform/actions/browser/toolbar", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/telemetry/common/telemetry", "vs/platform/theme/common/iconRegistry", "vs/css!vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget"], "vs/nls!vs/editor/contrib/hover/browser/hover": [], "vs/css!vs/editor/contrib/hover/browser/hover": [], "===anonymous30===": ["vs/editor/contrib/hover/browser/hover.nls", "vs/editor/contrib/hover/browser/hover.nls.keys"], "vs/editor/contrib/hover/browser/hover.nls": [], "vs/editor/contrib/hover/browser/hover.nls.keys": [], "vs/editor/contrib/inlayHints/browser/inlayHintsController": ["require", "exports", "vs/base/browser/dom", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/map", "vs/base/common/types", "vs/base/common/uri", "vs/editor/browser/editorDom", "vs/editor/browser/stableEditorScroll", "vs/editor/common/config/editorOptions", "vs/editor/common/core/editOperation", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/editor/common/model", "vs/editor/common/model/textModel", "vs/editor/common/services/languageFeatureDebounce", "vs/editor/common/services/languageFeatures", "vs/editor/common/services/resolverService", "vs/editor/contrib/gotoSymbol/browser/link/clickLinkGesture", "vs/editor/contrib/inlayHints/browser/inlayHints", "vs/editor/contrib/inlayHints/browser/inlayHintsLocations", "vs/platform/commands/common/commands", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/notification/common/notification", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/inlayHints/browser/inlayHintsHover": ["require", "exports", "vs/base/common/async", "vs/base/common/htmlContent", "vs/editor/common/core/position", "vs/editor/common/model/textModel", "vs/editor/contrib/hover/browser/hoverTypes", "vs/editor/common/languages/language", "vs/editor/common/services/resolverService", "vs/editor/contrib/hover/browser/getHover", "vs/editor/contrib/hover/browser/markdownHoverParticipant", "vs/editor/contrib/inlayHints/browser/inlayHintsController", "vs/platform/configuration/common/configuration", "vs/platform/opener/common/opener", "vs/editor/common/services/languageFeatures", "vs/nls!vs/editor/contrib/inlayHints/browser/inlayHintsHover", "vs/base/common/platform", "vs/editor/contrib/inlayHints/browser/inlayHints", "vs/base/common/arrays"], "vs/editor/common/commands/shiftCommand": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/cursorColumns", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/languages/enterAction", "vs/editor/common/languages/languageConfigurationRegistry"], "vs/editor/common/core/editOperation": ["require", "exports", "vs/editor/common/core/range"], "vs/editor/contrib/indentation/browser/indentUtils": ["require", "exports"], "vs/nls!vs/editor/contrib/indentation/browser/indentation": [], "vs/editor/common/core/indentation": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/cursorColumns"], "vs/editor/common/languages/autoIndent": ["require", "exports", "vs/base/common/strings", "vs/editor/common/languages/languageConfiguration", "vs/editor/common/languages/supports", "vs/editor/common/languages/languageConfigurationRegistry"], "===anonymous31===": ["vs/editor/contrib/indentation/browser/indentation.nls", "vs/editor/contrib/indentation/browser/indentation.nls.keys"], "vs/editor/contrib/indentation/browser/indentation.nls": [], "vs/editor/contrib/indentation/browser/indentation.nls.keys": [], "vs/nls!vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace": [], "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplaceCommand": ["require", "exports", "vs/editor/common/core/selection"], "vs/css!vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace": [], "===anonymous32===": ["vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace.nls", "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace.nls.keys"], "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace.nls": [], "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace.nls.keys": [], "vs/nls!vs/editor/contrib/lineSelection/browser/lineSelection": [], "===anonymous33===": ["vs/editor/contrib/lineSelection/browser/lineSelection.nls", "vs/editor/contrib/lineSelection/browser/lineSelection.nls.keys"], "vs/editor/contrib/lineSelection/browser/lineSelection.nls": [], "vs/editor/contrib/lineSelection/browser/lineSelection.nls.keys": [], "vs/nls!vs/editor/contrib/linkedEditing/browser/linkedEditing": [], "vs/css!vs/editor/contrib/linkedEditing/browser/linkedEditing": [], "===anonymous34===": ["vs/editor/contrib/linkedEditing/browser/linkedEditing.nls", "vs/editor/contrib/linkedEditing/browser/linkedEditing.nls.keys"], "vs/editor/contrib/linkedEditing/browser/linkedEditing.nls": [], "vs/editor/contrib/linkedEditing/browser/linkedEditing.nls.keys": [], "vs/base/common/resources": ["require", "exports", "vs/base/common/extpath", "vs/base/common/network", "vs/base/common/path", "vs/base/common/platform", "vs/base/common/strings", "vs/base/common/uri"], "vs/editor/contrib/links/browser/getLinks": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/types", "vs/base/common/uri", "vs/editor/common/core/range", "vs/editor/common/services/model", "vs/platform/commands/common/commands", "vs/editor/common/services/languageFeatures"], "vs/nls!vs/editor/contrib/links/browser/links": [], "vs/css!vs/editor/contrib/links/browser/links": [], "===anonymous35===": ["vs/editor/contrib/links/browser/links.nls", "vs/editor/contrib/links/browser/links.nls.keys"], "vs/editor/contrib/links/browser/links.nls": [], "vs/editor/contrib/links/browser/links.nls.keys": [], "vs/editor/common/commands/trimTrailingWhitespaceCommand": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/editOperation", "vs/editor/common/core/range"], "vs/editor/contrib/linesOperations/browser/copyLinesCommand": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/editor/contrib/linesOperations/browser/moveLinesCommand": ["require", "exports", "vs/base/common/strings", "vs/editor/common/commands/shiftCommand", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/languages/languageConfiguration", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/contrib/indentation/browser/indentUtils", "vs/editor/common/languages/autoIndent", "vs/editor/common/languages/enterAction"], "vs/editor/contrib/linesOperations/browser/sortLinesCommand": ["require", "exports", "vs/editor/common/core/editOperation", "vs/editor/common/core/range"], "vs/nls!vs/editor/contrib/linesOperations/browser/linesOperations": [], "===anonymous36===": ["vs/editor/contrib/linesOperations/browser/linesOperations.nls", "vs/editor/contrib/linesOperations/browser/linesOperations.nls.keys"], "vs/editor/contrib/linesOperations/browser/linesOperations.nls": [], "vs/editor/contrib/linesOperations/browser/linesOperations.nls.keys": [], "vs/nls!vs/editor/contrib/multicursor/browser/multicursor": [], "vs/editor/contrib/wordHighlighter/browser/highlightDecorations": ["require", "exports", "vs/editor/common/model", "vs/editor/common/model/textModel", "vs/editor/common/languages", "vs/nls!vs/editor/contrib/wordHighlighter/browser/highlightDecorations", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/wordHighlighter/browser/highlightDecorations"], "===anonymous37===": ["vs/editor/contrib/multicursor/browser/multicursor.nls", "vs/editor/contrib/multicursor/browser/multicursor.nls.keys"], "vs/editor/contrib/multicursor/browser/multicursor.nls": [], "vs/editor/contrib/multicursor/browser/multicursor.nls.keys": [], "vs/editor/contrib/parameterHints/browser/parameterHintsModel": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/characterClassifier", "vs/editor/common/languages", "vs/editor/contrib/parameterHints/browser/provideSignatureHelp"], "vs/editor/contrib/parameterHints/browser/provideSignatureHelp": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/types", "vs/base/common/uri", "vs/editor/common/core/position", "vs/editor/common/languages", "vs/editor/common/services/languageFeatures", "vs/editor/common/services/resolverService", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey"], "vs/nls!vs/editor/contrib/parameterHints/browser/parameterHints": [], "vs/editor/contrib/parameterHints/browser/parameterHintsWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/aria/aria", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/codicons", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/base/common/types", "vs/editor/common/languages/language", "vs/editor/contrib/markdownRenderer/browser/markdownRenderer", "vs/editor/contrib/parameterHints/browser/provideSignatureHelp", "vs/nls!vs/editor/contrib/parameterHints/browser/parameterHintsWidget", "vs/platform/contextkey/common/contextkey", "vs/platform/opener/common/opener", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/iconRegistry", "vs/base/common/themables", "vs/css!vs/editor/contrib/parameterHints/browser/parameterHints"], "===anonymous38===": ["vs/editor/contrib/parameterHints/browser/parameterHints.nls", "vs/editor/contrib/parameterHints/browser/parameterHints.nls.keys"], "vs/editor/contrib/parameterHints/browser/parameterHints.nls": [], "vs/editor/contrib/parameterHints/browser/parameterHints.nls.keys": [], "vs/editor/browser/services/bulkEditService": ["require", "exports", "vs/platform/instantiation/common/instantiation", "vs/base/common/uri", "vs/base/common/types"], "vs/editor/common/services/textResourceConfiguration": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/nls!vs/editor/contrib/rename/browser/rename": [], "vs/editor/contrib/rename/browser/renameInputField": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/common/core/position", "vs/nls!vs/editor/contrib/rename/browser/renameInputField", "vs/platform/contextkey/common/contextkey", "vs/platform/keybinding/common/keybinding", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/rename/browser/renameInputField"], "===anonymous39===": ["vs/editor/contrib/rename/browser/rename.nls", "vs/editor/contrib/rename/browser/rename.nls.keys"], "vs/editor/contrib/rename/browser/rename.nls": [], "vs/editor/contrib/rename/browser/rename.nls.keys": [], "vs/editor/common/services/semanticTokensProviderStyling": ["require", "exports", "vs/editor/common/encodedTokenAttributes", "vs/platform/theme/common/themeService", "vs/platform/log/common/log", "vs/editor/common/tokens/sparseMultilineTokens", "vs/editor/common/languages/language"], "vs/editor/contrib/semanticTokens/common/getSemanticTokens": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/uri", "vs/editor/common/services/model", "vs/platform/commands/common/commands", "vs/base/common/types", "vs/editor/common/services/semanticTokensDto", "vs/editor/common/core/range", "vs/editor/common/services/languageFeatures"], "vs/editor/common/services/semanticTokensStyling": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/semanticTokens/common/semanticTokensConfig": ["require", "exports"], "vs/editor/contrib/suggest/browser/suggest": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/filters", "vs/base/common/lifecycle", "vs/base/common/stopwatch", "vs/base/common/types", "vs/base/common/uri", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/services/resolverService", "vs/editor/contrib/snippet/browser/snippetParser", "vs/nls!vs/editor/contrib/suggest/browser/suggest", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/editor/common/services/languageFeatures", "vs/platform/history/browser/contextScopedHistoryWidget"], "vs/nls!vs/editor/contrib/snippet/browser/snippetController2": [], "vs/editor/contrib/snippet/browser/snippetSession": ["require", "exports", "vs/base/common/arrays", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/common/core/editOperation", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/model/textModel", "vs/platform/label/common/label", "vs/platform/workspace/common/workspace", "vs/editor/contrib/snippet/browser/snippetParser", "vs/editor/contrib/snippet/browser/snippetVariables", "vs/css!vs/editor/contrib/snippet/browser/snippetSession"], "===anonymous40===": ["vs/editor/contrib/snippet/browser/snippetController2.nls", "vs/editor/contrib/snippet/browser/snippetController2.nls.keys"], "vs/editor/contrib/snippet/browser/snippetController2.nls": [], "vs/editor/contrib/snippet/browser/snippetController2.nls.keys": [], "vs/editor/contrib/smartSelect/browser/bracketSelections": ["require", "exports", "vs/base/common/linkedList", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/contrib/smartSelect/browser/wordSelections": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/range"], "vs/nls!vs/editor/contrib/smartSelect/browser/smartSelect": [], "===anonymous41===": ["vs/editor/contrib/smartSelect/browser/smartSelect.nls", "vs/editor/contrib/smartSelect/browser/smartSelect.nls.keys"], "vs/editor/contrib/smartSelect/browser/smartSelect.nls": [], "vs/editor/contrib/smartSelect/browser/smartSelect.nls.keys": [], "vs/editor/contrib/stickyScroll/browser/stickyScrollActions": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/nls!vs/editor/contrib/stickyScroll/browser/stickyScrollActions", "vs/platform/action/common/actionCommonCategories", "vs/platform/actions/common/actions", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/stickyScroll/browser/stickyScrollController"], "vs/editor/contrib/stickyScroll/browser/stickyScrollController": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/stickyScroll/browser/stickyScrollWidget", "vs/editor/contrib/stickyScroll/browser/stickyScrollProvider", "vs/platform/instantiation/common/instantiation", "vs/platform/contextview/browser/contextView", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/gotoSymbol/browser/link/clickLinkGesture", "vs/editor/common/core/range", "vs/editor/contrib/gotoSymbol/browser/goToSymbol", "vs/editor/contrib/inlayHints/browser/inlayHintsLocations", "vs/editor/common/core/position", "vs/base/common/cancellation", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/services/languageFeatureDebounce", "vs/base/browser/dom", "vs/editor/contrib/stickyScroll/browser/stickyScrollElement", "vs/base/browser/mouseEvent", "vs/editor/contrib/folding/browser/folding", "vs/editor/contrib/folding/browser/foldingModel"], "vs/nls!vs/editor/contrib/tokenization/browser/tokenization": [], "===anonymous42===": ["vs/editor/contrib/tokenization/browser/tokenization.nls", "vs/editor/contrib/tokenization/browser/tokenization.nls.keys"], "vs/editor/contrib/tokenization/browser/tokenization.nls": [], "vs/editor/contrib/tokenization/browser/tokenization.nls.keys": [], "vs/base/common/keybindings": ["require", "exports", "vs/base/common/errors"], "vs/editor/contrib/snippet/browser/snippetParser": ["require", "exports"], "vs/editor/contrib/suggest/browser/suggestMemory": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle", "vs/base/common/map", "vs/base/common/ternarySearchTree", "vs/editor/common/languages", "vs/platform/configuration/common/configuration", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/storage/common/storage"], "vs/editor/contrib/suggest/browser/wordContextKey": ["require", "exports", "vs/platform/contextkey/common/contextkey"], "vs/nls!vs/editor/contrib/suggest/browser/suggestController": [], "vs/editor/contrib/suggest/browser/suggestAlternatives": ["require", "exports", "vs/platform/contextkey/common/contextkey"], "vs/editor/contrib/suggest/browser/suggestCommitCharacters": ["require", "exports", "vs/base/common/arrays", "vs/base/common/lifecycle", "vs/editor/common/core/characterClassifier"], "vs/editor/contrib/suggest/browser/suggestModel": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/common/core/selection", "vs/editor/common/services/editorW<PERSON>ker", "vs/editor/contrib/suggest/browser/wordDistance", "vs/platform/clipboard/common/clipboardService", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/log/common/log", "vs/platform/telemetry/common/telemetry", "vs/editor/contrib/suggest/browser/completionModel", "vs/editor/contrib/suggest/browser/suggest", "vs/editor/common/services/languageFeatures", "vs/base/common/filters", "vs/base/common/types", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys", "vs/editor/contrib/snippet/browser/snippetController2", "vs/platform/environment/common/environment"], "vs/editor/contrib/suggest/browser/suggestOvertypingCapturer": ["require", "exports", "vs/base/common/lifecycle"], "vs/editor/contrib/suggest/browser/suggestWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/list/listWidget", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/numbers", "vs/base/common/strings", "vs/editor/browser/widget/embeddedCodeEditorWidget", "vs/editor/contrib/suggest/browser/suggestWidgetStatus", "vs/nls!vs/editor/contrib/suggest/browser/suggestWidget", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/storage/common/storage", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/theme", "vs/platform/theme/common/themeService", "vs/base/browser/ui/resizable/resizable", "vs/editor/contrib/suggest/browser/suggest", "vs/editor/contrib/suggest/browser/suggestWidgetDetails", "vs/editor/contrib/suggest/browser/suggestWidgetRenderer", "vs/platform/theme/browser/defaultStyles", "vs/base/browser/ui/aria/aria", "vs/base/browser/ui/codicons/codiconStyles", "vs/css!vs/editor/contrib/suggest/browser/media/suggest", "vs/editor/contrib/symbolIcons/browser/symbolIcons"], "===anonymous43===": ["vs/editor/contrib/suggest/browser/suggestController.nls", "vs/editor/contrib/suggest/browser/suggestController.nls.keys"], "vs/editor/contrib/suggest/browser/suggestController.nls": [], "vs/editor/contrib/suggest/browser/suggestController.nls.keys": [], "vs/base/common/filters": ["require", "exports", "vs/base/common/map", "vs/base/common/strings"], "vs/editor/contrib/suggest/browser/completionModel": ["require", "exports", "vs/base/common/arrays", "vs/base/common/filters", "vs/base/common/strings"], "vs/editor/contrib/suggest/browser/wordDistance": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/core/range", "vs/editor/contrib/smartSelect/browser/bracketSelections"], "vs/nls!vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode": [], "===anonymous44===": ["vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode.nls", "vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode.nls.keys"], "vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode.nls": [], "vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode.nls.keys": [], "vs/editor/common/viewModel/viewModelDecorations": ["require", "exports", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/viewModel", "vs/editor/common/config/editorOptions"], "vs/editor/contrib/unicodeHighlighter/browser/bannerController": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionbar", "vs/base/common/actions", "vs/base/common/lifecycle", "vs/editor/contrib/markdownRenderer/browser/markdownRenderer", "vs/platform/instantiation/common/instantiation", "vs/platform/opener/browser/link", "vs/platform/theme/common/iconRegistry", "vs/base/common/themables", "vs/css!vs/editor/contrib/unicodeHighlighter/browser/bannerController"], "vs/nls!vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter": [], "vs/platform/workspace/common/workspaceTrust": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/css!vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter": [], "===anonymous45===": ["vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter.nls", "vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter.nls.keys"], "vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter.nls": [], "vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter.nls.keys": [], "vs/nls!vs/editor/contrib/wordHighlighter/browser/wordHighlighter": [], "===anonymous46===": ["vs/editor/contrib/wordHighlighter/browser/wordHighlighter.nls", "vs/editor/contrib/wordHighlighter/browser/wordHighlighter.nls.keys"], "vs/editor/contrib/wordHighlighter/browser/wordHighlighter.nls": [], "vs/editor/contrib/wordHighlighter/browser/wordHighlighter.nls.keys": [], "vs/nls!vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators": [], "===anonymous47===": ["vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators.nls", "vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators.nls.keys"], "vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators.nls": [], "vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators.nls.keys": [], "vs/editor/common/core/wordCharacterClassifier": ["require", "exports", "vs/editor/common/core/characterClassifier"], "vs/nls!vs/editor/contrib/wordOperations/browser/wordOperations": [], "===anonymous48===": ["vs/editor/contrib/wordOperations/browser/wordOperations.nls", "vs/editor/contrib/wordOperations/browser/wordOperations.nls.keys"], "vs/editor/contrib/wordOperations/browser/wordOperations.nls": [], "vs/editor/contrib/wordOperations/browser/wordOperations.nls.keys": [], "vs/nls!vs/editor/contrib/readOnlyMessage/browser/contribution": [], "===anonymous49===": ["vs/editor/contrib/readOnlyMessage/browser/contribution.nls", "vs/editor/contrib/readOnlyMessage/browser/contribution.nls.keys"], "vs/editor/contrib/readOnlyMessage/browser/contribution.nls": [], "vs/editor/contrib/readOnlyMessage/browser/contribution.nls.keys": [], "vs/nls!vs/editor/common/standaloneStrings": [], "===anonymous50===": ["vs/editor/common/standaloneStrings.nls", "vs/editor/common/standaloneStrings.nls.keys"], "vs/editor/common/standaloneStrings.nls": [], "vs/editor/common/standaloneStrings.nls.keys": [], "vs/css!vs/base/browser/ui/codicons/codicon/codicon": [], "vs/css!vs/base/browser/ui/codicons/codicon/codicon-modifiers": [], "vs/editor/common/tokenizationRegistry": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/nls!vs/editor/common/languages": [], "===anonymous51===": ["vs/editor/common/languages.nls", "vs/editor/common/languages.nls.keys"], "vs/editor/common/languages.nls": [], "vs/editor/common/languages.nls.keys": [], "vs/nls!vs/platform/quickinput/browser/helpQuickAccess": [], "===anonymous52===": ["vs/platform/quickinput/browser/helpQuickAccess.nls", "vs/platform/quickinput/browser/helpQuickAccess.nls.keys"], "vs/platform/quickinput/browser/helpQuickAccess.nls": [], "vs/platform/quickinput/browser/helpQuickAccess.nls.keys": [], "vs/nls!vs/editor/common/editorContextKeys": [], "===anonymous53===": ["vs/editor/common/editorContextKeys.nls", "vs/editor/common/editorContextKeys.nls.keys"], "vs/editor/common/editorContextKeys.nls": [], "vs/editor/common/editorContextKeys.nls.keys": [], "vs/base/common/fuzzyScorer": ["require", "exports", "vs/base/common/filters", "vs/base/common/path", "vs/base/common/platform", "vs/base/common/strings"], "vs/editor/contrib/quickAccess/browser/editorNavigationQuickAccess": ["require", "exports", "vs/base/common/functional", "vs/base/common/lifecycle", "vs/editor/browser/editorBrowser", "vs/editor/common/model", "vs/editor/common/core/editorColorRegistry", "vs/platform/theme/common/themeService", "vs/base/browser/ui/aria/aria"], "vs/nls!vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess": [], "vs/base/common/arraysFind": ["require", "exports"], "===anonymous54===": ["vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess.nls", "vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess.nls.keys"], "vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess.nls": [], "vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess.nls.keys": [], "vs/base/common/map": ["require", "exports"], "vs/platform/instantiation/common/extensions": ["require", "exports", "vs/platform/instantiation/common/descriptors"], "vs/nls!vs/editor/contrib/symbolIcons/browser/symbolIcons": [], "vs/css!vs/editor/contrib/symbolIcons/browser/symbolIcons": [], "===anonymous55===": ["vs/editor/contrib/symbolIcons/browser/symbolIcons.nls", "vs/editor/contrib/symbolIcons/browser/symbolIcons.nls.keys"], "vs/editor/contrib/symbolIcons/browser/symbolIcons.nls": [], "vs/editor/contrib/symbolIcons/browser/symbolIcons.nls.keys": [], "vs/base/common/iconLabels": ["require", "exports", "vs/base/common/filters", "vs/base/common/strings", "vs/base/common/themables"], "vs/platform/quickinput/browser/commandsQuickAccess": ["require", "exports", "vs/base/common/errorMessage", "vs/base/common/errors", "vs/base/common/filters", "vs/base/common/functional", "vs/base/common/lifecycle", "vs/base/common/map", "vs/base/common/tfIdf", "vs/nls!vs/platform/quickinput/browser/commandsQuickAccess", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/dialogs/common/dialogs", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/quickinput/browser/pickerQuickAccess", "vs/platform/storage/common/storage", "vs/platform/telemetry/common/telemetry"], "vs/nls!vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess": [], "===anonymous56===": ["vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess.nls", "vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess.nls.keys"], "vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess.nls": [], "vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess.nls.keys": [], "vs/nls!vs/editor/contrib/gotoSymbol/browser/peek/referencesController": [], "vs/platform/list/browser/listService": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/list/listPaging", "vs/base/browser/ui/list/listWidget", "vs/base/browser/ui/table/tableWidget", "vs/base/browser/ui/tree/abstractTree", "vs/base/browser/ui/tree/asyncDataTree", "vs/base/browser/ui/tree/dataTree", "vs/base/browser/ui/tree/objectTree", "vs/base/common/event", "vs/base/common/lifecycle", "vs/nls!vs/platform/list/browser/listService", "vs/platform/configuration/common/configuration", "vs/platform/configuration/common/configurationRegistry", "vs/platform/contextkey/common/contextkey", "vs/platform/contextkey/common/contextkeys", "vs/platform/contextview/browser/contextView", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/registry/common/platform", "vs/platform/theme/browser/defaultStyles"], "vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/splitview/splitview", "vs/base/common/color", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/network", "vs/base/common/resources", "vs/editor/browser/widget/embeddedCodeEditorWidget", "vs/editor/common/core/range", "vs/editor/common/model/textModel", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/languages/modesRegistry", "vs/editor/common/languages/language", "vs/editor/common/services/resolverService", "vs/editor/contrib/gotoSymbol/browser/peek/referencesTree", "vs/editor/contrib/peekView/browser/peekView", "vs/nls!vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/label/common/label", "vs/platform/list/browser/listService", "vs/platform/theme/common/themeService", "vs/platform/undoRedo/common/undoRedo", "vs/editor/contrib/gotoSymbol/browser/referencesModel", "vs/css!vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget"], "===anonymous57===": ["vs/editor/contrib/gotoSymbol/browser/peek/referencesController.nls", "vs/editor/contrib/gotoSymbol/browser/peek/referencesController.nls.keys"], "vs/editor/contrib/gotoSymbol/browser/peek/referencesController.nls": [], "vs/editor/contrib/gotoSymbol/browser/peek/referencesController.nls.keys": [], "vs/base/common/severity": ["require", "exports", "vs/base/common/strings"], "vs/base/parts/storage/common/storage": ["require", "exports", "vs/base/common/async", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/marshalling", "vs/base/common/types"], "vs/platform/contextkey/common/scanner": ["require", "exports", "vs/base/common/errors", "vs/nls!vs/platform/contextkey/common/scanner"], "vs/nls!vs/platform/contextkey/common/contextkey": [], "===anonymous58===": ["vs/platform/contextkey/common/contextkey.nls", "vs/platform/contextkey/common/contextkey.nls.keys"], "vs/platform/contextkey/common/contextkey.nls": [], "vs/platform/contextkey/common/contextkey.nls.keys": [], "vs/editor/common/languages/supports/tokenization": ["require", "exports", "vs/base/common/color"], "vs/editor/standalone/common/themes": ["require", "exports", "vs/editor/common/core/editorColorRegistry", "vs/platform/theme/common/colorRegistry"], "vs/platform/theme/browser/iconsStyleSheet": ["require", "exports", "vs/base/browser/dom", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/themables", "vs/platform/theme/common/iconRegistry"], "vs/base/common/process": ["require", "exports", "vs/base/common/platform"], "vs/base/common/uint": ["require", "exports"], "vs/editor/common/diff/linesDiffComputer": ["require", "exports"], "vs/editor/common/diff/rangeMapping": ["require", "exports", "vs/editor/common/core/lineRange"], "vs/editor/common/core/lineRange": ["require", "exports", "vs/base/common/errors", "vs/editor/common/core/offsetRange", "vs/editor/common/core/range", "vs/base/common/arraysFind"], "vs/editor/common/core/offsetRange": ["require", "exports", "vs/base/common/errors"], "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/diffAlgorithm": ["require", "exports", "vs/base/common/arrays", "vs/base/common/errors", "vs/editor/common/core/offsetRange"], "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/dynamicProgrammingDiffing": ["require", "exports", "vs/editor/common/core/offsetRange", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/diffAlgorithm", "vs/editor/common/diff/defaultLinesDiffComputer/utils"], "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/myersDiffAlgorithm": ["require", "exports", "vs/editor/common/core/offsetRange", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/diffAlgorithm"], "vs/editor/common/diff/defaultLinesDiffComputer/computeMovedLines": ["require", "exports", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/diffAlgorithm", "vs/editor/common/diff/rangeMapping", "vs/base/common/arrays", "vs/base/common/arraysFind", "vs/base/common/map", "vs/editor/common/core/lineRange", "vs/editor/common/core/offsetRange", "vs/editor/common/diff/defaultLinesDiffComputer/linesSliceCharSequence", "vs/editor/common/diff/defaultLinesDiffComputer/utils", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/myersDiffAlgorithm"], "vs/editor/common/diff/defaultLinesDiffComputer/heuristicSequenceOptimizations": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/core/offsetRange", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/diffAlgorithm"], "vs/editor/common/diff/defaultLinesDiffComputer/linesSliceCharSequence": ["require", "exports", "vs/base/common/arraysFind", "vs/editor/common/core/offsetRange", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/diff/defaultLinesDiffComputer/utils"], "vs/editor/common/diff/defaultLinesDiffComputer/lineSequence": ["require", "exports"], "vs/editor/browser/config/charWidthReader": ["require", "exports", "vs/editor/browser/config/domFontInfo"], "vs/editor/browser/services/editorWorkerService": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle", "vs/base/common/worker/simpleWorker", "vs/base/browser/defaultWorkerFactory", "vs/editor/common/core/range", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/services/editorSimpleWorker", "vs/editor/common/services/model", "vs/editor/common/services/textResourceConfiguration", "vs/base/common/arrays", "vs/platform/log/common/log", "vs/base/common/stopwatch", "vs/base/common/errors", "vs/editor/common/services/languageFeatures", "vs/editor/common/diff/linesDiffComputer", "vs/editor/common/diff/rangeMapping", "vs/editor/common/core/lineRange"], "vs/editor/common/languages/languageConfiguration": ["require", "exports"], "vs/editor/common/languages/supports": ["require", "exports"], "vs/editor/common/languages/supports/characterPair": ["require", "exports", "vs/editor/common/languages/languageConfiguration"], "vs/editor/common/languages/supports/electricCharacter": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/languages/supports", "vs/editor/common/languages/supports/richEditBrackets"], "vs/editor/common/languages/supports/indentRules": ["require", "exports"], "vs/editor/common/languages/supports/onEnter": ["require", "exports", "vs/base/common/errors", "vs/base/common/strings", "vs/editor/common/languages/languageConfiguration"], "vs/editor/common/languages/supports/richEditBrackets": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/stringBuilder", "vs/editor/common/core/range"], "vs/editor/common/languages/supports/languageBracketsConfiguration": ["require", "exports", "vs/base/common/cache"], "vs/nls!vs/editor/common/languages/modesRegistry": [], "vs/base/common/mime": ["require", "exports"], "===anonymous59===": ["vs/editor/common/languages/modesRegistry.nls", "vs/editor/common/languages/modesRegistry.nls.keys"], "vs/editor/common/languages/modesRegistry.nls": [], "vs/editor/common/languages/modesRegistry.nls.keys": [], "vs/base/browser/trustedTypes": ["require", "exports", "vs/base/common/errors"], "vs/editor/common/tokens/lineTokens": ["require", "exports", "vs/editor/common/encodedTokenAttributes"], "vs/editor/common/viewLayout/viewLineRenderer": ["require", "exports", "vs/nls!vs/editor/common/viewLayout/viewLineRenderer", "vs/base/common/strings", "vs/editor/common/core/stringBuilder", "vs/editor/common/viewLayout/lineDecorations", "vs/editor/common/viewLayout/linePart"], "vs/editor/common/viewModel": ["require", "exports", "vs/base/common/arrays", "vs/base/common/strings", "vs/editor/common/core/range"], "vs/editor/standalone/browser/standaloneCodeEditorService": ["require", "exports", "vs/base/browser/dom", "vs/base/common/network", "vs/editor/browser/services/abstractCodeEditorService", "vs/editor/browser/services/codeEditorService", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/extensions", "vs/platform/theme/common/themeService"], "vs/platform/audioCues/browser/audioCueService": ["require", "exports", "vs/platform/instantiation/common/instantiation", "vs/nls!vs/platform/audioCues/browser/audioCueService"], "vs/platform/configuration/common/configurationModels": ["require", "exports", "vs/base/common/arrays", "vs/base/common/map", "vs/base/common/objects", "vs/base/common/types", "vs/base/common/uri", "vs/platform/configuration/common/configuration", "vs/platform/configuration/common/configurationRegistry", "vs/platform/registry/common/platform"], "vs/platform/keybinding/common/abstractKeybindingService": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/ime", "vs/base/common/lifecycle", "vs/nls!vs/platform/keybinding/common/abstractKeybindingService", "vs/platform/keybinding/common/keybindingResolver"], "vs/platform/keybinding/common/keybindingResolver": ["require", "exports", "vs/platform/contextkey/common/contextkey"], "vs/platform/keybinding/common/resolvedKeybindingItem": ["require", "exports"], "vs/platform/keybinding/common/usLayoutResolvedKeybinding": ["require", "exports", "vs/base/common/keyCodes", "vs/base/common/keybindings", "vs/platform/keybinding/common/baseResolvedKeybinding", "vs/platform/keybinding/common/resolvedKeybindingItem"], "vs/platform/label/common/label": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/layout/browser/layoutService": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/contextview/browser/contextViewService": ["require", "exports", "vs/base/browser/ui/contextview/contextview", "vs/base/common/lifecycle", "vs/platform/layout/browser/layoutService"], "vs/editor/common/services/languageService": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/services/languagesRegistry", "vs/base/common/arrays", "vs/editor/common/languages", "vs/editor/common/languages/modesRegistry"], "vs/platform/contextview/browser/contextMenuService": ["require", "exports", "vs/base/browser/dom", "vs/base/common/actions", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/actions/browser/menuEntryActionViewItem", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/keybinding/common/keybinding", "vs/platform/notification/common/notification", "vs/platform/telemetry/common/telemetry", "vs/platform/contextview/browser/contextMenuHandler", "vs/platform/contextview/browser/contextView"], "vs/editor/browser/services/openerService": ["require", "exports", "vs/base/browser/dom", "vs/base/common/cancellation", "vs/base/common/linkedList", "vs/base/common/map", "vs/base/common/marshalling", "vs/base/common/network", "vs/base/common/resources", "vs/base/common/uri", "vs/editor/browser/services/codeEditorService", "vs/platform/commands/common/commands", "vs/platform/editor/common/editor", "vs/platform/opener/common/opener"], "vs/editor/common/services/markerDecorationsService": ["require", "exports", "vs/platform/markers/common/markers", "vs/base/common/lifecycle", "vs/editor/common/model", "vs/platform/theme/common/themeService", "vs/editor/common/core/editorColorRegistry", "vs/editor/common/services/model", "vs/editor/common/core/range", "vs/base/common/network", "vs/base/common/event", "vs/platform/theme/common/colorRegistry", "vs/base/common/map", "vs/base/common/collections"], "vs/editor/common/services/markerDecorations": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/common/services/modelService": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/editor/common/model/textModel", "vs/editor/common/core/textModelDefaults", "vs/editor/common/languages/modesRegistry", "vs/editor/common/languages/language", "vs/editor/common/services/textResourceConfiguration", "vs/platform/configuration/common/configuration", "vs/platform/undoRedo/common/undoRedo", "vs/base/common/hash", "vs/editor/common/model/editStack", "vs/base/common/network", "vs/base/common/objects", "vs/editor/common/languages/languageConfigurationRegistry"], "vs/editor/standalone/browser/quickInput/standaloneQuickInputService": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/platform/theme/common/themeService", "vs/base/common/cancellation", "vs/platform/instantiation/common/instantiation", "vs/platform/contextkey/common/contextkey", "vs/editor/standalone/browser/standaloneLayoutService", "vs/editor/browser/services/codeEditorService", "vs/platform/quickinput/browser/quickInputService", "vs/base/common/functional", "vs/css!vs/editor/standalone/browser/quickInput/standaloneQuickInput"], "vs/platform/accessibility/browser/accessibilityService": ["require", "exports", "vs/base/browser/dom", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/accessibility/common/accessibility", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/layout/browser/layoutService"], "vs/platform/actions/common/menuService": ["require", "exports", "vs/base/common/async", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/base/common/actions", "vs/platform/storage/common/storage", "vs/base/common/arrays", "vs/nls!vs/platform/actions/common/menuService"], "vs/platform/clipboard/browser/clipboardService": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dom", "vs/base/common/async", "vs/base/common/lifecycle", "vs/platform/layout/browser/layoutService", "vs/platform/log/common/log"], "vs/platform/contextkey/browser/contextKeyService": ["require", "exports", "vs/base/common/event", "vs/base/common/iterator", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/base/common/ternarySearchTree", "vs/base/common/uri", "vs/nls!vs/platform/contextkey/browser/contextKeyService", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey"], "vs/platform/instantiation/common/descriptors": ["require", "exports"], "vs/platform/instantiation/common/instantiationService": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/platform/instantiation/common/descriptors", "vs/platform/instantiation/common/graph", "vs/platform/instantiation/common/instantiation", "vs/platform/instantiation/common/serviceCollection", "vs/base/common/linkedList"], "vs/platform/markers/common/markerService": ["require", "exports", "vs/base/common/arrays", "vs/base/common/event", "vs/base/common/iterator", "vs/base/common/map", "vs/base/common/network", "vs/base/common/uri", "vs/platform/markers/common/markers"], "vs/platform/configuration/common/configurations": ["require", "exports", "vs/base/common/lifecycle", "vs/platform/configuration/common/configurationModels", "vs/platform/configuration/common/configurationRegistry", "vs/platform/registry/common/platform"], "vs/platform/log/common/logService": ["require", "exports", "vs/base/common/lifecycle", "vs/platform/log/common/log"], "vs/platform/environment/common/environment": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/standalone/browser/standaloneLayoutService": ["require", "exports", "vs/base/browser/dom", "vs/base/common/event", "vs/platform/layout/browser/layoutService", "vs/editor/browser/services/codeEditorService", "vs/platform/instantiation/common/extensions"], "vs/platform/undoRedo/common/undoRedoService": ["require", "exports", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/network", "vs/base/common/severity", "vs/nls!vs/platform/undoRedo/common/undoRedoService", "vs/platform/dialogs/common/dialogs", "vs/platform/instantiation/common/extensions", "vs/platform/notification/common/notification", "vs/platform/undoRedo/common/undoRedo"], "vs/editor/common/services/semanticTokensStylingService": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/common/languages/language", "vs/platform/theme/common/themeService", "vs/platform/log/common/log", "vs/editor/common/services/semanticTokensProviderStyling", "vs/editor/common/services/semanticTokensStyling", "vs/platform/instantiation/common/extensions"], "vs/editor/common/services/languageFeaturesService": ["require", "exports", "vs/editor/common/languageFeatureRegistry", "vs/editor/common/services/languageFeatures", "vs/platform/instantiation/common/extensions"], "vs/nls!vs/platform/markers/common/markers": [], "===anonymous60===": ["vs/platform/markers/common/markers.nls", "vs/platform/markers/common/markers.nls.keys"], "vs/platform/markers/common/markers.nls": [], "vs/platform/markers/common/markers.nls.keys": [], "vs/editor/standalone/common/monarch/monarchCommon": ["require", "exports"], "vs/css!vs/base/browser/ui/aria/aria": [], "vs/editor/contrib/editorState/browser/keybindingCancellation": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/platform/contextkey/common/contextkey", "vs/base/common/cancellation", "vs/base/common/linkedList", "vs/platform/instantiation/common/instantiation", "vs/platform/instantiation/common/extensions", "vs/nls!vs/editor/contrib/editorState/browser/keybindingCancellation"], "vs/base/browser/iframe": ["require", "exports"], "vs/editor/common/commands/surroundSelectionCommand": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/editor/common/languages/enterAction": ["require", "exports", "vs/editor/common/languages/languageConfiguration", "vs/editor/common/languages/languageConfigurationRegistry"], "vs/base/common/observable": ["require", "exports", "vs/base/common/observableInternal/base", "vs/base/common/observableInternal/derived", "vs/base/common/observableInternal/autorun", "vs/base/common/observableInternal/utils", "vs/base/common/observableInternal/logging"], "vs/editor/browser/widget/diffEditor/accessibleDiffViewer": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/trustedTypes", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/actions", "vs/base/common/arrays", "vs/base/common/codicons", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/themables", "vs/editor/browser/config/domFontInfo", "vs/editor/browser/widget/diffEditor/utils", "vs/editor/common/config/editorOptions", "vs/editor/common/core/lineRange", "vs/editor/common/core/offsetRange", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/diff/rangeMapping", "vs/editor/common/languages/language", "vs/editor/common/tokens/lineTokens", "vs/editor/common/viewLayout/viewLineRenderer", "vs/editor/common/viewModel", "vs/nls!vs/editor/browser/widget/diffEditor/accessibleDiffViewer", "vs/platform/audioCues/browser/audioCueService", "vs/platform/instantiation/common/instantiation", "vs/platform/theme/common/iconRegistry", "vs/css!vs/editor/browser/widget/diffEditor/accessibleDiff<PERSON>iewer"], "vs/editor/browser/widget/diffEditor/diffEditorDecorations": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/editor/browser/widget/diffEditor/decorations", "vs/editor/browser/widget/diffEditor/movedBlocksLines", "vs/editor/browser/widget/diffEditor/utils", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/browser/widget/diffEditor/diffEditorSash": ["require", "exports", "vs/base/browser/ui/sash/sash", "vs/base/common/lifecycle", "vs/base/common/observable"], "vs/editor/browser/widget/diffEditor/hideUnchangedRegionsFeature": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/common/arrays", "vs/base/common/codicons", "vs/base/common/event", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/themables", "vs/base/common/types", "vs/editor/browser/widget/diffEditor/outlineModel", "vs/editor/browser/widget/diffEditor/utils", "vs/editor/common/core/lineRange", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/editor/common/services/languageFeatures", "vs/nls!vs/editor/browser/widget/diffEditor/hideUnchangedRegionsFeature"], "vs/editor/browser/widget/diffEditor/lineAlignment": ["require", "exports", "vs/base/browser/dom", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/themables", "vs/base/common/types", "vs/editor/browser/config/domFontInfo", "vs/editor/browser/stableEditorScroll", "vs/editor/browser/widget/diffEditor/decorations", "vs/editor/browser/widget/diffEditor/diffEditorViewModel", "vs/editor/browser/widget/diffEditor/inlineDiffDeletedCodeMargin", "vs/editor/browser/widget/diffEditor/renderLines", "vs/editor/browser/widget/diffEditor/utils", "vs/editor/common/core/lineRange", "vs/editor/common/core/position", "vs/editor/common/viewModel", "vs/platform/clipboard/common/clipboardService", "vs/platform/contextview/browser/contextView"], "vs/editor/browser/widget/diffEditor/movedBlocksLines": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionbar", "vs/base/common/actions", "vs/base/common/arrays", "vs/base/common/arraysFind", "vs/base/common/codicons", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/themables", "vs/editor/browser/widget/diffEditor/utils", "vs/editor/common/core/offsetRange", "vs/nls!vs/editor/browser/widget/diffEditor/movedBlocksLines"], "vs/editor/browser/widget/diffEditor/overviewRulerPart": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/ui/scrollbar/scrollbarState", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/editor/browser/widget/diffEditor/utils", "vs/editor/common/core/position", "vs/editor/common/viewModel/overviewZoneManager", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/browser/widget/diffEditor/utils": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/hotReload", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/editor/browser/config/elementSizeObserver"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/range"], "vs/editor/browser/widget/diffEditor/delegatingEditorImpl": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/editor/browser/widget/diffEditor/diffEditorEditors": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/editor/browser/widget/diffEditor/overviewRulerPart", "vs/editor/common/config/editorOptions", "vs/nls!vs/editor/browser/widget/diffEditor/diffEditorEditors", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding"], "vs/editor/browser/widget/diffEditor/diffEditorOptions": ["require", "exports", "vs/base/common/observable", "vs/editor/common/config/diffEditor", "vs/editor/common/config/editorOptions"], "vs/editor/browser/widget/diffEditor/diffEditorViewModel": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/editor/browser/widget/diffEditor/diffProviderFactoryService", "vs/editor/browser/widget/diffEditor/utils", "vs/editor/common/core/lineRange", "vs/editor/common/diff/defaultLinesDiffComputer/defaultLinesDiffComputer", "vs/editor/common/diff/rangeMapping", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/beforeEditPositionMapper", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/combineTextEditInfos", "vs/editor/common/diff/defaultLinesDiffComputer/heuristicSequenceOptimizations"], "vs/css!vs/editor/browser/widget/diffEditor/style": [], "vs/editor/browser/widget/diffEditor/colors": ["require", "exports", "vs/nls!vs/editor/browser/widget/diffEditor/colors", "vs/platform/theme/common/colorRegistry"], "vs/base/common/symbols": ["require", "exports"], "vs/editor/common/core/eolCounter": ["require", "exports"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsImpl": ["require", "exports", "vs/base/common/arrays", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/range", "vs/editor/common/languages/supports", "vs/editor/common/languages/supports/richEditBrackets", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/bracketPairsTree"], "vs/editor/common/model/bracketPairsTextModelPart/colorizedBracketPairsDecorationProvider": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/range", "vs/editor/common/core/editorColorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/common/model/editStack": ["require", "exports", "vs/nls!vs/editor/common/model/editStack", "vs/base/common/errors", "vs/editor/common/core/selection", "vs/base/common/uri", "vs/editor/common/core/textChange", "vs/base/common/buffer", "vs/base/common/resources"], "vs/editor/common/model/guidesTextModelPart": ["require", "exports", "vs/base/common/arraysFind", "vs/base/common/strings", "vs/editor/common/core/cursorColumns", "vs/editor/common/core/range", "vs/editor/common/model/textModelPart", "vs/editor/common/model/utils", "vs/editor/common/textModelGuides", "vs/base/common/errors"], "vs/editor/common/model/indentationGuesser": ["require", "exports"], "vs/editor/common/model/intervalTree": ["require", "exports"], "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBuffer": ["require", "exports", "vs/base/common/event", "vs/base/common/strings", "vs/editor/common/core/range", "vs/editor/common/model", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeBase", "vs/editor/common/core/eolCounter", "vs/editor/common/core/textChange", "vs/base/common/lifecycle"], "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBufferBuilder": ["require", "exports", "vs/base/common/strings", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeBase", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBuffer"], "vs/editor/common/model/tokenizationTextModelPart": ["require", "exports", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/eolCounter", "vs/editor/common/core/lineRange", "vs/editor/common/core/position", "vs/editor/common/core/wordHelper", "vs/editor/common/languages", "vs/editor/common/model/textModelPart", "vs/editor/common/model/textModelTokens", "vs/editor/common/tokens/contiguousMultilineTokensBuilder", "vs/editor/common/tokens/contiguousTokensStore", "vs/editor/common/tokens/sparseTokensStore"], "vs/editor/common/textModelEvents": ["require", "exports"], "vs/platform/undoRedo/common/undoRedo": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/nls!vs/platform/theme/common/colorRegistry": [], "vs/platform/jsonschemas/common/jsonContributionRegistry": ["require", "exports", "vs/base/common/event", "vs/platform/registry/common/platform"], "===anonymous61===": ["vs/platform/theme/common/colorRegistry.nls", "vs/platform/theme/common/colorRegistry.nls.keys"], "vs/platform/theme/common/colorRegistry.nls": [], "vs/platform/theme/common/colorRegistry.nls.keys": [], "vs/editor/browser/config/elementSizeObserver": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/event"], "vs/editor/browser/config/migrateOptions": ["require", "exports"], "vs/base/browser/fastDomNode": ["require", "exports"], "vs/editor/browser/controller/pointerHandler": ["require", "exports", "vs/base/browser/dom", "vs/base/common/platform", "vs/base/browser/touch", "vs/base/common/lifecycle", "vs/editor/browser/controller/mouseHandler", "vs/editor/browser/editorDom", "vs/base/browser/canIUse", "vs/editor/browser/controller/textAreaInput"], "vs/editor/browser/controller/textAreaHandler": ["require", "exports", "vs/nls!vs/editor/browser/controller/textAreaHandler", "vs/base/browser/browser", "vs/base/browser/fastDomNode", "vs/base/common/platform", "vs/base/common/strings", "vs/editor/browser/config/domFontInfo", "vs/editor/browser/controller/textAreaInput", "vs/editor/browser/controller/textAreaState", "vs/editor/browser/view/viewPart", "vs/editor/browser/viewParts/lineNumbers/lineNumbers", "vs/editor/browser/viewParts/margin/margin", "vs/editor/common/config/editorOptions", "vs/editor/common/core/wordCharacterClassifier", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/base/browser/ui/mouseCursor/mouseCursor", "vs/editor/common/languages", "vs/base/common/color", "vs/base/common/ime", "vs/platform/keybinding/common/keybinding", "vs/css!vs/editor/browser/controller/textAreaHandler"], "vs/editor/browser/view/viewController": ["require", "exports", "vs/editor/browser/coreCommands", "vs/editor/common/core/position", "vs/base/common/platform"], "vs/editor/browser/view/viewOverlays": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/browser/config/domFontInfo", "vs/editor/browser/view/viewLayer", "vs/editor/browser/view/viewPart"], "vs/editor/browser/view/viewPart": ["require", "exports", "vs/editor/common/viewEventHandler"], "vs/editor/browser/viewParts/contentWidgets/contentWidgets": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/editor/browser/view/viewPart"], "vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight": ["require", "exports", "vs/editor/browser/view/dynamicViewOverlay", "vs/editor/common/core/editorColorRegistry", "vs/base/common/arrays", "vs/platform/theme/common/themeService", "vs/editor/common/core/selection", "vs/platform/theme/common/theme", "vs/css!vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight"], "vs/editor/browser/viewParts/decorations/decorations": ["require", "exports", "vs/editor/browser/view/dynamicViewOverlay", "vs/editor/browser/view/renderingContext", "vs/editor/common/core/range", "vs/css!vs/editor/browser/viewParts/decorations/decorations"], "vs/editor/browser/viewParts/editorScrollbar/editorScrollbar": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/editor/browser/view/viewPart", "vs/platform/theme/common/themeService"], "vs/editor/browser/viewParts/indentGuides/indentGuides": ["require", "exports", "vs/editor/browser/view/dynamicViewOverlay", "vs/editor/common/core/editorColorRegistry", "vs/platform/theme/common/themeService", "vs/editor/common/core/position", "vs/base/common/arrays", "vs/base/common/types", "vs/editor/common/model/guidesTextModelPart", "vs/editor/common/textModelGuides", "vs/css!vs/editor/browser/viewParts/indentGuides/indentGuides"], "vs/editor/browser/viewParts/lineNumbers/lineNumbers": ["require", "exports", "vs/base/common/platform", "vs/editor/browser/view/dynamicViewOverlay", "vs/editor/common/core/position", "vs/platform/theme/common/themeService", "vs/editor/common/core/editorColorRegistry", "vs/css!vs/editor/browser/viewParts/lineNumbers/lineNumbers"], "vs/editor/browser/viewParts/lines/viewLines": ["require", "exports", "vs/base/browser/ui/mouseCursor/mouseCursor", "vs/base/common/async", "vs/base/common/platform", "vs/editor/browser/config/domFontInfo", "vs/editor/browser/view/renderingContext", "vs/editor/browser/view/viewLayer", "vs/editor/browser/view/viewPart", "vs/editor/browser/viewParts/lines/domReadingContext", "vs/editor/browser/viewParts/lines/viewLine", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/css!vs/editor/browser/viewParts/lines/viewLines"], "vs/editor/browser/viewParts/linesDecorations/linesDecorations": ["require", "exports", "vs/editor/browser/viewParts/glyphMargin/glyphMargin", "vs/css!vs/editor/browser/viewParts/linesDecorations/linesDecorations"], "vs/editor/browser/viewParts/margin/margin": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/browser/view/viewPart", "vs/css!vs/editor/browser/viewParts/margin/margin"], "vs/editor/browser/viewParts/marginDecorations/marginDecorations": ["require", "exports", "vs/editor/browser/viewParts/glyphMargin/glyphMargin", "vs/css!vs/editor/browser/viewParts/marginDecorations/marginDecorations"], "vs/editor/browser/viewParts/minimap/minimap": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/globalPointerMoveMonitor", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/strings", "vs/editor/browser/view/viewLayer", "vs/editor/browser/view/viewPart", "vs/editor/common/config/editorOptions", "vs/editor/common/core/range", "vs/editor/common/core/rgba", "vs/editor/common/viewModel/minimapTokensColorTracker", "vs/editor/common/viewModel", "vs/platform/theme/common/colorRegistry", "vs/editor/common/core/selection", "vs/base/browser/touch", "vs/editor/browser/viewParts/minimap/minimapCharRendererFactory", "vs/editor/common/model", "vs/base/common/functional", "vs/css!vs/editor/browser/viewParts/minimap/minimap"], "vs/editor/browser/viewParts/overlayWidgets/overlayWidgets": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/browser/view/viewPart", "vs/css!vs/editor/browser/viewParts/overlayWidgets/overlayWidgets"], "vs/editor/browser/viewParts/overviewRuler/decorationsOverviewRuler": ["require", "exports", "vs/base/browser/fastDomNode", "vs/base/common/color", "vs/editor/browser/view/viewPart", "vs/editor/common/core/position", "vs/editor/common/languages", "vs/editor/common/core/editorColorRegistry", "vs/editor/common/viewModel", "vs/base/common/arrays"], "vs/editor/browser/viewParts/overviewRuler/overviewRuler": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/common/viewModel/overviewZoneManager", "vs/editor/common/viewEventHandler"], "vs/editor/browser/viewParts/rulers/rulers": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/browser/view/viewPart", "vs/css!vs/editor/browser/viewParts/rulers/rulers"], "vs/editor/browser/viewParts/scrollDecoration/scrollDecoration": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/browser/view/viewPart", "vs/css!vs/editor/browser/viewParts/scrollDecoration/scrollDecoration"], "vs/editor/browser/viewParts/selections/selections": ["require", "exports", "vs/editor/browser/view/dynamicViewOverlay", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/browser/viewParts/selections/selections"], "vs/editor/browser/viewParts/viewCursors/viewCursors": ["require", "exports", "vs/base/browser/fastDomNode", "vs/base/common/async", "vs/editor/browser/view/viewPart", "vs/editor/browser/viewParts/viewCursors/viewCursor", "vs/editor/common/config/editorOptions", "vs/editor/common/core/editorColorRegistry", "vs/platform/theme/common/themeService", "vs/platform/theme/common/theme", "vs/css!vs/editor/browser/viewParts/viewCursors/viewCursors"], "vs/editor/browser/viewParts/viewZones/viewZones": ["require", "exports", "vs/base/browser/fastDomNode", "vs/base/common/errors", "vs/editor/browser/view/viewPart", "vs/editor/common/core/position"], "vs/editor/browser/view/renderingContext": ["require", "exports"], "vs/editor/common/viewModel/viewContext": ["require", "exports", "vs/editor/common/editorTheme"], "vs/editor/common/viewLayout/viewLinesViewportData": ["require", "exports", "vs/editor/common/core/range"], "vs/editor/common/viewEventHandler": ["require", "exports", "vs/base/common/lifecycle"], "vs/editor/browser/controller/mouseTarget": ["require", "exports", "vs/editor/browser/editorDom", "vs/editor/browser/view/viewPart", "vs/editor/browser/viewParts/lines/viewLine", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/cursorColumns", "vs/base/browser/dom", "vs/editor/common/cursor/cursorAtomicMoveOperations"], "vs/editor/browser/viewParts/blockDecorations/blockDecorations": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/browser/view/viewPart", "vs/css!vs/editor/browser/viewParts/blockDecorations/blockDecorations"], "vs/base/browser/performance": ["require", "exports"], "vs/editor/browser/viewParts/whitespace/whitespace": ["require", "exports", "vs/editor/browser/view/dynamicViewOverlay", "vs/base/common/strings", "vs/editor/common/viewLayout/viewLineRenderer", "vs/editor/common/core/position", "vs/editor/common/core/editorColorRegistry", "vs/css!vs/editor/browser/viewParts/whitespace/whitespace"], "vs/editor/browser/viewParts/glyphMargin/glyphMargin": ["require", "exports", "vs/base/browser/fastDomNode", "vs/base/common/arrays", "vs/editor/browser/view/dynamicViewOverlay", "vs/editor/browser/view/viewPart", "vs/editor/common/core/range", "vs/css!vs/editor/browser/viewParts/glyphMargin/glyphMargin"], "vs/nls!vs/editor/common/core/editorColorRegistry": [], "===anonymous62===": ["vs/editor/common/core/editorColorRegistry.nls", "vs/editor/common/core/editorColorRegistry.nls.keys"], "vs/editor/common/core/editorColorRegistry.nls": [], "vs/editor/common/core/editorColorRegistry.nls.keys": [], "vs/editor/common/cursor/cursor": ["require", "exports", "vs/base/common/errors", "vs/base/common/strings", "vs/editor/common/cursor/cursorCollection", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/cursor/cursorContext", "vs/editor/common/cursor/cursorDeleteOperations", "vs/editor/common/cursor/cursorTypeOperations", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/textModelEvents", "vs/editor/common/viewEvents", "vs/base/common/lifecycle", "vs/editor/common/viewModelEventDispatcher"], "vs/editor/common/languages/textToHtmlTokenizer": ["require", "exports", "vs/base/common/strings", "vs/editor/common/tokens/lineTokens", "vs/editor/common/languages", "vs/editor/common/languages/nullTokenize"], "vs/editor/common/viewEvents": ["require", "exports"], "vs/editor/common/viewLayout/viewLayout": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/scrollable", "vs/editor/common/viewLayout/linesLayout", "vs/editor/common/viewModel", "vs/editor/common/viewModelEventDispatcher"], "vs/editor/common/viewModel/minimapTokensColorTracker": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/rgba", "vs/editor/common/languages"], "vs/editor/common/viewModelEventDispatcher": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/editor/common/viewModel/viewModelLines": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/textModelGuides", "vs/editor/common/model/textModel", "vs/editor/common/textModelEvents", "vs/editor/common/viewEvents", "vs/editor/common/viewModel/modelLineProjection", "vs/editor/common/model/prefixSumComputer", "vs/editor/common/viewModel"], "vs/editor/common/modelLineProjectionData": ["require", "exports", "vs/base/common/assert", "vs/editor/common/core/position", "vs/editor/common/model"], "vs/editor/common/core/stringBuilder": ["require", "exports", "vs/base/common/strings", "vs/base/common/platform", "vs/base/common/buffer"], "vs/editor/common/cursor/cursorAtomicMoveOperations": ["require", "exports", "vs/editor/common/core/cursorColumns"], "vs/base/browser/event": ["require", "exports", "vs/base/common/event"], "vs/editor/browser/controller/textAreaState": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/range"], "vs/editor/common/config/diffEditor": ["require", "exports"], "vs/nls!vs/editor/common/config/editorConfigurationSchema": [], "===anonymous63===": ["vs/editor/common/config/editorConfigurationSchema.nls", "vs/editor/common/config/editorConfigurationSchema.nls.keys"], "vs/editor/common/config/editorConfigurationSchema.nls": [], "vs/editor/common/config/editorConfigurationSchema.nls.keys": [], "vs/editor/contrib/codeAction/browser/codeAction": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/uri", "vs/editor/browser/services/bulkEditService", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/services/languageFeatures", "vs/editor/common/services/model", "vs/editor/contrib/editorState/browser/editorState", "vs/nls!vs/editor/contrib/codeAction/browser/codeAction", "vs/platform/commands/common/commands", "vs/platform/notification/common/notification", "vs/platform/progress/common/progress", "vs/platform/telemetry/common/telemetry", "vs/editor/contrib/codeAction/common/types"], "vs/nls!vs/editor/contrib/codeAction/browser/codeActionCommands": [], "vs/editor/contrib/codeAction/common/types": ["require", "exports", "vs/base/common/errors"], "vs/editor/contrib/codeAction/browser/codeActionModel": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/editor/common/core/position", "vs/editor/common/core/selection", "vs/platform/contextkey/common/contextkey", "vs/platform/progress/common/progress", "vs/editor/contrib/codeAction/common/types", "vs/editor/contrib/codeAction/browser/codeAction"], "===anonymous64===": ["vs/editor/contrib/codeAction/browser/codeActionCommands.nls", "vs/editor/contrib/codeAction/browser/codeActionCommands.nls.keys"], "vs/editor/contrib/codeAction/browser/codeActionCommands.nls": [], "vs/editor/contrib/codeAction/browser/codeActionCommands.nls.keys": [], "vs/editor/contrib/codeAction/browser/codeActionKeybindingResolver": ["require", "exports", "vs/base/common/lazy", "vs/editor/contrib/codeAction/browser/codeAction", "vs/editor/contrib/codeAction/common/types", "vs/platform/keybinding/common/keybinding"], "vs/editor/contrib/codeAction/browser/codeActionMenu": ["require", "exports", "vs/base/common/codicons", "vs/editor/contrib/codeAction/common/types", "vs/nls!vs/editor/contrib/codeAction/browser/codeActionMenu", "vs/base/browser/ui/codicons/codiconStyles", "vs/editor/contrib/symbolIcons/browser/symbolIcons"], "vs/nls!vs/editor/contrib/codeAction/browser/codeActionController": [], "vs/platform/actionWidget/browser/actionWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionbar", "vs/base/common/lifecycle", "vs/nls!vs/platform/actionWidget/browser/actionWidget", "vs/platform/actionWidget/browser/actionList", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/theme/common/colorRegistry", "vs/css!vs/platform/actionWidget/browser/actionWidget"], "===anonymous65===": ["vs/editor/contrib/codeAction/browser/codeActionController.nls", "vs/editor/contrib/codeAction/browser/codeActionController.nls.keys"], "vs/editor/contrib/codeAction/browser/codeActionController.nls": [], "vs/editor/contrib/codeAction/browser/codeActionController.nls.keys": [], "vs/base/browser/touch": ["require", "exports", "vs/base/browser/dom", "vs/base/common/arrays", "vs/base/common/decorators", "vs/base/common/lifecycle", "vs/base/common/linkedList"], "vs/editor/common/model/utils": ["require", "exports"], "vs/nls!vs/editor/contrib/codeAction/browser/lightBulbWidget": [], "vs/css!vs/editor/contrib/codeAction/browser/lightBulbWidget": [], "===anonymous66===": ["vs/editor/contrib/codeAction/browser/lightBulbWidget.nls", "vs/editor/contrib/codeAction/browser/lightBulbWidget.nls.keys"], "vs/editor/contrib/codeAction/browser/lightBulbWidget.nls": [], "vs/editor/contrib/codeAction/browser/lightBulbWidget.nls.keys": [], "vs/nls!vs/platform/configuration/common/configurationRegistry": [], "===anonymous67===": ["vs/platform/configuration/common/configurationRegistry.nls", "vs/platform/configuration/common/configurationRegistry.nls.keys"], "vs/platform/configuration/common/configurationRegistry.nls": [], "vs/platform/configuration/common/configurationRegistry.nls.keys": [], "vs/editor/contrib/colorPicker/browser/defaultDocumentColorProvider": ["require", "exports", "vs/base/common/color", "vs/editor/browser/services/editorWorkerService", "vs/editor/common/services/model", "vs/editor/common/languages/languageConfigurationRegistry", "vs/base/common/lifecycle", "vs/editor/common/services/languageFeatures", "vs/editor/common/editorFeatures"], "vs/base/browser/ui/iconLabel/iconLabels": ["require", "exports", "vs/base/browser/dom", "vs/base/common/themables"], "vs/css!vs/editor/contrib/codelens/browser/codelensWidget": [], "vs/base/common/numbers": ["require", "exports"], "vs/editor/browser/editorDom": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/globalPointerMoveMonitor", "vs/base/browser/mouseEvent", "vs/base/common/async", "vs/base/common/lifecycle", "vs/platform/theme/common/colorRegistry"], "vs/editor/contrib/colorPicker/browser/color": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/uri", "vs/editor/common/core/range", "vs/editor/common/services/model", "vs/platform/commands/common/commands", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/colorPicker/browser/defaultDocumentColorProvider", "vs/platform/configuration/common/configuration"], "vs/editor/contrib/colorPicker/browser/colorPickerModel": ["require", "exports", "vs/base/common/event"], "vs/editor/contrib/colorPicker/browser/colorPickerWidget": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dom", "vs/base/browser/globalPointerMoveMonitor", "vs/base/browser/ui/widget", "vs/base/common/codicons", "vs/base/common/color", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/themables", "vs/nls!vs/editor/contrib/colorPicker/browser/colorPickerWidget", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/iconRegistry", "vs/css!vs/editor/contrib/colorPicker/browser/colorPicker"], "vs/base/browser/dnd": ["require", "exports", "vs/base/common/mime"], "vs/base/browser/ui/iconLabel/iconLabelHover": ["require", "exports", "vs/base/browser/dom", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/htmlContent", "vs/base/common/iconLabels", "vs/base/common/lifecycle", "vs/base/common/types", "vs/nls!vs/base/browser/ui/iconLabel/iconLabelHover"], "vs/base/browser/ui/selectBox/selectBox": ["require", "exports", "vs/base/browser/ui/selectBox/selectBoxCustom", "vs/base/browser/ui/selectBox/selectBoxNative", "vs/base/browser/ui/widget", "vs/base/common/platform", "vs/css!vs/base/browser/ui/selectBox/selectBox"], "vs/nls!vs/base/browser/ui/actionbar/actionViewItems": [], "vs/css!vs/base/browser/ui/actionbar/actionbar": [], "===anonymous68===": ["vs/base/browser/ui/actionbar/actionViewItems.nls", "vs/base/browser/ui/actionbar/actionViewItems.nls.keys"], "vs/base/browser/ui/actionbar/actionViewItems.nls": [], "vs/base/browser/ui/actionbar/actionViewItems.nls.keys": [], "vs/nls!vs/base/common/actions": [], "===anonymous69===": ["vs/base/common/actions.nls", "vs/base/common/actions.nls.keys"], "vs/base/common/actions.nls": [], "vs/base/common/actions.nls.keys": [], "vs/nls!vs/platform/workspace/common/workspace": [], "vs/base/common/ternarySearchTree": ["require", "exports", "vs/base/common/strings"], "===anonymous70===": ["vs/platform/workspace/common/workspace.nls", "vs/platform/workspace/common/workspace.nls.keys"], "vs/platform/workspace/common/workspace.nls": [], "vs/platform/workspace/common/workspace.nls.keys": [], "vs/base/common/dataTransfer": ["require", "exports", "vs/base/common/arrays", "vs/base/common/iterator", "vs/base/common/uuid"], "vs/base/common/uuid": ["require", "exports"], "vs/editor/browser/dnd": ["require", "exports", "vs/base/browser/dnd", "vs/base/common/dataTransfer", "vs/base/common/mime", "vs/base/common/uri", "vs/platform/dnd/browser/dnd"], "vs/editor/contrib/dropOrPasteInto/browser/edit": ["require", "exports", "vs/editor/browser/services/bulkEditService"], "vs/nls!vs/editor/contrib/dropOrPasteInto/browser/copyPasteController": [], "vs/editor/contrib/dropOrPasteInto/browser/postEditWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/button/button", "vs/base/common/actions", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/browser/services/bulkEditService", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/css!vs/editor/contrib/dropOrPasteInto/browser/postEditWidget"], "===anonymous71===": ["vs/editor/contrib/dropOrPasteInto/browser/copyPasteController.nls", "vs/editor/contrib/dropOrPasteInto/browser/copyPasteController.nls.keys"], "vs/editor/contrib/dropOrPasteInto/browser/copyPasteController.nls": [], "vs/editor/contrib/dropOrPasteInto/browser/copyPasteController.nls.keys": [], "vs/nls!vs/editor/contrib/dropOrPasteInto/browser/defaultProviders": [], "===anonymous72===": ["vs/editor/contrib/dropOrPasteInto/browser/defaultProviders.nls", "vs/editor/contrib/dropOrPasteInto/browser/defaultProviders.nls.keys"], "vs/editor/contrib/dropOrPasteInto/browser/defaultProviders.nls": [], "vs/editor/contrib/dropOrPasteInto/browser/defaultProviders.nls.keys": [], "vs/editor/common/services/treeViewsDnd": ["require", "exports"], "vs/editor/common/services/treeViewsDndService": ["require", "exports", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/editor/common/services/treeViewsDnd"], "vs/nls!vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController": [], "vs/platform/dnd/browser/dnd": ["require", "exports", "vs/platform/registry/common/platform"], "===anonymous73===": ["vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController.nls", "vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController.nls.keys"], "vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController.nls": [], "vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController.nls.keys": [], "vs/editor/contrib/find/browser/findDecorations": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/model", "vs/editor/common/model/textModel", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/find/browser/replaceAllCommand": ["require", "exports", "vs/editor/common/core/range"], "vs/editor/contrib/find/browser/replacePattern": ["require", "exports", "vs/base/common/search"], "vs/base/browser/ui/findinput/findInputToggles": ["require", "exports", "vs/base/browser/ui/toggle/toggle", "vs/base/common/codicons", "vs/nls!vs/base/browser/ui/findinput/findInputToggles"], "vs/base/browser/ui/widget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/mouseEvent", "vs/base/browser/touch", "vs/base/common/lifecycle"], "vs/css!vs/editor/contrib/find/browser/findOptionsWidget": [], "vs/base/browser/ui/toggle/toggle": ["require", "exports", "vs/base/browser/ui/widget", "vs/base/common/themables", "vs/base/common/event", "vs/css!vs/base/browser/ui/toggle/toggle"], "vs/base/browser/ui/sash/sash": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/touch", "vs/base/common/async", "vs/base/common/decorators", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/css!vs/base/browser/ui/sash/sash"], "vs/nls!vs/editor/contrib/find/browser/findWidget": [], "vs/platform/history/browser/contextScopedHistoryWidget": ["require", "exports", "vs/base/browser/ui/findinput/findInput", "vs/base/browser/ui/findinput/replaceInput", "vs/platform/contextkey/common/contextkey", "vs/platform/keybinding/common/keybindingsRegistry", "vs/nls!vs/platform/history/browser/contextScopedHistoryWidget", "vs/base/common/lifecycle"], "vs/platform/history/browser/historyWidgetKeybindingHint": ["require", "exports"], "vs/platform/theme/browser/defaultStyles": ["require", "exports", "vs/platform/theme/common/colorRegistry", "vs/base/common/color"], "vs/css!vs/editor/contrib/find/browser/findWidget": [], "===anonymous74===": ["vs/editor/contrib/find/browser/findWidget.nls", "vs/editor/contrib/find/browser/findWidget.nls.keys"], "vs/editor/contrib/find/browser/findWidget.nls": [], "vs/editor/contrib/find/browser/findWidget.nls.keys": [], "vs/nls!vs/editor/contrib/folding/browser/foldingDecorations": [], "===anonymous75===": ["vs/editor/contrib/folding/browser/foldingDecorations.nls", "vs/editor/contrib/folding/browser/foldingDecorations.nls.keys"], "vs/editor/contrib/folding/browser/foldingDecorations.nls": [], "vs/editor/contrib/folding/browser/foldingDecorations.nls.keys": [], "vs/editor/contrib/inlineCompletions/browser/commandIds": ["require", "exports"], "vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys": ["require", "exports", "vs/base/common/observable", "vs/base/common/strings", "vs/editor/common/core/cursorColumns", "vs/platform/contextkey/common/contextkey", "vs/base/common/lifecycle", "vs/nls!vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys"], "vs/nls!vs/editor/contrib/inlineCompletions/browser/commands": [], "===anonymous76===": ["vs/editor/contrib/inlineCompletions/browser/commands.nls", "vs/editor/contrib/inlineCompletions/browser/commands.nls.keys"], "vs/editor/contrib/inlineCompletions/browser/commands.nls": [], "vs/editor/contrib/inlineCompletions/browser/commands.nls.keys": [], "vs/editor/contrib/markdownRenderer/browser/markdownRenderer": ["require", "exports", "vs/base/browser/markdownRenderer", "vs/base/browser/trustedTypes", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/browser/config/domFontInfo", "vs/editor/common/languages/language", "vs/editor/common/languages/modesRegistry", "vs/editor/common/languages/textToHtmlTokenizer", "vs/platform/opener/common/opener", "vs/css!vs/editor/contrib/markdownRenderer/browser/renderedMarkdown"], "vs/nls!vs/editor/contrib/inlineCompletions/browser/hoverParticipant": [], "===anonymous77===": ["vs/editor/contrib/inlineCompletions/browser/hoverParticipant.nls", "vs/editor/contrib/inlineCompletions/browser/hoverParticipant.nls.keys"], "vs/editor/contrib/inlineCompletions/browser/hoverParticipant.nls": [], "vs/editor/contrib/inlineCompletions/browser/hoverParticipant.nls.keys": [], "vs/editor/contrib/inlineCompletions/browser/ghostTextWidget": ["require", "exports", "vs/base/browser/trustedTypes", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/strings", "vs/editor/browser/config/domFontInfo", "vs/editor/common/config/editorOptions", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/stringBuilder", "vs/editor/common/languages/language", "vs/editor/common/model", "vs/editor/common/tokens/lineTokens", "vs/editor/common/viewLayout/lineDecorations", "vs/editor/common/viewLayout/viewLineRenderer", "vs/editor/contrib/inlineCompletions/browser/ghostText", "vs/editor/contrib/inlineCompletions/browser/utils", "vs/css!vs/editor/contrib/inlineCompletions/browser/ghostText"], "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsModel": ["require", "exports", "vs/base/common/arraysFind", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/types", "vs/editor/common/core/editOperation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/contrib/inlineCompletions/browser/ghostText", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsSource", "vs/editor/contrib/inlineCompletions/browser/utils", "vs/editor/contrib/snippet/browser/snippetController2", "vs/platform/commands/common/commands", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/inlineCompletions/browser/suggestWidgetInlineCompletionProvider": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/editor/contrib/snippet/browser/snippetParser", "vs/editor/contrib/snippet/browser/snippetSession", "vs/editor/contrib/suggest/browser/suggestController", "vs/base/common/observable", "vs/editor/contrib/inlineCompletions/browser/singleTextEdit", "vs/base/common/arrays", "vs/base/common/arraysFind"], "vs/nls!vs/editor/contrib/inlineCompletions/browser/inlineCompletionsController": [], "===anonymous78===": ["vs/editor/contrib/inlineCompletions/browser/inlineCompletionsController.nls", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsController.nls.keys"], "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsController.nls": [], "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsController.nls.keys": [], "vs/nls!vs/editor/contrib/gotoSymbol/browser/symbolNavigation": [], "===anonymous79===": ["vs/editor/contrib/gotoSymbol/browser/symbolNavigation.nls", "vs/editor/contrib/gotoSymbol/browser/symbolNavigation.nls.keys"], "vs/editor/contrib/gotoSymbol/browser/symbolNavigation.nls": [], "vs/editor/contrib/gotoSymbol/browser/symbolNavigation.nls.keys": [], "vs/base/common/idGenerator": ["require", "exports"], "vs/nls!vs/editor/contrib/gotoSymbol/browser/referencesModel": [], "===anonymous80===": ["vs/editor/contrib/gotoSymbol/browser/referencesModel.nls", "vs/editor/contrib/gotoSymbol/browser/referencesModel.nls.keys"], "vs/editor/contrib/gotoSymbol/browser/referencesModel.nls": [], "vs/editor/contrib/gotoSymbol/browser/referencesModel.nls.keys": [], "vs/base/browser/markdownRenderer": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/dompurify/dompurify", "vs/base/browser/event", "vs/base/browser/formattedTextRenderer", "vs/base/browser/keyboardEvent", "vs/base/browser/mouseEvent", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/htmlContent", "vs/base/common/iconLabels", "vs/base/common/idGenerator", "vs/base/common/lazy", "vs/base/common/lifecycle", "vs/base/common/marked/marked", "vs/base/common/marshalling", "vs/base/common/network", "vs/base/common/objects", "vs/base/common/resources", "vs/base/common/strings", "vs/base/common/uri"], "vs/nls!vs/editor/contrib/message/browser/messageController": [], "vs/css!vs/editor/contrib/message/browser/messageController": [], "===anonymous81===": ["vs/editor/contrib/message/browser/messageController.nls", "vs/editor/contrib/message/browser/messageController.nls.keys"], "vs/editor/contrib/message/browser/messageController.nls": [], "vs/editor/contrib/message/browser/messageController.nls.keys": [], "vs/base/browser/ui/actionbar/actionbar": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/common/actions", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/types", "vs/css!vs/base/browser/ui/actionbar/actionbar"], "vs/editor/contrib/zoneWidget/browser/zoneWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/sash/sash", "vs/base/common/color", "vs/base/common/idGenerator", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/editor/common/core/range", "vs/editor/common/model/textModel", "vs/css!vs/editor/contrib/zoneWidget/browser/zoneWidget"], "vs/nls!vs/editor/contrib/peekView/browser/peekView": [], "vs/platform/actions/browser/menuEntryActionViewItem": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/browser/ui/dropdown/dropdownActionViewItem", "vs/base/common/actions", "vs/base/common/keybindingLabels", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/nls!vs/platform/actions/browser/menuEntryActionViewItem", "vs/platform/actions/common/actions", "vs/platform/action/common/action", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/notification/common/notification", "vs/platform/storage/common/storage", "vs/platform/theme/common/themeService", "vs/base/common/themables", "vs/platform/theme/common/theme", "vs/base/common/types", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/browser/defaultStyles", "vs/platform/accessibility/common/accessibility", "vs/css!vs/platform/actions/browser/menuEntryActionViewItem"], "vs/css!vs/editor/contrib/peekView/browser/media/peekViewWidget": [], "===anonymous82===": ["vs/editor/contrib/peekView/browser/peekView.nls", "vs/editor/contrib/peekView/browser/peekView.nls.keys"], "vs/editor/contrib/peekView/browser/peekView.nls": [], "vs/editor/contrib/peekView/browser/peekView.nls.keys": [], "vs/nls!vs/platform/contextkey/common/contextkeys": [], "===anonymous83===": ["vs/platform/contextkey/common/contextkeys.nls", "vs/platform/contextkey/common/contextkeys.nls.keys"], "vs/platform/contextkey/common/contextkeys.nls": [], "vs/platform/contextkey/common/contextkeys.nls.keys": [], "vs/nls!vs/platform/theme/common/iconRegistry": [], "===anonymous84===": ["vs/platform/theme/common/iconRegistry.nls", "vs/platform/theme/common/iconRegistry.nls.keys"], "vs/platform/theme/common/iconRegistry.nls": [], "vs/platform/theme/common/iconRegistry.nls.keys": [], "vs/base/browser/ui/scrollbar/scrollableElement": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/mouseEvent", "vs/base/browser/ui/scrollbar/horizontalScrollbar", "vs/base/browser/ui/scrollbar/verticalScrollbar", "vs/base/browser/ui/widget", "vs/base/common/async", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/scrollable", "vs/css!vs/base/browser/ui/scrollbar/media/scrollbars"], "vs/nls!vs/editor/contrib/gotoError/browser/gotoErrorWidget": [], "vs/platform/severityIcon/browser/severityIcon": ["require", "exports", "vs/base/common/codicons", "vs/base/common/themables", "vs/base/common/severity", "vs/css!vs/platform/severityIcon/browser/media/severityIcon"], "vs/css!vs/editor/contrib/gotoError/browser/media/gotoErrorWidget": [], "===anonymous85===": ["vs/editor/contrib/gotoError/browser/gotoErrorWidget.nls", "vs/editor/contrib/gotoError/browser/gotoErrorWidget.nls.keys"], "vs/editor/contrib/gotoError/browser/gotoErrorWidget.nls": [], "vs/editor/contrib/gotoError/browser/gotoErrorWidget.nls.keys": [], "vs/base/browser/ui/hover/hoverWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/lifecycle", "vs/nls!vs/base/browser/ui/hover/hoverWidget", "vs/css!vs/base/browser/ui/hover/hover"], "vs/editor/contrib/hover/browser/hoverOperation": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/editor/contrib/hover/browser/resizableContentWidget": ["require", "exports", "vs/base/browser/ui/resizable/resizable", "vs/base/common/lifecycle", "vs/editor/common/core/position", "vs/base/browser/dom"], "vs/editor/contrib/hover/browser/getHover": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/editor/browser/editorExtensions", "vs/editor/common/services/languageFeatures"], "vs/nls!vs/editor/contrib/hover/browser/markdownHoverParticipant": [], "===anonymous86===": ["vs/editor/contrib/hover/browser/markdownHoverParticipant.nls", "vs/editor/contrib/hover/browser/markdownHoverParticipant.nls.keys"], "vs/editor/contrib/hover/browser/markdownHoverParticipant.nls": [], "vs/editor/contrib/hover/browser/markdownHoverParticipant.nls.keys": [], "vs/nls!vs/editor/contrib/hover/browser/markerHoverParticipant": [], "===anonymous87===": ["vs/editor/contrib/hover/browser/markerHoverParticipant.nls", "vs/editor/contrib/hover/browser/markerHoverParticipant.nls.keys"], "vs/editor/contrib/hover/browser/markerHoverParticipant.nls": [], "vs/editor/contrib/hover/browser/markerHoverParticipant.nls.keys": [], "vs/base/browser/ui/keybindingLabel/keybindingLabel": ["require", "exports", "vs/base/browser/dom", "vs/base/common/keybindingLabels", "vs/base/common/objects", "vs/nls!vs/base/browser/ui/keybindingLabel/keybindingLabel", "vs/css!vs/base/browser/ui/keybindingLabel/keybindingLabel"], "vs/nls!vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget": [], "vs/platform/actions/browser/toolbar": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/mouseEvent", "vs/base/browser/ui/toolbar/toolbar", "vs/base/common/actions", "vs/base/common/arrays", "vs/base/common/collections", "vs/base/common/iterator", "vs/base/common/lifecycle", "vs/nls!vs/platform/actions/browser/toolbar", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/keybinding/common/keybinding", "vs/platform/telemetry/common/telemetry"], "vs/css!vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget": [], "===anonymous88===": ["vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget.nls", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget.nls.keys"], "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget.nls": [], "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget.nls.keys": [], "vs/editor/contrib/inlayHints/browser/inlayHints": ["require", "exports", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/base/common/network", "vs/base/common/uri"], "vs/editor/contrib/inlayHints/browser/inlayHintsLocations": ["require", "exports", "vs/base/browser/dom", "vs/base/common/actions", "vs/base/common/cancellation", "vs/base/common/uuid", "vs/editor/common/core/range", "vs/editor/common/services/resolverService", "vs/editor/contrib/gotoSymbol/browser/goToCommands", "vs/editor/contrib/peekView/browser/peekView", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/instantiation/common/instantiation", "vs/platform/notification/common/notification"], "vs/nls!vs/editor/contrib/inlayHints/browser/inlayHintsHover": [], "===anonymous89===": ["vs/editor/contrib/inlayHints/browser/inlayHintsHover.nls", "vs/editor/contrib/inlayHints/browser/inlayHintsHover.nls.keys"], "vs/editor/contrib/inlayHints/browser/inlayHintsHover.nls": [], "vs/editor/contrib/inlayHints/browser/inlayHintsHover.nls.keys": [], "vs/base/common/extpath": ["require", "exports", "vs/base/common/path", "vs/base/common/platform", "vs/base/common/strings"], "vs/nls!vs/editor/contrib/wordHighlighter/browser/highlightDecorations": [], "vs/css!vs/editor/contrib/wordHighlighter/browser/highlightDecorations": [], "===anonymous90===": ["vs/editor/contrib/wordHighlighter/browser/highlightDecorations.nls", "vs/editor/contrib/wordHighlighter/browser/highlightDecorations.nls.keys"], "vs/editor/contrib/wordHighlighter/browser/highlightDecorations.nls": [], "vs/editor/contrib/wordHighlighter/browser/highlightDecorations.nls.keys": [], "vs/nls!vs/editor/contrib/parameterHints/browser/parameterHintsWidget": [], "vs/css!vs/editor/contrib/parameterHints/browser/parameterHints": [], "===anonymous91===": ["vs/editor/contrib/parameterHints/browser/parameterHintsWidget.nls", "vs/editor/contrib/parameterHints/browser/parameterHintsWidget.nls.keys"], "vs/editor/contrib/parameterHints/browser/parameterHintsWidget.nls": [], "vs/editor/contrib/parameterHints/browser/parameterHintsWidget.nls.keys": [], "vs/nls!vs/editor/contrib/rename/browser/renameInputField": [], "vs/css!vs/editor/contrib/rename/browser/renameInputField": [], "===anonymous92===": ["vs/editor/contrib/rename/browser/renameInputField.nls", "vs/editor/contrib/rename/browser/renameInputField.nls.keys"], "vs/editor/contrib/rename/browser/renameInputField.nls": [], "vs/editor/contrib/rename/browser/renameInputField.nls.keys": [], "vs/editor/common/tokens/sparseMultilineTokens": ["require", "exports", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/eolCounter"], "vs/editor/common/services/semanticTokensDto": ["require", "exports", "vs/base/common/buffer", "vs/base/common/platform"], "vs/nls!vs/editor/contrib/suggest/browser/suggest": [], "===anonymous93===": ["vs/editor/contrib/suggest/browser/suggest.nls", "vs/editor/contrib/suggest/browser/suggest.nls.keys"], "vs/editor/contrib/suggest/browser/suggest.nls": [], "vs/editor/contrib/suggest/browser/suggest.nls.keys": [], "vs/editor/contrib/snippet/browser/snippetVariables": ["require", "exports", "vs/base/common/labels", "vs/base/common/path", "vs/base/common/resources", "vs/base/common/strings", "vs/base/common/uuid", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/contrib/snippet/browser/snippetParser", "vs/nls!vs/editor/contrib/snippet/browser/snippetVariables", "vs/platform/workspace/common/workspace"], "vs/css!vs/editor/contrib/snippet/browser/snippetSession": [], "vs/nls!vs/editor/contrib/stickyScroll/browser/stickyScrollActions": [], "vs/platform/action/common/actionCommonCategories": ["require", "exports", "vs/nls!vs/platform/action/common/actionCommonCategories"], "===anonymous94===": ["vs/editor/contrib/stickyScroll/browser/stickyScrollActions.nls", "vs/editor/contrib/stickyScroll/browser/stickyScrollActions.nls.keys"], "vs/editor/contrib/stickyScroll/browser/stickyScrollActions.nls": [], "vs/editor/contrib/stickyScroll/browser/stickyScrollActions.nls.keys": [], "vs/editor/contrib/stickyScroll/browser/stickyScrollWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/trustedTypes", "vs/base/common/arrays", "vs/base/common/lifecycle", "vs/base/common/themables", "vs/editor/browser/viewParts/lines/viewLine", "vs/editor/browser/widget/embeddedCodeEditorWidget", "vs/editor/common/core/position", "vs/editor/common/core/stringBuilder", "vs/editor/common/viewLayout/lineDecorations", "vs/editor/common/viewLayout/viewLineRenderer", "vs/editor/contrib/folding/browser/foldingDecorations", "vs/css!vs/editor/contrib/stickyScroll/browser/stickyScroll"], "vs/editor/contrib/stickyScroll/browser/stickyScrollProvider": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/common/services/languageFeatures", "vs/base/common/cancellation", "vs/base/common/async", "vs/base/common/arrays", "vs/base/common/event", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/contrib/stickyScroll/browser/stickyScrollModelProvider"], "vs/editor/contrib/stickyScroll/browser/stickyScrollElement": ["require", "exports"], "vs/base/browser/ui/list/listWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/keyboardEvent", "vs/base/browser/touch", "vs/base/browser/ui/aria/aria", "vs/base/browser/ui/list/splice", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/color", "vs/base/common/decorators", "vs/base/common/event", "vs/base/common/filters", "vs/base/common/lifecycle", "vs/base/common/numbers", "vs/base/common/platform", "vs/base/common/types", "vs/base/browser/ui/list/list", "vs/base/browser/ui/list/listView", "vs/base/browser/mouseEvent", "vs/css!vs/base/browser/ui/list/list"], "vs/editor/contrib/suggest/browser/suggestWidgetStatus": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionbar", "vs/base/common/lifecycle", "vs/nls!vs/editor/contrib/suggest/browser/suggestWidgetStatus", "vs/platform/actions/browser/menuEntryActionViewItem", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation"], "vs/nls!vs/editor/contrib/suggest/browser/suggestWidget": [], "vs/base/browser/ui/resizable/resizable": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/sash/sash", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/editor/contrib/suggest/browser/suggestWidgetDetails": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/codicons", "vs/base/common/themables", "vs/base/common/event", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/editor/contrib/markdownRenderer/browser/markdownRenderer", "vs/base/browser/ui/resizable/resizable", "vs/nls!vs/editor/contrib/suggest/browser/suggestWidgetDetails", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/suggest/browser/suggestWidgetRenderer": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/iconLabel/iconLabel", "vs/base/common/codicons", "vs/base/common/themables", "vs/base/common/event", "vs/base/common/filters", "vs/base/common/lifecycle", "vs/base/common/uri", "vs/editor/common/languages", "vs/editor/common/services/getIconClasses", "vs/editor/common/services/model", "vs/editor/common/languages/language", "vs/nls!vs/editor/contrib/suggest/browser/suggestWidgetRenderer", "vs/platform/files/common/files", "vs/platform/theme/common/iconRegistry", "vs/platform/theme/common/themeService", "vs/editor/contrib/suggest/browser/suggestWidgetDetails"], "vs/css!vs/editor/contrib/suggest/browser/media/suggest": [], "===anonymous95===": ["vs/editor/contrib/suggest/browser/suggestWidget.nls", "vs/editor/contrib/suggest/browser/suggestWidget.nls.keys"], "vs/editor/contrib/suggest/browser/suggestWidget.nls": [], "vs/editor/contrib/suggest/browser/suggestWidget.nls.keys": [], "vs/platform/opener/browser/link": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/keyboardEvent", "vs/base/browser/touch", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/opener/common/opener", "vs/css!vs/platform/opener/browser/link"], "vs/css!vs/editor/contrib/unicodeHighlighter/browser/bannerController": [], "vs/base/common/errorMessage": ["require", "exports", "vs/base/common/arrays", "vs/base/common/types", "vs/nls!vs/base/common/errorMessage"], "vs/base/common/tfIdf": ["require", "exports"], "vs/nls!vs/platform/quickinput/browser/commandsQuickAccess": [], "vs/platform/quickinput/browser/pickerQuickAccess": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/lifecycle", "vs/base/common/types"], "===anonymous96===": ["vs/platform/quickinput/browser/commandsQuickAccess.nls", "vs/platform/quickinput/browser/commandsQuickAccess.nls.keys"], "vs/platform/quickinput/browser/commandsQuickAccess.nls": [], "vs/platform/quickinput/browser/commandsQuickAccess.nls.keys": [], "vs/base/browser/ui/list/listPaging": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/browser/ui/list/listWidget", "vs/css!vs/base/browser/ui/list/list"], "vs/base/browser/ui/table/tableWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/list/listWidget", "vs/base/browser/ui/splitview/splitview", "vs/base/common/event", "vs/base/common/lifecycle", "vs/css!vs/base/browser/ui/table/table"], "vs/base/browser/ui/tree/abstractTree": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/keyboardEvent", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/findinput/findInput", "vs/base/browser/ui/inputbox/inputBox", "vs/base/browser/ui/list/listView", "vs/base/browser/ui/list/listWidget", "vs/base/browser/ui/toggle/toggle", "vs/base/browser/ui/tree/indexTreeModel", "vs/base/browser/ui/tree/tree", "vs/base/common/actions", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/themables", "vs/base/common/map", "vs/base/common/event", "vs/base/common/filters", "vs/base/common/lifecycle", "vs/base/common/numbers", "vs/base/common/types", "vs/nls!vs/base/browser/ui/tree/abstractTree", "vs/css!vs/base/browser/ui/tree/media/tree"], "vs/base/browser/ui/tree/asyncDataTree": ["require", "exports", "vs/base/browser/ui/list/listView", "vs/base/browser/ui/tree/abstractTree", "vs/base/browser/ui/tree/indexTreeModel", "vs/base/browser/ui/tree/objectTree", "vs/base/browser/ui/tree/tree", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/themables", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/iterator", "vs/base/common/lifecycle", "vs/base/common/types"], "vs/base/browser/ui/tree/dataTree": ["require", "exports", "vs/base/browser/ui/tree/abstractTree", "vs/base/browser/ui/tree/objectTreeModel"], "vs/base/browser/ui/tree/objectTree": ["require", "exports", "vs/base/browser/ui/tree/abstractTree", "vs/base/browser/ui/tree/compressedObjectTreeModel", "vs/base/browser/ui/tree/objectTreeModel", "vs/base/common/decorators", "vs/base/common/iterator"], "vs/nls!vs/platform/list/browser/listService": [], "===anonymous97===": ["vs/platform/list/browser/listService.nls", "vs/platform/list/browser/listService.nls.keys"], "vs/platform/list/browser/listService.nls": [], "vs/platform/list/browser/listService.nls.keys": [], "vs/base/browser/ui/splitview/splitview": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/ui/sash/sash", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/arrays", "vs/base/common/color", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/numbers", "vs/base/common/scrollable", "vs/base/common/types", "vs/css!vs/base/browser/ui/splitview/splitview"], "vs/editor/contrib/gotoSymbol/browser/peek/referencesTree": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/countBadge/countBadge", "vs/base/browser/ui/highlightedlabel/highlightedLabel", "vs/base/browser/ui/iconLabel/iconLabel", "vs/base/common/filters", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/editor/common/services/resolverService", "vs/nls!vs/editor/contrib/gotoSymbol/browser/peek/referencesTree", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/label/common/label", "vs/platform/theme/browser/defaultStyles", "vs/editor/contrib/gotoSymbol/browser/referencesModel"], "vs/nls!vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget": [], "vs/css!vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget": [], "===anonymous98===": ["vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget.nls", "vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget.nls.keys"], "vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget.nls": [], "vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget.nls.keys": [], "vs/base/common/marshalling": ["require", "exports", "vs/base/common/buffer", "vs/base/common/uri"], "vs/nls!vs/platform/contextkey/common/scanner": [], "===anonymous99===": ["vs/platform/contextkey/common/scanner.nls", "vs/platform/contextkey/common/scanner.nls.keys"], "vs/platform/contextkey/common/scanner.nls": [], "vs/platform/contextkey/common/scanner.nls.keys": [], "vs/editor/common/diff/defaultLinesDiffComputer/utils": ["require", "exports"], "vs/base/browser/defaultWorkerFactory": ["require", "exports", "vs/base/browser/trustedTypes", "vs/base/common/errors", "vs/base/common/network", "vs/base/common/worker/simpleWorker"], "vs/nls!vs/editor/common/viewLayout/viewLineRenderer": [], "vs/editor/common/viewLayout/lineDecorations": ["require", "exports", "vs/base/common/strings"], "vs/editor/common/viewLayout/linePart": ["require", "exports"], "===anonymous100===": ["vs/editor/common/viewLayout/viewLineRenderer.nls", "vs/editor/common/viewLayout/viewLineRenderer.nls.keys"], "vs/editor/common/viewLayout/viewLineRenderer.nls": [], "vs/editor/common/viewLayout/viewLineRenderer.nls.keys": [], "vs/editor/browser/services/abstractCodeEditorService": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/platform/theme/common/themeService"], "vs/nls!vs/platform/audioCues/browser/audioCueService": [], "===anonymous101===": ["vs/platform/audioCues/browser/audioCueService.nls", "vs/platform/audioCues/browser/audioCueService.nls.keys"], "vs/platform/audioCues/browser/audioCueService.nls": [], "vs/platform/audioCues/browser/audioCueService.nls.keys": [], "vs/base/common/ime": ["require", "exports", "vs/base/common/event"], "vs/nls!vs/platform/keybinding/common/abstractKeybindingService": [], "===anonymous102===": ["vs/platform/keybinding/common/abstractKeybindingService.nls", "vs/platform/keybinding/common/abstractKeybindingService.nls.keys"], "vs/platform/keybinding/common/abstractKeybindingService.nls": [], "vs/platform/keybinding/common/abstractKeybindingService.nls.keys": [], "vs/platform/keybinding/common/baseResolvedKeybinding": ["require", "exports", "vs/base/common/errors", "vs/base/common/keybindingLabels", "vs/base/common/keybindings"], "vs/base/browser/ui/contextview/contextview": ["require", "exports", "vs/base/browser/canIUse", "vs/base/browser/dom", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/range", "vs/css!vs/base/browser/ui/contextview/contextview"], "vs/editor/common/services/languagesRegistry": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/common/services/languagesAssociations", "vs/editor/common/languages/modesRegistry", "vs/platform/configuration/common/configurationRegistry", "vs/platform/registry/common/platform"], "vs/platform/contextview/browser/contextMenuHandler": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/mouseEvent", "vs/base/browser/ui/menu/menu", "vs/base/common/actions", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/platform/theme/browser/defaultStyles"], "vs/platform/editor/common/editor": ["require", "exports"], "vs/base/common/collections": ["require", "exports"], "vs/platform/quickinput/browser/quickInputService": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/event", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/layout/browser/layoutService", "vs/platform/list/browser/listService", "vs/platform/opener/common/opener", "vs/platform/quickinput/browser/quickAccess", "vs/platform/theme/browser/defaultStyles", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/platform/quickinput/browser/quickInputController"], "vs/css!vs/editor/standalone/browser/quickInput/standaloneQuickInput": [], "vs/nls!vs/platform/actions/common/menuService": [], "===anonymous103===": ["vs/platform/actions/common/menuService.nls", "vs/platform/actions/common/menuService.nls.keys"], "vs/platform/actions/common/menuService.nls": [], "vs/platform/actions/common/menuService.nls.keys": [], "vs/nls!vs/platform/contextkey/browser/contextKeyService": [], "===anonymous104===": ["vs/platform/contextkey/browser/contextKeyService.nls", "vs/platform/contextkey/browser/contextKeyService.nls.keys"], "vs/platform/contextkey/browser/contextKeyService.nls": [], "vs/platform/contextkey/browser/contextKeyService.nls.keys": [], "vs/platform/instantiation/common/graph": ["require", "exports"], "vs/nls!vs/platform/undoRedo/common/undoRedoService": [], "===anonymous105===": ["vs/platform/undoRedo/common/undoRedoService.nls", "vs/platform/undoRedo/common/undoRedoService.nls.keys"], "vs/platform/undoRedo/common/undoRedoService.nls": [], "vs/platform/undoRedo/common/undoRedoService.nls.keys": [], "vs/editor/common/languageFeatureRegistry": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/model", "vs/editor/common/languageSelector"], "vs/nls!vs/editor/contrib/editorState/browser/keybindingCancellation": [], "===anonymous106===": ["vs/editor/contrib/editorState/browser/keybindingCancellation.nls", "vs/editor/contrib/editorState/browser/keybindingCancellation.nls.keys"], "vs/editor/contrib/editorState/browser/keybindingCancellation.nls": [], "vs/editor/contrib/editorState/browser/keybindingCancellation.nls.keys": [], "vs/base/common/observableInternal/base": ["require", "exports", "vs/base/common/observableInternal/logging"], "vs/base/common/observableInternal/derived": ["require", "exports", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/observableInternal/base", "vs/base/common/observableInternal/logging"], "vs/base/common/observableInternal/autorun": ["require", "exports", "vs/base/common/assert", "vs/base/common/lifecycle", "vs/base/common/observableInternal/base", "vs/base/common/observableInternal/logging"], "vs/base/common/observableInternal/utils": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/observableInternal/autorun", "vs/base/common/observableInternal/base", "vs/base/common/observableInternal/logging"], "vs/base/common/observableInternal/logging": ["require", "exports"], "vs/nls!vs/editor/browser/widget/diffEditor/accessibleDiffViewer": [], "vs/css!vs/editor/browser/widget/diffEditor/accessibleDiffViewer": [], "===anonymous107===": ["vs/editor/browser/widget/diffEditor/accessibleDiffViewer.nls", "vs/editor/browser/widget/diffEditor/accessibleDiffViewer.nls.keys"], "vs/editor/browser/widget/diffEditor/accessibleDiffViewer.nls": [], "vs/editor/browser/widget/diffEditor/accessibleDiffViewer.nls.keys": [], "vs/editor/browser/widget/diffEditor/decorations": ["require", "exports", "vs/base/common/codicons", "vs/base/common/htmlContent", "vs/base/common/themables", "vs/editor/common/model/textModel", "vs/nls!vs/editor/browser/widget/diffEditor/decorations", "vs/platform/theme/common/iconRegistry"], "vs/editor/browser/widget/diffEditor/outlineModel": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/iterator", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/nls!vs/editor/browser/widget/diffEditor/hideUnchangedRegionsFeature": [], "===anonymous108===": ["vs/editor/browser/widget/diffEditor/hideUnchangedRegionsFeature.nls", "vs/editor/browser/widget/diffEditor/hideUnchangedRegionsFeature.nls.keys"], "vs/editor/browser/widget/diffEditor/hideUnchangedRegionsFeature.nls": [], "vs/editor/browser/widget/diffEditor/hideUnchangedRegionsFeature.nls.keys": [], "vs/editor/browser/widget/diffEditor/inlineDiffDeletedCodeMargin": ["require", "exports", "vs/base/browser/dom", "vs/base/common/actions", "vs/base/common/codicons", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/themables", "vs/nls!vs/editor/browser/widget/diffEditor/inlineDiffDeletedCodeMargin"], "vs/editor/browser/widget/diffEditor/renderLines": ["require", "exports", "vs/base/browser/trustedTypes", "vs/editor/browser/config/domFontInfo", "vs/editor/common/config/editorOptions", "vs/editor/common/core/stringBuilder", "vs/editor/common/viewLayout/lineDecorations", "vs/editor/common/viewLayout/viewLineRenderer", "vs/editor/common/viewModel"], "vs/nls!vs/editor/browser/widget/diffEditor/movedBlocksLines": [], "===anonymous109===": ["vs/editor/browser/widget/diffEditor/movedBlocksLines.nls", "vs/editor/browser/widget/diffEditor/movedBlocksLines.nls.keys"], "vs/editor/browser/widget/diffEditor/movedBlocksLines.nls": [], "vs/editor/browser/widget/diffEditor/movedBlocksLines.nls.keys": [], "vs/base/browser/ui/scrollbar/scrollbarState": ["require", "exports"], "vs/editor/common/viewModel/overviewZoneManager": ["require", "exports"], "vs/base/common/hotReload": ["require", "exports", "vs/base/common/process"], "vs/nls!vs/editor/browser/widget/diffEditor/diffEditorEditors": [], "===anonymous110===": ["vs/editor/browser/widget/diffEditor/diffEditorEditors.nls", "vs/editor/browser/widget/diffEditor/diffEditorEditors.nls.keys"], "vs/editor/browser/widget/diffEditor/diffEditorEditors.nls": [], "vs/editor/browser/widget/diffEditor/diffEditorEditors.nls.keys": [], "vs/editor/browser/widget/diffEditor/diffProviderFactoryService": ["require", "exports", "vs/editor/browser/widget/diffEditor/workerBasedDocumentDiffProvider", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/beforeEditPositionMapper": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/combineTextEditInfos": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/beforeEditPositionMapper", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length"], "vs/nls!vs/editor/browser/widget/diffEditor/colors": [], "===anonymous111===": ["vs/editor/browser/widget/diffEditor/colors.nls", "vs/editor/browser/widget/diffEditor/colors.nls.keys"], "vs/editor/browser/widget/diffEditor/colors.nls": [], "vs/editor/browser/widget/diffEditor/colors.nls.keys": [], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/bracketPairsTree": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/textModelBracketPairs", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/beforeEditPositionMapper", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/brackets", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/parser", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/tokenizer", "vs/base/common/arrays", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/combineTextEditInfos"], "vs/nls!vs/editor/common/model/editStack": [], "vs/editor/common/core/textChange": ["require", "exports", "vs/base/common/buffer", "vs/editor/common/core/stringBuilder"], "vs/base/common/buffer": ["require", "exports", "vs/base/common/lazy"], "===anonymous112===": ["vs/editor/common/model/editStack.nls", "vs/editor/common/model/editStack.nls.keys"], "vs/editor/common/model/editStack.nls": [], "vs/editor/common/model/editStack.nls.keys": [], "vs/editor/common/model/textModelPart": ["require", "exports", "vs/base/common/lifecycle"], "vs/editor/common/textModelGuides": ["require", "exports"], "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeBase": ["require", "exports", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/model", "vs/editor/common/model/pieceTreeTextBuffer/rbTreeBase", "vs/editor/common/model/textModelSearch"], "vs/editor/common/model/textModelTokens": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/platform", "vs/base/common/stopwatch", "vs/editor/common/core/eolCounter", "vs/editor/common/core/lineRange", "vs/editor/common/core/offsetRange", "vs/editor/common/languages/nullTokenize", "vs/editor/common/model/fixedArray", "vs/editor/common/tokens/contiguousMultilineTokensBuilder", "vs/editor/common/tokens/lineTokens"], "vs/editor/common/tokens/contiguousMultilineTokensBuilder": ["require", "exports", "vs/editor/common/tokens/contiguousMultilineTokens"], "vs/editor/common/tokens/contiguousTokensStore": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/core/position", "vs/editor/common/tokens/contiguousTokensEditing", "vs/editor/common/tokens/lineTokens", "vs/editor/common/encodedTokenAttributes"], "vs/editor/common/tokens/sparseTokensStore": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/tokens/lineTokens"], "vs/editor/browser/controller/mouseHandler": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/mouseEvent", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/editor/browser/controller/mouseTarget", "vs/editor/browser/editorDom", "vs/editor/common/config/editorZoom", "vs/editor/common/core/position", "vs/editor/common/core/selection", "vs/editor/common/viewEventHandler", "vs/base/browser/ui/scrollbar/scrollableElement"], "vs/nls!vs/editor/browser/controller/textAreaHandler": [], "vs/base/browser/ui/mouseCursor/mouseCursor": ["require", "exports", "vs/css!vs/base/browser/ui/mouseCursor/mouseCursor"], "vs/css!vs/editor/browser/controller/textAreaHandler": [], "===anonymous113===": ["vs/editor/browser/controller/textAreaHandler.nls", "vs/editor/browser/controller/textAreaHandler.nls.keys"], "vs/editor/browser/controller/textAreaHandler.nls": [], "vs/editor/browser/controller/textAreaHandler.nls.keys": [], "vs/editor/browser/view/viewLayer": ["require", "exports", "vs/base/browser/fastDomNode", "vs/base/browser/trustedTypes", "vs/base/common/errors", "vs/editor/common/core/stringBuilder"], "vs/editor/browser/view/dynamicViewOverlay": ["require", "exports", "vs/editor/common/viewEventHandler"], "vs/css!vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight": [], "vs/css!vs/editor/browser/viewParts/decorations/decorations": [], "vs/css!vs/editor/browser/viewParts/indentGuides/indentGuides": [], "vs/css!vs/editor/browser/viewParts/lineNumbers/lineNumbers": [], "vs/editor/browser/viewParts/lines/domReadingContext": ["require", "exports"], "vs/editor/browser/viewParts/lines/viewLine": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/fastDomNode", "vs/base/common/platform", "vs/editor/browser/viewParts/lines/rangeUtil", "vs/editor/browser/view/renderingContext", "vs/editor/common/viewLayout/lineDecorations", "vs/editor/common/viewLayout/viewLineRenderer", "vs/platform/theme/common/theme", "vs/editor/common/config/editorOptions"], "vs/css!vs/editor/browser/viewParts/lines/viewLines": [], "vs/css!vs/editor/browser/viewParts/linesDecorations/linesDecorations": [], "vs/css!vs/editor/browser/viewParts/margin/margin": [], "vs/css!vs/editor/browser/viewParts/marginDecorations/marginDecorations": [], "vs/base/browser/globalPointerMoveMonitor": ["require", "exports", "vs/base/browser/dom", "vs/base/common/lifecycle"], "vs/editor/common/core/rgba": ["require", "exports"], "vs/editor/browser/viewParts/minimap/minimapCharRendererFactory": ["require", "exports", "vs/editor/browser/viewParts/minimap/minimapChar<PERSON><PERSON>er", "vs/editor/browser/viewParts/minimap/minimapCharSheet", "vs/editor/browser/viewParts/minimap/minimapPreBaked", "vs/base/common/uint"], "vs/css!vs/editor/browser/viewParts/minimap/minimap": [], "vs/css!vs/editor/browser/viewParts/overlayWidgets/overlayWidgets": [], "vs/css!vs/editor/browser/viewParts/rulers/rulers": [], "vs/css!vs/editor/browser/viewParts/scrollDecoration/scrollDecoration": [], "vs/css!vs/editor/browser/viewParts/selections/selections": [], "vs/editor/browser/viewParts/viewCursors/viewCursor": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/common/strings", "vs/editor/browser/config/domFontInfo", "vs/editor/common/config/editorOptions", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/base/browser/ui/mouseCursor/mouseCursor"], "vs/css!vs/editor/browser/viewParts/viewCursors/viewCursors": [], "vs/editor/common/editorTheme": ["require", "exports"], "vs/css!vs/editor/browser/viewParts/blockDecorations/blockDecorations": [], "vs/css!vs/editor/browser/viewParts/whitespace/whitespace": [], "vs/css!vs/editor/browser/viewParts/glyphMargin/glyphMargin": [], "vs/editor/common/cursor/cursorCollection": ["require", "exports", "vs/base/common/arrays", "vs/base/common/arraysFind", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/cursor/oneCursor", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/editor/common/cursor/cursorContext": ["require", "exports"], "vs/base/common/scrollable": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/editor/common/viewLayout/linesLayout": ["require", "exports", "vs/base/common/strings"], "vs/editor/common/viewModel/modelLineProjection": ["require", "exports", "vs/editor/common/tokens/lineTokens", "vs/editor/common/core/position", "vs/editor/common/textModelEvents", "vs/editor/common/viewModel"], "vs/nls!vs/editor/contrib/codeAction/browser/codeAction": [], "===anonymous114===": ["vs/editor/contrib/codeAction/browser/codeAction.nls", "vs/editor/contrib/codeAction/browser/codeAction.nls.keys"], "vs/editor/contrib/codeAction/browser/codeAction.nls": [], "vs/editor/contrib/codeAction/browser/codeAction.nls.keys": [], "vs/nls!vs/editor/contrib/codeAction/browser/codeActionMenu": [], "===anonymous115===": ["vs/editor/contrib/codeAction/browser/codeActionMenu.nls", "vs/editor/contrib/codeAction/browser/codeActionMenu.nls.keys"], "vs/editor/contrib/codeAction/browser/codeActionMenu.nls": [], "vs/editor/contrib/codeAction/browser/codeActionMenu.nls.keys": [], "vs/nls!vs/platform/actionWidget/browser/actionWidget": [], "vs/platform/actionWidget/browser/actionList": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/keybindingLabel/keybindingLabel", "vs/base/browser/ui/list/listWidget", "vs/base/common/cancellation", "vs/base/common/codicons", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/themables", "vs/nls!vs/platform/actionWidget/browser/actionList", "vs/platform/contextview/browser/contextView", "vs/platform/keybinding/common/keybinding", "vs/platform/theme/browser/defaultStyles", "vs/platform/theme/common/colorRegistry", "vs/css!vs/platform/actionWidget/browser/actionWidget"], "vs/css!vs/platform/actionWidget/browser/actionWidget": [], "===anonymous116===": ["vs/platform/actionWidget/browser/actionWidget.nls", "vs/platform/actionWidget/browser/actionWidget.nls.keys"], "vs/platform/actionWidget/browser/actionWidget.nls": [], "vs/platform/actionWidget/browser/actionWidget.nls.keys": [], "vs/base/common/decorators": ["require", "exports"], "vs/nls!vs/editor/contrib/colorPicker/browser/colorPickerWidget": [], "===anonymous117===": ["vs/editor/contrib/colorPicker/browser/colorPickerWidget.nls", "vs/editor/contrib/colorPicker/browser/colorPickerWidget.nls.keys"], "vs/editor/contrib/colorPicker/browser/colorPickerWidget.nls": [], "vs/editor/contrib/colorPicker/browser/colorPickerWidget.nls.keys": [], "vs/nls!vs/base/browser/ui/iconLabel/iconLabelHover": [], "===anonymous118===": ["vs/base/browser/ui/iconLabel/iconLabelHover.nls", "vs/base/browser/ui/iconLabel/iconLabelHover.nls.keys"], "vs/base/browser/ui/iconLabel/iconLabelHover.nls": [], "vs/base/browser/ui/iconLabel/iconLabelHover.nls.keys": [], "vs/base/browser/ui/selectBox/selectBoxCustom": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/keyboardEvent", "vs/base/browser/markdownRenderer", "vs/base/browser/ui/list/listWidget", "vs/base/common/arrays", "vs/base/common/event", "vs/base/common/keyCodes", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/nls!vs/base/browser/ui/selectBox/selectBoxCustom", "vs/css!vs/base/browser/ui/selectBox/selectBoxCustom"], "vs/base/browser/ui/selectBox/selectBoxNative": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/touch", "vs/base/common/arrays", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform"], "vs/css!vs/base/browser/ui/selectBox/selectBox": [], "vs/base/browser/ui/button/button": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/dompurify/dompurify", "vs/base/browser/keyboardEvent", "vs/base/browser/markdownRenderer", "vs/base/browser/touch", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/common/color", "vs/base/common/event", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/css!vs/base/browser/ui/button/button"], "vs/css!vs/editor/contrib/dropOrPasteInto/browser/postEditWidget": [], "vs/base/common/search": ["require", "exports", "vs/base/common/strings"], "vs/nls!vs/base/browser/ui/findinput/findInputToggles": [], "===anonymous119===": ["vs/base/browser/ui/findinput/findInputToggles.nls", "vs/base/browser/ui/findinput/findInputToggles.nls.keys"], "vs/base/browser/ui/findinput/findInputToggles.nls": [], "vs/base/browser/ui/findinput/findInputToggles.nls.keys": [], "vs/css!vs/base/browser/ui/toggle/toggle": [], "vs/css!vs/base/browser/ui/sash/sash": [], "vs/base/browser/ui/findinput/findInput": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/findinput/findInputToggles", "vs/base/browser/ui/inputbox/inputBox", "vs/base/browser/ui/widget", "vs/base/common/event", "vs/nls!vs/base/browser/ui/findinput/findInput", "vs/base/common/lifecycle", "vs/css!vs/base/browser/ui/findinput/findInput"], "vs/base/browser/ui/findinput/replaceInput": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/toggle/toggle", "vs/base/browser/ui/inputbox/inputBox", "vs/base/browser/ui/widget", "vs/base/common/codicons", "vs/base/common/event", "vs/nls!vs/base/browser/ui/findinput/replaceInput", "vs/css!vs/base/browser/ui/findinput/findInput"], "vs/nls!vs/platform/history/browser/contextScopedHistoryWidget": [], "===anonymous120===": ["vs/platform/history/browser/contextScopedHistoryWidget.nls", "vs/platform/history/browser/contextScopedHistoryWidget.nls.keys"], "vs/platform/history/browser/contextScopedHistoryWidget.nls": [], "vs/platform/history/browser/contextScopedHistoryWidget.nls.keys": [], "vs/nls!vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys": [], "===anonymous121===": ["vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys.nls", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys.nls.keys"], "vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys.nls": [], "vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys.nls.keys": [], "vs/css!vs/editor/contrib/markdownRenderer/browser/renderedMarkdown": [], "vs/editor/contrib/inlineCompletions/browser/ghostText": ["require", "exports", "vs/editor/contrib/inlineCompletions/browser/utils"], "vs/editor/contrib/inlineCompletions/browser/utils": ["require", "exports", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/css!vs/editor/contrib/inlineCompletions/browser/ghostText": [], "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsSource": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/filters", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/editor/common/core/position", "vs/editor/common/languages", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/inlineCompletions/browser/provideInlineCompletions", "vs/editor/contrib/inlineCompletions/browser/singleTextEdit"], "vs/editor/contrib/inlineCompletions/browser/singleTextEdit": ["require", "exports", "vs/base/common/diff/diff", "vs/base/common/strings", "vs/editor/common/core/range", "vs/editor/contrib/inlineCompletions/browser/ghostText", "vs/editor/contrib/inlineCompletions/browser/utils"], "vs/base/browser/formattedTextRenderer": ["require", "exports", "vs/base/browser/dom"], "vs/base/common/marked/marked": ["exports"], "vs/css!vs/editor/contrib/zoneWidget/browser/zoneWidget": [], "vs/base/browser/ui/dropdown/dropdownActionViewItem": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/browser/ui/dropdown/dropdown", "vs/base/common/event", "vs/css!vs/base/browser/ui/dropdown/dropdown"], "vs/base/common/keybindingLabels": ["require", "exports", "vs/nls!vs/base/common/keybindingLabels"], "vs/nls!vs/platform/actions/browser/menuEntryActionViewItem": [], "vs/platform/action/common/action": ["require", "exports"], "vs/css!vs/platform/actions/browser/menuEntryActionViewItem": [], "===anonymous122===": ["vs/platform/actions/browser/menuEntryActionViewItem.nls", "vs/platform/actions/browser/menuEntryActionViewItem.nls.keys"], "vs/platform/actions/browser/menuEntryActionViewItem.nls": [], "vs/platform/actions/browser/menuEntryActionViewItem.nls.keys": [], "vs/base/browser/ui/scrollbar/horizontalScrollbar": ["require", "exports", "vs/base/browser/mouseEvent", "vs/base/browser/ui/scrollbar/abstractScrollbar", "vs/base/browser/ui/scrollbar/scrollbarArrow", "vs/base/browser/ui/scrollbar/scrollbarState", "vs/base/common/codicons"], "vs/base/browser/ui/scrollbar/verticalScrollbar": ["require", "exports", "vs/base/browser/mouseEvent", "vs/base/browser/ui/scrollbar/abstractScrollbar", "vs/base/browser/ui/scrollbar/scrollbarArrow", "vs/base/browser/ui/scrollbar/scrollbarState", "vs/base/common/codicons"], "vs/css!vs/base/browser/ui/scrollbar/media/scrollbars": [], "vs/css!vs/platform/severityIcon/browser/media/severityIcon": [], "vs/nls!vs/base/browser/ui/hover/hoverWidget": [], "vs/css!vs/base/browser/ui/hover/hover": [], "===anonymous123===": ["vs/base/browser/ui/hover/hoverWidget.nls", "vs/base/browser/ui/hover/hoverWidget.nls.keys"], "vs/base/browser/ui/hover/hoverWidget.nls": [], "vs/base/browser/ui/hover/hoverWidget.nls.keys": [], "vs/nls!vs/base/browser/ui/keybindingLabel/keybindingLabel": [], "vs/css!vs/base/browser/ui/keybindingLabel/keybindingLabel": [], "===anonymous124===": ["vs/base/browser/ui/keybindingLabel/keybindingLabel.nls", "vs/base/browser/ui/keybindingLabel/keybindingLabel.nls.keys"], "vs/base/browser/ui/keybindingLabel/keybindingLabel.nls": [], "vs/base/browser/ui/keybindingLabel/keybindingLabel.nls.keys": [], "vs/base/browser/ui/toolbar/toolbar": ["require", "exports", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/dropdown/dropdownActionViewItem", "vs/base/common/actions", "vs/base/common/codicons", "vs/base/common/themables", "vs/base/common/event", "vs/base/common/lifecycle", "vs/nls!vs/base/browser/ui/toolbar/toolbar", "vs/css!vs/base/browser/ui/toolbar/toolbar"], "vs/nls!vs/platform/actions/browser/toolbar": [], "===anonymous125===": ["vs/platform/actions/browser/toolbar.nls", "vs/platform/actions/browser/toolbar.nls.keys"], "vs/platform/actions/browser/toolbar.nls": [], "vs/platform/actions/browser/toolbar.nls.keys": [], "vs/base/common/labels": ["require", "exports", "vs/base/common/extpath", "vs/base/common/platform"], "vs/nls!vs/editor/contrib/snippet/browser/snippetVariables": [], "===anonymous126===": ["vs/editor/contrib/snippet/browser/snippetVariables.nls", "vs/editor/contrib/snippet/browser/snippetVariables.nls.keys"], "vs/editor/contrib/snippet/browser/snippetVariables.nls": [], "vs/editor/contrib/snippet/browser/snippetVariables.nls.keys": [], "vs/nls!vs/platform/action/common/actionCommonCategories": [], "===anonymous127===": ["vs/platform/action/common/actionCommonCategories.nls", "vs/platform/action/common/actionCommonCategories.nls.keys"], "vs/platform/action/common/actionCommonCategories.nls": [], "vs/platform/action/common/actionCommonCategories.nls.keys": [], "vs/css!vs/editor/contrib/stickyScroll/browser/stickyScroll": [], "vs/editor/contrib/stickyScroll/browser/stickyScrollModelProvider": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/documentSymbols/browser/outlineModel", "vs/base/common/async", "vs/editor/contrib/folding/browser/folding", "vs/editor/contrib/folding/browser/syntaxRangeProvider", "vs/editor/contrib/folding/browser/indentRangeProvider", "vs/editor/common/languages/languageConfigurationRegistry", "vs/base/common/errors", "vs/editor/contrib/stickyScroll/browser/stickyScrollElement", "vs/base/common/iterator"], "vs/base/browser/ui/list/splice": ["require", "exports"], "vs/base/browser/ui/list/list": ["require", "exports"], "vs/base/browser/ui/list/listView": ["require", "exports", "vs/base/browser/dnd", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/touch", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/decorators", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/range", "vs/base/common/scrollable", "vs/base/browser/ui/list/rangeMap", "vs/base/browser/ui/list/rowCache", "vs/base/common/errors"], "vs/css!vs/base/browser/ui/list/list": [], "vs/nls!vs/editor/contrib/suggest/browser/suggestWidgetStatus": [], "===anonymous128===": ["vs/editor/contrib/suggest/browser/suggestWidgetStatus.nls", "vs/editor/contrib/suggest/browser/suggestWidgetStatus.nls.keys"], "vs/editor/contrib/suggest/browser/suggestWidgetStatus.nls": [], "vs/editor/contrib/suggest/browser/suggestWidgetStatus.nls.keys": [], "vs/nls!vs/editor/contrib/suggest/browser/suggestWidgetDetails": [], "===anonymous129===": ["vs/editor/contrib/suggest/browser/suggestWidgetDetails.nls", "vs/editor/contrib/suggest/browser/suggestWidgetDetails.nls.keys"], "vs/editor/contrib/suggest/browser/suggestWidgetDetails.nls": [], "vs/editor/contrib/suggest/browser/suggestWidgetDetails.nls.keys": [], "vs/base/browser/ui/iconLabel/iconLabel": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/highlightedlabel/highlightedLabel", "vs/base/browser/ui/iconLabel/iconLabelHover", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/base/common/range", "vs/css!vs/base/browser/ui/iconLabel/iconlabel"], "vs/editor/common/services/getIconClasses": ["require", "exports", "vs/base/common/network", "vs/base/common/resources", "vs/editor/common/languages/modesRegistry", "vs/platform/files/common/files"], "vs/nls!vs/editor/contrib/suggest/browser/suggestWidgetRenderer": [], "vs/platform/files/common/files": ["require", "exports"], "===anonymous130===": ["vs/editor/contrib/suggest/browser/suggestWidgetRenderer.nls", "vs/editor/contrib/suggest/browser/suggestWidgetRenderer.nls.keys"], "vs/editor/contrib/suggest/browser/suggestWidgetRenderer.nls": [], "vs/editor/contrib/suggest/browser/suggestWidgetRenderer.nls.keys": [], "vs/css!vs/platform/opener/browser/link": [], "vs/nls!vs/base/common/errorMessage": [], "===anonymous131===": ["vs/base/common/errorMessage.nls", "vs/base/common/errorMessage.nls.keys"], "vs/base/common/errorMessage.nls": [], "vs/base/common/errorMessage.nls.keys": [], "vs/css!vs/base/browser/ui/table/table": [], "vs/base/browser/ui/inputbox/inputBox": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/formattedTextRenderer", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/aria/aria", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/browser/ui/widget", "vs/base/common/event", "vs/base/common/history", "vs/base/common/objects", "vs/nls!vs/base/browser/ui/inputbox/inputBox", "vs/css!vs/base/browser/ui/inputbox/inputBox"], "vs/base/browser/ui/tree/indexTreeModel": ["require", "exports", "vs/base/browser/ui/tree/tree", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/symbols", "vs/base/common/diff/diff", "vs/base/common/event", "vs/base/common/iterator"], "vs/base/browser/ui/tree/tree": ["require", "exports"], "vs/nls!vs/base/browser/ui/tree/abstractTree": [], "vs/css!vs/base/browser/ui/tree/media/tree": [], "===anonymous132===": ["vs/base/browser/ui/tree/abstractTree.nls", "vs/base/browser/ui/tree/abstractTree.nls.keys"], "vs/base/browser/ui/tree/abstractTree.nls": [], "vs/base/browser/ui/tree/abstractTree.nls.keys": [], "vs/base/browser/ui/tree/objectTreeModel": ["require", "exports", "vs/base/browser/ui/tree/indexTreeModel", "vs/base/browser/ui/tree/tree", "vs/base/common/iterator"], "vs/base/browser/ui/tree/compressedObjectTreeModel": ["require", "exports", "vs/base/browser/ui/tree/objectTreeModel", "vs/base/browser/ui/tree/tree", "vs/base/common/arrays", "vs/base/common/event", "vs/base/common/iterator"], "vs/css!vs/base/browser/ui/splitview/splitview": [], "vs/base/browser/ui/countBadge/countBadge": ["require", "exports", "vs/base/browser/dom", "vs/base/common/strings", "vs/css!vs/base/browser/ui/countBadge/countBadge"], "vs/base/browser/ui/highlightedlabel/highlightedLabel": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/common/objects"], "vs/nls!vs/editor/contrib/gotoSymbol/browser/peek/referencesTree": [], "===anonymous133===": ["vs/editor/contrib/gotoSymbol/browser/peek/referencesTree.nls", "vs/editor/contrib/gotoSymbol/browser/peek/referencesTree.nls.keys"], "vs/editor/contrib/gotoSymbol/browser/peek/referencesTree.nls": [], "vs/editor/contrib/gotoSymbol/browser/peek/referencesTree.nls.keys": [], "vs/base/common/range": ["require", "exports"], "vs/css!vs/base/browser/ui/contextview/contextview": [], "vs/editor/common/services/languagesAssociations": ["require", "exports", "vs/base/common/glob", "vs/base/common/mime", "vs/base/common/network", "vs/base/common/path", "vs/base/common/resources", "vs/base/common/strings", "vs/editor/common/languages/modesRegistry"], "vs/base/browser/ui/menu/menu": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/touch", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/mouseEvent", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/browser/ui/contextview/contextview", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/actions", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/themables", "vs/base/common/iconLabels", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/strings"], "vs/platform/quickinput/browser/quickAccess": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/instantiation/common/instantiation", "vs/platform/quickinput/common/quickAccess", "vs/platform/quickinput/common/quickInput", "vs/platform/registry/common/platform"], "vs/platform/quickinput/browser/quickInputController": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/button/button", "vs/base/browser/ui/countBadge/countBadge", "vs/base/browser/ui/progressbar/progressbar", "vs/base/common/cancellation", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/severity", "vs/nls!vs/platform/quickinput/browser/quickInputController", "vs/platform/quickinput/common/quickInput", "vs/platform/quickinput/browser/quickInputBox", "vs/platform/quickinput/browser/quickInputList", "vs/platform/quickinput/browser/quickInput"], "vs/editor/common/languageSelector": ["require", "exports", "vs/base/common/glob", "vs/base/common/path"], "vs/nls!vs/editor/browser/widget/diffEditor/decorations": [], "===anonymous134===": ["vs/editor/browser/widget/diffEditor/decorations.nls", "vs/editor/browser/widget/diffEditor/decorations.nls.keys"], "vs/editor/browser/widget/diffEditor/decorations.nls": [], "vs/editor/browser/widget/diffEditor/decorations.nls.keys": [], "vs/nls!vs/editor/browser/widget/diffEditor/inlineDiffDeletedCodeMargin": [], "===anonymous135===": ["vs/editor/browser/widget/diffEditor/inlineDiffDeletedCodeMargin.nls", "vs/editor/browser/widget/diffEditor/inlineDiffDeletedCodeMargin.nls.keys"], "vs/editor/browser/widget/diffEditor/inlineDiffDeletedCodeMargin.nls": [], "vs/editor/browser/widget/diffEditor/inlineDiffDeletedCodeMargin.nls.keys": [], "vs/editor/browser/widget/diffEditor/workerBasedDocumentDiffProvider": ["require", "exports", "vs/base/common/event", "vs/base/common/stopwatch", "vs/editor/common/core/lineRange", "vs/editor/common/diff/rangeMapping", "vs/editor/common/services/editorW<PERSON>ker", "vs/platform/telemetry/common/telemetry"], "vs/editor/common/textModelBracketPairs": ["require", "exports"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/brackets": ["require", "exports", "vs/base/common/strings", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/ast", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/tokenizer"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/parser": ["require", "exports", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/ast", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/beforeEditPositionMapper", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/concat23Trees", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/nodeReader"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet": ["require", "exports"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/tokenizer": ["require", "exports", "vs/base/common/errors", "vs/editor/common/encodedTokenAttributes", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/ast", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet"], "vs/editor/common/model/pieceTreeTextBuffer/rbTreeBase": ["require", "exports"], "vs/editor/common/model/fixedArray": ["require", "exports", "vs/base/common/arrays"], "vs/editor/common/tokens/contiguousMultilineTokens": ["require", "exports"], "vs/editor/common/tokens/contiguousTokensEditing": ["require", "exports", "vs/editor/common/tokens/lineTokens"], "vs/css!vs/base/browser/ui/mouseCursor/mouseCursor": [], "vs/editor/browser/viewParts/lines/rangeUtil": ["require", "exports", "vs/editor/browser/view/renderingContext"], "vs/editor/browser/viewParts/minimap/minimapCharRenderer": ["require", "exports", "vs/editor/browser/viewParts/minimap/minimapCharSheet", "vs/base/common/uint"], "vs/editor/browser/viewParts/minimap/minimapCharSheet": ["require", "exports"], "vs/editor/browser/viewParts/minimap/minimapPreBaked": ["require", "exports", "vs/base/common/functional"], "vs/editor/common/cursor/oneCursor": ["require", "exports", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/nls!vs/platform/actionWidget/browser/actionList": [], "===anonymous136===": ["vs/platform/actionWidget/browser/actionList.nls", "vs/platform/actionWidget/browser/actionList.nls.keys"], "vs/platform/actionWidget/browser/actionList.nls": [], "vs/platform/actionWidget/browser/actionList.nls.keys": [], "vs/nls!vs/base/browser/ui/selectBox/selectBoxCustom": [], "vs/css!vs/base/browser/ui/selectBox/selectBoxCustom": [], "===anonymous137===": ["vs/base/browser/ui/selectBox/selectBoxCustom.nls", "vs/base/browser/ui/selectBox/selectBoxCustom.nls.keys"], "vs/base/browser/ui/selectBox/selectBoxCustom.nls": [], "vs/base/browser/ui/selectBox/selectBoxCustom.nls.keys": [], "vs/css!vs/base/browser/ui/button/button": [], "vs/nls!vs/base/browser/ui/findinput/findInput": [], "vs/css!vs/base/browser/ui/findinput/findInput": [], "===anonymous138===": ["vs/base/browser/ui/findinput/findInput.nls", "vs/base/browser/ui/findinput/findInput.nls.keys"], "vs/base/browser/ui/findinput/findInput.nls": [], "vs/base/browser/ui/findinput/findInput.nls.keys": [], "vs/nls!vs/base/browser/ui/findinput/replaceInput": [], "===anonymous139===": ["vs/base/browser/ui/findinput/replaceInput.nls", "vs/base/browser/ui/findinput/replaceInput.nls.keys"], "vs/base/browser/ui/findinput/replaceInput.nls": [], "vs/base/browser/ui/findinput/replaceInput.nls.keys": [], "vs/editor/contrib/inlineCompletions/browser/provideInlineCompletions": ["require", "exports", "vs/base/common/assert", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/map", "vs/base/common/errors", "vs/editor/common/core/range", "vs/editor/common/model/bracketPairsTextModelPart/fixBrackets", "vs/editor/contrib/inlineCompletions/browser/utils", "vs/editor/contrib/snippet/browser/snippetParser"], "vs/base/browser/ui/dropdown/dropdown": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/touch", "vs/base/common/actions", "vs/base/common/event", "vs/css!vs/base/browser/ui/dropdown/dropdown"], "vs/css!vs/base/browser/ui/dropdown/dropdown": [], "vs/nls!vs/base/common/keybindingLabels": [], "===anonymous140===": ["vs/base/common/keybindingLabels.nls", "vs/base/common/keybindingLabels.nls.keys"], "vs/base/common/keybindingLabels.nls": [], "vs/base/common/keybindingLabels.nls.keys": [], "vs/base/browser/ui/scrollbar/abstractScrollbar": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/globalPointerMoveMonitor", "vs/base/browser/ui/scrollbar/scrollbarArrow", "vs/base/browser/ui/scrollbar/scrollbarVisibilityController", "vs/base/browser/ui/widget", "vs/base/common/platform"], "vs/base/browser/ui/scrollbar/scrollbarArrow": ["require", "exports", "vs/base/browser/globalPointerMoveMonitor", "vs/base/browser/ui/widget", "vs/base/common/async", "vs/base/common/themables", "vs/base/browser/dom"], "vs/nls!vs/base/browser/ui/toolbar/toolbar": [], "vs/css!vs/base/browser/ui/toolbar/toolbar": [], "===anonymous141===": ["vs/base/browser/ui/toolbar/toolbar.nls", "vs/base/browser/ui/toolbar/toolbar.nls.keys"], "vs/base/browser/ui/toolbar/toolbar.nls": [], "vs/base/browser/ui/toolbar/toolbar.nls.keys": [], "vs/base/browser/ui/list/rangeMap": ["require", "exports", "vs/base/common/range"], "vs/base/browser/ui/list/rowCache": ["require", "exports", "vs/base/browser/dom"], "vs/css!vs/base/browser/ui/iconLabel/iconlabel": [], "vs/base/common/history": ["require", "exports", "vs/base/common/navigator"], "vs/nls!vs/base/browser/ui/inputbox/inputBox": [], "vs/css!vs/base/browser/ui/inputbox/inputBox": [], "===anonymous142===": ["vs/base/browser/ui/inputbox/inputBox.nls", "vs/base/browser/ui/inputbox/inputBox.nls.keys"], "vs/base/browser/ui/inputbox/inputBox.nls": [], "vs/base/browser/ui/inputbox/inputBox.nls.keys": [], "vs/css!vs/base/browser/ui/countBadge/countBadge": [], "vs/base/common/glob": ["require", "exports", "vs/base/common/async", "vs/base/common/extpath", "vs/base/common/map", "vs/base/common/path", "vs/base/common/platform", "vs/base/common/strings"], "vs/base/browser/ui/progressbar/progressbar": ["require", "exports", "vs/base/browser/dom", "vs/base/common/async", "vs/base/common/lifecycle", "vs/css!vs/base/browser/ui/progressbar/progressbar"], "vs/nls!vs/platform/quickinput/browser/quickInputController": [], "vs/platform/quickinput/browser/quickInputBox": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/mouseEvent", "vs/base/browser/ui/findinput/findInput", "vs/base/common/lifecycle", "vs/base/common/severity", "vs/css!vs/platform/quickinput/browser/media/quickInput"], "vs/platform/quickinput/browser/quickInputList": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/iconLabel/iconLabel", "vs/base/browser/ui/keybindingLabel/keybindingLabel", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/comparers", "vs/base/common/decorators", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/iconLabels", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/strings", "vs/nls!vs/platform/quickinput/browser/quickInputList", "vs/platform/quickinput/browser/quickInputUtils", "vs/base/common/lazy", "vs/base/common/uri", "vs/platform/theme/common/theme", "vs/css!vs/platform/quickinput/browser/media/quickInput"], "vs/platform/quickinput/browser/quickInput": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/ui/toggle/toggle", "vs/base/common/actions", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/severity", "vs/base/common/themables", "vs/nls!vs/platform/quickinput/browser/quickInput", "vs/platform/quickinput/common/quickInput", "vs/platform/quickinput/browser/quickInputList", "vs/platform/quickinput/browser/quickInputUtils", "vs/css!vs/platform/quickinput/browser/media/quickInput"], "===anonymous143===": ["vs/platform/quickinput/browser/quickInputController.nls", "vs/platform/quickinput/browser/quickInputController.nls.keys"], "vs/platform/quickinput/browser/quickInputController.nls": [], "vs/platform/quickinput/browser/quickInputController.nls.keys": [], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/ast": ["require", "exports", "vs/base/common/errors", "vs/editor/common/core/cursorColumns", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/concat23Trees": ["require", "exports", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/ast"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/nodeReader": ["require", "exports", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length"], "vs/editor/common/model/bracketPairsTextModelPart/fixBrackets": ["require", "exports", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/brackets", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/parser", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/tokenizer"], "vs/base/browser/ui/scrollbar/scrollbarVisibilityController": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle"], "vs/base/common/navigator": ["require", "exports"], "vs/css!vs/base/browser/ui/progressbar/progressbar": [], "vs/css!vs/platform/quickinput/browser/media/quickInput": [], "vs/base/common/comparers": ["require", "exports", "vs/base/common/lazy"], "vs/nls!vs/platform/quickinput/browser/quickInputList": [], "vs/platform/quickinput/browser/quickInputUtils": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/common/event", "vs/base/browser/keyboardEvent", "vs/base/browser/touch", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/common/idGenerator", "vs/base/common/linkedText", "vs/nls!vs/platform/quickinput/browser/quickInputUtils", "vs/css!vs/platform/quickinput/browser/media/quickInput"], "===anonymous144===": ["vs/platform/quickinput/browser/quickInputList.nls", "vs/platform/quickinput/browser/quickInputList.nls.keys"], "vs/platform/quickinput/browser/quickInputList.nls": [], "vs/platform/quickinput/browser/quickInputList.nls.keys": [], "vs/nls!vs/platform/quickinput/browser/quickInput": [], "===anonymous145===": ["vs/platform/quickinput/browser/quickInput.nls", "vs/platform/quickinput/browser/quickInput.nls.keys"], "vs/platform/quickinput/browser/quickInput.nls": [], "vs/platform/quickinput/browser/quickInput.nls.keys": [], "vs/base/common/linkedText": ["require", "exports", "vs/base/common/decorators"], "vs/nls!vs/platform/quickinput/browser/quickInputUtils": [], "===anonymous146===": ["vs/platform/quickinput/browser/quickInputUtils.nls", "vs/platform/quickinput/browser/quickInputUtils.nls.keys"], "vs/platform/quickinput/browser/quickInputUtils.nls": [], "vs/platform/quickinput/browser/quickInputUtils.nls.keys": []}, "bundles": {"vs/editor/editor.main": ["vs/base/browser/dompurify/dompurify", "vs/base/browser/fastDomNode", "vs/base/browser/iframe", "vs/base/browser/performance", "vs/base/browser/ui/list/list", "vs/base/browser/ui/list/splice", "vs/base/browser/ui/scrollbar/scrollbarState", "vs/base/browser/ui/tree/tree", "vs/base/common/arrays", "vs/base/common/arraysFind", "vs/base/common/cache", "vs/base/common/collections", "vs/base/common/color", "vs/base/common/decorators", "vs/base/common/diff/diffChange", "vs/base/common/errors", "vs/base/browser/trustedTypes", "vs/base/common/assert", "vs/base/common/functional", "vs/base/common/idGenerator", "vs/base/common/iterator", "vs/base/common/keyCodes", "vs/base/common/keybindings", "vs/base/common/lazy", "vs/base/common/buffer", "vs/base/common/comparers", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/base/common/linkedText", "vs/base/common/map", "vs/base/common/marked/marked", "vs/base/common/mime", "vs/base/browser/dnd", "vs/base/common/navigator", "vs/base/common/history", "vs/base/common/numbers", "vs/base/common/observableInternal/logging", "vs/base/common/observableInternal/base", "vs/base/common/observableInternal/autorun", "vs/base/common/observableInternal/derived", "vs/base/common/observableInternal/utils", "vs/base/common/observable", "vs/base/common/range", "vs/base/browser/ui/list/rangeMap", "vs/base/common/stopwatch", "vs/base/common/event", "vs/base/browser/browser", "vs/base/browser/event", "vs/base/common/cancellation", "vs/base/common/ime", "vs/base/common/scrollable", "vs/base/common/strings", "vs/base/common/filters", "vs/base/common/hash", "vs/base/common/diff/diff", "vs/base/common/search", "vs/base/common/severity", "vs/base/common/symbols", "vs/base/common/ternarySearchTree", "vs/base/common/tfIdf", "vs/base/common/types", "vs/base/common/codicons", "vs/base/common/objects", "vs/base/common/themables", "vs/base/common/iconLabels", "vs/base/common/uint", "vs/base/common/uuid", "vs/base/common/dataTransfer", "vs/css!vs/base/browser/ui/actionbar/actionbar", "vs/css!vs/base/browser/ui/aria/aria", "vs/css!vs/base/browser/ui/button/button", "vs/css!vs/base/browser/ui/codicons/codicon/codicon", "vs/css!vs/base/browser/ui/codicons/codicon/codicon-modifiers", "vs/base/browser/ui/codicons/codiconStyles", "vs/css!vs/base/browser/ui/contextview/contextview", "vs/css!vs/base/browser/ui/countBadge/countBadge", "vs/css!vs/base/browser/ui/dropdown/dropdown", "vs/css!vs/base/browser/ui/findinput/findInput", "vs/css!vs/base/browser/ui/hover/hover", "vs/css!vs/base/browser/ui/iconLabel/iconlabel", "vs/css!vs/base/browser/ui/inputbox/inputBox", "vs/css!vs/base/browser/ui/keybindingLabel/keybindingLabel", "vs/css!vs/base/browser/ui/list/list", "vs/css!vs/base/browser/ui/mouseCursor/mouseCursor", "vs/base/browser/ui/mouseCursor/mouseCursor", "vs/css!vs/base/browser/ui/progressbar/progressbar", "vs/css!vs/base/browser/ui/sash/sash", "vs/css!vs/base/browser/ui/scrollbar/media/scrollbars", "vs/css!vs/base/browser/ui/selectBox/selectBox", "vs/css!vs/base/browser/ui/selectBox/selectBoxCustom", "vs/css!vs/base/browser/ui/splitview/splitview", "vs/css!vs/base/browser/ui/table/table", "vs/css!vs/base/browser/ui/toggle/toggle", "vs/css!vs/base/browser/ui/toolbar/toolbar", "vs/css!vs/base/browser/ui/tree/media/tree", "vs/css!vs/editor/browser/controller/textAreaHandler", "vs/css!vs/editor/browser/viewParts/blockDecorations/blockDecorations", "vs/css!vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight", "vs/css!vs/editor/browser/viewParts/decorations/decorations", "vs/css!vs/editor/browser/viewParts/glyphMargin/glyphMargin", "vs/css!vs/editor/browser/viewParts/indentGuides/indentGuides", "vs/css!vs/editor/browser/viewParts/lineNumbers/lineNumbers", "vs/css!vs/editor/browser/viewParts/lines/viewLines", "vs/css!vs/editor/browser/viewParts/linesDecorations/linesDecorations", "vs/css!vs/editor/browser/viewParts/margin/margin", "vs/css!vs/editor/browser/viewParts/marginDecorations/marginDecorations", "vs/css!vs/editor/browser/viewParts/minimap/minimap", "vs/css!vs/editor/browser/viewParts/overlayWidgets/overlayWidgets", "vs/css!vs/editor/browser/viewParts/rulers/rulers", "vs/css!vs/editor/browser/viewParts/scrollDecoration/scrollDecoration", "vs/css!vs/editor/browser/viewParts/selections/selections", "vs/css!vs/editor/browser/viewParts/viewCursors/viewCursors", "vs/css!vs/editor/browser/viewParts/whitespace/whitespace", "vs/css!vs/editor/browser/widget/diffEditor/accessibleDiff<PERSON>iewer", "vs/css!vs/editor/browser/widget/diffEditor/style", "vs/css!vs/editor/browser/widget/media/editor", "vs/css!vs/editor/contrib/anchorSelect/browser/anchorSelect", "vs/css!vs/editor/contrib/bracketMatching/browser/bracketMatching", "vs/css!vs/editor/contrib/codeAction/browser/lightBulbWidget", "vs/css!vs/editor/contrib/codelens/browser/codelensWidget", "vs/css!vs/editor/contrib/colorPicker/browser/colorPicker", "vs/css!vs/editor/contrib/dnd/browser/dnd", "vs/css!vs/editor/contrib/dropOrPasteInto/browser/postEditWidget", "vs/css!vs/editor/contrib/find/browser/findOptionsWidget", "vs/css!vs/editor/contrib/find/browser/findWidget", "vs/css!vs/editor/contrib/folding/browser/folding", "vs/css!vs/editor/contrib/gotoError/browser/media/gotoErrorWidget", "vs/css!vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition", "vs/css!vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget", "vs/css!vs/editor/contrib/hover/browser/hover", "vs/css!vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace", "vs/css!vs/editor/contrib/inlineCompletions/browser/ghostText", "vs/css!vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget", "vs/css!vs/editor/contrib/inlineProgress/browser/inlineProgressWidget", "vs/css!vs/editor/contrib/linkedEditing/browser/linkedEditing", "vs/css!vs/editor/contrib/links/browser/links", "vs/css!vs/editor/contrib/markdownRenderer/browser/renderedMarkdown", "vs/css!vs/editor/contrib/message/browser/messageController", "vs/css!vs/editor/contrib/parameterHints/browser/parameterHints", "vs/css!vs/editor/contrib/peekView/browser/media/peekViewWidget", "vs/css!vs/editor/contrib/rename/browser/renameInputField", "vs/css!vs/editor/contrib/snippet/browser/snippetSession", "vs/css!vs/editor/contrib/stickyScroll/browser/stickyScroll", "vs/css!vs/editor/contrib/suggest/browser/media/suggest", "vs/css!vs/editor/contrib/symbolIcons/browser/symbolIcons", "vs/css!vs/editor/contrib/unicodeHighlighter/browser/bannerController", "vs/css!vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter", "vs/css!vs/editor/contrib/wordHighlighter/browser/highlightDecorations", "vs/css!vs/editor/contrib/zoneWidget/browser/zoneWidget", "vs/css!vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard", "vs/css!vs/editor/standalone/browser/inspectTokens/inspectTokens", "vs/css!vs/editor/standalone/browser/quickInput/standaloneQuickInput", "vs/css!vs/editor/standalone/browser/standalone-tokens", "vs/css!vs/platform/actionWidget/browser/actionWidget", "vs/css!vs/platform/actions/browser/menuEntryActionViewItem", "vs/css!vs/platform/opener/browser/link", "vs/css!vs/platform/quickinput/browser/media/quickInput", "vs/css!vs/platform/severityIcon/browser/media/severityIcon", "vs/editor/browser/config/domFontInfo", "vs/editor/browser/config/charWidthReader", "vs/editor/browser/config/elementSizeObserver", "vs/editor/browser/config/migrateOptions", "vs/editor/browser/config/tabFocus", "vs/editor/browser/stableEditorScroll", "vs/editor/browser/view/renderingContext", "vs/editor/browser/viewParts/lines/domReadingContext", "vs/editor/browser/viewParts/lines/rangeUtil", "vs/editor/browser/viewParts/minimap/minimapCharSheet", "vs/editor/browser/viewParts/minimap/minimapChar<PERSON><PERSON>er", "vs/editor/browser/viewParts/minimap/minimapPreBaked", "vs/editor/browser/viewParts/minimap/minimapCharRendererFactory", "vs/editor/browser/widget/diffEditor/delegatingEditorImpl", "vs/editor/common/config/diffEditor", "vs/editor/common/config/editorZoom", "vs/editor/common/core/characterClassifier", "vs/editor/common/core/cursorColumns", "vs/editor/common/core/eolCounter", "vs/editor/common/core/indentation", "vs/editor/common/core/offsetRange", "vs/editor/common/core/position", "vs/editor/browser/view/viewUserInputEvents", "vs/editor/common/core/range", "vs/editor/browser/controller/textAreaState", "vs/editor/browser/widget/diffEditor/outlineModel", "vs/editor/common/core/editOperation", "vs/editor/common/commands/trimTrailingWhitespaceCommand", "vs/editor/common/core/lineRange", "vs/editor/common/core/rgba", "vs/editor/common/core/selection", "vs/editor/common/commands/replaceCommand", "vs/editor/common/commands/surroundSelectionCommand", "vs/editor/common/core/textModelDefaults", "vs/editor/common/core/wordCharacterClassifier", "vs/editor/common/core/wordHelper", "vs/editor/common/cursor/cursorAtomicMoveOperations", "vs/editor/common/cursor/cursorContext", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/diffAlgorithm", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/myersDiffAlgorithm", "vs/editor/common/diff/defaultLinesDiffComputer/heuristicSequenceOptimizations", "vs/editor/common/diff/defaultLinesDiffComputer/lineSequence", "vs/editor/common/diff/defaultLinesDiffComputer/utils", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/dynamicProgrammingDiffing", "vs/editor/common/diff/defaultLinesDiffComputer/linesSliceCharSequence", "vs/editor/common/diff/linesDiffComputer", "vs/editor/common/diff/rangeMapping", "vs/editor/common/diff/defaultLinesDiffComputer/computeMovedLines", "vs/editor/common/diff/defaultLinesDiffComputer/defaultLinesDiffComputer", "vs/editor/common/diff/legacyLinesDiffComputer", "vs/editor/common/diff/linesDiffComputers", "vs/editor/common/editorAction", "vs/editor/common/editor<PERSON><PERSON><PERSON>", "vs/editor/browser/editorBrowser", "vs/editor/common/editorFeatures", "vs/editor/common/editorTheme", "vs/editor/common/encodedTokenAttributes", "vs/editor/common/languages/defaultDocumentColorsComputer", "vs/editor/common/languages/languageConfiguration", "vs/editor/common/languages/linkComputer", "vs/editor/common/languages/supports", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/cursor/cursorColumnSelection", "vs/editor/common/cursor/cursorMoveOperations", "vs/editor/common/cursor/cursorDeleteOperations", "vs/editor/common/cursor/cursorWordOperations", "vs/editor/common/cursor/cursorMoveCommands", "vs/editor/common/cursor/oneCursor", "vs/editor/common/cursor/cursorCollection", "vs/editor/common/languages/supports/characterPair", "vs/editor/common/languages/supports/indentRules", "vs/editor/common/languages/supports/inplaceReplaceSupport", "vs/editor/common/languages/supports/languageBracketsConfiguration", "vs/editor/common/languages/supports/onEnter", "vs/editor/common/languages/supports/tokenization", "vs/editor/common/model", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/beforeEditPositionMapper", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/combineTextEditInfos", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/nodeReader", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/ast", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/concat23Trees", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/parser", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/tokenizer", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/brackets", "vs/editor/common/model/bracketPairsTextModelPart/fixBrackets", "vs/editor/common/model/fixedArray", "vs/editor/common/model/indentationG<PERSON>ser", "vs/editor/common/model/intervalTree", "vs/editor/common/model/pieceTreeTextBuffer/rbTreeBase", "vs/editor/common/model/prefixSumComputer", "vs/editor/common/model/mirrorTextModel", "vs/editor/common/model/textModelPart", "vs/editor/common/model/textModelSearch", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeBase", "vs/editor/common/model/utils", "vs/editor/common/modelLineProjectionData", "vs/editor/common/services/treeViewsDnd", "vs/editor/common/services/unicodeTextModelHighlighter", "vs/editor/common/standalone/standaloneEnums", "vs/editor/common/textModelBracketPairs", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/bracketPairsTree", "vs/editor/common/textModelEvents", "vs/editor/common/textModelGuides", "vs/editor/common/model/guidesTextModelPart", "vs/editor/common/tokenizationRegistry", "vs/editor/common/tokens/contiguousMultilineTokens", "vs/editor/common/tokens/contiguousMultilineTokensBuilder", "vs/editor/common/tokens/lineTokens", "vs/editor/common/tokens/contiguousTokensEditing", "vs/editor/common/tokens/contiguousTokensStore", "vs/editor/common/tokens/sparseMultilineTokens", "vs/editor/common/tokens/sparseTokensStore", "vs/editor/common/viewEventHandler", "vs/editor/browser/view/dynamicViewOverlay", "vs/editor/browser/view/viewPart", "vs/editor/browser/viewParts/blockDecorations/blockDecorations", "vs/editor/browser/viewParts/decorations/decorations", "vs/editor/browser/viewParts/glyphMargin/glyphMargin", "vs/editor/browser/viewParts/linesDecorations/linesDecorations", "vs/editor/browser/viewParts/margin/margin", "vs/editor/browser/viewParts/marginDecorations/marginDecorations", "vs/editor/browser/viewParts/overlayWidgets/overlayWidgets", "vs/editor/browser/viewParts/rulers/rulers", "vs/editor/browser/viewParts/scrollDecoration/scrollDecoration", "vs/editor/browser/viewParts/viewZones/viewZones", "vs/editor/common/viewEvents", "vs/editor/common/viewLayout/lineDecorations", "vs/editor/common/viewLayout/linePart", "vs/editor/common/viewLayout/linesLayout", "vs/editor/common/viewLayout/viewLinesViewportData", "vs/editor/common/viewModel", "vs/editor/common/viewModel/modelLineProjection", "vs/editor/common/viewModel/monospaceLineBreaksComputer", "vs/editor/common/viewModel/overviewZoneManager", "vs/editor/browser/viewParts/overviewRuler/overviewRuler", "vs/editor/common/viewModel/viewContext", "vs/editor/common/viewModelEventDispatcher", "vs/editor/common/viewLayout/viewLayout", "vs/editor/contrib/caretOperations/browser/moveCaretCommand", "vs/editor/contrib/codeAction/common/types", "vs/editor/contrib/colorPicker/browser/colorPickerModel", "vs/editor/contrib/comment/browser/blockCommentCommand", "vs/editor/contrib/comment/browser/lineCommentCommand", "vs/editor/contrib/dnd/browser/dragAndDropCommand", "vs/editor/contrib/find/browser/replaceAllCommand", "vs/editor/contrib/find/browser/replacePattern", "vs/editor/contrib/folding/browser/foldingRanges", "vs/editor/contrib/folding/browser/foldingModel", "vs/editor/contrib/folding/browser/hiddenRangeModel", "vs/editor/contrib/folding/browser/indentRangeProvider", "vs/editor/contrib/folding/browser/syntaxRangeProvider", "vs/editor/contrib/format/browser/formattingEdit", "vs/editor/contrib/hover/browser/hoverTypes", "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplaceCommand", "vs/editor/contrib/indentation/browser/indentUtils", "vs/editor/contrib/inlineCompletions/browser/commandIds", "vs/editor/contrib/inlineCompletions/browser/utils", "vs/editor/contrib/inlineCompletions/browser/ghostText", "vs/editor/contrib/inlineCompletions/browser/singleTextEdit", "vs/editor/contrib/linesOperations/browser/copyLinesCommand", "vs/editor/contrib/linesOperations/browser/sortLinesCommand", "vs/editor/contrib/semanticTokens/common/semanticTokensConfig", "vs/editor/contrib/smartSelect/browser/bracketSelections", "vs/editor/contrib/smartSelect/browser/wordSelections", "vs/editor/contrib/snippet/browser/snippetParser", "vs/editor/contrib/stickyScroll/browser/stickyScrollElement", "vs/editor/contrib/suggest/browser/completionModel", "vs/editor/contrib/suggest/browser/suggestCommitCharacters", "vs/editor/contrib/suggest/browser/suggestOvertypingCapturer", "vs/editor/contrib/suggest/browser/wordDistance", "vs/editor/standalone/common/monarch/monarch<PERSON><PERSON>mon", "vs/editor/standalone/common/monarch/monarchCompile", "vs/nls!vs/base/browser/ui/actionbar/actionViewItems", "vs/nls!vs/base/browser/ui/findinput/findInput", "vs/nls!vs/base/browser/ui/findinput/findInputToggles", "vs/nls!vs/base/browser/ui/findinput/replaceInput", "vs/nls!vs/base/browser/ui/hover/hoverWidget", "vs/nls!vs/base/browser/ui/iconLabel/iconLabelHover", "vs/nls!vs/base/browser/ui/inputbox/inputBox", "vs/nls!vs/base/browser/ui/keybindingLabel/keybindingLabel", "vs/nls!vs/base/browser/ui/selectBox/selectBoxCustom", "vs/nls!vs/base/browser/ui/toolbar/toolbar", "vs/nls!vs/base/browser/ui/tree/abstractTree", "vs/nls!vs/base/common/actions", "vs/base/common/actions", "vs/nls!vs/base/common/errorMessage", "vs/base/common/errorMessage", "vs/nls!vs/base/common/keybindingLabels", "vs/base/common/keybindingLabels", "vs/nls!vs/base/common/platform", "vs/base/common/platform", "vs/base/browser/canIUse", "vs/base/browser/keyboardEvent", "vs/base/browser/mouseEvent", "vs/base/common/async", "vs/base/browser/ui/scrollbar/scrollbarVisibilityController", "vs/base/browser/ui/tree/indexTreeModel", "vs/base/browser/ui/tree/objectTreeModel", "vs/base/browser/ui/tree/compressedObjectTreeModel", "vs/base/common/process", "vs/base/common/hotReload", "vs/base/common/path", "vs/base/common/extpath", "vs/base/common/fuzzyScorer", "vs/base/common/glob", "vs/base/common/labels", "vs/base/common/uri", "vs/base/common/marshalling", "vs/base/common/network", "vs/base/browser/dom", "vs/base/browser/formattedTextRenderer", "vs/base/browser/globalPointerMoveMonitor", "vs/base/browser/touch", "vs/base/browser/ui/aria/aria", "vs/base/browser/ui/contextview/contextview", "vs/base/browser/ui/countBadge/countBadge", "vs/base/browser/ui/dropdown/dropdown", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/browser/ui/highlightedlabel/highlightedLabel", "vs/base/browser/ui/keybindingLabel/keybindingLabel", "vs/base/browser/ui/list/rowCache", "vs/base/browser/ui/progressbar/progressbar", "vs/base/browser/ui/sash/sash", "vs/base/browser/ui/resizable/resizable", "vs/base/browser/ui/selectBox/selectBoxNative", "vs/base/browser/ui/widget", "vs/base/browser/ui/scrollbar/scrollbarArrow", "vs/base/browser/ui/scrollbar/abstractScrollbar", "vs/base/browser/ui/scrollbar/horizontalScrollbar", "vs/base/browser/ui/scrollbar/verticalScrollbar", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/browser/ui/hover/hoverWidget", "vs/base/browser/ui/list/listView", "vs/base/browser/ui/list/listWidget", "vs/base/browser/ui/list/listPaging", "vs/base/browser/ui/splitview/splitview", "vs/base/browser/ui/table/tableWidget", "vs/base/browser/ui/toggle/toggle", "vs/base/browser/ui/findinput/findInputToggles", "vs/base/common/resources", "vs/base/common/htmlContent", "vs/base/browser/markdownRenderer", "vs/base/browser/ui/button/button", "vs/base/browser/ui/iconLabel/iconLabelHover", "vs/base/browser/ui/iconLabel/iconLabel", "vs/base/browser/ui/selectBox/selectBoxCustom", "vs/base/browser/ui/selectBox/selectBox", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/dropdown/dropdownActionViewItem", "vs/base/browser/ui/inputbox/inputBox", "vs/base/browser/ui/findinput/findInput", "vs/base/browser/ui/findinput/replaceInput", "vs/base/browser/ui/menu/menu", "vs/base/browser/ui/toolbar/toolbar", "vs/base/browser/ui/tree/abstractTree", "vs/base/browser/ui/tree/dataTree", "vs/base/browser/ui/tree/objectTree", "vs/base/browser/ui/tree/asyncDataTree", "vs/base/common/worker/simpleWorker", "vs/base/browser/defaultWorkerFactory", "vs/base/parts/storage/common/storage", "vs/editor/browser/controller/textAreaInput", "vs/editor/browser/viewParts/contentWidgets/contentWidgets", "vs/editor/browser/widget/codeEditorContributions", "vs/editor/browser/widget/diffEditor/diffEditorSash", "vs/editor/browser/widget/diffEditor/utils", "vs/editor/common/core/stringBuilder", "vs/editor/browser/view/domLineBreaksComputer", "vs/editor/browser/view/viewLayer", "vs/editor/browser/view/viewOverlays", "vs/editor/common/core/textChange", "vs/editor/common/languageSelector", "vs/editor/common/languageFeatureRegistry", "vs/editor/common/languages/supports/richEditBrackets", "vs/editor/common/languages/supports/electricCharacter", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsImpl", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBuffer", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBufferBuilder", "vs/editor/common/services/semanticTokensDto", "vs/editor/contrib/gotoSymbol/browser/link/clickLinkGesture", "vs/editor/contrib/hover/browser/hoverOperation", "vs/editor/contrib/hover/browser/resizableContentWidget", "vs/editor/contrib/inlayHints/browser/inlayHints", "vs/editor/contrib/inlineCompletions/browser/provideInlineCompletions", "vs/nls!vs/editor/browser/controller/textAreaHandler", "vs/nls!vs/editor/browser/coreCommands", "vs/nls!vs/editor/browser/editorExtensions", "vs/nls!vs/editor/browser/widget/codeEditorWidget", "vs/nls!vs/editor/browser/widget/diffEditor/accessibleDiffViewer", "vs/nls!vs/editor/browser/widget/diffEditor/colors", "vs/nls!vs/editor/browser/widget/diffEditor/decorations", "vs/nls!vs/editor/browser/widget/diffEditor/diffEditor.contribution", "vs/nls!vs/editor/browser/widget/diffEditor/diffEditorEditors", "vs/nls!vs/editor/browser/widget/diffEditor/hideUnchangedRegionsFeature", "vs/nls!vs/editor/browser/widget/diffEditor/inlineDiffDeletedCodeMargin", "vs/editor/browser/widget/diffEditor/inlineDiffDeletedCodeMargin", "vs/nls!vs/editor/browser/widget/diffEditor/movedBlocksLines", "vs/editor/browser/widget/diffEditor/movedBlocksLines", "vs/nls!vs/editor/common/config/editorConfigurationSchema", "vs/nls!vs/editor/common/config/editorOptions", "vs/editor/common/config/editorOptions", "vs/editor/browser/viewParts/viewCursors/viewCursor", "vs/editor/browser/widget/diffEditor/diffEditorOptions", "vs/editor/common/config/fontInfo", "vs/editor/browser/config/fontMeasurements", "vs/editor/common/viewModel/viewModelDecorations", "vs/nls!vs/editor/common/core/editorColorRegistry", "vs/nls!vs/editor/common/editorContextKeys", "vs/nls!vs/editor/common/languages", "vs/editor/common/languages", "vs/editor/common/languages/nullTokenize", "vs/editor/common/languages/textToHtmlTokenizer", "vs/editor/common/model/textModelTokens", "vs/editor/common/model/tokenizationTextModelPart", "vs/editor/common/services/editorBaseApi", "vs/editor/common/services/editorSimpleWorker", "vs/editor/common/viewModel/minimapTokensColorTracker", "vs/nls!vs/editor/common/languages/modesRegistry", "vs/nls!vs/editor/common/model/editStack", "vs/editor/common/model/editStack", "vs/nls!vs/editor/common/standaloneStrings", "vs/editor/common/standaloneStrings", "vs/nls!vs/editor/common/viewLayout/viewLineRenderer", "vs/editor/common/viewLayout/viewLineRenderer", "vs/editor/browser/widget/diffEditor/renderLines", "vs/nls!vs/editor/contrib/anchorSelect/browser/anchorSelect", "vs/nls!vs/editor/contrib/bracketMatching/browser/bracketMatching", "vs/nls!vs/editor/contrib/caretOperations/browser/caretOperations", "vs/nls!vs/editor/contrib/caretOperations/browser/transpose", "vs/nls!vs/editor/contrib/clipboard/browser/clipboard", "vs/nls!vs/editor/contrib/codeAction/browser/codeAction", "vs/nls!vs/editor/contrib/codeAction/browser/codeActionCommands", "vs/nls!vs/editor/contrib/codeAction/browser/codeActionContributions", "vs/nls!vs/editor/contrib/codeAction/browser/codeActionController", "vs/nls!vs/editor/contrib/codeAction/browser/codeActionMenu", "vs/nls!vs/editor/contrib/codeAction/browser/lightBulbWidget", "vs/nls!vs/editor/contrib/codelens/browser/codelensController", "vs/nls!vs/editor/contrib/colorPicker/browser/colorPickerWidget", "vs/nls!vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions", "vs/nls!vs/editor/contrib/comment/browser/comment", "vs/nls!vs/editor/contrib/contextmenu/browser/contextmenu", "vs/nls!vs/editor/contrib/cursorUndo/browser/cursorUndo", "vs/nls!vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution", "vs/nls!vs/editor/contrib/dropOrPasteInto/browser/copyPasteController", "vs/nls!vs/editor/contrib/dropOrPasteInto/browser/defaultProviders", "vs/nls!vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution", "vs/nls!vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController", "vs/nls!vs/editor/contrib/editorState/browser/keybindingCancellation", "vs/nls!vs/editor/contrib/find/browser/findController", "vs/nls!vs/editor/contrib/find/browser/findWidget", "vs/nls!vs/editor/contrib/folding/browser/folding", "vs/nls!vs/editor/contrib/folding/browser/foldingDecorations", "vs/nls!vs/editor/contrib/fontZoom/browser/fontZoom", "vs/nls!vs/editor/contrib/format/browser/format", "vs/nls!vs/editor/contrib/format/browser/formatActions", "vs/nls!vs/editor/contrib/gotoError/browser/gotoError", "vs/nls!vs/editor/contrib/gotoError/browser/gotoErrorWidget", "vs/nls!vs/editor/contrib/gotoSymbol/browser/goToCommands", "vs/nls!vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition", "vs/nls!vs/editor/contrib/gotoSymbol/browser/peek/referencesController", "vs/nls!vs/editor/contrib/gotoSymbol/browser/peek/referencesTree", "vs/nls!vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget", "vs/nls!vs/editor/contrib/gotoSymbol/browser/referencesModel", "vs/editor/contrib/gotoSymbol/browser/referencesModel", "vs/nls!vs/editor/contrib/gotoSymbol/browser/symbolNavigation", "vs/nls!vs/editor/contrib/hover/browser/hover", "vs/nls!vs/editor/contrib/hover/browser/markdownHoverParticipant", "vs/nls!vs/editor/contrib/hover/browser/markerHoverParticipant", "vs/nls!vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace", "vs/nls!vs/editor/contrib/indentation/browser/indentation", "vs/nls!vs/editor/contrib/inlayHints/browser/inlayHintsHover", "vs/nls!vs/editor/contrib/inlineCompletions/browser/commands", "vs/nls!vs/editor/contrib/inlineCompletions/browser/hoverParticipant", "vs/nls!vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys", "vs/nls!vs/editor/contrib/inlineCompletions/browser/inlineCompletionsController", "vs/nls!vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget", "vs/nls!vs/editor/contrib/lineSelection/browser/lineSelection", "vs/nls!vs/editor/contrib/linesOperations/browser/linesOperations", "vs/nls!vs/editor/contrib/linkedEditing/browser/linkedEditing", "vs/nls!vs/editor/contrib/links/browser/links", "vs/nls!vs/editor/contrib/message/browser/messageController", "vs/nls!vs/editor/contrib/multicursor/browser/multicursor", "vs/nls!vs/editor/contrib/parameterHints/browser/parameterHints", "vs/nls!vs/editor/contrib/parameterHints/browser/parameterHintsWidget", "vs/nls!vs/editor/contrib/peekView/browser/peekView", "vs/nls!vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess", "vs/nls!vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess", "vs/nls!vs/editor/contrib/readOnlyMessage/browser/contribution", "vs/nls!vs/editor/contrib/rename/browser/rename", "vs/nls!vs/editor/contrib/rename/browser/renameInputField", "vs/nls!vs/editor/contrib/smartSelect/browser/smartSelect", "vs/nls!vs/editor/contrib/snippet/browser/snippetController2", "vs/nls!vs/editor/contrib/snippet/browser/snippetVariables", "vs/nls!vs/editor/contrib/stickyScroll/browser/stickyScrollActions", "vs/nls!vs/editor/contrib/suggest/browser/suggest", "vs/nls!vs/editor/contrib/suggest/browser/suggestController", "vs/nls!vs/editor/contrib/suggest/browser/suggestWidget", "vs/nls!vs/editor/contrib/suggest/browser/suggestWidgetDetails", "vs/nls!vs/editor/contrib/suggest/browser/suggestWidgetRenderer", "vs/nls!vs/editor/contrib/suggest/browser/suggestWidgetStatus", "vs/nls!vs/editor/contrib/symbolIcons/browser/symbolIcons", "vs/nls!vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode", "vs/nls!vs/editor/contrib/tokenization/browser/tokenization", "vs/nls!vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter", "vs/nls!vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators", "vs/nls!vs/editor/contrib/wordHighlighter/browser/highlightDecorations", "vs/nls!vs/editor/contrib/wordHighlighter/browser/wordHighlighter", "vs/nls!vs/editor/contrib/wordOperations/browser/wordOperations", "vs/nls!vs/platform/action/common/actionCommonCategories", "vs/nls!vs/platform/actionWidget/browser/actionList", "vs/nls!vs/platform/actionWidget/browser/actionWidget", "vs/nls!vs/platform/actions/browser/menuEntryActionViewItem", "vs/nls!vs/platform/actions/browser/toolbar", "vs/nls!vs/platform/actions/common/menuService", "vs/nls!vs/platform/audioCues/browser/audioCueService", "vs/nls!vs/platform/configuration/common/configurationRegistry", "vs/nls!vs/platform/contextkey/browser/contextKeyService", "vs/nls!vs/platform/contextkey/common/contextkey", "vs/nls!vs/platform/contextkey/common/contextkeys", "vs/nls!vs/platform/contextkey/common/scanner", "vs/nls!vs/platform/history/browser/contextScopedHistoryWidget", "vs/nls!vs/platform/keybinding/common/abstractKeybindingService", "vs/nls!vs/platform/list/browser/listService", "vs/nls!vs/platform/markers/common/markers", "vs/nls!vs/platform/quickinput/browser/commandsQuickAccess", "vs/nls!vs/platform/quickinput/browser/helpQuickAccess", "vs/nls!vs/platform/quickinput/browser/quickInput", "vs/nls!vs/platform/quickinput/browser/quickInputController", "vs/nls!vs/platform/quickinput/browser/quickInputList", "vs/nls!vs/platform/quickinput/browser/quickInputUtils", "vs/nls!vs/platform/theme/common/colorRegistry", "vs/nls!vs/platform/theme/common/iconRegistry", "vs/nls!vs/platform/undoRedo/common/undoRedoService", "vs/nls!vs/platform/workspace/common/workspace", "vs/platform/action/common/action", "vs/platform/action/common/actionCommonCategories", "vs/platform/contextkey/common/scanner", "vs/platform/editor/common/editor", "vs/platform/extensions/common/extensions", "vs/platform/files/common/files", "vs/platform/history/browser/historyWidgetKeybindingHint", "vs/platform/instantiation/common/descriptors", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/graph", "vs/platform/instantiation/common/instantiation", "vs/editor/browser/services/bulkEditService", "vs/editor/browser/services/codeEditorService", "vs/editor/common/languages/language", "vs/editor/common/services/editorW<PERSON>ker", "vs/editor/common/services/languageFeatures", "vs/editor/browser/widget/diffEditor/hideUnchangedRegionsFeature", "vs/editor/common/services/languageFeaturesService", "vs/editor/common/services/markerDecorations", "vs/editor/common/services/model", "vs/editor/common/services/resolverService", "vs/editor/common/services/semanticTokensStyling", "vs/editor/common/services/textResourceConfiguration", "vs/editor/common/services/treeViewsDndService", "vs/editor/contrib/dropOrPasteInto/browser/edit", "vs/editor/contrib/inlineCompletions/browser/ghostTextWidget", "vs/editor/standalone/common/standaloneTheme", "vs/platform/audioCues/browser/audioCueService", "vs/platform/clipboard/common/clipboardService", "vs/platform/commands/common/commands", "vs/editor/contrib/codelens/browser/codelens", "vs/editor/contrib/links/browser/getLinks", "vs/editor/contrib/semanticTokens/common/getSemanticTokens", "vs/platform/configuration/common/configuration", "vs/editor/standalone/common/monarch/monarchLexer", "vs/editor/standalone/browser/colorizer", "vs/platform/contextkey/common/contextkey", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys", "vs/editor/contrib/parameterHints/browser/provideSignatureHelp", "vs/editor/contrib/parameterHints/browser/parameterHintsModel", "vs/editor/contrib/suggest/browser/suggestAlternatives", "vs/editor/contrib/suggest/browser/wordContextKey", "vs/platform/accessibility/common/accessibility", "vs/editor/browser/config/editorConfiguration", "vs/platform/contextkey/browser/contextKeyService", "vs/platform/contextkey/common/contextkeys", "vs/platform/contextview/browser/contextView", "vs/platform/dialogs/common/dialogs", "vs/platform/environment/common/environment", "vs/platform/instantiation/common/serviceCollection", "vs/platform/instantiation/common/instantiationService", "vs/platform/keybinding/common/baseResolvedKeybinding", "vs/platform/keybinding/common/keybinding", "vs/editor/contrib/dropOrPasteInto/browser/postEditWidget", "vs/platform/keybinding/common/keybindingResolver", "vs/platform/keybinding/common/abstractKeybindingService", "vs/platform/keybinding/common/resolvedKeybindingItem", "vs/platform/keybinding/common/usLayoutResolvedKeybinding", "vs/platform/label/common/label", "vs/platform/layout/browser/layoutService", "vs/editor/standalone/browser/standaloneLayoutService", "vs/platform/accessibility/browser/accessibilityService", "vs/platform/contextview/browser/contextViewService", "vs/platform/log/common/log", "vs/platform/clipboard/browser/clipboardService", "vs/platform/log/common/logService", "vs/platform/markers/common/markers", "vs/editor/contrib/gotoError/browser/markerNavigationService", "vs/platform/markers/common/markerService", "vs/platform/notification/common/notification", "vs/platform/opener/common/opener", "vs/editor/browser/services/openerService", "vs/editor/common/services/languageFeatureDebounce", "vs/editor/contrib/documentSymbols/browser/outlineModel", "vs/editor/contrib/documentSymbols/browser/documentSymbols", "vs/platform/opener/browser/link", "vs/platform/progress/common/progress", "vs/platform/quickinput/browser/pickerQuickAccess", "vs/platform/quickinput/browser/quickInputBox", "vs/platform/quickinput/browser/quickInputUtils", "vs/platform/quickinput/common/quickInput", "vs/platform/registry/common/platform", "vs/platform/dnd/browser/dnd", "vs/editor/browser/dnd", "vs/platform/jsonschemas/common/jsonContributionRegistry", "vs/platform/configuration/common/configurationRegistry", "vs/editor/common/config/editorConfigurationSchema", "vs/editor/common/languages/modesRegistry", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/browser/services/editorWorkerService", "vs/editor/browser/services/webWorker", "vs/editor/common/languages/autoIndent", "vs/editor/common/languages/enterAction", "vs/editor/common/commands/shiftCommand", "vs/editor/common/cursor/cursorTypeOperations", "vs/editor/common/cursor/cursor", "vs/editor/common/services/getIconClasses", "vs/editor/common/services/languagesAssociations", "vs/editor/common/services/languagesRegistry", "vs/editor/common/services/languageService", "vs/editor/contrib/colorPicker/browser/defaultDocumentColorProvider", "vs/editor/contrib/colorPicker/browser/color", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsSource", "vs/editor/contrib/linesOperations/browser/moveLinesCommand", "vs/editor/contrib/markdownRenderer/browser/markdownRenderer", "vs/editor/contrib/hover/browser/marginHover", "vs/editor/contrib/suggest/browser/suggestWidgetDetails", "vs/platform/configuration/common/configurationModels", "vs/platform/configuration/common/configurations", "vs/platform/keybinding/common/keybindingsRegistry", "vs/platform/actions/common/actions", "vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode", "vs/platform/history/browser/contextScopedHistoryWidget", "vs/editor/contrib/suggest/browser/suggest", "vs/platform/quickinput/common/quickAccess", "vs/platform/quickinput/browser/helpQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneHelpQuickAccess", "vs/platform/quickinput/browser/quickAccess", "vs/platform/severityIcon/browser/severityIcon", "vs/platform/storage/common/storage", "vs/editor/contrib/codelens/browser/codeLensCache", "vs/editor/contrib/suggest/browser/suggestMemory", "vs/platform/actions/common/menuService", "vs/platform/telemetry/common/telemetry", "vs/editor/browser/editorExtensions", "vs/editor/browser/coreCommands", "vs/editor/browser/services/markerDecorations", "vs/editor/browser/view/viewController", "vs/editor/browser/widget/diffEditor/workerBasedDocumentDiffProvider", "vs/editor/browser/widget/diffEditor/diffProviderFactoryService", "vs/editor/browser/widget/diffEditor/diffEditorViewModel", "vs/editor/contrib/anchorSelect/browser/anchorSelect", "vs/editor/contrib/caretOperations/browser/caretOperations", "vs/editor/contrib/caretOperations/browser/transpose", "vs/editor/contrib/clipboard/browser/clipboard", "vs/editor/contrib/comment/browser/comment", "vs/editor/contrib/cursorUndo/browser/cursorUndo", "vs/editor/contrib/editorState/browser/keybindingCancellation", "vs/editor/contrib/editorState/browser/editorState", "vs/editor/contrib/codeAction/browser/codeAction", "vs/editor/contrib/codeAction/browser/codeActionKeybindingResolver", "vs/editor/contrib/codeAction/browser/codeActionModel", "vs/editor/contrib/codeAction/browser/lightBulbWidget", "vs/editor/contrib/fontZoom/browser/fontZoom", "vs/editor/contrib/format/browser/format", "vs/editor/contrib/format/browser/formatActions", "vs/editor/contrib/gotoSymbol/browser/goToSymbol", "vs/editor/contrib/gotoSymbol/browser/symbolNavigation", "vs/editor/contrib/hover/browser/getHover", "vs/editor/contrib/hover/browser/markdownHoverParticipant", "vs/editor/contrib/indentation/browser/indentation", "vs/editor/contrib/lineSelection/browser/lineSelection", "vs/editor/contrib/linesOperations/browser/linesOperations", "vs/editor/contrib/longLinesHelper/browser/longLinesHelper", "vs/editor/contrib/message/browser/messageController", "vs/editor/contrib/readOnlyMessage/browser/contribution", "vs/editor/contrib/smartSelect/browser/smartSelect", "vs/editor/contrib/suggest/browser/suggestInlineCompletions", "vs/editor/contrib/tokenization/browser/tokenization", "vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators", "vs/editor/contrib/wordOperations/browser/wordOperations", "vs/editor/contrib/wordPartOperations/browser/wordPartOperations", "vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard", "vs/editor/standalone/browser/inspectTokens/inspectTokens", "vs/platform/actions/browser/toolbar", "vs/platform/quickinput/browser/commandsQuickAccess", "vs/editor/contrib/quickAccess/browser/commandsQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneCommandsQuickAccess", "vs/platform/theme/common/colorRegistry", "vs/editor/browser/editorDom", "vs/editor/browser/viewParts/minimap/minimap", "vs/editor/browser/widget/diffEditor/colors", "vs/editor/contrib/symbolIcons/browser/symbolIcons", "vs/editor/contrib/codeAction/browser/codeActionMenu", "vs/platform/theme/browser/defaultStyles", "vs/editor/contrib/gotoSymbol/browser/peek/referencesTree", "vs/platform/actionWidget/browser/actionList", "vs/platform/actionWidget/browser/actionWidget", "vs/platform/contextview/browser/contextMenuHandler", "vs/platform/list/browser/listService", "vs/platform/theme/common/iconRegistry", "vs/editor/browser/widget/diffEditor/accessibleDiffViewer", "vs/editor/contrib/colorPicker/browser/colorPickerWidget", "vs/editor/contrib/parameterHints/browser/parameterHintsWidget", "vs/editor/contrib/parameterHints/browser/parameterHints", "vs/editor/contrib/unicodeHighlighter/browser/bannerController", "vs/platform/theme/browser/iconsStyleSheet", "vs/platform/theme/common/theme", "vs/editor/browser/viewParts/lines/viewLine", "vs/editor/browser/controller/mouseTarget", "vs/editor/browser/controller/mouseHandler", "vs/editor/browser/controller/pointerHandler", "vs/editor/browser/viewParts/lines/viewLines", "vs/platform/quickinput/browser/quickInputList", "vs/platform/quickinput/browser/quickInput", "vs/platform/quickinput/browser/quickInputController", "vs/platform/theme/common/themeService", "vs/editor/browser/services/abstractCodeEditorService", "vs/editor/browser/viewParts/editorScrollbar/editorScrollbar", "vs/editor/browser/viewParts/selections/selections", "vs/editor/browser/widget/diffEditor/overviewRulerPart", "vs/editor/browser/widget/diffEditor/diffEditorEditors", "vs/editor/common/core/editorColorRegistry", "vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight", "vs/editor/browser/viewParts/indentGuides/indentGuides", "vs/editor/browser/viewParts/lineNumbers/lineNumbers", "vs/editor/browser/controller/text<PERSON><PERSON>Hand<PERSON>", "vs/editor/browser/viewParts/overviewRuler/decorationsOverviewRuler", "vs/editor/browser/viewParts/viewCursors/viewCursors", "vs/editor/browser/viewParts/whitespace/whitespace", "vs/editor/browser/view", "vs/editor/common/model/bracketPairsTextModelPart/colorizedBracketPairsDecorationProvider", "vs/editor/common/services/markerDecorationsService", "vs/editor/common/services/semanticTokensProviderStyling", "vs/editor/common/services/semanticTokensStylingService", "vs/editor/contrib/quickAccess/browser/editorNavigationQuickAccess", "vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess", "vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess", "vs/editor/contrib/rename/browser/renameInputField", "vs/editor/contrib/rename/browser/rename", "vs/editor/contrib/semanticTokens/browser/documentSemanticTokens", "vs/editor/contrib/semanticTokens/browser/viewportSemanticTokens", "vs/editor/contrib/suggest/browser/suggestWidgetRenderer", "vs/editor/standalone/browser/quickAccess/standaloneGotoLineQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneGotoSymbolQuickAccess", "vs/editor/standalone/browser/standaloneCodeEditorService", "vs/editor/standalone/common/themes", "vs/editor/standalone/browser/standaloneThemeService", "vs/editor/standalone/browser/toggleHighContrast/toggleHighContrast", "vs/platform/actions/browser/menuEntryActionViewItem", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget", "vs/editor/contrib/suggest/browser/suggestWidgetStatus", "vs/platform/contextview/browser/contextMenuService", "vs/platform/quickinput/browser/quickInputService", "vs/editor/standalone/browser/quickInput/standaloneQuickInputService", "vs/platform/undoRedo/common/undoRedo", "vs/editor/common/model/textModel", "vs/editor/browser/widget/diffEditor/decorations", "vs/editor/browser/widget/diffEditor/diffEditorDecorations", "vs/editor/browser/widget/diffEditor/lineAlignment", "vs/editor/common/services/modelService", "vs/editor/common/viewModel/viewModelLines", "vs/editor/common/viewModel/viewModelImpl", "vs/editor/browser/widget/codeEditorWidget", "vs/editor/browser/widget/diffEditor/diffEditorWidget", "vs/editor/browser/widget/diffEditor/diffEditor.contribution", "vs/editor/browser/widget/embeddedCodeEditorWidget", "vs/editor/contrib/bracketMatching/browser/bracketMatching", "vs/editor/contrib/codeAction/browser/codeActionController", "vs/editor/contrib/codeAction/browser/codeActionCommands", "vs/editor/contrib/codeAction/browser/codeActionContributions", "vs/editor/contrib/codelens/browser/codelensWidget", "vs/editor/contrib/codelens/browser/codelensController", "vs/editor/contrib/colorPicker/browser/colorDetector", "vs/editor/contrib/colorPicker/browser/colorHoverParticipant", "vs/editor/contrib/dnd/browser/dnd", "vs/editor/contrib/find/browser/findDecorations", "vs/editor/contrib/find/browser/findModel", "vs/editor/contrib/find/browser/findOptionsWidget", "vs/editor/contrib/find/browser/findState", "vs/editor/contrib/find/browser/findWidget", "vs/editor/contrib/find/browser/findController", "vs/editor/contrib/folding/browser/foldingDecorations", "vs/editor/contrib/folding/browser/folding", "vs/editor/contrib/hover/browser/contentHover", "vs/editor/contrib/colorPicker/browser/standaloneColorPickerWidget", "vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions", "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace", "vs/editor/contrib/inlineProgress/browser/inlineProgress", "vs/editor/contrib/dropOrPasteInto/browser/copyPasteController", "vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController", "vs/editor/contrib/linkedEditing/browser/linkedEditing", "vs/editor/contrib/links/browser/links", "vs/editor/contrib/stickyScroll/browser/stickyScrollModelProvider", "vs/editor/contrib/stickyScroll/browser/stickyScrollProvider", "vs/editor/contrib/stickyScroll/browser/stickyScrollWidget", "vs/editor/contrib/suggest/browser/suggestWidget", "vs/editor/contrib/wordHighlighter/browser/highlightDecorations", "vs/editor/contrib/multicursor/browser/multicursor", "vs/editor/contrib/wordHighlighter/browser/wordHighlighter", "vs/editor/contrib/zoneWidget/browser/zoneWidget", "vs/editor/contrib/peekView/browser/peekView", "vs/editor/contrib/gotoError/browser/gotoErrorWidget", "vs/editor/contrib/gotoError/browser/gotoError", "vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget", "vs/editor/contrib/gotoSymbol/browser/peek/referencesController", "vs/editor/contrib/gotoSymbol/browser/goToCommands", "vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition", "vs/editor/contrib/hover/browser/markerHoverParticipant", "vs/editor/contrib/hover/browser/hover", "vs/editor/contrib/colorPicker/browser/colorContributions", "vs/editor/contrib/inlayHints/browser/inlayHintsLocations", "vs/editor/contrib/inlayHints/browser/inlayHintsController", "vs/editor/contrib/inlayHints/browser/inlayHintsHover", "vs/editor/contrib/inlayHints/browser/inlayHintsContribution", "vs/editor/contrib/stickyScroll/browser/stickyScrollController", "vs/editor/contrib/stickyScroll/browser/stickyScrollActions", "vs/editor/contrib/stickyScroll/browser/stickyScrollContribution", "vs/editor/standalone/browser/referenceSearch/standaloneReferenceSearch", "vs/platform/undoRedo/common/undoRedoService", "vs/platform/workspace/common/workspace", "vs/editor/contrib/contextmenu/browser/contextmenu", "vs/editor/contrib/dropOrPasteInto/browser/defaultProviders", "vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution", "vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution", "vs/editor/contrib/snippet/browser/snippetVariables", "vs/editor/contrib/snippet/browser/snippetSession", "vs/editor/contrib/snippet/browser/snippetController2", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsModel", "vs/editor/contrib/suggest/browser/suggestModel", "vs/editor/contrib/suggest/browser/suggestController", "vs/editor/contrib/inlineCompletions/browser/suggestWidgetInlineCompletionProvider", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsController", "vs/editor/contrib/inlineCompletions/browser/commands", "vs/editor/contrib/inlineCompletions/browser/hoverParticipant", "vs/editor/contrib/inlineCompletions/browser/inlineCompletions.contribution", "vs/platform/workspace/common/workspaceTrust", "vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter", "vs/editor/editor.all", "vs/editor/standalone/browser/standaloneServices", "vs/editor/standalone/browser/standaloneCodeEditor", "vs/editor/standalone/browser/standaloneEditor", "vs/editor/standalone/browser/standaloneLanguages", "vs/editor/editor.api", "vs/editor/editor.main"], "vs/base/common/worker/simpleWorker": ["vs/base/common/arrays", "vs/base/common/arraysFind", "vs/base/common/cache", "vs/base/common/color", "vs/base/common/diff/diffChange", "vs/base/common/errors", "vs/base/common/assert", "vs/base/common/functional", "vs/base/common/iterator", "vs/base/common/keyCodes", "vs/base/common/lazy", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/base/common/map", "vs/base/common/stopwatch", "vs/base/common/event", "vs/base/common/cancellation", "vs/base/common/strings", "vs/base/common/hash", "vs/base/common/diff/diff", "vs/base/common/types", "vs/base/common/codicons", "vs/base/common/objects", "vs/base/common/uint", "vs/editor/common/core/characterClassifier", "vs/editor/common/core/offsetRange", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/lineRange", "vs/editor/common/core/selection", "vs/editor/common/core/wordCharacterClassifier", "vs/editor/common/core/wordHelper", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/diffAlgorithm", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/myersDiffAlgorithm", "vs/editor/common/diff/defaultLinesDiffComputer/heuristicSequenceOptimizations", "vs/editor/common/diff/defaultLinesDiffComputer/lineSequence", "vs/editor/common/diff/defaultLinesDiffComputer/utils", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/dynamicProgrammingDiffing", "vs/editor/common/diff/defaultLinesDiffComputer/linesSliceCharSequence", "vs/editor/common/diff/linesDiffComputer", "vs/editor/common/diff/rangeMapping", "vs/editor/common/diff/defaultLinesDiffComputer/computeMovedLines", "vs/editor/common/diff/defaultLinesDiffComputer/defaultLinesDiffComputer", "vs/editor/common/diff/legacyLinesDiffComputer", "vs/editor/common/diff/linesDiffComputers", "vs/editor/common/languages/defaultDocumentColorsComputer", "vs/editor/common/languages/linkComputer", "vs/editor/common/languages/supports/inplaceReplaceSupport", "vs/editor/common/model", "vs/editor/common/model/prefixSumComputer", "vs/editor/common/model/mirrorTextModel", "vs/editor/common/model/textModelSearch", "vs/editor/common/services/unicodeTextModelHighlighter", "vs/editor/common/standalone/standaloneEnums", "vs/editor/common/tokenizationRegistry", "vs/nls!vs/base/common/platform", "vs/base/common/platform", "vs/base/common/process", "vs/base/common/path", "vs/base/common/uri", "vs/base/common/worker/simpleWorker", "vs/nls!vs/editor/common/languages", "vs/editor/common/languages", "vs/editor/common/services/editorBaseApi", "vs/editor/common/services/editorSimpleWorker"]}}
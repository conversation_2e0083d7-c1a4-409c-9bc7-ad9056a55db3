{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/redis/redis.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/redis/redis.ts\nvar conf = {\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".redis\",\n  ignoreCase: true,\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    \"APPEND\",\n    \"AUTH\",\n    \"BGREWRITEAOF\",\n    \"BGSAVE\",\n    \"BITCOUNT\",\n    \"BITFIELD\",\n    \"BITOP\",\n    \"BITPOS\",\n    \"BLPOP\",\n    \"BRPOP\",\n    \"BRPOPLPUSH\",\n    \"CLIENT\",\n    \"KILL\",\n    \"LIST\",\n    \"GETNAME\",\n    \"PAUSE\",\n    \"REPLY\",\n    \"SETNAME\",\n    \"CLUSTER\",\n    \"ADDSLOTS\",\n    \"COUNT-FAILURE-REPORTS\",\n    \"COUNTKEYSINSLOT\",\n    \"DELSLOTS\",\n    \"FAILOVER\",\n    \"FORGET\",\n    \"GETKEYSINSLOT\",\n    \"INFO\",\n    \"KEYSLOT\",\n    \"MEET\",\n    \"NODES\",\n    \"REPLICATE\",\n    \"RESET\",\n    \"SAVECONFIG\",\n    \"SET-CONFIG-EPOCH\",\n    \"SETSLOT\",\n    \"SLAVES\",\n    \"SLOTS\",\n    \"COMMAND\",\n    \"COUNT\",\n    \"GETKEYS\",\n    \"CONFIG\",\n    \"GET\",\n    \"REWRITE\",\n    \"SET\",\n    \"RESETSTAT\",\n    \"DBSIZE\",\n    \"DEBUG\",\n    \"OBJECT\",\n    \"SEGFAULT\",\n    \"DECR\",\n    \"DECRBY\",\n    \"DEL\",\n    \"DISCARD\",\n    \"DUMP\",\n    \"ECHO\",\n    \"EVAL\",\n    \"EVALSHA\",\n    \"EXEC\",\n    \"EXISTS\",\n    \"EXPIRE\",\n    \"EXPIREAT\",\n    \"FLUSHALL\",\n    \"FLUSHDB\",\n    \"GEOADD\",\n    \"GEOHASH\",\n    \"GEOPOS\",\n    \"GEODIST\",\n    \"GEORADIUS\",\n    \"GEORADIUSBYMEMBER\",\n    \"GETBIT\",\n    \"GETRANGE\",\n    \"GETSET\",\n    \"HDEL\",\n    \"HEXISTS\",\n    \"HGET\",\n    \"HGETALL\",\n    \"HINCRBY\",\n    \"HINCRBYFLOAT\",\n    \"HKEYS\",\n    \"HLEN\",\n    \"HMGET\",\n    \"HMSET\",\n    \"HSET\",\n    \"HSETNX\",\n    \"HSTRLEN\",\n    \"HVALS\",\n    \"INCR\",\n    \"INCRBY\",\n    \"INCRBYFLOAT\",\n    \"KEYS\",\n    \"LASTSAVE\",\n    \"LINDEX\",\n    \"LINSERT\",\n    \"LLEN\",\n    \"LPOP\",\n    \"LPUSH\",\n    \"LPUSHX\",\n    \"LRANGE\",\n    \"LREM\",\n    \"LSET\",\n    \"LTRIM\",\n    \"MGET\",\n    \"MIGRATE\",\n    \"MONITOR\",\n    \"MOVE\",\n    \"MSET\",\n    \"MSETNX\",\n    \"MULTI\",\n    \"PERSIST\",\n    \"PEXPIRE\",\n    \"PEXPIREAT\",\n    \"PFADD\",\n    \"PFCOUNT\",\n    \"PFMERGE\",\n    \"PING\",\n    \"PSETEX\",\n    \"PSUBSCRIBE\",\n    \"PUBSUB\",\n    \"PTTL\",\n    \"PUBLISH\",\n    \"PUNSUBSCRIBE\",\n    \"QUIT\",\n    \"RANDOMKEY\",\n    \"READONLY\",\n    \"READWRITE\",\n    \"RENAME\",\n    \"RENAMENX\",\n    \"RESTORE\",\n    \"ROLE\",\n    \"RPOP\",\n    \"RPOPLPUSH\",\n    \"RPUSH\",\n    \"RPUSHX\",\n    \"SADD\",\n    \"SAVE\",\n    \"SCARD\",\n    \"SCRIPT\",\n    \"FLUSH\",\n    \"LOAD\",\n    \"SDIFF\",\n    \"SDIFFSTORE\",\n    \"SELECT\",\n    \"SETBIT\",\n    \"SETEX\",\n    \"SETNX\",\n    \"SETRANGE\",\n    \"SHUTDOWN\",\n    \"SINTER\",\n    \"SINTERSTORE\",\n    \"SISMEMBER\",\n    \"SLAVEOF\",\n    \"SLOWLOG\",\n    \"SMEMBERS\",\n    \"SMOVE\",\n    \"SORT\",\n    \"SPOP\",\n    \"SRANDMEMBER\",\n    \"SREM\",\n    \"STRLEN\",\n    \"SUBSCRIBE\",\n    \"SUNION\",\n    \"SUNIONSTORE\",\n    \"SWAPDB\",\n    \"SYNC\",\n    \"TIME\",\n    \"TOUCH\",\n    \"TTL\",\n    \"TYPE\",\n    \"UNSUBSCRIBE\",\n    \"UNLINK\",\n    \"UNWATCH\",\n    \"WAIT\",\n    \"WATCH\",\n    \"ZADD\",\n    \"ZCARD\",\n    \"ZCOUNT\",\n    \"ZINCRBY\",\n    \"ZINTERSTORE\",\n    \"ZLEXCOUNT\",\n    \"ZRANGE\",\n    \"ZRANGEBYLEX\",\n    \"ZREVRANGEBYLEX\",\n    \"ZRANGEBYSCORE\",\n    \"ZRANK\",\n    \"ZREM\",\n    \"ZREMRANGEBYLEX\",\n    \"ZREMRANGEBYRANK\",\n    \"ZREMRANGEBYSCORE\",\n    \"ZREVRANGE\",\n    \"ZREVRANGEBYSCORE\",\n    \"ZREVRANK\",\n    \"ZSCORE\",\n    \"ZUNIONSTORE\",\n    \"SCAN\",\n    \"SSCAN\",\n    \"HSCAN\",\n    \"ZSCAN\"\n  ],\n  operators: [],\n  builtinFunctions: [],\n  builtinVariables: [],\n  pseudoColumns: [],\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@pseudoColumns\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      { include: \"@scopes\" },\n      [/[;,.]/, \"delimiter\"],\n      [/[()]/, \"@brackets\"],\n      [\n        /[\\w@#$]+/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@operators\": \"operator\",\n            \"@builtinVariables\": \"predefined\",\n            \"@builtinFunctions\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[<>=!%&+\\-*/|~^]/, \"operator\"]\n    ],\n    whitespace: [[/\\s+/, \"white\"]],\n    pseudoColumns: [\n      [\n        /[$][A-Za-z_][\\w@#$]*/,\n        {\n          cases: {\n            \"@pseudoColumns\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    numbers: [\n      [/0[xX][0-9a-fA-F]*/, \"number\"],\n      [/[$][+-]*\\d*(\\.\\d*)?/, \"number\"],\n      [/((\\d+(\\.\\d*)?)|(\\.\\d+))([eE][\\-+]?\\d+)?/, \"number\"]\n    ],\n    strings: [\n      [/'/, { token: \"string\", next: \"@string\" }],\n      [/\"/, { token: \"string.double\", next: \"@stringDouble\" }]\n    ],\n    string: [\n      [/[^']+/, \"string\"],\n      [/''/, \"string\"],\n      [/'/, { token: \"string\", next: \"@pop\" }]\n    ],\n    stringDouble: [\n      [/[^\"]+/, \"string.double\"],\n      [/\"\"/, \"string.double\"],\n      [/\"/, { token: \"string.double\", next: \"@pop\" }]\n    ],\n    scopes: []\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AAQA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,EAC1D;AAAA,EACA,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW,CAAC;AAAA,EACZ,kBAAkB,CAAC;AAAA,EACnB,kBAAkB,CAAC;AAAA,EACnB,eAAe,CAAC;AAAA,EAChB,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,iBAAiB;AAAA,MAC5B,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,UAAU;AAAA,MACrB,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,QAAQ,WAAW;AAAA,MACpB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa;AAAA,YACb,cAAc;AAAA,YACd,qBAAqB;AAAA,YACrB,qBAAqB;AAAA,YACrB,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,oBAAoB,UAAU;AAAA,IACjC;AAAA,IACA,YAAY,CAAC,CAAC,OAAO,OAAO,CAAC;AAAA,IAC7B,eAAe;AAAA,MACb;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,kBAAkB;AAAA,YAClB,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP,CAAC,qBAAqB,QAAQ;AAAA,MAC9B,CAAC,uBAAuB,QAAQ;AAAA,MAChC,CAAC,2CAA2C,QAAQ;AAAA,IACtD;AAAA,IACA,SAAS;AAAA,MACP,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,UAAU,CAAC;AAAA,MAC1C,CAAC,KAAK,EAAE,OAAO,iBAAiB,MAAM,gBAAgB,CAAC;AAAA,IACzD;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,MAAM,QAAQ;AAAA,MACf,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,IACzC;AAAA,IACA,cAAc;AAAA,MACZ,CAAC,SAAS,eAAe;AAAA,MACzB,CAAC,MAAM,eAAe;AAAA,MACtB,CAAC,KAAK,EAAE,OAAO,iBAAiB,MAAM,OAAO,CAAC;AAAA,IAChD;AAAA,IACA,QAAQ,CAAC;AAAA,EACX;AACF;", "names": []}
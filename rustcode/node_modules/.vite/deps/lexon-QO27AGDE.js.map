{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/lexon/lexon.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/lexon/lexon.ts\nvar conf = {\n  comments: {\n    lineComment: \"COMMENT\"\n  },\n  brackets: [[\"(\", \")\"]],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \":\", close: \".\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"`\", close: \"`\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \":\", close: \".\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*(::\\\\s*|COMMENT\\\\s+)#region\"),\n      end: new RegExp(\"^\\\\s*(::\\\\s*|COMMENT\\\\s+)#endregion\")\n    }\n  }\n};\nvar language = {\n  tokenPostfix: \".lexon\",\n  ignoreCase: true,\n  keywords: [\n    \"lexon\",\n    \"lex\",\n    \"clause\",\n    \"terms\",\n    \"contracts\",\n    \"may\",\n    \"pay\",\n    \"pays\",\n    \"appoints\",\n    \"into\",\n    \"to\"\n  ],\n  typeKeywords: [\"amount\", \"person\", \"key\", \"time\", \"date\", \"asset\", \"text\"],\n  operators: [\n    \"less\",\n    \"greater\",\n    \"equal\",\n    \"le\",\n    \"gt\",\n    \"or\",\n    \"and\",\n    \"add\",\n    \"added\",\n    \"subtract\",\n    \"subtracted\",\n    \"multiply\",\n    \"multiplied\",\n    \"times\",\n    \"divide\",\n    \"divided\",\n    \"is\",\n    \"be\",\n    \"certified\"\n  ],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  tokenizer: {\n    root: [\n      [/^(\\s*)(comment:?(?:\\s.*|))$/, [\"\", \"comment\"]],\n      [\n        /\"/,\n        {\n          token: \"identifier.quote\",\n          bracket: \"@open\",\n          next: \"@quoted_identifier\"\n        }\n      ],\n      [\n        \"LEX$\",\n        {\n          token: \"keyword\",\n          bracket: \"@open\",\n          next: \"@identifier_until_period\"\n        }\n      ],\n      [\"LEXON\", { token: \"keyword\", bracket: \"@open\", next: \"@semver\" }],\n      [\n        \":\",\n        {\n          token: \"delimiter\",\n          bracket: \"@open\",\n          next: \"@identifier_until_period\"\n        }\n      ],\n      [\n        /[a-z_$][\\w$]*/,\n        {\n          cases: {\n            \"@operators\": \"operator\",\n            \"@typeKeywords\": \"keyword.type\",\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      { include: \"@whitespace\" },\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [/@symbols/, \"delimiter\"],\n      [/\\d*\\.\\d*\\.\\d*/, \"number.semver\"],\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F]+/, \"number.hex\"],\n      [/\\d+/, \"number\"],\n      [/[;,.]/, \"delimiter\"]\n    ],\n    quoted_identifier: [\n      [/[^\\\\\"]+/, \"identifier\"],\n      [/\"/, { token: \"identifier.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    space_identifier_until_period: [\n      [\":\", \"delimiter\"],\n      [\" \", { token: \"white\", next: \"@identifier_rest\" }]\n    ],\n    identifier_until_period: [\n      { include: \"@whitespace\" },\n      [\":\", { token: \"delimiter\", next: \"@identifier_rest\" }],\n      [/[^\\\\.]+/, \"identifier\"],\n      [/\\./, { token: \"delimiter\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    identifier_rest: [\n      [/[^\\\\.]+/, \"identifier\"],\n      [/\\./, { token: \"delimiter\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    semver: [\n      { include: \"@whitespace\" },\n      [\":\", \"delimiter\"],\n      [/\\d*\\.\\d*\\.\\d*/, { token: \"number.semver\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    whitespace: [[/[ \\t\\r\\n]+/, \"white\"]]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AAQA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,EACf;AAAA,EACA,UAAU,CAAC,CAAC,KAAK,GAAG,CAAC;AAAA,EACrB,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,MACP,OAAO,IAAI,OAAO,kCAAkC;AAAA,MACpD,KAAK,IAAI,OAAO,qCAAqC;AAAA,IACvD;AAAA,EACF;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,cAAc,CAAC,UAAU,UAAU,OAAO,QAAQ,QAAQ,SAAS,MAAM;AAAA,EACzE,WAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,SAAS;AAAA,EACT,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,CAAC,+BAA+B,CAAC,IAAI,SAAS,CAAC;AAAA,MAC/C;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QACR;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QACR;AAAA,MACF;AAAA,MACA,CAAC,SAAS,EAAE,OAAO,WAAW,SAAS,SAAS,MAAM,UAAU,CAAC;AAAA,MACjE;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QACR;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,cAAc;AAAA,YACd,iBAAiB;AAAA,YACjB,aAAa;AAAA,YACb,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,oBAAoB,WAAW;AAAA,MAChC,CAAC,YAAY,WAAW;AAAA,MACxB,CAAC,iBAAiB,eAAe;AAAA,MACjC,CAAC,4BAA4B,cAAc;AAAA,MAC3C,CAAC,qBAAqB,YAAY;AAAA,MAClC,CAAC,OAAO,QAAQ;AAAA,MAChB,CAAC,SAAS,WAAW;AAAA,IACvB;AAAA,IACA,mBAAmB;AAAA,MACjB,CAAC,WAAW,YAAY;AAAA,MACxB,CAAC,KAAK,EAAE,OAAO,oBAAoB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IACtE;AAAA,IACA,+BAA+B;AAAA,MAC7B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,KAAK,EAAE,OAAO,SAAS,MAAM,mBAAmB,CAAC;AAAA,IACpD;AAAA,IACA,yBAAyB;AAAA,MACvB,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,KAAK,EAAE,OAAO,aAAa,MAAM,mBAAmB,CAAC;AAAA,MACtD,CAAC,WAAW,YAAY;AAAA,MACxB,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAChE;AAAA,IACA,iBAAiB;AAAA,MACf,CAAC,WAAW,YAAY;AAAA,MACxB,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAChE;AAAA,IACA,QAAQ;AAAA,MACN,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,iBAAiB,EAAE,OAAO,iBAAiB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAC/E;AAAA,IACA,YAAY,CAAC,CAAC,cAAc,OAAO,CAAC;AAAA,EACtC;AACF;", "names": []}
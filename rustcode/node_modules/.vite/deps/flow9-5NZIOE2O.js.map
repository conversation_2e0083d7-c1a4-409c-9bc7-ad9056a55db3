{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/flow9/flow9.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/flow9/flow9.ts\nvar conf = {\n  comments: {\n    blockComment: [\"/*\", \"*/\"],\n    lineComment: \"//\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"<\", close: \">\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".flow\",\n  keywords: [\n    \"import\",\n    \"require\",\n    \"export\",\n    \"forbid\",\n    \"native\",\n    \"if\",\n    \"else\",\n    \"cast\",\n    \"unsafe\",\n    \"switch\",\n    \"default\"\n  ],\n  types: [\n    \"io\",\n    \"mutable\",\n    \"bool\",\n    \"int\",\n    \"double\",\n    \"string\",\n    \"flow\",\n    \"void\",\n    \"ref\",\n    \"true\",\n    \"false\",\n    \"with\"\n  ],\n  operators: [\n    \"=\",\n    \">\",\n    \"<\",\n    \"<=\",\n    \">=\",\n    \"==\",\n    \"!\",\n    \"!=\",\n    \":=\",\n    \"::=\",\n    \"&&\",\n    \"||\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"@\",\n    \"&\",\n    \"%\",\n    \":\",\n    \"->\",\n    \"\\\\\",\n    \"$\",\n    \"??\",\n    \"^\"\n  ],\n  symbols: /[@$=><!~?:&|+\\-*\\\\\\/\\^%]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  tokenizer: {\n    root: [\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@types\": \"type\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      { include: \"@whitespace\" },\n      [/[{}()\\[\\]]/, \"delimiter\"],\n      [/[<>](?!@symbols)/, \"delimiter\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      [/((0(x|X)[0-9a-fA-F]*)|(([0-9]+\\.?[0-9]*)|(\\.[0-9]+))((e|E)(\\+|-)?[0-9]+)?)/, \"number\"],\n      [/[;,.]/, \"delimiter\"],\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      [/\"/, \"string\", \"@string\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AAQA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,cAAc,CAAC,MAAM,IAAI;AAAA,IACzB,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,EAC7C;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,SAAS;AAAA,EACT,SAAS;AAAA,EACT,WAAW;AAAA,IACT,MAAM;AAAA,MACJ;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa;AAAA,YACb,UAAU;AAAA,YACV,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,oBAAoB,WAAW;AAAA,MAChC;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,8EAA8E,QAAQ;AAAA,MACvF,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,KAAK,UAAU,SAAS;AAAA,IAC3B;AAAA,IACA,YAAY;AAAA,MACV,CAAC,cAAc,EAAE;AAAA,MACjB,CAAC,QAAQ,WAAW,UAAU;AAAA,MAC9B,CAAC,WAAW,SAAS;AAAA,IACvB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,SAAS,SAAS;AAAA,IACrB;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,UAAU,MAAM;AAAA,IACxB;AAAA,EACF;AACF;", "names": []}
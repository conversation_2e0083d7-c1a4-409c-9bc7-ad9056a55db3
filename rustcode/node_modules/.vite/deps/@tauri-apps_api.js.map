{"version": 3, "sources": ["../../@tauri-apps/api/app.js", "../../@tauri-apps/api/cli.js", "../../@tauri-apps/api/clipboard.js", "../../@tauri-apps/api/dialog.js", "../../@tauri-apps/api/fs.js", "../../@tauri-apps/api/globalShortcut.js", "../../@tauri-apps/api/http.js", "../../@tauri-apps/api/notification.js", "../../@tauri-apps/api/path.js", "../../@tauri-apps/api/helpers/os-check.js", "../../@tauri-apps/api/process.js", "../../@tauri-apps/api/shell.js", "../../@tauri-apps/api/updater.js", "../../@tauri-apps/api/os.js", "../../@tauri-apps/api/index.js"], "sourcesContent": ["import { invokeTauriCommand } from './helpers/tauri.js';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Get application metadata.\n *\n * This package is also accessible with `window.__TAURI__.app` when [`build.withGlobalTauri`](https://tauri.app/v1/api/config/#buildconfig.withglobaltauri) in `tauri.conf.json` is set to `true`.\n *\n * The APIs must be added to [`tauri.allowlist.app`](https://tauri.app/v1/api/config/#allowlistconfig.app) in `tauri.conf.json`:\n * ```json\n * {\n *   \"tauri\": {\n *     \"allowlist\": {\n *       \"app\": {\n *         \"all\": true, // enable all app APIs\n *         \"show\": true,\n *         \"hide\": true\n *       }\n *     }\n *   }\n * }\n * ```\n * It is recommended to allowlist only the APIs you use for optimal bundle size and security.\n *\n * @module\n */\n/**\n * Gets the application version.\n * @example\n * ```typescript\n * import { getVersion } from '@tauri-apps/api/app';\n * const appVersion = await getVersion();\n * ```\n *\n * @since 1.0.0\n */\nasync function getVersion() {\n    return invokeTauriCommand({\n        __tauriModule: 'App',\n        message: {\n            cmd: 'getAppVersion'\n        }\n    });\n}\n/**\n * Gets the application name.\n * @example\n * ```typescript\n * import { getName } from '@tauri-apps/api/app';\n * const appName = await getName();\n * ```\n *\n * @since 1.0.0\n */\nasync function getName() {\n    return invokeTauriCommand({\n        __tauriModule: 'App',\n        message: {\n            cmd: 'getAppName'\n        }\n    });\n}\n/**\n * Gets the Tauri version.\n *\n * @example\n * ```typescript\n * import { getTauriVersion } from '@tauri-apps/api/app';\n * const tauriVersion = await getTauriVersion();\n * ```\n *\n * @since 1.0.0\n */\nasync function getTauriVersion() {\n    return invokeTauriCommand({\n        __tauriModule: 'App',\n        message: {\n            cmd: 'getTauriVersion'\n        }\n    });\n}\n/**\n * Shows the application on macOS. This function does not automatically focus any specific app window.\n *\n * @example\n * ```typescript\n * import { show } from '@tauri-apps/api/app';\n * await show();\n * ```\n *\n * @since 1.2.0\n */\nasync function show() {\n    return invokeTauriCommand({\n        __tauriModule: 'App',\n        message: {\n            cmd: 'show'\n        }\n    });\n}\n/**\n * Hides the application on macOS.\n *\n * @example\n * ```typescript\n * import { hide } from '@tauri-apps/api/app';\n * await hide();\n * ```\n *\n * @since 1.2.0\n */\nasync function hide() {\n    return invokeTauriCommand({\n        __tauriModule: 'App',\n        message: {\n            cmd: 'hide'\n        }\n    });\n}\n\nexport { getName, getTauriVersion, getVersion, hide, show };\n", "import { invokeTauriCommand } from './helpers/tauri.js';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Parse arguments from your Command Line Interface.\n *\n * This package is also accessible with `window.__TAURI__.cli` when [`build.withGlobalTauri`](https://tauri.app/v1/api/config/#buildconfig.withglobaltauri) in `tauri.conf.json` is set to `true`.\n * @module\n */\n/**\n * Parse the arguments provided to the current process and get the matches using the configuration defined [`tauri.cli`](https://tauri.app/v1/api/config/#tauriconfig.cli) in `tauri.conf.json`\n * @example\n * ```typescript\n * import { getMatches } from '@tauri-apps/api/cli';\n * const matches = await getMatches();\n * if (matches.subcommand?.name === 'run') {\n *   // `./your-app run $ARGS` was executed\n *   const args = matches.subcommand?.matches.args\n *   if ('debug' in args) {\n *     // `./your-app run --debug` was executed\n *   }\n * } else {\n *   const args = matches.args\n *   // `./your-app $ARGS` was executed\n * }\n * ```\n *\n * @since 1.0.0\n */\nasync function getMatches() {\n    return invokeTauriCommand({\n        __tauriModule: 'Cli',\n        message: {\n            cmd: 'cliMatches'\n        }\n    });\n}\n\nexport { getMatches };\n", "import { invokeTauriCommand } from './helpers/tauri.js';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Read and write to the system clipboard.\n *\n * This package is also accessible with `window.__TAURI__.clipboard` when [`build.withGlobalTauri`](https://tauri.app/v1/api/config/#buildconfig.withglobaltauri) in `tauri.conf.json` is set to `true`.\n *\n * The APIs must be added to [`tauri.allowlist.clipboard`](https://tauri.app/v1/api/config/#allowlistconfig.clipboard) in `tauri.conf.json`:\n * ```json\n * {\n *   \"tauri\": {\n *     \"allowlist\": {\n *       \"clipboard\": {\n *         \"all\": true, // enable all Clipboard APIs\n *         \"writeText\": true,\n *         \"readText\": true\n *       }\n *     }\n *   }\n * }\n * ```\n * It is recommended to allowlist only the APIs you use for optimal bundle size and security.\n *\n * @module\n */\n/**\n * Writes plain text to the clipboard.\n * @example\n * ```typescript\n * import { writeText, readText } from '@tauri-apps/api/clipboard';\n * await writeText('Tauri is awesome!');\n * assert(await readText(), 'Tauri is awesome!');\n * ```\n *\n * @returns A promise indicating the success or failure of the operation.\n *\n * @since 1.0.0.\n */\nasync function writeText(text) {\n    return invokeTauriCommand({\n        __tauriModule: 'Clipboard',\n        message: {\n            cmd: 'writeText',\n            data: text\n        }\n    });\n}\n/**\n * Gets the clipboard content as plain text.\n * @example\n * ```typescript\n * import { readText } from '@tauri-apps/api/clipboard';\n * const clipboardText = await readText();\n * ```\n * @since 1.0.0.\n */\nasync function readText() {\n    return invokeTauriCommand({\n        __tauriModule: 'Clipboard',\n        message: {\n            cmd: 'readText',\n            // if data is not set, `serde` will ignore the custom deserializer\n            // that is set when the API is not allowlisted\n            data: null\n        }\n    });\n}\n\nexport { readText, writeText };\n", "import { invokeTauriCommand } from './helpers/tauri.js';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Native system dialogs for opening and saving files.\n *\n * This package is also accessible with `window.__TAURI__.dialog` when [`build.withGlobalTauri`](https://tauri.app/v1/api/config/#buildconfig.withglobaltauri) in `tauri.conf.json` is set to `true`.\n *\n * The APIs must be added to [`tauri.allowlist.dialog`](https://tauri.app/v1/api/config/#allowlistconfig.dialog) in `tauri.conf.json`:\n * ```json\n * {\n *   \"tauri\": {\n *     \"allowlist\": {\n *       \"dialog\": {\n *         \"all\": true, // enable all dialog APIs\n *         \"ask\": true, // enable dialog ask API\n *         \"confirm\": true, // enable dialog confirm API\n *         \"message\": true, // enable dialog message API\n *         \"open\": true, // enable file open API\n *         \"save\": true // enable file save API\n *       }\n *     }\n *   }\n * }\n * ```\n * It is recommended to allowlist only the APIs you use for optimal bundle size and security.\n * @module\n */\n/**\n * Open a file/directory selection dialog.\n *\n * The selected paths are added to the filesystem and asset protocol allowlist scopes.\n * When security is more important than the easy of use of this API,\n * prefer writing a dedicated command instead.\n *\n * Note that the allowlist scope change is not persisted, so the values are cleared when the application is restarted.\n * You can save it to the filesystem using [tauri-plugin-persisted-scope](https://github.com/tauri-apps/plugins-workspace/tree/v1/plugins/persisted-scope).\n * @example\n * ```typescript\n * import { open } from '@tauri-apps/api/dialog';\n * // Open a selection dialog for image files\n * const selected = await open({\n *   multiple: true,\n *   filters: [{\n *     name: 'Image',\n *     extensions: ['png', 'jpeg']\n *   }]\n * });\n * if (Array.isArray(selected)) {\n *   // user selected multiple files\n * } else if (selected === null) {\n *   // user cancelled the selection\n * } else {\n *   // user selected a single file\n * }\n * ```\n *\n * @example\n * ```typescript\n * import { open } from '@tauri-apps/api/dialog';\n * import { appDir } from '@tauri-apps/api/path';\n * // Open a selection dialog for directories\n * const selected = await open({\n *   directory: true,\n *   multiple: true,\n *   defaultPath: await appDir(),\n * });\n * if (Array.isArray(selected)) {\n *   // user selected multiple directories\n * } else if (selected === null) {\n *   // user cancelled the selection\n * } else {\n *   // user selected a single directory\n * }\n * ```\n *\n * @returns A promise resolving to the selected path(s)\n *\n * @since 1.0.0\n */\nasync function open(options = {}) {\n    if (typeof options === 'object') {\n        Object.freeze(options);\n    }\n    return invokeTauriCommand({\n        __tauriModule: 'Dialog',\n        message: {\n            cmd: 'openDialog',\n            options\n        }\n    });\n}\n/**\n * Open a file/directory save dialog.\n *\n * The selected path is added to the filesystem and asset protocol allowlist scopes.\n * When security is more important than the easy of use of this API,\n * prefer writing a dedicated command instead.\n *\n * Note that the allowlist scope change is not persisted, so the values are cleared when the application is restarted.\n * You can save it to the filesystem using [tauri-plugin-persisted-scope](https://github.com/tauri-apps/plugins-workspace/tree/v1/plugins/persisted-scope).\n * @example\n * ```typescript\n * import { save } from '@tauri-apps/api/dialog';\n * const filePath = await save({\n *   filters: [{\n *     name: 'Image',\n *     extensions: ['png', 'jpeg']\n *   }]\n * });\n * ```\n *\n * @returns A promise resolving to the selected path.\n *\n * @since 1.0.0\n */\nasync function save(options = {}) {\n    if (typeof options === 'object') {\n        Object.freeze(options);\n    }\n    return invokeTauriCommand({\n        __tauriModule: 'Dialog',\n        message: {\n            cmd: 'saveDialog',\n            options\n        }\n    });\n}\n/**\n * Shows a message dialog with an `Ok` button.\n * @example\n * ```typescript\n * import { message } from '@tauri-apps/api/dialog';\n * await message('Tauri is awesome', 'Tauri');\n * await message('File not found', { title: 'Tauri', type: 'error' });\n * ```\n *\n * @param message The message to show.\n * @param options The dialog's options. If a string, it represents the dialog title.\n *\n * @returns A promise indicating the success or failure of the operation.\n *\n * @since 1.0.0\n *\n */\nasync function message(message, options) {\n    var _a, _b;\n    const opts = typeof options === 'string' ? { title: options } : options;\n    return invokeTauriCommand({\n        __tauriModule: 'Dialog',\n        message: {\n            cmd: 'messageDialog',\n            message: message.toString(),\n            title: (_a = opts === null || opts === void 0 ? void 0 : opts.title) === null || _a === void 0 ? void 0 : _a.toString(),\n            type: opts === null || opts === void 0 ? void 0 : opts.type,\n            buttonLabel: (_b = opts === null || opts === void 0 ? void 0 : opts.okLabel) === null || _b === void 0 ? void 0 : _b.toString()\n        }\n    });\n}\n/**\n * Shows a question dialog with `Yes` and `No` buttons.\n * @example\n * ```typescript\n * import { ask } from '@tauri-apps/api/dialog';\n * const yes = await ask('Are you sure?', 'Tauri');\n * const yes2 = await ask('This action cannot be reverted. Are you sure?', { title: 'Tauri', type: 'warning' });\n * ```\n *\n * @param message The message to show.\n * @param options The dialog's options. If a string, it represents the dialog title.\n *\n * @returns A promise resolving to a boolean indicating whether `Yes` was clicked or not.\n *\n * @since 1.0.0\n */\nasync function ask(message, options) {\n    var _a, _b, _c, _d, _e;\n    const opts = typeof options === 'string' ? { title: options } : options;\n    return invokeTauriCommand({\n        __tauriModule: 'Dialog',\n        message: {\n            cmd: 'askDialog',\n            message: message.toString(),\n            title: (_a = opts === null || opts === void 0 ? void 0 : opts.title) === null || _a === void 0 ? void 0 : _a.toString(),\n            type: opts === null || opts === void 0 ? void 0 : opts.type,\n            buttonLabels: [\n                (_c = (_b = opts === null || opts === void 0 ? void 0 : opts.okLabel) === null || _b === void 0 ? void 0 : _b.toString()) !== null && _c !== void 0 ? _c : 'Yes',\n                (_e = (_d = opts === null || opts === void 0 ? void 0 : opts.cancelLabel) === null || _d === void 0 ? void 0 : _d.toString()) !== null && _e !== void 0 ? _e : 'No'\n            ]\n        }\n    });\n}\n/**\n * Shows a question dialog with `Ok` and `Cancel` buttons.\n * @example\n * ```typescript\n * import { confirm } from '@tauri-apps/api/dialog';\n * const confirmed = await confirm('Are you sure?', 'Tauri');\n * const confirmed2 = await confirm('This action cannot be reverted. Are you sure?', { title: 'Tauri', type: 'warning' });\n * ```\n *\n * @param message The message to show.\n * @param options The dialog's options. If a string, it represents the dialog title.\n *\n * @returns A promise resolving to a boolean indicating whether `Ok` was clicked or not.\n *\n * @since 1.0.0\n */\nasync function confirm(message, options) {\n    var _a, _b, _c, _d, _e;\n    const opts = typeof options === 'string' ? { title: options } : options;\n    return invokeTauriCommand({\n        __tauriModule: 'Dialog',\n        message: {\n            cmd: 'confirmDialog',\n            message: message.toString(),\n            title: (_a = opts === null || opts === void 0 ? void 0 : opts.title) === null || _a === void 0 ? void 0 : _a.toString(),\n            type: opts === null || opts === void 0 ? void 0 : opts.type,\n            buttonLabels: [\n                (_c = (_b = opts === null || opts === void 0 ? void 0 : opts.okLabel) === null || _b === void 0 ? void 0 : _b.toString()) !== null && _c !== void 0 ? _c : 'Ok',\n                (_e = (_d = opts === null || opts === void 0 ? void 0 : opts.cancelLabel) === null || _d === void 0 ? void 0 : _d.toString()) !== null && _e !== void 0 ? _e : 'Cancel'\n            ]\n        }\n    });\n}\n\nexport { ask, confirm, message, open, save };\n", "import { invokeTauriCommand } from './helpers/tauri.js';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Access the file system.\n *\n * This package is also accessible with `window.__TAURI__.fs` when [`build.withGlobalTauri`](https://tauri.app/v1/api/config/#buildconfig.withglobaltauri) in `tauri.conf.json` is set to `true`.\n *\n * The APIs must be added to [`tauri.allowlist.fs`](https://tauri.app/v1/api/config/#allowlistconfig.fs) in `tauri.conf.json`:\n * ```json\n * {\n *   \"tauri\": {\n *     \"allowlist\": {\n *       \"fs\": {\n *         \"all\": true, // enable all FS APIs\n *         \"readFile\": true,\n *         \"writeFile\": true,\n *         \"readDir\": true,\n *         \"copyFile\": true,\n *         \"createDir\": true,\n *         \"removeDir\": true,\n *         \"removeFile\": true,\n *         \"renameFile\": true,\n *         \"exists\": true\n *       }\n *     }\n *   }\n * }\n * ```\n * It is recommended to allowlist only the APIs you use for optimal bundle size and security.\n *\n * ## Security\n *\n * This module prevents path traversal, not allowing absolute paths or parent dir components\n * (i.e. \"/usr/path/to/file\" or \"../path/to/file\" paths are not allowed).\n * Paths accessed with this API must be relative to one of the {@link BaseDirectory | base directories}\n * so if you need access to arbitrary filesystem paths, you must write such logic on the core layer instead.\n *\n * The API has a scope configuration that forces you to restrict the paths that can be accessed using glob patterns.\n *\n * The scope configuration is an array of glob patterns describing folder paths that are allowed.\n * For instance, this scope configuration only allows accessing files on the\n * *databases* folder of the {@link path.appDataDir | $APPDATA directory}:\n * ```json\n * {\n *   \"tauri\": {\n *     \"allowlist\": {\n *       \"fs\": {\n *         \"scope\": [\"$APPDATA/databases/*\"]\n *       }\n *     }\n *   }\n * }\n * ```\n *\n * Notice the use of the `$APPDATA` variable. The value is injected at runtime, resolving to the {@link path.appDataDir | app data directory}.\n * The available variables are:\n * {@link path.appConfigDir | `$APPCONFIG`}, {@link path.appDataDir | `$APPDATA`}, {@link path.appLocalDataDir | `$APPLOCALDATA`},\n * {@link path.appCacheDir | `$APPCACHE`}, {@link path.appLogDir | `$APPLOG`},\n * {@link path.audioDir | `$AUDIO`}, {@link path.cacheDir | `$CACHE`}, {@link path.configDir | `$CONFIG`}, {@link path.dataDir | `$DATA`},\n * {@link path.localDataDir | `$LOCALDATA`}, {@link path.desktopDir | `$DESKTOP`}, {@link path.documentDir | `$DOCUMENT`},\n * {@link path.downloadDir | `$DOWNLOAD`}, {@link path.executableDir | `$EXE`}, {@link path.fontDir | `$FONT`}, {@link path.homeDir | `$HOME`},\n * {@link path.pictureDir | `$PICTURE`}, {@link path.publicDir | `$PUBLIC`}, {@link path.runtimeDir | `$RUNTIME`},\n * {@link path.templateDir | `$TEMPLATE`}, {@link path.videoDir | `$VIDEO`}, {@link path.resourceDir | `$RESOURCE`}, {@link path.appDir | `$APP`},\n * {@link path.logDir | `$LOG`}, {@link os.tempdir | `$TEMP`}.\n *\n * Trying to execute any API with a URL not configured on the scope results in a promise rejection due to denied access.\n *\n * Note that this scope applies to **all** APIs on this module.\n *\n * @module\n */\n/**\n * @since 1.0.0\n */\nvar BaseDirectory;\n(function (BaseDirectory) {\n    BaseDirectory[BaseDirectory[\"Audio\"] = 1] = \"Audio\";\n    BaseDirectory[BaseDirectory[\"Cache\"] = 2] = \"Cache\";\n    BaseDirectory[BaseDirectory[\"Config\"] = 3] = \"Config\";\n    BaseDirectory[BaseDirectory[\"Data\"] = 4] = \"Data\";\n    BaseDirectory[BaseDirectory[\"LocalData\"] = 5] = \"LocalData\";\n    BaseDirectory[BaseDirectory[\"Desktop\"] = 6] = \"Desktop\";\n    BaseDirectory[BaseDirectory[\"Document\"] = 7] = \"Document\";\n    BaseDirectory[BaseDirectory[\"Download\"] = 8] = \"Download\";\n    BaseDirectory[BaseDirectory[\"Executable\"] = 9] = \"Executable\";\n    BaseDirectory[BaseDirectory[\"Font\"] = 10] = \"Font\";\n    BaseDirectory[BaseDirectory[\"Home\"] = 11] = \"Home\";\n    BaseDirectory[BaseDirectory[\"Picture\"] = 12] = \"Picture\";\n    BaseDirectory[BaseDirectory[\"Public\"] = 13] = \"Public\";\n    BaseDirectory[BaseDirectory[\"Runtime\"] = 14] = \"Runtime\";\n    BaseDirectory[BaseDirectory[\"Template\"] = 15] = \"Template\";\n    BaseDirectory[BaseDirectory[\"Video\"] = 16] = \"Video\";\n    BaseDirectory[BaseDirectory[\"Resource\"] = 17] = \"Resource\";\n    BaseDirectory[BaseDirectory[\"App\"] = 18] = \"App\";\n    BaseDirectory[BaseDirectory[\"Log\"] = 19] = \"Log\";\n    BaseDirectory[BaseDirectory[\"Temp\"] = 20] = \"Temp\";\n    BaseDirectory[BaseDirectory[\"AppConfig\"] = 21] = \"AppConfig\";\n    BaseDirectory[BaseDirectory[\"AppData\"] = 22] = \"AppData\";\n    BaseDirectory[BaseDirectory[\"AppLocalData\"] = 23] = \"AppLocalData\";\n    BaseDirectory[BaseDirectory[\"AppCache\"] = 24] = \"AppCache\";\n    BaseDirectory[BaseDirectory[\"AppLog\"] = 25] = \"AppLog\";\n})(BaseDirectory || (BaseDirectory = {}));\n/**\n * Reads a file as an UTF-8 encoded string.\n * @example\n * ```typescript\n * import { readTextFile, BaseDirectory } from '@tauri-apps/api/fs';\n * // Read the text file in the `$APPCONFIG/app.conf` path\n * const contents = await readTextFile('app.conf', { dir: BaseDirectory.AppConfig });\n * ```\n *\n * @since 1.0.0\n */\nasync function readTextFile(filePath, options = {}) {\n    return invokeTauriCommand({\n        __tauriModule: 'Fs',\n        message: {\n            cmd: 'readTextFile',\n            path: filePath,\n            options\n        }\n    });\n}\n/**\n * Reads a file as byte array.\n * @example\n * ```typescript\n * import { readBinaryFile, BaseDirectory } from '@tauri-apps/api/fs';\n * // Read the image file in the `$RESOURCEDIR/avatar.png` path\n * const contents = await readBinaryFile('avatar.png', { dir: BaseDirectory.Resource });\n * ```\n *\n * @since 1.0.0\n */\nasync function readBinaryFile(filePath, options = {}) {\n    const arr = await invokeTauriCommand({\n        __tauriModule: 'Fs',\n        message: {\n            cmd: 'readFile',\n            path: filePath,\n            options\n        }\n    });\n    return Uint8Array.from(arr);\n}\n/**\n * Writes a UTF-8 text file.\n *\n * @returns A promise indicating the success or failure of the operation.\n *\n * @since 1.0.0\n */\nasync function writeTextFile(path, contents, options) {\n    if (typeof options === 'object') {\n        Object.freeze(options);\n    }\n    if (typeof path === 'object') {\n        Object.freeze(path);\n    }\n    const file = { path: '', contents: '' };\n    let fileOptions = options;\n    if (typeof path === 'string') {\n        file.path = path;\n    }\n    else {\n        file.path = path.path;\n        file.contents = path.contents;\n    }\n    if (typeof contents === 'string') {\n        file.contents = contents !== null && contents !== void 0 ? contents : '';\n    }\n    else {\n        fileOptions = contents;\n    }\n    return invokeTauriCommand({\n        __tauriModule: 'Fs',\n        message: {\n            cmd: 'writeFile',\n            path: file.path,\n            contents: Array.from(new TextEncoder().encode(file.contents)),\n            options: fileOptions\n        }\n    });\n}\n/**\n * Writes a byte array content to a file.\n *\n * @returns A promise indicating the success or failure of the operation.\n *\n * @since 1.0.0\n */\nasync function writeBinaryFile(path, contents, options) {\n    if (typeof options === 'object') {\n        Object.freeze(options);\n    }\n    if (typeof path === 'object') {\n        Object.freeze(path);\n    }\n    const file = { path: '', contents: [] };\n    let fileOptions = options;\n    if (typeof path === 'string') {\n        file.path = path;\n    }\n    else {\n        file.path = path.path;\n        file.contents = path.contents;\n    }\n    if (contents && 'dir' in contents) {\n        fileOptions = contents;\n    }\n    else if (typeof path === 'string') {\n        // @ts-expect-error in this case `contents` is always a BinaryFileContents\n        file.contents = contents !== null && contents !== void 0 ? contents : [];\n    }\n    return invokeTauriCommand({\n        __tauriModule: 'Fs',\n        message: {\n            cmd: 'writeFile',\n            path: file.path,\n            contents: Array.from(file.contents instanceof ArrayBuffer\n                ? new Uint8Array(file.contents)\n                : file.contents),\n            options: fileOptions\n        }\n    });\n}\n/**\n * List directory files.\n * @example\n * ```typescript\n * import { readDir, BaseDirectory } from '@tauri-apps/api/fs';\n * // Reads the `$APPDATA/users` directory recursively\n * const entries = await readDir('users', { dir: BaseDirectory.AppData, recursive: true });\n *\n * function processEntries(entries) {\n *   for (const entry of entries) {\n *     console.log(`Entry: ${entry.path}`);\n *     if (entry.children) {\n *       processEntries(entry.children)\n *     }\n *   }\n * }\n * ```\n *\n * @since 1.0.0\n */\nasync function readDir(dir, options = {}) {\n    return invokeTauriCommand({\n        __tauriModule: 'Fs',\n        message: {\n            cmd: 'readDir',\n            path: dir,\n            options\n        }\n    });\n}\n/**\n * Creates a directory.\n * If one of the path's parent components doesn't exist\n * and the `recursive` option isn't set to true, the promise will be rejected.\n * @example\n * ```typescript\n * import { createDir, BaseDirectory } from '@tauri-apps/api/fs';\n * // Create the `$APPDATA/users` directory\n * await createDir('users', { dir: BaseDirectory.AppData, recursive: true });\n * ```\n *\n * @returns A promise indicating the success or failure of the operation.\n *\n * @since 1.0.0\n */\nasync function createDir(dir, options = {}) {\n    return invokeTauriCommand({\n        __tauriModule: 'Fs',\n        message: {\n            cmd: 'createDir',\n            path: dir,\n            options\n        }\n    });\n}\n/**\n * Removes a directory.\n * If the directory is not empty and the `recursive` option isn't set to true, the promise will be rejected.\n * @example\n * ```typescript\n * import { removeDir, BaseDirectory } from '@tauri-apps/api/fs';\n * // Remove the directory `$APPDATA/users`\n * await removeDir('users', { dir: BaseDirectory.AppData });\n * ```\n *\n * @returns A promise indicating the success or failure of the operation.\n *\n * @since 1.0.0\n */\nasync function removeDir(dir, options = {}) {\n    return invokeTauriCommand({\n        __tauriModule: 'Fs',\n        message: {\n            cmd: 'removeDir',\n            path: dir,\n            options\n        }\n    });\n}\n/**\n * Copies a file to a destination.\n * @example\n * ```typescript\n * import { copyFile, BaseDirectory } from '@tauri-apps/api/fs';\n * // Copy the `$APPCONFIG/app.conf` file to `$APPCONFIG/app.conf.bk`\n * await copyFile('app.conf', 'app.conf.bk', { dir: BaseDirectory.AppConfig });\n * ```\n *\n * @returns A promise indicating the success or failure of the operation.\n *\n * @since 1.0.0\n */\nasync function copyFile(source, destination, options = {}) {\n    return invokeTauriCommand({\n        __tauriModule: 'Fs',\n        message: {\n            cmd: 'copyFile',\n            source,\n            destination,\n            options\n        }\n    });\n}\n/**\n * Removes a file.\n * @example\n * ```typescript\n * import { removeFile, BaseDirectory } from '@tauri-apps/api/fs';\n * // Remove the `$APPConfig/app.conf` file\n * await removeFile('app.conf', { dir: BaseDirectory.AppConfig });\n * ```\n *\n * @returns A promise indicating the success or failure of the operation.\n *\n * @since 1.0.0\n */\nasync function removeFile(file, options = {}) {\n    return invokeTauriCommand({\n        __tauriModule: 'Fs',\n        message: {\n            cmd: 'removeFile',\n            path: file,\n            options\n        }\n    });\n}\n/**\n * Renames a file.\n * @example\n * ```typescript\n * import { renameFile, BaseDirectory } from '@tauri-apps/api/fs';\n * // Rename the `$APPDATA/avatar.png` file\n * await renameFile('avatar.png', 'deleted.png', { dir: BaseDirectory.AppData });\n * ```\n *\n * @returns A promise indicating the success or failure of the operation.\n *\n * @since 1.0.0\n */\nasync function renameFile(oldPath, newPath, options = {}) {\n    return invokeTauriCommand({\n        __tauriModule: 'Fs',\n        message: {\n            cmd: 'renameFile',\n            oldPath,\n            newPath,\n            options\n        }\n    });\n}\n/**\n * Check if a path exists.\n * @example\n * ```typescript\n * import { exists, BaseDirectory } from '@tauri-apps/api/fs';\n * // Check if the `$APPDATA/avatar.png` file exists\n * await exists('avatar.png', { dir: BaseDirectory.AppData });\n * ```\n *\n * @since 1.1.0\n */\nasync function exists(path, options = {}) {\n    return invokeTauriCommand({\n        __tauriModule: 'Fs',\n        message: {\n            cmd: 'exists',\n            path,\n            options\n        }\n    });\n}\n\nexport { BaseDirectory, BaseDirectory as Dir, copyFile, createDir, exists, readBinaryFile, readDir, readTextFile, removeDir, removeFile, renameFile, writeBinaryFile, writeTextFile as writeFile, writeTextFile };\n", "import { invokeTauriCommand } from './helpers/tauri.js';\nimport { transformCallback } from './tauri.js';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Register global shortcuts.\n *\n * This package is also accessible with `window.__TAURI__.globalShortcut` when [`build.withGlobalTauri`](https://tauri.app/v1/api/config/#buildconfig.withglobaltauri) in `tauri.conf.json` is set to `true`.\n *\n * The APIs must be added to [`tauri.allowlist.globalShortcut`](https://tauri.app/v1/api/config/#allowlistconfig.globalshortcut) in `tauri.conf.json`:\n * ```json\n * {\n *   \"tauri\": {\n *     \"allowlist\": {\n *       \"globalShortcut\": {\n *         \"all\": true // enable all global shortcut APIs\n *       }\n *     }\n *   }\n * }\n * ```\n * It is recommended to allowlist only the APIs you use for optimal bundle size and security.\n * @module\n */\n/**\n * Register a global shortcut.\n * @example\n * ```typescript\n * import { register } from '@tauri-apps/api/globalShortcut';\n * await register('CommandOrControl+Shift+C', () => {\n *   console.log('Shortcut triggered');\n * });\n * ```\n *\n * @param shortcut Shortcut definition, modifiers and key separated by \"+\" e.g. CmdOrControl+Q\n * @param handler Shortcut handler callback - takes the triggered shortcut as argument\n *\n * @since 1.0.0\n */\nasync function register(shortcut, handler) {\n    return invokeTauriCommand({\n        __tauriModule: 'GlobalShortcut',\n        message: {\n            cmd: 'register',\n            shortcut,\n            handler: transformCallback(handler)\n        }\n    });\n}\n/**\n * Register a collection of global shortcuts.\n * @example\n * ```typescript\n * import { registerAll } from '@tauri-apps/api/globalShortcut';\n * await registerAll(['CommandOrControl+Shift+C', 'Ctrl+Alt+F12'], (shortcut) => {\n *   console.log(`Shortcut ${shortcut} triggered`);\n * });\n * ```\n *\n * @param shortcuts Array of shortcut definitions, modifiers and key separated by \"+\" e.g. CmdOrControl+Q\n * @param handler Shortcut handler callback - takes the triggered shortcut as argument\n *\n * @since 1.0.0\n */\nasync function registerAll(shortcuts, handler) {\n    return invokeTauriCommand({\n        __tauriModule: 'GlobalShortcut',\n        message: {\n            cmd: 'registerAll',\n            shortcuts,\n            handler: transformCallback(handler)\n        }\n    });\n}\n/**\n * Determines whether the given shortcut is registered by this application or not.\n * @example\n * ```typescript\n * import { isRegistered } from '@tauri-apps/api/globalShortcut';\n * const isRegistered = await isRegistered('CommandOrControl+P');\n * ```\n *\n * @param shortcut Array of shortcut definitions, modifiers and key separated by \"+\" e.g. CmdOrControl+Q\n *\n * @since 1.0.0\n */\nasync function isRegistered(shortcut) {\n    return invokeTauriCommand({\n        __tauriModule: 'GlobalShortcut',\n        message: {\n            cmd: 'isRegistered',\n            shortcut\n        }\n    });\n}\n/**\n * Unregister a global shortcut.\n * @example\n * ```typescript\n * import { unregister } from '@tauri-apps/api/globalShortcut';\n * await unregister('CmdOrControl+Space');\n * ```\n *\n * @param shortcut shortcut definition, modifiers and key separated by \"+\" e.g. CmdOrControl+Q\n *\n * @since 1.0.0\n */\nasync function unregister(shortcut) {\n    return invokeTauriCommand({\n        __tauriModule: 'GlobalShortcut',\n        message: {\n            cmd: 'unregister',\n            shortcut\n        }\n    });\n}\n/**\n * Unregisters all shortcuts registered by the application.\n * @example\n * ```typescript\n * import { unregisterAll } from '@tauri-apps/api/globalShortcut';\n * await unregisterAll();\n * ```\n *\n * @since 1.0.0\n */\nasync function unregisterAll() {\n    return invokeTauriCommand({\n        __tauriModule: 'GlobalShortcut',\n        message: {\n            cmd: 'unregisterAll'\n        }\n    });\n}\n\nexport { isRegistered, register, registerAll, unregister, unregisterAll };\n", "import { invokeTauriCommand } from './helpers/tauri.js';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Access the HTTP client written in Rust.\n *\n * This package is also accessible with `window.__TAURI__.http` when [`build.withGlobalTauri`](https://tauri.app/v1/api/config/#buildconfig.withglobaltauri) in `tauri.conf.json` is set to `true`.\n *\n * The APIs must be allowlisted on `tauri.conf.json`:\n * ```json\n * {\n *   \"tauri\": {\n *     \"allowlist\": {\n *       \"http\": {\n *         \"all\": true, // enable all http APIs\n *         \"request\": true // enable HTTP request API\n *       }\n *     }\n *   }\n * }\n * ```\n * It is recommended to allowlist only the APIs you use for optimal bundle size and security.\n *\n * ## Security\n *\n * This API has a scope configuration that forces you to restrict the URLs and paths that can be accessed using glob patterns.\n *\n * For instance, this scope configuration only allows making HTTP requests to the GitHub API for the `tauri-apps` organization:\n * ```json\n * {\n *   \"tauri\": {\n *     \"allowlist\": {\n *       \"http\": {\n *         \"scope\": [\"https://api.github.com/repos/tauri-apps/*\"]\n *       }\n *     }\n *   }\n * }\n * ```\n * Trying to execute any API with a URL not configured on the scope results in a promise rejection due to denied access.\n *\n * @module\n */\n/**\n * @since 1.0.0\n */\nvar ResponseType;\n(function (ResponseType) {\n    ResponseType[ResponseType[\"JSON\"] = 1] = \"JSON\";\n    ResponseType[ResponseType[\"Text\"] = 2] = \"Text\";\n    ResponseType[ResponseType[\"Binary\"] = 3] = \"Binary\";\n})(ResponseType || (ResponseType = {}));\nasync function formBody(data) {\n    const form = {};\n    const append = async (key, v) => {\n        if (v !== null) {\n            let r;\n            if (typeof v === 'string') {\n                r = v;\n            }\n            else if (v instanceof Uint8Array || Array.isArray(v)) {\n                r = Array.from(v);\n            }\n            else if (v instanceof File) {\n                r = {\n                    file: Array.from(new Uint8Array(await v.arrayBuffer())),\n                    mime: v.type,\n                    fileName: v.name\n                };\n            }\n            else if (typeof v.file === 'string') {\n                r = { file: v.file, mime: v.mime, fileName: v.fileName };\n            }\n            else {\n                r = { file: Array.from(v.file), mime: v.mime, fileName: v.fileName };\n            }\n            form[String(key)] = r;\n        }\n    };\n    if (data instanceof FormData) {\n        for (const [key, value] of data) {\n            await append(key, value);\n        }\n    }\n    else {\n        for (const [key, value] of Object.entries(data)) {\n            await append(key, value);\n        }\n    }\n    return form;\n}\n/**\n * The body object to be used on POST and PUT requests.\n *\n * @since 1.0.0\n */\nclass Body {\n    /** @ignore */\n    constructor(type, payload) {\n        this.type = type;\n        this.payload = payload;\n    }\n    /**\n     * Creates a new form data body. The form data is an object where each key is the entry name,\n     * and the value is either a string or a file object.\n     *\n     * By default it sets the `application/x-www-form-urlencoded` Content-Type header,\n     * but you can set it to `multipart/form-data` if the Cargo feature `http-multipart` is enabled.\n     *\n     * Note that a file path must be allowed in the `fs` allowlist scope.\n     *\n     * @example\n     * ```typescript\n     * import { Body } from \"@tauri-apps/api/http\"\n     * const body = Body.form({\n     *   key: 'value',\n     *   image: {\n     *     file: '/path/to/file', // either a path or an array buffer of the file contents\n     *     mime: 'image/jpeg', // optional\n     *     fileName: 'image.jpg' // optional\n     *   }\n     * });\n     *\n     * // alternatively, use a FormData:\n     * const form = new FormData();\n     * form.append('key', 'value');\n     * form.append('image', file, 'image.png');\n     * const formBody = Body.form(form);\n     * ```\n     *\n     * @param data The body data.\n     *\n     * @returns The body object ready to be used on the POST and PUT requests.\n     */\n    static form(data) {\n        return new Body('Form', data);\n    }\n    /**\n     * Creates a new JSON body.\n     * @example\n     * ```typescript\n     * import { Body } from \"@tauri-apps/api/http\"\n     * Body.json({\n     *   registered: true,\n     *   name: 'tauri'\n     * });\n     * ```\n     *\n     * @param data The body JSON object.\n     *\n     * @returns The body object ready to be used on the POST and PUT requests.\n     */\n    static json(data) {\n        return new Body('Json', data);\n    }\n    /**\n     * Creates a new UTF-8 string body.\n     * @example\n     * ```typescript\n     * import { Body } from \"@tauri-apps/api/http\"\n     * Body.text('The body content as a string');\n     * ```\n     *\n     * @param value The body string.\n     *\n     * @returns The body object ready to be used on the POST and PUT requests.\n     */\n    static text(value) {\n        return new Body('Text', value);\n    }\n    /**\n     * Creates a new byte array body.\n     * @example\n     * ```typescript\n     * import { Body } from \"@tauri-apps/api/http\"\n     * Body.bytes(new Uint8Array([1, 2, 3]));\n     * ```\n     *\n     * @param bytes The body byte array.\n     *\n     * @returns The body object ready to be used on the POST and PUT requests.\n     */\n    static bytes(bytes) {\n        // stringifying Uint8Array doesn't return an array of numbers, so we create one here\n        return new Body('Bytes', Array.from(bytes instanceof ArrayBuffer ? new Uint8Array(bytes) : bytes));\n    }\n}\n/**\n * Response object.\n *\n * @since 1.0.0\n * */\nclass Response {\n    /** @ignore */\n    constructor(response) {\n        this.url = response.url;\n        this.status = response.status;\n        this.ok = this.status >= 200 && this.status < 300;\n        this.headers = response.headers;\n        this.rawHeaders = response.rawHeaders;\n        this.data = response.data;\n    }\n}\n/**\n * @since 1.0.0\n */\nclass Client {\n    /** @ignore */\n    constructor(id) {\n        this.id = id;\n    }\n    /**\n     * Drops the client instance.\n     * @example\n     * ```typescript\n     * import { getClient } from '@tauri-apps/api/http';\n     * const client = await getClient();\n     * await client.drop();\n     * ```\n     */\n    async drop() {\n        return invokeTauriCommand({\n            __tauriModule: 'Http',\n            message: {\n                cmd: 'dropClient',\n                client: this.id\n            }\n        });\n    }\n    /**\n     * Makes an HTTP request.\n     * @example\n     * ```typescript\n     * import { getClient } from '@tauri-apps/api/http';\n     * const client = await getClient();\n     * const response = await client.request({\n     *   method: 'GET',\n     *   url: 'http://localhost:3003/users',\n     * });\n     * ```\n     */\n    async request(options) {\n        var _a;\n        const jsonResponse = !options.responseType || options.responseType === ResponseType.JSON;\n        if (jsonResponse) {\n            options.responseType = ResponseType.Text;\n        }\n        if (((_a = options.body) === null || _a === void 0 ? void 0 : _a.type) === 'Form') {\n            options.body.payload = await formBody(options.body.payload);\n        }\n        return invokeTauriCommand({\n            __tauriModule: 'Http',\n            message: {\n                cmd: 'httpRequest',\n                client: this.id,\n                options\n            }\n        }).then((res) => {\n            const response = new Response(res);\n            if (jsonResponse) {\n                /* eslint-disable */\n                try {\n                    response.data = JSON.parse(response.data);\n                }\n                catch (e) {\n                    if (response.ok && response.data === '') {\n                        response.data = {};\n                    }\n                    else if (response.ok) {\n                        throw Error(`Failed to parse response \\`${response.data}\\` as JSON: ${e};\n              try setting the \\`responseType\\` option to \\`ResponseType.Text\\` or \\`ResponseType.Binary\\` if the API does not return a JSON response.`);\n                    }\n                }\n                /* eslint-enable */\n                return response;\n            }\n            return response;\n        });\n    }\n    /**\n     * Makes a GET request.\n     * @example\n     * ```typescript\n     * import { getClient, ResponseType } from '@tauri-apps/api/http';\n     * const client = await getClient();\n     * const response = await client.get('http://localhost:3003/users', {\n     *   timeout: 30,\n     *   // the expected response type\n     *   responseType: ResponseType.JSON\n     * });\n     * ```\n     */\n    async get(url, options) {\n        return this.request({\n            method: 'GET',\n            url,\n            ...options\n        });\n    }\n    /**\n     * Makes a POST request.\n     * @example\n     * ```typescript\n     * import { getClient, Body, ResponseType } from '@tauri-apps/api/http';\n     * const client = await getClient();\n     * const response = await client.post('http://localhost:3003/users', {\n     *   body: Body.json({\n     *     name: 'tauri',\n     *     password: 'awesome'\n     *   }),\n     *   // in this case the server returns a simple string\n     *   responseType: ResponseType.Text,\n     * });\n     * ```\n     */\n    async post(url, body, options) {\n        return this.request({\n            method: 'POST',\n            url,\n            body,\n            ...options\n        });\n    }\n    /**\n     * Makes a PUT request.\n     * @example\n     * ```typescript\n     * import { getClient, Body } from '@tauri-apps/api/http';\n     * const client = await getClient();\n     * const response = await client.put('http://localhost:3003/users/1', {\n     *   body: Body.form({\n     *     file: {\n     *       file: '/home/<USER>/avatar.png',\n     *       mime: 'image/png',\n     *       fileName: 'avatar.png'\n     *     }\n     *   })\n     * });\n     * ```\n     */\n    async put(url, body, options) {\n        return this.request({\n            method: 'PUT',\n            url,\n            body,\n            ...options\n        });\n    }\n    /**\n     * Makes a PATCH request.\n     * @example\n     * ```typescript\n     * import { getClient, Body } from '@tauri-apps/api/http';\n     * const client = await getClient();\n     * const response = await client.patch('http://localhost:3003/users/1', {\n     *   body: Body.json({ email: '<EMAIL>' })\n     * });\n     * ```\n     */\n    async patch(url, options) {\n        return this.request({\n            method: 'PATCH',\n            url,\n            ...options\n        });\n    }\n    /**\n     * Makes a DELETE request.\n     * @example\n     * ```typescript\n     * import { getClient } from '@tauri-apps/api/http';\n     * const client = await getClient();\n     * const response = await client.delete('http://localhost:3003/users/1');\n     * ```\n     */\n    async delete(url, options) {\n        return this.request({\n            method: 'DELETE',\n            url,\n            ...options\n        });\n    }\n}\n/**\n * Creates a new client using the specified options.\n * @example\n * ```typescript\n * import { getClient } from '@tauri-apps/api/http';\n * const client = await getClient();\n * ```\n *\n * @param options Client configuration.\n *\n * @returns A promise resolving to the client instance.\n *\n * @since 1.0.0\n */\nasync function getClient(options) {\n    return invokeTauriCommand({\n        __tauriModule: 'Http',\n        message: {\n            cmd: 'createClient',\n            options\n        }\n    }).then((id) => new Client(id));\n}\n/** @internal */\nlet defaultClient = null;\n/**\n * Perform an HTTP request using the default client.\n * @example\n * ```typescript\n * import { fetch } from '@tauri-apps/api/http';\n * const response = await fetch('http://localhost:3003/users/2', {\n *   method: 'GET',\n *   timeout: 30,\n * });\n * ```\n */\nasync function fetch(url, options) {\n    var _a;\n    if (defaultClient === null) {\n        defaultClient = await getClient();\n    }\n    return defaultClient.request({\n        url,\n        method: (_a = options === null || options === void 0 ? void 0 : options.method) !== null && _a !== void 0 ? _a : 'GET',\n        ...options\n    });\n}\n\nexport { Body, Client, Response, ResponseType, fetch, getClient };\n", "import { invokeTauriCommand } from './helpers/tauri.js';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Send toast notifications (brief auto-expiring OS window element) to your user.\n * Can also be used with the Notification Web API.\n *\n * This package is also accessible with `window.__TAURI__.notification` when [`build.withGlobalTauri`](https://tauri.app/v1/api/config/#buildconfig.withglobaltauri) in `tauri.conf.json` is set to `true`.\n *\n * The APIs must be added to [`tauri.allowlist.notification`](https://tauri.app/v1/api/config/#allowlistconfig.notification) in `tauri.conf.json`:\n * ```json\n * {\n *   \"tauri\": {\n *     \"allowlist\": {\n *       \"notification\": {\n *         \"all\": true // enable all notification APIs\n *       }\n *     }\n *   }\n * }\n * ```\n * It is recommended to allowlist only the APIs you use for optimal bundle size and security.\n * @module\n */\n/**\n * Checks if the permission to send notifications is granted.\n * @example\n * ```typescript\n * import { isPermissionGranted } from '@tauri-apps/api/notification';\n * const permissionGranted = await isPermissionGranted();\n * ```\n *\n * @since 1.0.0\n */\nasync function isPermissionGranted() {\n    if (window.Notification.permission !== 'default') {\n        return Promise.resolve(window.Notification.permission === 'granted');\n    }\n    return invokeTauriCommand({\n        __tauriModule: 'Notification',\n        message: {\n            cmd: 'isNotificationPermissionGranted'\n        }\n    });\n}\n/**\n * Requests the permission to send notifications.\n * @example\n * ```typescript\n * import { isPermissionGranted, requestPermission } from '@tauri-apps/api/notification';\n * let permissionGranted = await isPermissionGranted();\n * if (!permissionGranted) {\n *   const permission = await requestPermission();\n *   permissionGranted = permission === 'granted';\n * }\n * ```\n *\n * @returns A promise resolving to whether the user granted the permission or not.\n *\n * @since 1.0.0\n */\nasync function requestPermission() {\n    return window.Notification.requestPermission();\n}\n/**\n * Sends a notification to the user.\n * @example\n * ```typescript\n * import { isPermissionGranted, requestPermission, sendNotification } from '@tauri-apps/api/notification';\n * let permissionGranted = await isPermissionGranted();\n * if (!permissionGranted) {\n *   const permission = await requestPermission();\n *   permissionGranted = permission === 'granted';\n * }\n * if (permissionGranted) {\n *   sendNotification('Tauri is awesome!');\n *   sendNotification({ title: 'TAURI', body: 'Tauri is awesome!' });\n * }\n * ```\n *\n * @since 1.0.0\n */\nfunction sendNotification(options) {\n    if (typeof options === 'string') {\n        // eslint-disable-next-line no-new\n        new window.Notification(options);\n    }\n    else {\n        // eslint-disable-next-line no-new\n        new window.Notification(options.title, options);\n    }\n}\n\nexport { isPermissionGranted, requestPermission, sendNotification };\n", "import { invokeTauriCommand } from './helpers/tauri.js';\nimport { BaseDirectory } from './fs.js';\nimport { isWindows } from './helpers/os-check.js';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * The path module provides utilities for working with file and directory paths.\n *\n * This package is also accessible with `window.__TAURI__.path` when [`build.withGlobalTauri`](https://tauri.app/v1/api/config/#buildconfig.withglobaltauri) in `tauri.conf.json` is set to `true`.\n *\n * The APIs must be added to [`tauri.allowlist.path`](https://tauri.app/v1/api/config/#allowlistconfig.path) in `tauri.conf.json`:\n * ```json\n * {\n *   \"tauri\": {\n *     \"allowlist\": {\n *       \"path\": {\n *         \"all\": true, // enable all Path APIs\n *       }\n *     }\n *   }\n * }\n * ```\n * It is recommended to allowlist only the APIs you use for optimal bundle size and security.\n * @module\n */\n/**\n * Returns the path to the suggested directory for your app config files.\n *\n * @deprecated since 1.2.0: Will be removed in 2.0.0. Use {@link appConfigDir} or {@link appDataDir} instead.\n * @since 1.0.0\n */\nasync function appDir() {\n    return appConfigDir();\n}\n/**\n * Returns the path to the suggested directory for your app's config files.\n * Resolves to `${configDir}/${bundleIdentifier}`, where `bundleIdentifier` is the value [`tauri.bundle.identifier`](https://tauri.app/v1/api/config/#bundleconfig.identifier) is configured in `tauri.conf.json`.\n * @example\n * ```typescript\n * import { appConfigDir } from '@tauri-apps/api/path';\n * const appConfigDirPath = await appConfigDir();\n * ```\n *\n * @since 1.2.0\n */\nasync function appConfigDir() {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolvePath',\n            path: '',\n            directory: BaseDirectory.AppConfig\n        }\n    });\n}\n/**\n * Returns the path to the suggested directory for your app's data files.\n * Resolves to `${dataDir}/${bundleIdentifier}`, where `bundleIdentifier` is the value [`tauri.bundle.identifier`](https://tauri.app/v1/api/config/#bundleconfig.identifier) is configured in `tauri.conf.json`.\n * @example\n * ```typescript\n * import { appDataDir } from '@tauri-apps/api/path';\n * const appDataDirPath = await appDataDir();\n * ```\n *\n * @since 1.2.0\n */\nasync function appDataDir() {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolvePath',\n            path: '',\n            directory: BaseDirectory.AppData\n        }\n    });\n}\n/**\n * Returns the path to the suggested directory for your app's local data files.\n * Resolves to `${localDataDir}/${bundleIdentifier}`, where `bundleIdentifier` is the value [`tauri.bundle.identifier`](https://tauri.app/v1/api/config/#bundleconfig.identifier) is configured in `tauri.conf.json`.\n * @example\n * ```typescript\n * import { appLocalDataDir } from '@tauri-apps/api/path';\n * const appLocalDataDirPath = await appLocalDataDir();\n * ```\n *\n * @since 1.2.0\n */\nasync function appLocalDataDir() {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolvePath',\n            path: '',\n            directory: BaseDirectory.AppLocalData\n        }\n    });\n}\n/**\n * Returns the path to the suggested directory for your app's cache files.\n * Resolves to `${cacheDir}/${bundleIdentifier}`, where `bundleIdentifier` is the value [`tauri.bundle.identifier`](https://tauri.app/v1/api/config/#bundleconfig.identifier) is configured in `tauri.conf.json`.\n * @example\n * ```typescript\n * import { appCacheDir } from '@tauri-apps/api/path';\n * const appCacheDirPath = await appCacheDir();\n * ```\n *\n * @since 1.2.0\n */\nasync function appCacheDir() {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolvePath',\n            path: '',\n            directory: BaseDirectory.AppCache\n        }\n    });\n}\n/**\n * Returns the path to the user's audio directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to [`xdg-user-dirs`](https://www.freedesktop.org/wiki/Software/xdg-user-dirs/)' `XDG_MUSIC_DIR`.\n * - **macOS:** Resolves to `$HOME/Music`.\n * - **Windows:** Resolves to `{FOLDERID_Music}`.\n * @example\n * ```typescript\n * import { audioDir } from '@tauri-apps/api/path';\n * const audioDirPath = await audioDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function audioDir() {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolvePath',\n            path: '',\n            directory: BaseDirectory.Audio\n        }\n    });\n}\n/**\n * Returns the path to the user's cache directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to `$XDG_CACHE_HOME` or `$HOME/.cache`.\n * - **macOS:** Resolves to `$HOME/Library/Caches`.\n * - **Windows:** Resolves to `{FOLDERID_LocalAppData}`.\n * @example\n * ```typescript\n * import { cacheDir } from '@tauri-apps/api/path';\n * const cacheDirPath = await cacheDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function cacheDir() {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolvePath',\n            path: '',\n            directory: BaseDirectory.Cache\n        }\n    });\n}\n/**\n * Returns the path to the user's config directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to `$XDG_CONFIG_HOME` or `$HOME/.config`.\n * - **macOS:** Resolves to `$HOME/Library/Application Support`.\n * - **Windows:** Resolves to `{FOLDERID_RoamingAppData}`.\n * @example\n * ```typescript\n * import { configDir } from '@tauri-apps/api/path';\n * const configDirPath = await configDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function configDir() {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolvePath',\n            path: '',\n            directory: BaseDirectory.Config\n        }\n    });\n}\n/**\n * Returns the path to the user's data directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to `$XDG_DATA_HOME` or `$HOME/.local/share`.\n * - **macOS:** Resolves to `$HOME/Library/Application Support`.\n * - **Windows:** Resolves to `{FOLDERID_RoamingAppData}`.\n * @example\n * ```typescript\n * import { dataDir } from '@tauri-apps/api/path';\n * const dataDirPath = await dataDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function dataDir() {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolvePath',\n            path: '',\n            directory: BaseDirectory.Data\n        }\n    });\n}\n/**\n * Returns the path to the user's desktop directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to [`xdg-user-dirs`](https://www.freedesktop.org/wiki/Software/xdg-user-dirs/)' `XDG_DESKTOP_DIR`.\n * - **macOS:** Resolves to `$HOME/Desktop`.\n * - **Windows:** Resolves to `{FOLDERID_Desktop}`.\n * @example\n * ```typescript\n * import { desktopDir } from '@tauri-apps/api/path';\n * const desktopPath = await desktopDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function desktopDir() {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolvePath',\n            path: '',\n            directory: BaseDirectory.Desktop\n        }\n    });\n}\n/**\n * Returns the path to the user's document directory.\n * @example\n * ```typescript\n * import { documentDir } from '@tauri-apps/api/path';\n * const documentDirPath = await documentDir();\n * ```\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to [`xdg-user-dirs`](https://www.freedesktop.org/wiki/Software/xdg-user-dirs/)' `XDG_DOCUMENTS_DIR`.\n * - **macOS:** Resolves to `$HOME/Documents`.\n * - **Windows:** Resolves to `{FOLDERID_Documents}`.\n *\n * @since 1.0.0\n */\nasync function documentDir() {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolvePath',\n            path: '',\n            directory: BaseDirectory.Document\n        }\n    });\n}\n/**\n * Returns the path to the user's download directory.\n *\n * #### Platform-specific\n *\n * - **Linux**: Resolves to [`xdg-user-dirs`](https://www.freedesktop.org/wiki/Software/xdg-user-dirs/)' `XDG_DOWNLOAD_DIR`.\n * - **macOS**: Resolves to `$HOME/Downloads`.\n * - **Windows**: Resolves to `{FOLDERID_Downloads}`.\n * @example\n * ```typescript\n * import { downloadDir } from '@tauri-apps/api/path';\n * const downloadDirPath = await downloadDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function downloadDir() {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolvePath',\n            path: '',\n            directory: BaseDirectory.Download\n        }\n    });\n}\n/**\n * Returns the path to the user's executable directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to `$XDG_BIN_HOME/../bin` or `$XDG_DATA_HOME/../bin` or `$HOME/.local/bin`.\n * - **macOS:** Not supported.\n * - **Windows:** Not supported.\n * @example\n * ```typescript\n * import { executableDir } from '@tauri-apps/api/path';\n * const executableDirPath = await executableDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function executableDir() {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolvePath',\n            path: '',\n            directory: BaseDirectory.Executable\n        }\n    });\n}\n/**\n * Returns the path to the user's font directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to `$XDG_DATA_HOME/fonts` or `$HOME/.local/share/fonts`.\n * - **macOS:** Resolves to `$HOME/Library/Fonts`.\n * - **Windows:** Not supported.\n * @example\n * ```typescript\n * import { fontDir } from '@tauri-apps/api/path';\n * const fontDirPath = await fontDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function fontDir() {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolvePath',\n            path: '',\n            directory: BaseDirectory.Font\n        }\n    });\n}\n/**\n * Returns the path to the user's home directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to `$HOME`.\n * - **macOS:** Resolves to `$HOME`.\n * - **Windows:** Resolves to `{FOLDERID_Profile}`.\n * @example\n * ```typescript\n * import { homeDir } from '@tauri-apps/api/path';\n * const homeDirPath = await homeDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function homeDir() {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolvePath',\n            path: '',\n            directory: BaseDirectory.Home\n        }\n    });\n}\n/**\n * Returns the path to the user's local data directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to `$XDG_DATA_HOME` or `$HOME/.local/share`.\n * - **macOS:** Resolves to `$HOME/Library/Application Support`.\n * - **Windows:** Resolves to `{FOLDERID_LocalAppData}`.\n * @example\n * ```typescript\n * import { localDataDir } from '@tauri-apps/api/path';\n * const localDataDirPath = await localDataDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function localDataDir() {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolvePath',\n            path: '',\n            directory: BaseDirectory.LocalData\n        }\n    });\n}\n/**\n * Returns the path to the user's picture directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to [`xdg-user-dirs`](https://www.freedesktop.org/wiki/Software/xdg-user-dirs/)' `XDG_PICTURES_DIR`.\n * - **macOS:** Resolves to `$HOME/Pictures`.\n * - **Windows:** Resolves to `{FOLDERID_Pictures}`.\n * @example\n * ```typescript\n * import { pictureDir } from '@tauri-apps/api/path';\n * const pictureDirPath = await pictureDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function pictureDir() {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolvePath',\n            path: '',\n            directory: BaseDirectory.Picture\n        }\n    });\n}\n/**\n * Returns the path to the user's public directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to [`xdg-user-dirs`](https://www.freedesktop.org/wiki/Software/xdg-user-dirs/)' `XDG_PUBLICSHARE_DIR`.\n * - **macOS:** Resolves to `$HOME/Public`.\n * - **Windows:** Resolves to `{FOLDERID_Public}`.\n * @example\n * ```typescript\n * import { publicDir } from '@tauri-apps/api/path';\n * const publicDirPath = await publicDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function publicDir() {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolvePath',\n            path: '',\n            directory: BaseDirectory.Public\n        }\n    });\n}\n/**\n * Returns the path to the application's resource directory.\n * To resolve a resource path, see the [[resolveResource | `resolveResource API`]].\n * @example\n * ```typescript\n * import { resourceDir } from '@tauri-apps/api/path';\n * const resourceDirPath = await resourceDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function resourceDir() {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolvePath',\n            path: '',\n            directory: BaseDirectory.Resource\n        }\n    });\n}\n/**\n * Resolve the path to a resource file.\n * @example\n * ```typescript\n * import { resolveResource } from '@tauri-apps/api/path';\n * const resourcePath = await resolveResource('script.sh');\n * ```\n *\n * @param resourcePath The path to the resource.\n * Must follow the same syntax as defined in `tauri.conf.json > tauri > bundle > resources`, i.e. keeping subfolders and parent dir components (`../`).\n * @returns The full path to the resource.\n *\n * @since 1.0.0\n */\nasync function resolveResource(resourcePath) {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolvePath',\n            path: resourcePath,\n            directory: BaseDirectory.Resource\n        }\n    });\n}\n/**\n * Returns the path to the user's runtime directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to `$XDG_RUNTIME_DIR`.\n * - **macOS:** Not supported.\n * - **Windows:** Not supported.\n * @example\n * ```typescript\n * import { runtimeDir } from '@tauri-apps/api/path';\n * const runtimeDirPath = await runtimeDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function runtimeDir() {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolvePath',\n            path: '',\n            directory: BaseDirectory.Runtime\n        }\n    });\n}\n/**\n * Returns the path to the user's template directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to [`xdg-user-dirs`](https://www.freedesktop.org/wiki/Software/xdg-user-dirs/)' `XDG_TEMPLATES_DIR`.\n * - **macOS:** Not supported.\n * - **Windows:** Resolves to `{FOLDERID_Templates}`.\n * @example\n * ```typescript\n * import { templateDir } from '@tauri-apps/api/path';\n * const templateDirPath = await templateDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function templateDir() {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolvePath',\n            path: '',\n            directory: BaseDirectory.Template\n        }\n    });\n}\n/**\n * Returns the path to the user's video directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to [`xdg-user-dirs`](https://www.freedesktop.org/wiki/Software/xdg-user-dirs/)' `XDG_VIDEOS_DIR`.\n * - **macOS:** Resolves to `$HOME/Movies`.\n * - **Windows:** Resolves to `{FOLDERID_Videos}`.\n * @example\n * ```typescript\n * import { videoDir } from '@tauri-apps/api/path';\n * const videoDirPath = await videoDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function videoDir() {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolvePath',\n            path: '',\n            directory: BaseDirectory.Video\n        }\n    });\n}\n/**\n * Returns the path to the suggested log directory.\n *\n * @deprecated since 1.2.0: Will be removed in 2.0.0. Use {@link appLogDir} instead.\n * @since 1.0.0\n */\nasync function logDir() {\n    return appLogDir();\n}\n/**\n * Returns the path to the suggested directory for your app's log files.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to `${configDir}/${bundleIdentifier}/logs`.\n * - **macOS:** Resolves to `${homeDir}/Library/Logs/{bundleIdentifier}`\n * - **Windows:** Resolves to `${configDir}/${bundleIdentifier}/logs`.\n * @example\n * ```typescript\n * import { appLogDir } from '@tauri-apps/api/path';\n * const appLogDirPath = await appLogDir();\n * ```\n *\n * @since 1.2.0\n */\nasync function appLogDir() {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolvePath',\n            path: '',\n            directory: BaseDirectory.AppLog\n        }\n    });\n}\n/**\n * Provides the platform-specific path segment separator:\n * - `\\` on Windows\n * - `/` on POSIX\n *\n * @since 1.0.0\n */\nconst sep = isWindows() ? '\\\\' : '/';\n/**\n * Provides the platform-specific path segment delimiter:\n * - `;` on Windows\n * - `:` on POSIX\n *\n * @since 1.0.0\n */\nconst delimiter = isWindows() ? ';' : ':';\n/**\n * Resolves a sequence of `paths` or `path` segments into an absolute path.\n * @example\n * ```typescript\n * import { resolve, appDataDir } from '@tauri-apps/api/path';\n * const appDataDirPath = await appDataDir();\n * const path = await resolve(appDataDirPath, '..', 'users', 'tauri', 'avatar.png');\n * ```\n *\n * @since 1.0.0\n */\nasync function resolve(...paths) {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'resolve',\n            paths\n        }\n    });\n}\n/**\n * Normalizes the given `path`, resolving `'..'` and `'.'` segments and resolve symbolic links.\n * @example\n * ```typescript\n * import { normalize, appDataDir } from '@tauri-apps/api/path';\n * const appDataDirPath = await appDataDir();\n * const path = await normalize(appDataDirPath, '..', 'users', 'tauri', 'avatar.png');\n * ```\n *\n * @since 1.0.0\n */\nasync function normalize(path) {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'normalize',\n            path\n        }\n    });\n}\n/**\n *  Joins all given `path` segments together using the platform-specific separator as a delimiter, then normalizes the resulting path.\n * @example\n * ```typescript\n * import { join, appDataDir } from '@tauri-apps/api/path';\n * const appDataDirPath = await appDataDir();\n * const path = await join(appDataDirPath, 'users', 'tauri', 'avatar.png');\n * ```\n *\n * @since 1.0.0\n */\nasync function join(...paths) {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'join',\n            paths\n        }\n    });\n}\n/**\n * Returns the directory name of a `path`. Trailing directory separators are ignored.\n * @example\n * ```typescript\n * import { dirname } from '@tauri-apps/api/path';\n * const dir = await dirname('/path/to/somedir/');\n * assert(dir === 'somedir');\n * ```\n *\n * @since 1.0.0\n */\nasync function dirname(path) {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'dirname',\n            path\n        }\n    });\n}\n/**\n * Returns the extension of the `path`.\n * @example\n * ```typescript\n * import { extname } from '@tauri-apps/api/path';\n * const ext = await extname('/path/to/file.html');\n * assert(ext === 'html');\n * ```\n *\n * @since 1.0.0\n */\nasync function extname(path) {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'extname',\n            path\n        }\n    });\n}\n/**\n * Returns the last portion of a `path`. Trailing directory separators are ignored.\n * @example\n * ```typescript\n * import { basename } from '@tauri-apps/api/path';\n * const base = await basename('path/to/app.conf');\n * assert(base === 'app.conf');\n * ```\n *\n * @param ext An optional file extension to be removed from the returned path.\n *\n * @since 1.0.0\n */\nasync function basename(path, ext) {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'basename',\n            path,\n            ext\n        }\n    });\n}\n/**\n * Returns whether the path is absolute or not.\n * @example\n * ```typescript\n * import { isAbsolute } from '@tauri-apps/api/path';\n * assert(await isAbsolute('/home/<USER>'));\n * ```\n *\n * @since 1.0.0\n */\nasync function isAbsolute(path) {\n    return invokeTauriCommand({\n        __tauriModule: 'Path',\n        message: {\n            cmd: 'isAbsolute',\n            path\n        }\n    });\n}\n\nexport { BaseDirectory, appCacheDir, appConfigDir, appDataDir, appDir, appLocalDataDir, appLogDir, audioDir, basename, cacheDir, configDir, dataDir, delimiter, desktopDir, dirname, documentDir, downloadDir, executableDir, extname, fontDir, homeDir, isAbsolute, join, localDataDir, logDir, normalize, pictureDir, publicDir, resolve, resolveResource, resourceDir, runtimeDir, sep, templateDir, videoDir };\n", "// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/** @ignore */\nfunction isWindows() {\n    return navigator.appVersion.includes('Win');\n}\n\nexport { isWindows };\n", "import { invokeTauriCommand } from './helpers/tauri.js';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Perform operations on the current process.\n *\n * This package is also accessible with `window.__TAURI__.process` when [`build.withGlobalTauri`](https://tauri.app/v1/api/config/#buildconfig.withglobaltauri) in `tauri.conf.json` is set to `true`.\n * @module\n */\n/**\n * Exits immediately with the given `exitCode`.\n * @example\n * ```typescript\n * import { exit } from '@tauri-apps/api/process';\n * await exit(1);\n * ```\n *\n * @param exitCode The exit code to use.\n * @returns A promise indicating the success or failure of the operation.\n *\n * @since 1.0.0\n */\nasync function exit(exitCode = 0) {\n    return invokeTauriCommand({\n        __tauriModule: 'Process',\n        message: {\n            cmd: 'exit',\n            exitCode\n        }\n    });\n}\n/**\n * Exits the current instance of the app then relaunches it.\n * @example\n * ```typescript\n * import { relaunch } from '@tauri-apps/api/process';\n * await relaunch();\n * ```\n *\n * @returns A promise indicating the success or failure of the operation.\n *\n * @since 1.0.0\n */\nasync function relaunch() {\n    return invokeTauriCommand({\n        __tauriModule: 'Process',\n        message: {\n            cmd: 'relaunch'\n        }\n    });\n}\n\nexport { exit, relaunch };\n", "import { invokeTauriCommand } from './helpers/tauri.js';\nimport { transformCallback } from './tauri.js';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Access the system shell.\n * Allows you to spawn child processes and manage files and URLs using their default application.\n *\n * This package is also accessible with `window.__TAURI__.shell` when [`build.withGlobalTauri`](https://tauri.app/v1/api/config/#buildconfig.withglobaltauri) in `tauri.conf.json` is set to `true`.\n *\n * The APIs must be added to [`tauri.allowlist.shell`](https://tauri.app/v1/api/config/#allowlistconfig.shell) in `tauri.conf.json`:\n * ```json\n * {\n *   \"tauri\": {\n *     \"allowlist\": {\n *       \"shell\": {\n *         \"all\": true, // enable all shell APIs\n *         \"execute\": true, // enable process spawn APIs\n *         \"sidecar\": true, // enable spawning sidecars\n *         \"open\": true // enable opening files/URLs using the default program\n *       }\n *     }\n *   }\n * }\n * ```\n * It is recommended to allowlist only the APIs you use for optimal bundle size and security.\n *\n * ## Security\n *\n * This API has a scope configuration that forces you to restrict the programs and arguments that can be used.\n *\n * ### Restricting access to the {@link open | `open`} API\n *\n * On the allowlist, `open: true` means that the {@link open} API can be used with any URL,\n * as the argument is validated with the `^((mailto:\\w+)|(tel:\\w+)|(https?://\\w+)).+` regex.\n * You can change that regex by changing the boolean value to a string, e.g. `open: ^https://github.com/`.\n *\n * ### Restricting access to the {@link Command | `Command`} APIs\n *\n * The `shell` allowlist object has a `scope` field that defines an array of CLIs that can be used.\n * Each CLI is a configuration object `{ name: string, cmd: string, sidecar?: bool, args?: boolean | Arg[] }`.\n *\n * - `name`: the unique identifier of the command, passed to the {@link Command.constructor | Command constructor}.\n * If it's a sidecar, this must be the value defined on `tauri.conf.json > tauri > bundle > externalBin`.\n * - `cmd`: the program that is executed on this configuration. If it's a sidecar, this value is ignored.\n * - `sidecar`: whether the object configures a sidecar or a system program.\n * - `args`: the arguments that can be passed to the program. By default no arguments are allowed.\n *   - `true` means that any argument list is allowed.\n *   - `false` means that no arguments are allowed.\n *   - otherwise an array can be configured. Each item is either a string representing the fixed argument value\n *     or a `{ validator: string }` that defines a regex validating the argument value.\n *\n * #### Example scope configuration\n *\n * CLI: `git commit -m \"the commit message\"`\n *\n * Configuration:\n * ```json\n * {\n *   \"scope\": [\n *     {\n *       \"name\": \"run-git-commit\",\n *       \"cmd\": \"git\",\n *       \"args\": [\"commit\", \"-m\", { \"validator\": \"\\\\S+\" }]\n *     }\n *   ]\n * }\n * ```\n * Usage:\n * ```typescript\n * import { Command } from '@tauri-apps/api/shell'\n * new Command('run-git-commit', ['commit', '-m', 'the commit message'])\n * ```\n *\n * Trying to execute any API with a program not configured on the scope results in a promise rejection due to denied access.\n *\n * @module\n */\n/**\n * @since 1.0.0\n */\nclass EventEmitter {\n    constructor() {\n        /** @ignore */\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n        this.eventListeners = Object.create(null);\n    }\n    /**\n     * Alias for `emitter.on(eventName, listener)`.\n     *\n     * @since 1.1.0\n     */\n    addListener(eventName, listener) {\n        return this.on(eventName, listener);\n    }\n    /**\n     * Alias for `emitter.off(eventName, listener)`.\n     *\n     * @since 1.1.0\n     */\n    removeListener(eventName, listener) {\n        return this.off(eventName, listener);\n    }\n    /**\n     * Adds the `listener` function to the end of the listeners array for the\n     * event named `eventName`. No checks are made to see if the `listener` has\n     * already been added. Multiple calls passing the same combination of `eventName`and `listener` will result in the `listener` being added, and called, multiple\n     * times.\n     *\n     * Returns a reference to the `EventEmitter`, so that calls can be chained.\n     *\n     * @since 1.0.0\n     */\n    on(eventName, listener) {\n        if (eventName in this.eventListeners) {\n            // eslint-disable-next-line security/detect-object-injection\n            this.eventListeners[eventName].push(listener);\n        }\n        else {\n            // eslint-disable-next-line security/detect-object-injection\n            this.eventListeners[eventName] = [listener];\n        }\n        return this;\n    }\n    /**\n     * Adds a **one-time**`listener` function for the event named `eventName`. The\n     * next time `eventName` is triggered, this listener is removed and then invoked.\n     *\n     * Returns a reference to the `EventEmitter`, so that calls can be chained.\n     *\n     * @since 1.1.0\n     */\n    once(eventName, listener) {\n        const wrapper = (...args) => {\n            this.removeListener(eventName, wrapper);\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n            listener(...args);\n        };\n        return this.addListener(eventName, wrapper);\n    }\n    /**\n     * Removes the all specified listener from the listener array for the event eventName\n     * Returns a reference to the `EventEmitter`, so that calls can be chained.\n     *\n     * @since 1.1.0\n     */\n    off(eventName, listener) {\n        if (eventName in this.eventListeners) {\n            // eslint-disable-next-line security/detect-object-injection\n            this.eventListeners[eventName] = this.eventListeners[eventName].filter((l) => l !== listener);\n        }\n        return this;\n    }\n    /**\n     * Removes all listeners, or those of the specified eventName.\n     *\n     * Returns a reference to the `EventEmitter`, so that calls can be chained.\n     *\n     * @since 1.1.0\n     */\n    removeAllListeners(event) {\n        if (event) {\n            // eslint-disable-next-line @typescript-eslint/no-dynamic-delete,security/detect-object-injection\n            delete this.eventListeners[event];\n        }\n        else {\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n            this.eventListeners = Object.create(null);\n        }\n        return this;\n    }\n    /**\n     * @ignore\n     * Synchronously calls each of the listeners registered for the event named`eventName`, in the order they were registered, passing the supplied arguments\n     * to each.\n     *\n     * @returns `true` if the event had listeners, `false` otherwise.\n     */\n    emit(eventName, ...args) {\n        if (eventName in this.eventListeners) {\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment,security/detect-object-injection\n            const listeners = this.eventListeners[eventName];\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n            for (const listener of listeners)\n                listener(...args);\n            return true;\n        }\n        return false;\n    }\n    /**\n     * Returns the number of listeners listening to the event named `eventName`.\n     *\n     * @since 1.1.0\n     */\n    listenerCount(eventName) {\n        if (eventName in this.eventListeners)\n            // eslint-disable-next-line security/detect-object-injection\n            return this.eventListeners[eventName].length;\n        return 0;\n    }\n    /**\n     * Adds the `listener` function to the _beginning_ of the listeners array for the\n     * event named `eventName`. No checks are made to see if the `listener` has\n     * already been added. Multiple calls passing the same combination of `eventName`and `listener` will result in the `listener` being added, and called, multiple\n     * times.\n     *\n     * Returns a reference to the `EventEmitter`, so that calls can be chained.\n     *\n     * @since 1.1.0\n     */\n    prependListener(eventName, listener) {\n        if (eventName in this.eventListeners) {\n            // eslint-disable-next-line security/detect-object-injection\n            this.eventListeners[eventName].unshift(listener);\n        }\n        else {\n            // eslint-disable-next-line security/detect-object-injection\n            this.eventListeners[eventName] = [listener];\n        }\n        return this;\n    }\n    /**\n     * Adds a **one-time**`listener` function for the event named `eventName` to the_beginning_ of the listeners array. The next time `eventName` is triggered, this\n     * listener is removed, and then invoked.\n     *\n     * Returns a reference to the `EventEmitter`, so that calls can be chained.\n     *\n     * @since 1.1.0\n     */\n    prependOnceListener(eventName, listener) {\n        const wrapper = (...args) => {\n            this.removeListener(eventName, wrapper);\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n            listener(...args);\n        };\n        return this.prependListener(eventName, wrapper);\n    }\n}\n/**\n * @since 1.1.0\n */\nclass Child {\n    constructor(pid) {\n        this.pid = pid;\n    }\n    /**\n     * Writes `data` to the `stdin`.\n     *\n     * @param data The message to write, either a string or a byte array.\n     * @example\n     * ```typescript\n     * import { Command } from '@tauri-apps/api/shell';\n     * const command = new Command('node');\n     * const child = await command.spawn();\n     * await child.write('message');\n     * await child.write([0, 1, 2, 3, 4, 5]);\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async write(data) {\n        return invokeTauriCommand({\n            __tauriModule: 'Shell',\n            message: {\n                cmd: 'stdinWrite',\n                pid: this.pid,\n                // correctly serialize Uint8Arrays\n                buffer: typeof data === 'string' ? data : Array.from(data)\n            }\n        });\n    }\n    /**\n     * Kills the child process.\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async kill() {\n        return invokeTauriCommand({\n            __tauriModule: 'Shell',\n            message: {\n                cmd: 'killChild',\n                pid: this.pid\n            }\n        });\n    }\n}\n/**\n * The entry point for spawning child processes.\n * It emits the `close` and `error` events.\n * @example\n * ```typescript\n * import { Command } from '@tauri-apps/api/shell';\n * const command = new Command('node');\n * command.on('close', data => {\n *   console.log(`command finished with code ${data.code} and signal ${data.signal}`)\n * });\n * command.on('error', error => console.error(`command error: \"${error}\"`));\n * command.stdout.on('data', line => console.log(`command stdout: \"${line}\"`));\n * command.stderr.on('data', line => console.log(`command stderr: \"${line}\"`));\n *\n * const child = await command.spawn();\n * console.log('pid:', child.pid);\n * ```\n *\n * @since 1.1.0\n *\n */\nclass Command extends EventEmitter {\n    /**\n     * Creates a new `Command` instance.\n     *\n     * @param program The program name to execute.\n     * It must be configured on `tauri.conf.json > tauri > allowlist > shell > scope`.\n     * @param args Program arguments.\n     * @param options Spawn options.\n     */\n    constructor(program, args = [], options) {\n        super();\n        /** Event emitter for the `stdout`. Emits the `data` event. */\n        this.stdout = new EventEmitter();\n        /** Event emitter for the `stderr`. Emits the `data` event. */\n        this.stderr = new EventEmitter();\n        this.program = program;\n        this.args = typeof args === 'string' ? [args] : args;\n        this.options = options !== null && options !== void 0 ? options : {};\n    }\n    /**\n     * Creates a command to execute the given sidecar program.\n     * @example\n     * ```typescript\n     * import { Command } from '@tauri-apps/api/shell';\n     * const command = Command.sidecar('my-sidecar');\n     * const output = await command.execute();\n     * ```\n     *\n     * @param program The program to execute.\n     * It must be configured on `tauri.conf.json > tauri > allowlist > shell > scope`.\n     */\n    static sidecar(program, args = [], options) {\n        const instance = new Command(program, args, options);\n        instance.options.sidecar = true;\n        return instance;\n    }\n    /**\n     * Executes the command as a child process, returning a handle to it.\n     *\n     * @returns A promise resolving to the child process handle.\n     */\n    async spawn() {\n        const program = this.program;\n        const args = this.args;\n        const options = this.options;\n        if (typeof args === 'object') {\n            Object.freeze(args);\n        }\n        const onEvent = (event) => {\n            switch (event.event) {\n                case 'Error':\n                    this.emit('error', event.payload);\n                    break;\n                case 'Terminated':\n                    this.emit('close', event.payload);\n                    break;\n                case 'Stdout':\n                    this.stdout.emit('data', event.payload);\n                    break;\n                case 'Stderr':\n                    this.stderr.emit('data', event.payload);\n                    break;\n            }\n        };\n        return invokeTauriCommand({\n            __tauriModule: 'Shell',\n            message: {\n                cmd: 'execute',\n                program,\n                args,\n                options,\n                onEventFn: transformCallback(onEvent)\n            }\n        }).then((pid) => new Child(pid));\n    }\n    /**\n     * Executes the command as a child process, waiting for it to finish and collecting all of its output.\n     * @example\n     * ```typescript\n     * import { Command } from '@tauri-apps/api/shell';\n     * const output = await new Command('echo', 'message').execute();\n     * assert(output.code === 0);\n     * assert(output.signal === null);\n     * assert(output.stdout === 'message');\n     * assert(output.stderr === '');\n     * ```\n     *\n     * @returns A promise resolving to the child process output.\n     */\n    async execute() {\n        const program = this.program;\n        const args = this.args;\n        const options = this.options;\n        if (typeof args === 'object') {\n            Object.freeze(args);\n        }\n        return invokeTauriCommand({\n            __tauriModule: 'Shell',\n            message: {\n                cmd: 'executeAndReturn',\n                program,\n                args,\n                options\n            }\n        });\n    }\n}\n/**\n * Opens a path or URL with the system's default app,\n * or the one specified with `openWith`.\n *\n * The `openWith` value must be one of `firefox`, `google chrome`, `chromium` `safari`,\n * `open`, `start`, `xdg-open`, `gio`, `gnome-open`, `kde-open` or `wslview`.\n *\n * @example\n * ```typescript\n * import { open } from '@tauri-apps/api/shell';\n * // opens the given URL on the default browser:\n * await open('https://github.com/tauri-apps/tauri');\n * // opens the given URL using `firefox`:\n * await open('https://github.com/tauri-apps/tauri', 'firefox');\n * // opens a file using the default program:\n * await open('/path/to/file');\n * ```\n *\n * @param path The path or URL to open.\n * This value is matched against the string regex defined on `tauri.conf.json > tauri > allowlist > shell > open`,\n * which defaults to `^((mailto:\\w+)|(tel:\\w+)|(https?://\\w+)).+`.\n * @param openWith The app to open the file or URL with.\n * Defaults to the system default application for the specified path type.\n *\n * @since 1.0.0\n */\nasync function open(path, openWith) {\n    return invokeTauriCommand({\n        __tauriModule: 'Shell',\n        message: {\n            cmd: 'open',\n            path,\n            with: openWith\n        }\n    });\n}\n\nexport { Child, Command, EventEmitter, open };\n", "import { listen, TauriEvent, emit, once } from './event.js';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Customize the auto updater flow.\n *\n * This package is also accessible with `window.__TAURI__.updater` when [`build.withGlobalTauri`](https://tauri.app/v1/api/config/#buildconfig.withglobaltauri) in `tauri.conf.json` is set to `true`.\n * @module\n */\n/**\n * Listen to an updater event.\n * @example\n * ```typescript\n * import { onUpdaterEvent } from \"@tauri-apps/api/updater\";\n * const unlisten = await onUpdaterEvent(({ error, status }) => {\n *  console.log('Updater event', error, status);\n * });\n *\n * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n * unlisten();\n * ```\n *\n * @returns A promise resolving to a function to unlisten to the event.\n * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n *\n * @since 1.0.2\n */\nasync function onUpdaterEvent(handler) {\n    return listen(TauriEvent.STATUS_UPDATE, (data) => {\n        handler(data === null || data === void 0 ? void 0 : data.payload);\n    });\n}\n/**\n * Install the update if there's one available.\n * @example\n * ```typescript\n * import { checkUpdate, installUpdate } from '@tauri-apps/api/updater';\n * const update = await checkUpdate();\n * if (update.shouldUpdate) {\n *   console.log(`Installing update ${update.manifest?.version}, ${update.manifest?.date}, ${update.manifest.body}`);\n *   await installUpdate();\n * }\n * ```\n *\n * @return A promise indicating the success or failure of the operation.\n *\n * @since 1.0.0\n */\nasync function installUpdate() {\n    let unlistenerFn;\n    function cleanListener() {\n        if (unlistenerFn) {\n            unlistenerFn();\n        }\n        unlistenerFn = undefined;\n    }\n    return new Promise((resolve, reject) => {\n        function onStatusChange(statusResult) {\n            if (statusResult.error) {\n                cleanListener();\n                reject(statusResult.error);\n                return;\n            }\n            // install complete\n            if (statusResult.status === 'DONE') {\n                cleanListener();\n                resolve();\n            }\n        }\n        // listen status change\n        onUpdaterEvent(onStatusChange)\n            .then((fn) => {\n            unlistenerFn = fn;\n        })\n            .catch((e) => {\n            cleanListener();\n            // dispatch the error to our checkUpdate\n            throw e;\n        });\n        // start the process we dont require much security as it's\n        // handled by rust\n        emit(TauriEvent.INSTALL_UPDATE).catch((e) => {\n            cleanListener();\n            // dispatch the error to our checkUpdate\n            throw e;\n        });\n    });\n}\n/**\n * Checks if an update is available.\n * @example\n * ```typescript\n * import { checkUpdate } from '@tauri-apps/api/updater';\n * const update = await checkUpdate();\n * // now run installUpdate() if needed\n * ```\n *\n * @return Promise resolving to the update status.\n *\n * @since 1.0.0\n */\nasync function checkUpdate() {\n    let unlistenerFn;\n    function cleanListener() {\n        if (unlistenerFn) {\n            unlistenerFn();\n        }\n        unlistenerFn = undefined;\n    }\n    return new Promise((resolve, reject) => {\n        function onUpdateAvailable(manifest) {\n            cleanListener();\n            resolve({\n                manifest,\n                shouldUpdate: true\n            });\n        }\n        function onStatusChange(statusResult) {\n            if (statusResult.error) {\n                cleanListener();\n                reject(statusResult.error);\n                return;\n            }\n            if (statusResult.status === 'UPTODATE') {\n                cleanListener();\n                resolve({\n                    shouldUpdate: false\n                });\n            }\n        }\n        // wait to receive the latest update\n        once(TauriEvent.UPDATE_AVAILABLE, (data) => {\n            onUpdateAvailable(data === null || data === void 0 ? void 0 : data.payload);\n        }).catch((e) => {\n            cleanListener();\n            // dispatch the error to our checkUpdate\n            throw e;\n        });\n        // listen status change\n        onUpdaterEvent(onStatusChange)\n            .then((fn) => {\n            unlistenerFn = fn;\n        })\n            .catch((e) => {\n            cleanListener();\n            // dispatch the error to our checkUpdate\n            throw e;\n        });\n        // start the process\n        emit(TauriEvent.CHECK_UPDATE).catch((e) => {\n            cleanListener();\n            // dispatch the error to our checkUpdate\n            throw e;\n        });\n    });\n}\n\nexport { checkUpdate, installUpdate, onUpdaterEvent };\n", "import { isWindows } from './helpers/os-check.js';\nimport { invokeTauriCommand } from './helpers/tauri.js';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Provides operating system-related utility methods and properties.\n *\n * This package is also accessible with `window.__TAURI__.os` when [`build.withGlobalTauri`](https://tauri.app/v1/api/config/#buildconfig.withglobaltauri) in `tauri.conf.json` is set to `true`.\n *\n * The APIs must be added to [`tauri.allowlist.os`](https://tauri.app/v1/api/config/#allowlistconfig.os) in `tauri.conf.json`:\n * ```json\n * {\n *   \"tauri\": {\n *     \"allowlist\": {\n *       \"os\": {\n *         \"all\": true, // enable all Os APIs\n *       }\n *     }\n *   }\n * }\n * ```\n * It is recommended to allowlist only the APIs you use for optimal bundle size and security.\n * @module\n */\n/**\n * The operating system-specific end-of-line marker.\n * - `\\n` on POSIX\n * - `\\r\\n` on Windows\n *\n * @since 1.0.0\n * */\nconst EOL = isWindows() ? '\\r\\n' : '\\n';\n/**\n * Returns a string identifying the operating system platform.\n * The value is set at compile time. Possible values are `'linux'`, `'darwin'`, `'ios'`, `'freebsd'`, `'dragonfly'`, `'netbsd'`, `'openbsd'`, `'solaris'`, `'android'`, `'win32'`\n * @example\n * ```typescript\n * import { platform } from '@tauri-apps/api/os';\n * const platformName = await platform();\n * ```\n *\n * @since 1.0.0\n *\n */\nasync function platform() {\n    return invokeTauriCommand({\n        __tauriModule: 'Os',\n        message: {\n            cmd: 'platform'\n        }\n    });\n}\n/**\n * Returns a string identifying the kernel version.\n * @example\n * ```typescript\n * import { version } from '@tauri-apps/api/os';\n * const osVersion = await version();\n * ```\n *\n * @since 1.0.0\n */\nasync function version() {\n    return invokeTauriCommand({\n        __tauriModule: 'Os',\n        message: {\n            cmd: 'version'\n        }\n    });\n}\n/**\n * Returns `'Linux'` on Linux, `'Darwin'` on macOS, and `'Windows_NT'` on Windows.\n * @example\n * ```typescript\n * import { type } from '@tauri-apps/api/os';\n * const osType = await type();\n * ```\n *\n * @since 1.0.0\n */\nasync function type() {\n    return invokeTauriCommand({\n        __tauriModule: 'Os',\n        message: {\n            cmd: 'osType'\n        }\n    });\n}\n/**\n * Returns the operating system CPU architecture for which the tauri app was compiled.\n * Possible values are `'x86'`, `'x86_64'`, `'arm'`, `'aarch64'`, `'mips'`, `'mips64'`, `'powerpc'`, `'powerpc64'`, `'riscv64'`, `'s390x'`, `'sparc64'`.\n * @example\n * ```typescript\n * import { arch } from '@tauri-apps/api/os';\n * const archName = await arch();\n * ```\n *\n * @since 1.0.0\n */\nasync function arch() {\n    return invokeTauriCommand({\n        __tauriModule: 'Os',\n        message: {\n            cmd: 'arch'\n        }\n    });\n}\n/**\n * Returns the operating system's default directory for temporary files as a string.\n * @example\n * ```typescript\n * import { tempdir } from '@tauri-apps/api/os';\n * const tempdirPath = await tempdir();\n * ```\n *\n * @since 1.0.0\n */\nasync function tempdir() {\n    return invokeTauriCommand({\n        __tauriModule: 'Os',\n        message: {\n            cmd: 'tempdir'\n        }\n    });\n}\n/**\n * Returns a String with a `BCP-47` language tag inside. If the locale couldn’t be obtained, `null` is returned instead.\n * @example\n * ```typescript\n * import { locale } from '@tauri-apps/api/os';\n * const locale = await locale();\n * if (locale) {\n *    // use the locale string here\n * }\n * ```\n *\n * @since 1.4.0\n */\nasync function locale() {\n    return invokeTauriCommand({\n        __tauriModule: 'Os',\n        message: {\n            cmd: 'locale'\n        }\n    });\n}\n\nexport { EOL, arch, locale, platform, tempdir, type, version };\n", "import * as app from './app.js';\nexport { app };\nimport * as cli from './cli.js';\nexport { cli };\nimport * as clipboard from './clipboard.js';\nexport { clipboard };\nimport * as dialog from './dialog.js';\nexport { dialog };\nimport * as event from './event.js';\nexport { event };\nimport * as fs from './fs.js';\nexport { fs };\nimport * as globalShortcut from './globalShortcut.js';\nexport { globalShortcut };\nimport * as http from './http.js';\nexport { http };\nimport * as notification from './notification.js';\nexport { notification };\nimport * as path from './path.js';\nexport { path };\nimport * as process from './process.js';\nexport { process };\nimport * as shell from './shell.js';\nexport { shell };\nimport { invoke as invoke$1 } from './tauri.js';\nimport * as tauri from './tauri.js';\nexport { tauri };\nimport * as updater from './updater.js';\nexport { updater };\nimport * as window from './window.js';\nexport { window };\nimport * as os from './os.js';\nexport { os };\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * The Tauri API allows you to interface with the backend layer.\n *\n * This module exposes all other modules as an object where the key is the module name, and the value is the module exports.\n * @example\n * ```typescript\n * import { app, dialog, event, fs, globalShortcut } from '@tauri-apps/api'\n * ```\n * @module\n */\n/** @ignore */\nconst invoke = invoke$1;\n\nexport { invoke };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsCA,eAAe,aAAa;AACxB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,IACT;AAAA,EACJ,CAAC;AACL;AAWA,eAAe,UAAU;AACrB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,IACT;AAAA,EACJ,CAAC;AACL;AAYA,eAAe,kBAAkB;AAC7B,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,IACT;AAAA,EACJ,CAAC;AACL;AAYA,eAAe,OAAO;AAClB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,IACT;AAAA,EACJ,CAAC;AACL;AAYA,eAAe,OAAO;AAClB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,IACT;AAAA,EACJ,CAAC;AACL;;;ACxHA;AAAA;AAAA;AAAA;AA+BA,eAAe,aAAa;AACxB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,IACT;AAAA,EACJ,CAAC;AACL;;;ACtCA;AAAA;AAAA;AAAA;AAAA;AAyCA,eAAe,UAAU,MAAM;AAC3B,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACV;AAAA,EACJ,CAAC;AACL;AAUA,eAAe,WAAW;AACtB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA;AAAA;AAAA,MAGL,MAAM;AAAA,IACV;AAAA,EACJ,CAAC;AACL;;;ACrEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkFA,eAAe,KAAK,UAAU,CAAC,GAAG;AAC9B,MAAI,OAAO,YAAY,UAAU;AAC7B,WAAO,OAAO,OAAO;AAAA,EACzB;AACA,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAyBA,eAAe,KAAK,UAAU,CAAC,GAAG;AAC9B,MAAI,OAAO,YAAY,UAAU;AAC7B,WAAO,OAAO,OAAO;AAAA,EACzB;AACA,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAkBA,eAAe,QAAQA,UAAS,SAAS;AACrC,MAAI,IAAI;AACR,QAAM,OAAO,OAAO,YAAY,WAAW,EAAE,OAAO,QAAQ,IAAI;AAChE,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,SAASA,SAAQ,SAAS;AAAA,MAC1B,QAAQ,KAAK,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS;AAAA,MACtH,MAAM,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK;AAAA,MACvD,cAAc,KAAK,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS;AAAA,IAClI;AAAA,EACJ,CAAC;AACL;AAiBA,eAAe,IAAIA,UAAS,SAAS;AACjC,MAAI,IAAI,IAAI,IAAI,IAAI;AACpB,QAAM,OAAO,OAAO,YAAY,WAAW,EAAE,OAAO,QAAQ,IAAI;AAChE,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,SAASA,SAAQ,SAAS;AAAA,MAC1B,QAAQ,KAAK,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS;AAAA,MACtH,MAAM,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK;AAAA,MACvD,cAAc;AAAA,SACT,MAAM,KAAK,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,SAC1J,MAAM,KAAK,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,MACnK;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAiBA,eAAe,QAAQA,UAAS,SAAS;AACrC,MAAI,IAAI,IAAI,IAAI,IAAI;AACpB,QAAM,OAAO,OAAO,YAAY,WAAW,EAAE,OAAO,QAAQ,IAAI;AAChE,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,SAASA,SAAQ,SAAS;AAAA,MAC1B,QAAQ,KAAK,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS;AAAA,MACtH,MAAM,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK;AAAA,MACvD,cAAc;AAAA,SACT,MAAM,KAAK,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,SAC1J,MAAM,KAAK,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,MACnK;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;;;AClOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6EA,IAAI;AAAA,CACH,SAAUC,gBAAe;AACtB,EAAAA,eAAcA,eAAc,OAAO,IAAI,CAAC,IAAI;AAC5C,EAAAA,eAAcA,eAAc,OAAO,IAAI,CAAC,IAAI;AAC5C,EAAAA,eAAcA,eAAc,QAAQ,IAAI,CAAC,IAAI;AAC7C,EAAAA,eAAcA,eAAc,MAAM,IAAI,CAAC,IAAI;AAC3C,EAAAA,eAAcA,eAAc,WAAW,IAAI,CAAC,IAAI;AAChD,EAAAA,eAAcA,eAAc,SAAS,IAAI,CAAC,IAAI;AAC9C,EAAAA,eAAcA,eAAc,UAAU,IAAI,CAAC,IAAI;AAC/C,EAAAA,eAAcA,eAAc,UAAU,IAAI,CAAC,IAAI;AAC/C,EAAAA,eAAcA,eAAc,YAAY,IAAI,CAAC,IAAI;AACjD,EAAAA,eAAcA,eAAc,MAAM,IAAI,EAAE,IAAI;AAC5C,EAAAA,eAAcA,eAAc,MAAM,IAAI,EAAE,IAAI;AAC5C,EAAAA,eAAcA,eAAc,SAAS,IAAI,EAAE,IAAI;AAC/C,EAAAA,eAAcA,eAAc,QAAQ,IAAI,EAAE,IAAI;AAC9C,EAAAA,eAAcA,eAAc,SAAS,IAAI,EAAE,IAAI;AAC/C,EAAAA,eAAcA,eAAc,UAAU,IAAI,EAAE,IAAI;AAChD,EAAAA,eAAcA,eAAc,OAAO,IAAI,EAAE,IAAI;AAC7C,EAAAA,eAAcA,eAAc,UAAU,IAAI,EAAE,IAAI;AAChD,EAAAA,eAAcA,eAAc,KAAK,IAAI,EAAE,IAAI;AAC3C,EAAAA,eAAcA,eAAc,KAAK,IAAI,EAAE,IAAI;AAC3C,EAAAA,eAAcA,eAAc,MAAM,IAAI,EAAE,IAAI;AAC5C,EAAAA,eAAcA,eAAc,WAAW,IAAI,EAAE,IAAI;AACjD,EAAAA,eAAcA,eAAc,SAAS,IAAI,EAAE,IAAI;AAC/C,EAAAA,eAAcA,eAAc,cAAc,IAAI,EAAE,IAAI;AACpD,EAAAA,eAAcA,eAAc,UAAU,IAAI,EAAE,IAAI;AAChD,EAAAA,eAAcA,eAAc,QAAQ,IAAI,EAAE,IAAI;AAClD,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAYxC,eAAe,aAAa,UAAU,UAAU,CAAC,GAAG;AAChD,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAYA,eAAe,eAAe,UAAU,UAAU,CAAC,GAAG;AAClD,QAAM,MAAM,MAAM,mBAAmB;AAAA,IACjC,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,SAAO,WAAW,KAAK,GAAG;AAC9B;AAQA,eAAe,cAAc,MAAM,UAAU,SAAS;AAClD,MAAI,OAAO,YAAY,UAAU;AAC7B,WAAO,OAAO,OAAO;AAAA,EACzB;AACA,MAAI,OAAO,SAAS,UAAU;AAC1B,WAAO,OAAO,IAAI;AAAA,EACtB;AACA,QAAM,OAAO,EAAE,MAAM,IAAI,UAAU,GAAG;AACtC,MAAI,cAAc;AAClB,MAAI,OAAO,SAAS,UAAU;AAC1B,SAAK,OAAO;AAAA,EAChB,OACK;AACD,SAAK,OAAO,KAAK;AACjB,SAAK,WAAW,KAAK;AAAA,EACzB;AACA,MAAI,OAAO,aAAa,UAAU;AAC9B,SAAK,WAAW,aAAa,QAAQ,aAAa,SAAS,WAAW;AAAA,EAC1E,OACK;AACD,kBAAc;AAAA,EAClB;AACA,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM,KAAK;AAAA,MACX,UAAU,MAAM,KAAK,IAAI,YAAY,EAAE,OAAO,KAAK,QAAQ,CAAC;AAAA,MAC5D,SAAS;AAAA,IACb;AAAA,EACJ,CAAC;AACL;AAQA,eAAe,gBAAgB,MAAM,UAAU,SAAS;AACpD,MAAI,OAAO,YAAY,UAAU;AAC7B,WAAO,OAAO,OAAO;AAAA,EACzB;AACA,MAAI,OAAO,SAAS,UAAU;AAC1B,WAAO,OAAO,IAAI;AAAA,EACtB;AACA,QAAM,OAAO,EAAE,MAAM,IAAI,UAAU,CAAC,EAAE;AACtC,MAAI,cAAc;AAClB,MAAI,OAAO,SAAS,UAAU;AAC1B,SAAK,OAAO;AAAA,EAChB,OACK;AACD,SAAK,OAAO,KAAK;AACjB,SAAK,WAAW,KAAK;AAAA,EACzB;AACA,MAAI,YAAY,SAAS,UAAU;AAC/B,kBAAc;AAAA,EAClB,WACS,OAAO,SAAS,UAAU;AAE/B,SAAK,WAAW,aAAa,QAAQ,aAAa,SAAS,WAAW,CAAC;AAAA,EAC3E;AACA,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM,KAAK;AAAA,MACX,UAAU,MAAM,KAAK,KAAK,oBAAoB,cACxC,IAAI,WAAW,KAAK,QAAQ,IAC5B,KAAK,QAAQ;AAAA,MACnB,SAAS;AAAA,IACb;AAAA,EACJ,CAAC;AACL;AAqBA,eAAe,QAAQ,KAAK,UAAU,CAAC,GAAG;AACtC,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAgBA,eAAe,UAAU,KAAK,UAAU,CAAC,GAAG;AACxC,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAeA,eAAe,UAAU,KAAK,UAAU,CAAC,GAAG;AACxC,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAcA,eAAe,SAAS,QAAQ,aAAa,UAAU,CAAC,GAAG;AACvD,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAcA,eAAe,WAAW,MAAM,UAAU,CAAC,GAAG;AAC1C,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAcA,eAAe,WAAW,SAAS,SAAS,UAAU,CAAC,GAAG;AACtD,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAYA,eAAe,OAAO,MAAM,UAAU,CAAC,GAAG;AACtC,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;;;AC/YA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyCA,eAAe,SAAS,UAAU,SAAS;AACvC,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,SAAS,kBAAkB,OAAO;AAAA,IACtC;AAAA,EACJ,CAAC;AACL;AAgBA,eAAe,YAAY,WAAW,SAAS;AAC3C,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,SAAS,kBAAkB,OAAO;AAAA,IACtC;AAAA,EACJ,CAAC;AACL;AAaA,eAAe,aAAa,UAAU;AAClC,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAaA,eAAe,WAAW,UAAU;AAChC,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAWA,eAAe,gBAAgB;AAC3B,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,IACT;AAAA,EACJ,CAAC;AACL;;;ACvIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgDA,IAAI;AAAA,CACH,SAAUC,eAAc;AACrB,EAAAA,cAAaA,cAAa,MAAM,IAAI,CAAC,IAAI;AACzC,EAAAA,cAAaA,cAAa,MAAM,IAAI,CAAC,IAAI;AACzC,EAAAA,cAAaA,cAAa,QAAQ,IAAI,CAAC,IAAI;AAC/C,GAAG,iBAAiB,eAAe,CAAC,EAAE;AACtC,eAAe,SAAS,MAAM;AAC1B,QAAM,OAAO,CAAC;AACd,QAAM,SAAS,OAAO,KAAK,MAAM;AAC7B,QAAI,MAAM,MAAM;AACZ,UAAI;AACJ,UAAI,OAAO,MAAM,UAAU;AACvB,YAAI;AAAA,MACR,WACS,aAAa,cAAc,MAAM,QAAQ,CAAC,GAAG;AAClD,YAAI,MAAM,KAAK,CAAC;AAAA,MACpB,WACS,aAAa,MAAM;AACxB,YAAI;AAAA,UACA,MAAM,MAAM,KAAK,IAAI,WAAW,MAAM,EAAE,YAAY,CAAC,CAAC;AAAA,UACtD,MAAM,EAAE;AAAA,UACR,UAAU,EAAE;AAAA,QAChB;AAAA,MACJ,WACS,OAAO,EAAE,SAAS,UAAU;AACjC,YAAI,EAAE,MAAM,EAAE,MAAM,MAAM,EAAE,MAAM,UAAU,EAAE,SAAS;AAAA,MAC3D,OACK;AACD,YAAI,EAAE,MAAM,MAAM,KAAK,EAAE,IAAI,GAAG,MAAM,EAAE,MAAM,UAAU,EAAE,SAAS;AAAA,MACvE;AACA,WAAK,OAAO,GAAG,CAAC,IAAI;AAAA,IACxB;AAAA,EACJ;AACA,MAAI,gBAAgB,UAAU;AAC1B,eAAW,CAAC,KAAK,KAAK,KAAK,MAAM;AAC7B,YAAM,OAAO,KAAK,KAAK;AAAA,IAC3B;AAAA,EACJ,OACK;AACD,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,IAAI,GAAG;AAC7C,YAAM,OAAO,KAAK,KAAK;AAAA,IAC3B;AAAA,EACJ;AACA,SAAO;AACX;AAMA,IAAM,OAAN,MAAM,MAAK;AAAA;AAAA,EAEP,YAAYC,OAAM,SAAS;AACvB,SAAK,OAAOA;AACZ,SAAK,UAAU;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiCA,OAAO,KAAK,MAAM;AACd,WAAO,IAAI,MAAK,QAAQ,IAAI;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,OAAO,KAAK,MAAM;AACd,WAAO,IAAI,MAAK,QAAQ,IAAI;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,OAAO,KAAK,OAAO;AACf,WAAO,IAAI,MAAK,QAAQ,KAAK;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,OAAO,MAAM,OAAO;AAEhB,WAAO,IAAI,MAAK,SAAS,MAAM,KAAK,iBAAiB,cAAc,IAAI,WAAW,KAAK,IAAI,KAAK,CAAC;AAAA,EACrG;AACJ;AAMA,IAAM,WAAN,MAAe;AAAA;AAAA,EAEX,YAAY,UAAU;AAClB,SAAK,MAAM,SAAS;AACpB,SAAK,SAAS,SAAS;AACvB,SAAK,KAAK,KAAK,UAAU,OAAO,KAAK,SAAS;AAC9C,SAAK,UAAU,SAAS;AACxB,SAAK,aAAa,SAAS;AAC3B,SAAK,OAAO,SAAS;AAAA,EACzB;AACJ;AAIA,IAAM,SAAN,MAAa;AAAA;AAAA,EAET,YAAY,IAAI;AACZ,SAAK,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,OAAO;AACT,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,QAAQ,KAAK;AAAA,MACjB;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,QAAQ,SAAS;AACnB,QAAI;AACJ,UAAM,eAAe,CAAC,QAAQ,gBAAgB,QAAQ,iBAAiB,aAAa;AACpF,QAAI,cAAc;AACd,cAAQ,eAAe,aAAa;AAAA,IACxC;AACA,UAAM,KAAK,QAAQ,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,QAAQ;AAC/E,cAAQ,KAAK,UAAU,MAAM,SAAS,QAAQ,KAAK,OAAO;AAAA,IAC9D;AACA,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,QAAQ,KAAK;AAAA,QACb;AAAA,MACJ;AAAA,IACJ,CAAC,EAAE,KAAK,CAAC,QAAQ;AACb,YAAM,WAAW,IAAI,SAAS,GAAG;AACjC,UAAI,cAAc;AAEd,YAAI;AACA,mBAAS,OAAO,KAAK,MAAM,SAAS,IAAI;AAAA,QAC5C,SACO,GAAG;AACN,cAAI,SAAS,MAAM,SAAS,SAAS,IAAI;AACrC,qBAAS,OAAO,CAAC;AAAA,UACrB,WACS,SAAS,IAAI;AAClB,kBAAM,MAAM,8BAA8B,SAAS,IAAI,eAAe,CAAC;AAAA,sJACuD;AAAA,UAClI;AAAA,QACJ;AAEA,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,MAAM,IAAI,KAAK,SAAS;AACpB,WAAO,KAAK,QAAQ;AAAA,MAChB,QAAQ;AAAA,MACR;AAAA,MACA,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,MAAM,KAAK,KAAK,MAAM,SAAS;AAC3B,WAAO,KAAK,QAAQ;AAAA,MAChB,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,MAAM,IAAI,KAAK,MAAM,SAAS;AAC1B,WAAO,KAAK,QAAQ;AAAA,MAChB,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,MAAM,KAAK,SAAS;AACtB,WAAO,KAAK,QAAQ;AAAA,MAChB,QAAQ;AAAA,MACR;AAAA,MACA,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,OAAO,KAAK,SAAS;AACvB,WAAO,KAAK,QAAQ;AAAA,MAChB,QAAQ;AAAA,MACR;AAAA,MACA,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AACJ;AAeA,eAAe,UAAU,SAAS;AAC9B,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,IACJ;AAAA,EACJ,CAAC,EAAE,KAAK,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;AAClC;AAEA,IAAI,gBAAgB;AAYpB,eAAe,MAAM,KAAK,SAAS;AAC/B,MAAI;AACJ,MAAI,kBAAkB,MAAM;AACxB,oBAAgB,MAAM,UAAU;AAAA,EACpC;AACA,SAAO,cAAc,QAAQ;AAAA,IACzB;AAAA,IACA,SAAS,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,QAAQ,OAAO,SAAS,KAAK;AAAA,IACjH,GAAG;AAAA,EACP,CAAC;AACL;;;AC/aA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoCA,eAAe,sBAAsB;AACjC,MAAI,OAAO,aAAa,eAAe,WAAW;AAC9C,WAAO,QAAQ,QAAQ,OAAO,aAAa,eAAe,SAAS;AAAA,EACvE;AACA,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,IACT;AAAA,EACJ,CAAC;AACL;AAiBA,eAAe,oBAAoB;AAC/B,SAAO,OAAO,aAAa,kBAAkB;AACjD;AAmBA,SAAS,iBAAiB,SAAS;AAC/B,MAAI,OAAO,YAAY,UAAU;AAE7B,QAAI,OAAO,aAAa,OAAO;AAAA,EACnC,OACK;AAED,QAAI,OAAO,aAAa,QAAQ,OAAO,OAAO;AAAA,EAClD;AACJ;;;AC7FA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACIA,SAAS,YAAY;AACjB,SAAO,UAAU,WAAW,SAAS,KAAK;AAC9C;;;AD2BA,eAAe,SAAS;AACpB,SAAO,aAAa;AACxB;AAYA,eAAe,eAAe;AAC1B,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,WAAW,cAAc;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAYA,eAAe,aAAa;AACxB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,WAAW,cAAc;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAYA,eAAe,kBAAkB;AAC7B,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,WAAW,cAAc;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAYA,eAAe,cAAc;AACzB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,WAAW,cAAc;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAiBA,eAAe,WAAW;AACtB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,WAAW,cAAc;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAiBA,eAAe,WAAW;AACtB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,WAAW,cAAc;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAiBA,eAAe,YAAY;AACvB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,WAAW,cAAc;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAiBA,eAAe,UAAU;AACrB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,WAAW,cAAc;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAiBA,eAAe,aAAa;AACxB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,WAAW,cAAc;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAiBA,eAAe,cAAc;AACzB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,WAAW,cAAc;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAiBA,eAAe,cAAc;AACzB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,WAAW,cAAc;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAiBA,eAAe,gBAAgB;AAC3B,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,WAAW,cAAc;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAiBA,eAAe,UAAU;AACrB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,WAAW,cAAc;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAiBA,eAAe,UAAU;AACrB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,WAAW,cAAc;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAiBA,eAAe,eAAe;AAC1B,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,WAAW,cAAc;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAiBA,eAAe,aAAa;AACxB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,WAAW,cAAc;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAiBA,eAAe,YAAY;AACvB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,WAAW,cAAc;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAYA,eAAe,cAAc;AACzB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,WAAW,cAAc;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAeA,eAAe,gBAAgB,cAAc;AACzC,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,WAAW,cAAc;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAiBA,eAAe,aAAa;AACxB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,WAAW,cAAc;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAiBA,eAAe,cAAc;AACzB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,WAAW,cAAc;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAiBA,eAAe,WAAW;AACtB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,WAAW,cAAc;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAOA,eAAe,SAAS;AACpB,SAAO,UAAU;AACrB;AAiBA,eAAe,YAAY;AACvB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,WAAW,cAAc;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAQA,IAAM,MAAM,UAAU,IAAI,OAAO;AAQjC,IAAM,YAAY,UAAU,IAAI,MAAM;AAYtC,eAAe,WAAW,OAAO;AAC7B,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAYA,eAAe,UAAU,MAAM;AAC3B,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAYA,eAAe,QAAQ,OAAO;AAC1B,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAYA,eAAe,QAAQ,MAAM;AACzB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAYA,eAAe,QAAQ,MAAM;AACzB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAcA,eAAe,SAAS,MAAM,KAAK;AAC/B,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAWA,eAAe,WAAW,MAAM;AAC5B,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;;;AErwBA;AAAA;AAAA;AAAA;AAAA;AAwBA,eAAe,KAAK,WAAW,GAAG;AAC9B,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAaA,eAAe,WAAW;AACtB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,IACT;AAAA,EACJ,CAAC;AACL;;;ACpDA;AAAA;AAAA;AAAA;AAAA;AAAA,cAAAC;AAAA;AAmFA,IAAM,eAAN,MAAmB;AAAA,EACf,cAAc;AAGV,SAAK,iBAAiB,uBAAO,OAAO,IAAI;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,WAAW,UAAU;AAC7B,WAAO,KAAK,GAAG,WAAW,QAAQ;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,WAAW,UAAU;AAChC,WAAO,KAAK,IAAI,WAAW,QAAQ;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,GAAG,WAAW,UAAU;AACpB,QAAI,aAAa,KAAK,gBAAgB;AAElC,WAAK,eAAe,SAAS,EAAE,KAAK,QAAQ;AAAA,IAChD,OACK;AAED,WAAK,eAAe,SAAS,IAAI,CAAC,QAAQ;AAAA,IAC9C;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,KAAK,WAAW,UAAU;AACtB,UAAM,UAAU,IAAI,SAAS;AACzB,WAAK,eAAe,WAAW,OAAO;AAEtC,eAAS,GAAG,IAAI;AAAA,IACpB;AACA,WAAO,KAAK,YAAY,WAAW,OAAO;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,WAAW,UAAU;AACrB,QAAI,aAAa,KAAK,gBAAgB;AAElC,WAAK,eAAe,SAAS,IAAI,KAAK,eAAe,SAAS,EAAE,OAAO,CAAC,MAAM,MAAM,QAAQ;AAAA,IAChG;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,mBAAmB,OAAO;AACtB,QAAI,OAAO;AAEP,aAAO,KAAK,eAAe,KAAK;AAAA,IACpC,OACK;AAED,WAAK,iBAAiB,uBAAO,OAAO,IAAI;AAAA,IAC5C;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,cAAc,MAAM;AACrB,QAAI,aAAa,KAAK,gBAAgB;AAElC,YAAM,YAAY,KAAK,eAAe,SAAS;AAE/C,iBAAW,YAAY;AACnB,iBAAS,GAAG,IAAI;AACpB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,WAAW;AACrB,QAAI,aAAa,KAAK;AAElB,aAAO,KAAK,eAAe,SAAS,EAAE;AAC1C,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,gBAAgB,WAAW,UAAU;AACjC,QAAI,aAAa,KAAK,gBAAgB;AAElC,WAAK,eAAe,SAAS,EAAE,QAAQ,QAAQ;AAAA,IACnD,OACK;AAED,WAAK,eAAe,SAAS,IAAI,CAAC,QAAQ;AAAA,IAC9C;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,oBAAoB,WAAW,UAAU;AACrC,UAAM,UAAU,IAAI,SAAS;AACzB,WAAK,eAAe,WAAW,OAAO;AAEtC,eAAS,GAAG,IAAI;AAAA,IACpB;AACA,WAAO,KAAK,gBAAgB,WAAW,OAAO;AAAA,EAClD;AACJ;AAIA,IAAM,QAAN,MAAY;AAAA,EACR,YAAY,KAAK;AACb,SAAK,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,MAAM,MAAM;AACd,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,KAAK,KAAK;AAAA;AAAA,QAEV,QAAQ,OAAO,SAAS,WAAW,OAAO,MAAM,KAAK,IAAI;AAAA,MAC7D;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,OAAO;AACT,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,KAAK,KAAK;AAAA,MACd;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AAsBA,IAAM,UAAN,MAAM,iBAAgB,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS/B,YAAY,SAAS,OAAO,CAAC,GAAG,SAAS;AACrC,UAAM;AAEN,SAAK,SAAS,IAAI,aAAa;AAE/B,SAAK,SAAS,IAAI,aAAa;AAC/B,SAAK,UAAU;AACf,SAAK,OAAO,OAAO,SAAS,WAAW,CAAC,IAAI,IAAI;AAChD,SAAK,UAAU,YAAY,QAAQ,YAAY,SAAS,UAAU,CAAC;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,OAAO,QAAQ,SAAS,OAAO,CAAC,GAAG,SAAS;AACxC,UAAM,WAAW,IAAI,SAAQ,SAAS,MAAM,OAAO;AACnD,aAAS,QAAQ,UAAU;AAC3B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,QAAQ;AACV,UAAM,UAAU,KAAK;AACrB,UAAM,OAAO,KAAK;AAClB,UAAM,UAAU,KAAK;AACrB,QAAI,OAAO,SAAS,UAAU;AAC1B,aAAO,OAAO,IAAI;AAAA,IACtB;AACA,UAAM,UAAU,CAAC,UAAU;AACvB,cAAQ,MAAM,OAAO;AAAA,QACjB,KAAK;AACD,eAAK,KAAK,SAAS,MAAM,OAAO;AAChC;AAAA,QACJ,KAAK;AACD,eAAK,KAAK,SAAS,MAAM,OAAO;AAChC;AAAA,QACJ,KAAK;AACD,eAAK,OAAO,KAAK,QAAQ,MAAM,OAAO;AACtC;AAAA,QACJ,KAAK;AACD,eAAK,OAAO,KAAK,QAAQ,MAAM,OAAO;AACtC;AAAA,MACR;AAAA,IACJ;AACA,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW,kBAAkB,OAAO;AAAA,MACxC;AAAA,IACJ,CAAC,EAAE,KAAK,CAAC,QAAQ,IAAI,MAAM,GAAG,CAAC;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,UAAU;AACZ,UAAM,UAAU,KAAK;AACrB,UAAM,OAAO,KAAK;AAClB,UAAM,UAAU,KAAK;AACrB,QAAI,OAAO,SAAS,UAAU;AAC1B,aAAO,OAAO,IAAI;AAAA,IACtB;AACA,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AA2BA,eAAeC,MAAK,MAAM,UAAU;AAChC,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,MAAM;AAAA,IACV;AAAA,EACJ,CAAC;AACL;;;ACncA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6BA,eAAe,eAAe,SAAS;AACnC,SAAO,OAAO,WAAW,eAAe,CAAC,SAAS;AAC9C,YAAQ,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,OAAO;AAAA,EACpE,CAAC;AACL;AAiBA,eAAe,gBAAgB;AAC3B,MAAI;AACJ,WAAS,gBAAgB;AACrB,QAAI,cAAc;AACd,mBAAa;AAAA,IACjB;AACA,mBAAe;AAAA,EACnB;AACA,SAAO,IAAI,QAAQ,CAACC,UAAS,WAAW;AACpC,aAAS,eAAe,cAAc;AAClC,UAAI,aAAa,OAAO;AACpB,sBAAc;AACd,eAAO,aAAa,KAAK;AACzB;AAAA,MACJ;AAEA,UAAI,aAAa,WAAW,QAAQ;AAChC,sBAAc;AACd,QAAAA,SAAQ;AAAA,MACZ;AAAA,IACJ;AAEA,mBAAe,cAAc,EACxB,KAAK,CAAC,OAAO;AACd,qBAAe;AAAA,IACnB,CAAC,EACI,MAAM,CAAC,MAAM;AACd,oBAAc;AAEd,YAAM;AAAA,IACV,CAAC;AAGD,SAAK,WAAW,cAAc,EAAE,MAAM,CAAC,MAAM;AACzC,oBAAc;AAEd,YAAM;AAAA,IACV,CAAC;AAAA,EACL,CAAC;AACL;AAcA,eAAe,cAAc;AACzB,MAAI;AACJ,WAAS,gBAAgB;AACrB,QAAI,cAAc;AACd,mBAAa;AAAA,IACjB;AACA,mBAAe;AAAA,EACnB;AACA,SAAO,IAAI,QAAQ,CAACA,UAAS,WAAW;AACpC,aAAS,kBAAkB,UAAU;AACjC,oBAAc;AACd,MAAAA,SAAQ;AAAA,QACJ;AAAA,QACA,cAAc;AAAA,MAClB,CAAC;AAAA,IACL;AACA,aAAS,eAAe,cAAc;AAClC,UAAI,aAAa,OAAO;AACpB,sBAAc;AACd,eAAO,aAAa,KAAK;AACzB;AAAA,MACJ;AACA,UAAI,aAAa,WAAW,YAAY;AACpC,sBAAc;AACd,QAAAA,SAAQ;AAAA,UACJ,cAAc;AAAA,QAClB,CAAC;AAAA,MACL;AAAA,IACJ;AAEA,SAAK,WAAW,kBAAkB,CAAC,SAAS;AACxC,wBAAkB,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,OAAO;AAAA,IAC9E,CAAC,EAAE,MAAM,CAAC,MAAM;AACZ,oBAAc;AAEd,YAAM;AAAA,IACV,CAAC;AAED,mBAAe,cAAc,EACxB,KAAK,CAAC,OAAO;AACd,qBAAe;AAAA,IACnB,CAAC,EACI,MAAM,CAAC,MAAM;AACd,oBAAc;AAEd,YAAM;AAAA,IACV,CAAC;AAED,SAAK,WAAW,YAAY,EAAE,MAAM,CAAC,MAAM;AACvC,oBAAc;AAEd,YAAM;AAAA,IACV,CAAC;AAAA,EACL,CAAC;AACL;;;AC7JA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiCA,IAAM,MAAM,UAAU,IAAI,SAAS;AAanC,eAAe,WAAW;AACtB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,IACT;AAAA,EACJ,CAAC;AACL;AAWA,eAAe,UAAU;AACrB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,IACT;AAAA,EACJ,CAAC;AACL;AAWA,eAAe,OAAO;AAClB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,IACT;AAAA,EACJ,CAAC;AACL;AAYA,eAAe,OAAO;AAClB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,IACT;AAAA,EACJ,CAAC;AACL;AAWA,eAAe,UAAU;AACrB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,IACT;AAAA,EACJ,CAAC;AACL;AAcA,eAAe,SAAS;AACpB,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,IACT;AAAA,EACJ,CAAC;AACL;;;ACnGA,IAAMC,UAAS;", "names": ["message", "BaseDirectory", "ResponseType", "type", "open", "open", "resolve", "invoke"]}
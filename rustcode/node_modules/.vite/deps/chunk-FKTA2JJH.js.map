{"version": 3, "sources": ["../../@tauri-apps/api/window.js", "../../@tauri-apps/api/helpers/tauri.js", "../../@tauri-apps/api/helpers/event.js", "../../@tauri-apps/api/event.js"], "sourcesContent": ["import { invokeTauriCommand } from './helpers/tauri.js';\nimport { listen, once, emit } from './helpers/event.js';\nimport { TauriEvent } from './event.js';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Provides APIs to create windows, communicate with other windows and manipulate the current window.\n *\n * This package is also accessible with `window.__TAURI__.window` when [`build.withGlobalTauri`](https://tauri.app/v1/api/config/#buildconfig.withglobaltauri) in `tauri.conf.json` is set to `true`.\n *\n * The APIs must be added to [`tauri.allowlist.window`](https://tauri.app/v1/api/config/#allowlistconfig.window) in `tauri.conf.json`:\n * ```json\n * {\n *   \"tauri\": {\n *     \"allowlist\": {\n *       \"window\": {\n *         \"all\": true, // enable all window APIs\n *         \"create\": true, // enable window creation\n *         \"center\": true,\n *         \"requestUserAttention\": true,\n *         \"setResizable\": true,\n *         \"setMaximizable\": true,\n *         \"setMinimizable\": true,\n *         \"setClosable\": true,\n *         \"setTitle\": true,\n *         \"maximize\": true,\n *         \"unmaximize\": true,\n *         \"minimize\": true,\n *         \"unminimize\": true,\n *         \"show\": true,\n *         \"hide\": true,\n *         \"close\": true,\n *         \"setDecorations\": true,\n *         \"setAlwaysOnTop\": true,\n *         \"setContentProtected\": true,\n *         \"setSize\": true,\n *         \"setMinSize\": true,\n *         \"setMaxSize\": true,\n *         \"setPosition\": true,\n *         \"setFullscreen\": true,\n *         \"setFocus\": true,\n *         \"setIcon\": true,\n *         \"setSkipTaskbar\": true,\n *         \"setCursorGrab\": true,\n *         \"setCursorVisible\": true,\n *         \"setCursorIcon\": true,\n *         \"setCursorPosition\": true,\n *         \"setIgnoreCursorEvents\": true,\n *         \"startDragging\": true,\n *         \"print\": true\n *       }\n *     }\n *   }\n * }\n * ```\n * It is recommended to allowlist only the APIs you use for optimal bundle size and security.\n *\n * ## Window events\n *\n * Events can be listened to using `appWindow.listen`:\n * ```typescript\n * import { appWindow } from \"@tauri-apps/api/window\";\n * appWindow.listen(\"my-window-event\", ({ event, payload }) => { });\n * ```\n *\n * @module\n */\n/**\n * A size represented in logical pixels.\n *\n * @since 1.0.0\n */\nclass LogicalSize {\n    constructor(width, height) {\n        this.type = 'Logical';\n        this.width = width;\n        this.height = height;\n    }\n}\n/**\n * A size represented in physical pixels.\n *\n * @since 1.0.0\n */\nclass PhysicalSize {\n    constructor(width, height) {\n        this.type = 'Physical';\n        this.width = width;\n        this.height = height;\n    }\n    /**\n     * Converts the physical size to a logical one.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * const factor = await appWindow.scaleFactor();\n     * const size = await appWindow.innerSize();\n     * const logical = size.toLogical(factor);\n     * ```\n     *  */\n    toLogical(scaleFactor) {\n        return new LogicalSize(this.width / scaleFactor, this.height / scaleFactor);\n    }\n}\n/**\n *  A position represented in logical pixels.\n *\n * @since 1.0.0\n */\nclass LogicalPosition {\n    constructor(x, y) {\n        this.type = 'Logical';\n        this.x = x;\n        this.y = y;\n    }\n}\n/**\n *  A position represented in physical pixels.\n *\n * @since 1.0.0\n */\nclass PhysicalPosition {\n    constructor(x, y) {\n        this.type = 'Physical';\n        this.x = x;\n        this.y = y;\n    }\n    /**\n     * Converts the physical position to a logical one.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * const factor = await appWindow.scaleFactor();\n     * const position = await appWindow.innerPosition();\n     * const logical = position.toLogical(factor);\n     * ```\n     * */\n    toLogical(scaleFactor) {\n        return new LogicalPosition(this.x / scaleFactor, this.y / scaleFactor);\n    }\n}\n/**\n * Attention type to request on a window.\n *\n * @since 1.0.0\n */\nvar UserAttentionType;\n(function (UserAttentionType) {\n    /**\n     * #### Platform-specific\n     * - **macOS:** Bounces the dock icon until the application is in focus.\n     * - **Windows:** Flashes both the window and the taskbar button until the application is in focus.\n     */\n    UserAttentionType[UserAttentionType[\"Critical\"] = 1] = \"Critical\";\n    /**\n     * #### Platform-specific\n     * - **macOS:** Bounces the dock icon once.\n     * - **Windows:** Flashes the taskbar button until the application is in focus.\n     */\n    UserAttentionType[UserAttentionType[\"Informational\"] = 2] = \"Informational\";\n})(UserAttentionType || (UserAttentionType = {}));\n/**\n * Get an instance of `WebviewWindow` for the current webview window.\n *\n * @since 1.0.0\n */\nfunction getCurrent() {\n    return new WebviewWindow(window.__TAURI_METADATA__.__currentWindow.label, {\n        // @ts-expect-error `skip` is not defined in the public API but it is handled by the constructor\n        skip: true\n    });\n}\n/**\n * Gets a list of instances of `WebviewWindow` for all available webview windows.\n *\n * @since 1.0.0\n */\nfunction getAll() {\n    return window.__TAURI_METADATA__.__windows.map((w) => new WebviewWindow(w.label, {\n        // @ts-expect-error `skip` is not defined in the public API but it is handled by the constructor\n        skip: true\n    }));\n}\n/** @ignore */\n// events that are emitted right here instead of by the created webview\nconst localTauriEvents = ['tauri://created', 'tauri://error'];\n/**\n * A webview window handle allows emitting and listening to events from the backend that are tied to the window.\n *\n * @ignore\n * @since 1.0.0\n */\nclass WebviewWindowHandle {\n    constructor(label) {\n        this.label = label;\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n        this.listeners = Object.create(null);\n    }\n    /**\n     * Listen to an event emitted by the backend or webview.\n     * The event must either be a global event or an event targetting this window.\n     *\n     * See {@link WebviewWindow.emit | `emit`} for more information.\n     *\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * const unlisten = await appWindow.listen<string>('state-changed', (event) => {\n     *   console.log(`Got error: ${payload}`);\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     *\n     * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n     * @param handler Event handler.\n     * @returns A promise resolving to a function to unlisten to the event.\n     */\n    async listen(event, handler) {\n        if (this._handleTauriEvent(event, handler)) {\n            return Promise.resolve(() => {\n                // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, security/detect-object-injection\n                const listeners = this.listeners[event];\n                listeners.splice(listeners.indexOf(handler), 1);\n            });\n        }\n        return listen(event, this.label, handler);\n    }\n    /**\n     * Listen to an one-off event.\n     * See {@link WebviewWindow.listen | `listen`} for more information.\n     *\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * const unlisten = await appWindow.once<null>('initialized', (event) => {\n     *   console.log(`Window initialized!`);\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     *\n     * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n     * @param handler Event handler.\n     * @returns A promise resolving to a function to unlisten to the event.\n     */\n    async once(event, handler) {\n        if (this._handleTauriEvent(event, handler)) {\n            return Promise.resolve(() => {\n                // eslint-disable-next-line security/detect-object-injection\n                const listeners = this.listeners[event];\n                listeners.splice(listeners.indexOf(handler), 1);\n            });\n        }\n        return once(event, this.label, handler);\n    }\n    /**\n     * Emits an event to the backend and all Tauri windows.\n     * The event will have this window's {@link WebviewWindow.label | label} as {@link Event.windowLabel | source window label}.\n     *\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.emit('window-loaded', { loggedIn: true, token: 'authToken' });\n     * ```\n     *\n     * This function can also be used to communicate between windows:\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.listen('sync-data', (event) => { });\n     *\n     * // on another window...\n     * import { WebviewWindow } from '@tauri-apps/api/window';\n     * const otherWindow = WebviewWindow.getByLabel('other')\n     * await otherWindow.emit('sync-data');\n     * ```\n     *\n     * Global listeners are also triggered:\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * import { listen } from '@tauri-apps/api/event';\n     * await listen('ping', (event) => { });\n     *\n     * await appWindow.emit('ping');\n     * ```\n     *\n     * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n     * @param payload Event payload.\n     */\n    async emit(event, payload) {\n        if (localTauriEvents.includes(event)) {\n            // eslint-disable-next-line\n            for (const handler of this.listeners[event] || []) {\n                handler({ event, id: -1, windowLabel: this.label, payload });\n            }\n            return Promise.resolve();\n        }\n        return emit(event, this.label, payload);\n    }\n    /** @ignore */\n    _handleTauriEvent(event, handler) {\n        if (localTauriEvents.includes(event)) {\n            if (!(event in this.listeners)) {\n                // eslint-disable-next-line\n                this.listeners[event] = [handler];\n            }\n            else {\n                // eslint-disable-next-line\n                this.listeners[event].push(handler);\n            }\n            return true;\n        }\n        return false;\n    }\n}\n/**\n * Manage the current window object.\n *\n * @ignore\n * @since 1.0.0\n */\nclass WindowManager extends WebviewWindowHandle {\n    // Getters\n    /**\n     * The scale factor that can be used to map physical pixels to logical pixels.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * const factor = await appWindow.scaleFactor();\n     * ```\n     *\n     * @returns The window's monitor scale factor.\n     * */\n    async scaleFactor() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'scaleFactor'\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * The position of the top-left hand corner of the window's client area relative to the top-left hand corner of the desktop.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * const position = await appWindow.innerPosition();\n     * ```\n     *\n     * @returns The window's inner position.\n     *  */\n    async innerPosition() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'innerPosition'\n                    }\n                }\n            }\n        }).then(({ x, y }) => new PhysicalPosition(x, y));\n    }\n    /**\n     * The position of the top-left hand corner of the window relative to the top-left hand corner of the desktop.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * const position = await appWindow.outerPosition();\n     * ```\n     *\n     * @returns The window's outer position.\n     *  */\n    async outerPosition() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'outerPosition'\n                    }\n                }\n            }\n        }).then(({ x, y }) => new PhysicalPosition(x, y));\n    }\n    /**\n     * The physical size of the window's client area.\n     * The client area is the content of the window, excluding the title bar and borders.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * const size = await appWindow.innerSize();\n     * ```\n     *\n     * @returns The window's inner size.\n     */\n    async innerSize() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'innerSize'\n                    }\n                }\n            }\n        }).then(({ width, height }) => new PhysicalSize(width, height));\n    }\n    /**\n     * The physical size of the entire window.\n     * These dimensions include the title bar and borders. If you don't want that (and you usually don't), use inner_size instead.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * const size = await appWindow.outerSize();\n     * ```\n     *\n     * @returns The window's outer size.\n     */\n    async outerSize() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'outerSize'\n                    }\n                }\n            }\n        }).then(({ width, height }) => new PhysicalSize(width, height));\n    }\n    /**\n     * Gets the window's current fullscreen state.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * const fullscreen = await appWindow.isFullscreen();\n     * ```\n     *\n     * @returns Whether the window is in fullscreen mode or not.\n     *  */\n    async isFullscreen() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'isFullscreen'\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Gets the window's current minimized state.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * const minimized = await appWindow.isMinimized();\n     * ```\n     *\n     * @since 1.3.0\n     * */\n    async isMinimized() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'isMinimized'\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Gets the window's current maximized state.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * const maximized = await appWindow.isMaximized();\n     * ```\n     *\n     * @returns Whether the window is maximized or not.\n     * */\n    async isMaximized() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'isMaximized'\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Gets the window's current focus state.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * const focused = await appWindow.isFocused();\n     * ```\n     *\n     * @returns Whether the window is focused or not.\n     *\n     * @since 1.4\n     * */\n    async isFocused() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'isFocused'\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Gets the window's current decorated state.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * const decorated = await appWindow.isDecorated();\n     * ```\n     *\n     * @returns Whether the window is decorated or not.\n     *  */\n    async isDecorated() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'isDecorated'\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Gets the window's current resizable state.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * const resizable = await appWindow.isResizable();\n     * ```\n     *\n     * @returns Whether the window is resizable or not.\n     *  */\n    async isResizable() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'isResizable'\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Gets the window’s native maximize button state.\n     *\n     * #### Platform-specific\n     *\n     * - **Linux / iOS / Android:** Unsupported.\n     *\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * const maximizable = await appWindow.isMaximizable();\n     * ```\n     *\n     * @returns Whether the window's native maximize button is enabled or not.\n     *  */\n    async isMaximizable() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'isMaximizable'\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Gets the window’s native minimize button state.\n     *\n     * #### Platform-specific\n     *\n     * - **Linux / iOS / Android:** Unsupported.\n     *\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * const minimizable = await appWindow.isMinimizable();\n     * ```\n     *\n     * @returns Whether the window's native minimize button is enabled or not.\n     *  */\n    async isMinimizable() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'isMinimizable'\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Gets the window’s native close button state.\n     *\n     * #### Platform-specific\n     *\n     * - **Linux / iOS / Android:** Unsupported.\n     *\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * const closable = await appWindow.isClosable();\n     * ```\n     *\n     * @returns Whether the window's native close button is enabled or not.\n     *  */\n    async isClosable() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'isClosable'\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Gets the window's current visible state.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * const visible = await appWindow.isVisible();\n     * ```\n     *\n     * @returns Whether the window is visible or not.\n     *  */\n    async isVisible() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'isVisible'\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Gets the window's current title.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * const title = await appWindow.title();\n     * ```\n     *\n     * @since 1.3.0\n     * */\n    async title() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'title'\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Gets the window's current theme.\n     *\n     * #### Platform-specific\n     *\n     * - **macOS:** Theme was introduced on macOS 10.14. Returns `light` on macOS 10.13 and below.\n     *\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * const theme = await appWindow.theme();\n     * ```\n     *\n     * @returns The window theme.\n     * */\n    async theme() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'theme'\n                    }\n                }\n            }\n        });\n    }\n    // Setters\n    /**\n     * Centers the window.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.center();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async center() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'center'\n                    }\n                }\n            }\n        });\n    }\n    /**\n     *  Requests user attention to the window, this has no effect if the application\n     * is already focused. How requesting for user attention manifests is platform dependent,\n     * see `UserAttentionType` for details.\n     *\n     * Providing `null` will unset the request for user attention. Unsetting the request for\n     * user attention might not be done automatically by the WM when the window receives input.\n     *\n     * #### Platform-specific\n     *\n     * - **macOS:** `null` has no effect.\n     * - **Linux:** Urgency levels have the same effect.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.requestUserAttention();\n     * ```\n     *\n     * @param requestType\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async requestUserAttention(requestType) {\n        let requestType_ = null;\n        if (requestType) {\n            if (requestType === UserAttentionType.Critical) {\n                requestType_ = { type: 'Critical' };\n            }\n            else {\n                requestType_ = { type: 'Informational' };\n            }\n        }\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'requestUserAttention',\n                        payload: requestType_\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Updates the window resizable flag.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.setResizable(false);\n     * ```\n     *\n     * @param resizable\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setResizable(resizable) {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'setResizable',\n                        payload: resizable\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Sets whether the window's native maximize button is enabled or not.\n     * If resizable is set to false, this setting is ignored.\n     *\n     * #### Platform-specific\n     *\n     * - **macOS:** Disables the \"zoom\" button in the window titlebar, which is also used to enter fullscreen mode.\n     * - **Linux / iOS / Android:** Unsupported.\n     *\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.setMaximizable(false);\n     * ```\n     *\n     * @param maximizable\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setMaximizable(maximizable) {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'setMaximizable',\n                        payload: maximizable\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Sets whether the window's native minimize button is enabled or not.\n     *\n     * #### Platform-specific\n     *\n     * - **Linux / iOS / Android:** Unsupported.\n     *\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.setMinimizable(false);\n     * ```\n     *\n     * @param minimizable\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setMinimizable(minimizable) {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'setMinimizable',\n                        payload: minimizable\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Sets whether the window's native close button is enabled or not.\n     *\n     * #### Platform-specific\n     *\n     * - **Linux:** GTK+ will do its best to convince the window manager not to show a close button. Depending on the system, this function may not have any effect when called on a window that is already visible\n     * - **iOS / Android:** Unsupported.\n     *\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.setClosable(false);\n     * ```\n     *\n     * @param closable\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setClosable(closable) {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'setClosable',\n                        payload: closable\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Sets the window title.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.setTitle('Tauri');\n     * ```\n     *\n     * @param title The new title\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setTitle(title) {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'setTitle',\n                        payload: title\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Maximizes the window.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.maximize();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async maximize() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'maximize'\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Unmaximizes the window.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.unmaximize();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async unmaximize() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'unmaximize'\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Toggles the window maximized state.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.toggleMaximize();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async toggleMaximize() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'toggleMaximize'\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Minimizes the window.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.minimize();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async minimize() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'minimize'\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Unminimizes the window.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.unminimize();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async unminimize() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'unminimize'\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Sets the window visibility to true.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.show();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async show() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'show'\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Sets the window visibility to false.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.hide();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async hide() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'hide'\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Closes the window.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.close();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async close() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'close'\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Whether the window should have borders and bars.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.setDecorations(false);\n     * ```\n     *\n     * @param decorations Whether the window should have borders and bars.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setDecorations(decorations) {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'setDecorations',\n                        payload: decorations\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Whether the window should always be on top of other windows.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.setAlwaysOnTop(true);\n     * ```\n     *\n     * @param alwaysOnTop Whether the window should always be on top of other windows or not.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setAlwaysOnTop(alwaysOnTop) {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'setAlwaysOnTop',\n                        payload: alwaysOnTop\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Prevents the window contents from being captured by other apps.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.setContentProtected(true);\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     *\n     * @since 1.2.0\n     */\n    async setContentProtected(protected_) {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'setContentProtected',\n                        payload: protected_\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Resizes the window with a new inner size.\n     * @example\n     * ```typescript\n     * import { appWindow, LogicalSize } from '@tauri-apps/api/window';\n     * await appWindow.setSize(new LogicalSize(600, 500));\n     * ```\n     *\n     * @param size The logical or physical inner size.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setSize(size) {\n        if (!size || (size.type !== 'Logical' && size.type !== 'Physical')) {\n            throw new Error('the `size` argument must be either a LogicalSize or a PhysicalSize instance');\n        }\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'setSize',\n                        payload: {\n                            type: size.type,\n                            data: {\n                                width: size.width,\n                                height: size.height\n                            }\n                        }\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Sets the window minimum inner size. If the `size` argument is not provided, the constraint is unset.\n     * @example\n     * ```typescript\n     * import { appWindow, PhysicalSize } from '@tauri-apps/api/window';\n     * await appWindow.setMinSize(new PhysicalSize(600, 500));\n     * ```\n     *\n     * @param size The logical or physical inner size, or `null` to unset the constraint.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setMinSize(size) {\n        if (size && size.type !== 'Logical' && size.type !== 'Physical') {\n            throw new Error('the `size` argument must be either a LogicalSize or a PhysicalSize instance');\n        }\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'setMinSize',\n                        payload: size\n                            ? {\n                                type: size.type,\n                                data: {\n                                    width: size.width,\n                                    height: size.height\n                                }\n                            }\n                            : null\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Sets the window maximum inner size. If the `size` argument is undefined, the constraint is unset.\n     * @example\n     * ```typescript\n     * import { appWindow, LogicalSize } from '@tauri-apps/api/window';\n     * await appWindow.setMaxSize(new LogicalSize(600, 500));\n     * ```\n     *\n     * @param size The logical or physical inner size, or `null` to unset the constraint.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setMaxSize(size) {\n        if (size && size.type !== 'Logical' && size.type !== 'Physical') {\n            throw new Error('the `size` argument must be either a LogicalSize or a PhysicalSize instance');\n        }\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'setMaxSize',\n                        payload: size\n                            ? {\n                                type: size.type,\n                                data: {\n                                    width: size.width,\n                                    height: size.height\n                                }\n                            }\n                            : null\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Sets the window outer position.\n     * @example\n     * ```typescript\n     * import { appWindow, LogicalPosition } from '@tauri-apps/api/window';\n     * await appWindow.setPosition(new LogicalPosition(600, 500));\n     * ```\n     *\n     * @param position The new position, in logical or physical pixels.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setPosition(position) {\n        if (!position ||\n            (position.type !== 'Logical' && position.type !== 'Physical')) {\n            throw new Error('the `position` argument must be either a LogicalPosition or a PhysicalPosition instance');\n        }\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'setPosition',\n                        payload: {\n                            type: position.type,\n                            data: {\n                                x: position.x,\n                                y: position.y\n                            }\n                        }\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Sets the window fullscreen state.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.setFullscreen(true);\n     * ```\n     *\n     * @param fullscreen Whether the window should go to fullscreen or not.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setFullscreen(fullscreen) {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'setFullscreen',\n                        payload: fullscreen\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Bring the window to front and focus.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.setFocus();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setFocus() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'setFocus'\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Sets the window icon.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.setIcon('/tauri/awesome.png');\n     * ```\n     *\n     * Note that you need the `icon-ico` or `icon-png` Cargo features to use this API.\n     * To enable it, change your Cargo.toml file:\n     * ```toml\n     * [dependencies]\n     * tauri = { version = \"...\", features = [\"...\", \"icon-png\"] }\n     * ```\n     *\n     * @param icon Icon bytes or path to the icon file.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setIcon(icon) {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'setIcon',\n                        payload: {\n                            // correctly serialize Uint8Arrays\n                            icon: typeof icon === 'string' ? icon : Array.from(icon)\n                        }\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Whether the window icon should be hidden from the taskbar or not.\n     *\n     * #### Platform-specific\n     *\n     * - **macOS:** Unsupported.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.setSkipTaskbar(true);\n     * ```\n     *\n     * @param skip true to hide window icon, false to show it.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setSkipTaskbar(skip) {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'setSkipTaskbar',\n                        payload: skip\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Grabs the cursor, preventing it from leaving the window.\n     *\n     * There's no guarantee that the cursor will be hidden. You should\n     * hide it by yourself if you want so.\n     *\n     * #### Platform-specific\n     *\n     * - **Linux:** Unsupported.\n     * - **macOS:** This locks the cursor in a fixed location, which looks visually awkward.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.setCursorGrab(true);\n     * ```\n     *\n     * @param grab `true` to grab the cursor icon, `false` to release it.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setCursorGrab(grab) {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'setCursorGrab',\n                        payload: grab\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Modifies the cursor's visibility.\n     *\n     * #### Platform-specific\n     *\n     * - **Windows:** The cursor is only hidden within the confines of the window.\n     * - **macOS:** The cursor is hidden as long as the window has input focus, even if the cursor is\n     *   outside of the window.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.setCursorVisible(false);\n     * ```\n     *\n     * @param visible If `false`, this will hide the cursor. If `true`, this will show the cursor.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setCursorVisible(visible) {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'setCursorVisible',\n                        payload: visible\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Modifies the cursor icon of the window.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.setCursorIcon('help');\n     * ```\n     *\n     * @param icon The new cursor icon.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setCursorIcon(icon) {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'setCursorIcon',\n                        payload: icon\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Changes the position of the cursor in window coordinates.\n     * @example\n     * ```typescript\n     * import { appWindow, LogicalPosition } from '@tauri-apps/api/window';\n     * await appWindow.setCursorPosition(new LogicalPosition(600, 300));\n     * ```\n     *\n     * @param position The new cursor position.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setCursorPosition(position) {\n        if (!position ||\n            (position.type !== 'Logical' && position.type !== 'Physical')) {\n            throw new Error('the `position` argument must be either a LogicalPosition or a PhysicalPosition instance');\n        }\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'setCursorPosition',\n                        payload: {\n                            type: position.type,\n                            data: {\n                                x: position.x,\n                                y: position.y\n                            }\n                        }\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Changes the cursor events behavior.\n     *\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.setIgnoreCursorEvents(true);\n     * ```\n     *\n     * @param ignore `true` to ignore the cursor events; `false` to process them as usual.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setIgnoreCursorEvents(ignore) {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'setIgnoreCursorEvents',\n                        payload: ignore\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Starts dragging the window.\n     * @example\n     * ```typescript\n     * import { appWindow } from '@tauri-apps/api/window';\n     * await appWindow.startDragging();\n     * ```\n     *\n     * @return A promise indicating the success or failure of the operation.\n     */\n    async startDragging() {\n        return invokeTauriCommand({\n            __tauriModule: 'Window',\n            message: {\n                cmd: 'manage',\n                data: {\n                    label: this.label,\n                    cmd: {\n                        type: 'startDragging'\n                    }\n                }\n            }\n        });\n    }\n    // Listeners\n    /**\n     * Listen to window resize.\n     *\n     * @example\n     * ```typescript\n     * import { appWindow } from \"@tauri-apps/api/window\";\n     * const unlisten = await appWindow.onResized(({ payload: size }) => {\n     *  console.log('Window resized', size);\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     *\n     * @since 1.0.2\n     */\n    async onResized(handler) {\n        return this.listen(TauriEvent.WINDOW_RESIZED, (e) => {\n            e.payload = mapPhysicalSize(e.payload);\n            handler(e);\n        });\n    }\n    /**\n     * Listen to window move.\n     *\n     * @example\n     * ```typescript\n     * import { appWindow } from \"@tauri-apps/api/window\";\n     * const unlisten = await appWindow.onMoved(({ payload: position }) => {\n     *  console.log('Window moved', position);\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     *\n     * @since 1.0.2\n     */\n    async onMoved(handler) {\n        return this.listen(TauriEvent.WINDOW_MOVED, (e) => {\n            e.payload = mapPhysicalPosition(e.payload);\n            handler(e);\n        });\n    }\n    /**\n     * Listen to window close requested. Emitted when the user requests to closes the window.\n     *\n     * @example\n     * ```typescript\n     * import { appWindow } from \"@tauri-apps/api/window\";\n     * import { confirm } from '@tauri-apps/api/dialog';\n     * const unlisten = await appWindow.onCloseRequested(async (event) => {\n     *   const confirmed = await confirm('Are you sure?');\n     *   if (!confirmed) {\n     *     // user did not confirm closing the window; let's prevent it\n     *     event.preventDefault();\n     *   }\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     *\n     * @since 1.0.2\n     */\n    /* eslint-disable @typescript-eslint/promise-function-async */\n    async onCloseRequested(handler) {\n        return this.listen(TauriEvent.WINDOW_CLOSE_REQUESTED, (event) => {\n            const evt = new CloseRequestedEvent(event);\n            void Promise.resolve(handler(evt)).then(() => {\n                if (!evt.isPreventDefault()) {\n                    return this.close();\n                }\n            });\n        });\n    }\n    /* eslint-enable */\n    /**\n     * Listen to window focus change.\n     *\n     * @example\n     * ```typescript\n     * import { appWindow } from \"@tauri-apps/api/window\";\n     * const unlisten = await appWindow.onFocusChanged(({ payload: focused }) => {\n     *  console.log('Focus changed, window is focused? ' + focused);\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     *\n     * @since 1.0.2\n     */\n    async onFocusChanged(handler) {\n        const unlistenFocus = await this.listen(TauriEvent.WINDOW_FOCUS, (event) => {\n            handler({ ...event, payload: true });\n        });\n        const unlistenBlur = await this.listen(TauriEvent.WINDOW_BLUR, (event) => {\n            handler({ ...event, payload: false });\n        });\n        return () => {\n            unlistenFocus();\n            unlistenBlur();\n        };\n    }\n    /**\n     * Listen to window scale change. Emitted when the window's scale factor has changed.\n     * The following user actions can cause DPI changes:\n     * - Changing the display's resolution.\n     * - Changing the display's scale factor (e.g. in Control Panel on Windows).\n     * - Moving the window to a display with a different scale factor.\n     *\n     * @example\n     * ```typescript\n     * import { appWindow } from \"@tauri-apps/api/window\";\n     * const unlisten = await appWindow.onScaleChanged(({ payload }) => {\n     *  console.log('Scale changed', payload.scaleFactor, payload.size);\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     *\n     * @since 1.0.2\n     */\n    async onScaleChanged(handler) {\n        return this.listen(TauriEvent.WINDOW_SCALE_FACTOR_CHANGED, handler);\n    }\n    /**\n     * Listen to the window menu item click. The payload is the item id.\n     *\n     * @example\n     * ```typescript\n     * import { appWindow } from \"@tauri-apps/api/window\";\n     * const unlisten = await appWindow.onMenuClicked(({ payload: menuId }) => {\n     *  console.log('Menu clicked: ' + menuId);\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     *\n     * @since 1.0.2\n     */\n    async onMenuClicked(handler) {\n        return this.listen(TauriEvent.MENU, handler);\n    }\n    /**\n     * Listen to a file drop event.\n     * The listener is triggered when the user hovers the selected files on the window,\n     * drops the files or cancels the operation.\n     *\n     * @example\n     * ```typescript\n     * import { appWindow } from \"@tauri-apps/api/window\";\n     * const unlisten = await appWindow.onFileDropEvent((event) => {\n     *  if (event.payload.type === 'hover') {\n     *    console.log('User hovering', event.payload.paths);\n     *  } else if (event.payload.type === 'drop') {\n     *    console.log('User dropped', event.payload.paths);\n     *  } else {\n     *    console.log('File drop cancelled');\n     *  }\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     *\n     * @since 1.0.2\n     */\n    async onFileDropEvent(handler) {\n        const unlistenFileDrop = await this.listen(TauriEvent.WINDOW_FILE_DROP, (event) => {\n            handler({ ...event, payload: { type: 'drop', paths: event.payload } });\n        });\n        const unlistenFileHover = await this.listen(TauriEvent.WINDOW_FILE_DROP_HOVER, (event) => {\n            handler({ ...event, payload: { type: 'hover', paths: event.payload } });\n        });\n        const unlistenCancel = await this.listen(TauriEvent.WINDOW_FILE_DROP_CANCELLED, (event) => {\n            handler({ ...event, payload: { type: 'cancel' } });\n        });\n        return () => {\n            unlistenFileDrop();\n            unlistenFileHover();\n            unlistenCancel();\n        };\n    }\n    /**\n     * Listen to the system theme change.\n     *\n     * @example\n     * ```typescript\n     * import { appWindow } from \"@tauri-apps/api/window\";\n     * const unlisten = await appWindow.onThemeChanged(({ payload: theme }) => {\n     *  console.log('New theme: ' + theme);\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     *\n     * @since 1.0.2\n     */\n    async onThemeChanged(handler) {\n        return this.listen(TauriEvent.WINDOW_THEME_CHANGED, handler);\n    }\n}\n/**\n * @since 1.0.2\n */\nclass CloseRequestedEvent {\n    constructor(event) {\n        this._preventDefault = false;\n        this.event = event.event;\n        this.windowLabel = event.windowLabel;\n        this.id = event.id;\n    }\n    preventDefault() {\n        this._preventDefault = true;\n    }\n    isPreventDefault() {\n        return this._preventDefault;\n    }\n}\n/**\n * Create new webview windows and get a handle to existing ones.\n *\n * Windows are identified by a *label*  a unique identifier that can be used to reference it later.\n * It may only contain alphanumeric characters `a-zA-Z` plus the following special characters `-`, `/`, `:` and `_`.\n *\n * @example\n * ```typescript\n * // loading embedded asset:\n * const webview = new WebviewWindow('theUniqueLabel', {\n *   url: 'path/to/page.html'\n * });\n * // alternatively, load a remote URL:\n * const webview = new WebviewWindow('theUniqueLabel', {\n *   url: 'https://github.com/tauri-apps/tauri'\n * });\n *\n * webview.once('tauri://created', function () {\n *  // webview window successfully created\n * });\n * webview.once('tauri://error', function (e) {\n *  // an error happened creating the webview window\n * });\n *\n * // emit an event to the backend\n * await webview.emit(\"some event\", \"data\");\n * // listen to an event from the backend\n * const unlisten = await webview.listen(\"event name\", e => {});\n * unlisten();\n * ```\n *\n * @since 1.0.2\n */\nclass WebviewWindow extends WindowManager {\n    /**\n     * Creates a new WebviewWindow.\n     * @example\n     * ```typescript\n     * import { WebviewWindow } from '@tauri-apps/api/window';\n     * const webview = new WebviewWindow('my-label', {\n     *   url: 'https://github.com/tauri-apps/tauri'\n     * });\n     * webview.once('tauri://created', function () {\n     *  // webview window successfully created\n     * });\n     * webview.once('tauri://error', function (e) {\n     *  // an error happened creating the webview window\n     * });\n     * ```\n     *\n     * * @param label The unique webview window label. Must be alphanumeric: `a-zA-Z-/:_`.\n     * @returns The WebviewWindow instance to communicate with the webview.\n     */\n    constructor(label, options = {}) {\n        super(label);\n        // @ts-expect-error `skip` is not a public API so it is not defined in WindowOptions\n        if (!(options === null || options === void 0 ? void 0 : options.skip)) {\n            invokeTauriCommand({\n                __tauriModule: 'Window',\n                message: {\n                    cmd: 'createWebview',\n                    data: {\n                        options: {\n                            label,\n                            ...options\n                        }\n                    }\n                }\n            })\n                .then(async () => this.emit('tauri://created'))\n                .catch(async (e) => this.emit('tauri://error', e));\n        }\n    }\n    /**\n     * Gets the WebviewWindow for the webview associated with the given label.\n     * @example\n     * ```typescript\n     * import { WebviewWindow } from '@tauri-apps/api/window';\n     * const mainWindow = WebviewWindow.getByLabel('main');\n     * ```\n     *\n     * @param label The webview window label.\n     * @returns The WebviewWindow instance to communicate with the webview or null if the webview doesn't exist.\n     */\n    static getByLabel(label) {\n        if (getAll().some((w) => w.label === label)) {\n            // @ts-expect-error `skip` is not defined in the public API but it is handled by the constructor\n            return new WebviewWindow(label, { skip: true });\n        }\n        return null;\n    }\n    /**\n     *  Gets the focused window.\n     * @example\n     * ```typescript\n     * import { WebviewWindow } from '@tauri-apps/api/window';\n     * const focusedWindow = WebviewWindow.getFocusedWindow();\n     * ```\n     *\n     * @returns The WebviewWindow instance to communicate with the webview or `undefined` if there is not any focused window.\n     *\n     * @since 1.4\n     */\n    static async getFocusedWindow() {\n        for (const w of getAll()) {\n            if (await w.isFocused()) {\n                return w;\n            }\n        }\n        return null;\n    }\n}\n/** The WebviewWindow for the current window. */\nlet appWindow;\nif ('__TAURI_METADATA__' in window) {\n    appWindow = new WebviewWindow(window.__TAURI_METADATA__.__currentWindow.label, {\n        // @ts-expect-error `skip` is not defined in the public API but it is handled by the constructor\n        skip: true\n    });\n}\nelse {\n    console.warn(`Could not find \"window.__TAURI_METADATA__\". The \"appWindow\" value will reference the \"main\" window label.\\nNote that this is not an issue if running this frontend on a browser instead of a Tauri window.`);\n    appWindow = new WebviewWindow('main', {\n        // @ts-expect-error `skip` is not defined in the public API but it is handled by the constructor\n        skip: true\n    });\n}\nfunction mapMonitor(m) {\n    return m === null\n        ? null\n        : {\n            name: m.name,\n            scaleFactor: m.scaleFactor,\n            position: mapPhysicalPosition(m.position),\n            size: mapPhysicalSize(m.size)\n        };\n}\nfunction mapPhysicalPosition(m) {\n    return new PhysicalPosition(m.x, m.y);\n}\nfunction mapPhysicalSize(m) {\n    return new PhysicalSize(m.width, m.height);\n}\n/**\n * Returns the monitor on which the window currently resides.\n * Returns `null` if current monitor can't be detected.\n * @example\n * ```typescript\n * import { currentMonitor } from '@tauri-apps/api/window';\n * const monitor = currentMonitor();\n * ```\n *\n * @since 1.0.0\n */\nasync function currentMonitor() {\n    return invokeTauriCommand({\n        __tauriModule: 'Window',\n        message: {\n            cmd: 'manage',\n            data: {\n                cmd: {\n                    type: 'currentMonitor'\n                }\n            }\n        }\n    }).then(mapMonitor);\n}\n/**\n * Returns the primary monitor of the system.\n * Returns `null` if it can't identify any monitor as a primary one.\n * @example\n * ```typescript\n * import { primaryMonitor } from '@tauri-apps/api/window';\n * const monitor = primaryMonitor();\n * ```\n *\n * @since 1.0.0\n */\nasync function primaryMonitor() {\n    return invokeTauriCommand({\n        __tauriModule: 'Window',\n        message: {\n            cmd: 'manage',\n            data: {\n                cmd: {\n                    type: 'primaryMonitor'\n                }\n            }\n        }\n    }).then(mapMonitor);\n}\n/**\n * Returns the list of all the monitors available on the system.\n * @example\n * ```typescript\n * import { availableMonitors } from '@tauri-apps/api/window';\n * const monitors = availableMonitors();\n * ```\n *\n * @since 1.0.0\n */\nasync function availableMonitors() {\n    return invokeTauriCommand({\n        __tauriModule: 'Window',\n        message: {\n            cmd: 'manage',\n            data: {\n                cmd: {\n                    type: 'availableMonitors'\n                }\n            }\n        }\n    }).then((ms) => ms.map(mapMonitor));\n}\n\nexport { CloseRequestedEvent, LogicalPosition, LogicalSize, PhysicalPosition, PhysicalSize, UserAttentionType, WebviewWindow, WebviewWindowHandle, WindowManager, appWindow, availableMonitors, currentMonitor, getAll, getCurrent, primaryMonitor };\n", "import { invoke } from '../tauri.js';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/** @ignore */\nasync function invokeTauriCommand(command) {\n    return invoke('tauri', command);\n}\n\nexport { invokeTauriCommand };\n", "import { invokeTauriCommand } from './tauri.js';\nimport { transformCallback } from '../tauri.js';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Unregister the event listener associated with the given name and id.\n *\n * @ignore\n * @param event The event name\n * @param eventId Event identifier\n * @returns\n */\nasync function _unlisten(event, eventId) {\n    return invokeTauriCommand({\n        __tauriModule: 'Event',\n        message: {\n            cmd: 'unlisten',\n            event,\n            eventId\n        }\n    });\n}\n/**\n * Emits an event to the backend.\n *\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n * @param [windowLabel] The label of the window to which the event is sent, if null/undefined the event will be sent to all windows\n * @param [payload] Event payload\n * @returns\n */\nasync function emit(event, windowLabel, payload) {\n    await invokeTauriCommand({\n        __tauriModule: 'Event',\n        message: {\n            cmd: 'emit',\n            event,\n            windowLabel,\n            payload\n        }\n    });\n}\n/**\n * Listen to an event from the backend.\n *\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n * @param handler Event handler callback.\n * @return A promise resolving to a function to unlisten to the event.\n */\nasync function listen(event, windowLabel, handler) {\n    return invokeTauriCommand({\n        __tauriModule: 'Event',\n        message: {\n            cmd: 'listen',\n            event,\n            windowLabel,\n            handler: transformCallback(handler)\n        }\n    }).then((eventId) => {\n        return async () => _unlisten(event, eventId);\n    });\n}\n/**\n * Listen to an one-off event from the backend.\n *\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n * @param handler Event handler callback.\n * @returns A promise resolving to a function to unlisten to the event.\n */\nasync function once(event, windowLabel, handler) {\n    return listen(event, windowLabel, (eventData) => {\n        handler(eventData);\n        _unlisten(event, eventData.id).catch(() => { });\n    });\n}\n\nexport { emit, listen, once };\n", "import { listen as listen$1, once as once$1, emit as emit$1 } from './helpers/event.js';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * The event system allows you to emit events to the backend and listen to events from it.\n *\n * This package is also accessible with `window.__TAURI__.event` when [`build.withGlobalTauri`](https://tauri.app/v1/api/config/#buildconfig.withglobaltauri) in `tauri.conf.json` is set to `true`.\n * @module\n */\n/**\n * @since 1.1.0\n */\nvar TauriEvent;\n(function (TauriEvent) {\n    TauriEvent[\"WINDOW_RESIZED\"] = \"tauri://resize\";\n    TauriEvent[\"WINDOW_MOVED\"] = \"tauri://move\";\n    TauriEvent[\"WINDOW_CLOSE_REQUESTED\"] = \"tauri://close-requested\";\n    TauriEvent[\"WINDOW_CREATED\"] = \"tauri://window-created\";\n    TauriEvent[\"WINDOW_DESTROYED\"] = \"tauri://destroyed\";\n    TauriEvent[\"WINDOW_FOCUS\"] = \"tauri://focus\";\n    TauriEvent[\"WINDOW_BLUR\"] = \"tauri://blur\";\n    TauriEvent[\"WINDOW_SCALE_FACTOR_CHANGED\"] = \"tauri://scale-change\";\n    TauriEvent[\"WINDOW_THEME_CHANGED\"] = \"tauri://theme-changed\";\n    TauriEvent[\"WINDOW_FILE_DROP\"] = \"tauri://file-drop\";\n    TauriEvent[\"WINDOW_FILE_DROP_HOVER\"] = \"tauri://file-drop-hover\";\n    TauriEvent[\"WINDOW_FILE_DROP_CANCELLED\"] = \"tauri://file-drop-cancelled\";\n    TauriEvent[\"MENU\"] = \"tauri://menu\";\n    TauriEvent[\"CHECK_UPDATE\"] = \"tauri://update\";\n    TauriEvent[\"UPDATE_AVAILABLE\"] = \"tauri://update-available\";\n    TauriEvent[\"INSTALL_UPDATE\"] = \"tauri://update-install\";\n    TauriEvent[\"STATUS_UPDATE\"] = \"tauri://update-status\";\n    TauriEvent[\"DOWNLOAD_PROGRESS\"] = \"tauri://update-download-progress\";\n})(TauriEvent || (TauriEvent = {}));\n/**\n * Listen to an event. The event can be either global or window-specific.\n * See {@link Event.windowLabel} to check the event source.\n *\n * @example\n * ```typescript\n * import { listen } from '@tauri-apps/api/event';\n * const unlisten = await listen<string>('error', (event) => {\n *   console.log(`Got error in window ${event.windowLabel}, payload: ${event.payload}`);\n * });\n *\n * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n * unlisten();\n * ```\n *\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n * @param handler Event handler callback.\n * @returns A promise resolving to a function to unlisten to the event.\n * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n *\n * @since 1.0.0\n */\nasync function listen(event, handler) {\n    return listen$1(event, null, handler);\n}\n/**\n * Listen to an one-off event. See {@link listen} for more information.\n *\n * @example\n * ```typescript\n * import { once } from '@tauri-apps/api/event';\n * interface LoadedPayload {\n *   loggedIn: boolean,\n *   token: string\n * }\n * const unlisten = await once<LoadedPayload>('loaded', (event) => {\n *   console.log(`App is loaded, loggedIn: ${event.payload.loggedIn}, token: ${event.payload.token}`);\n * });\n *\n * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n * unlisten();\n * ```\n *\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n * @returns A promise resolving to a function to unlisten to the event.\n * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n *\n * @since 1.0.0\n */\nasync function once(event, handler) {\n    return once$1(event, null, handler);\n}\n/**\n * Emits an event to the backend and all Tauri windows.\n * @example\n * ```typescript\n * import { emit } from '@tauri-apps/api/event';\n * await emit('frontend-loaded', { loggedIn: true, token: 'authToken' });\n * ```\n *\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n *\n * @since 1.0.0\n */\nasync function emit(event, payload) {\n    return emit$1(event, undefined, payload);\n}\n\nexport { TauriEvent, emit, listen, once };\n"], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACMA,eAAe,mBAAmB,SAAS;AACvC,SAAO,OAAO,SAAS,OAAO;AAClC;;;ACMA,eAAe,UAAU,OAAO,SAAS;AACrC,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AASA,eAAe,KAAK,OAAO,aAAa,SAAS;AAC7C,QAAM,mBAAmB;AAAA,IACrB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAQA,eAAe,OAAO,OAAO,aAAa,SAAS;AAC/C,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA,SAAS,kBAAkB,OAAO;AAAA,IACtC;AAAA,EACJ,CAAC,EAAE,KAAK,CAAC,YAAY;AACjB,WAAO,YAAY,UAAU,OAAO,OAAO;AAAA,EAC/C,CAAC;AACL;AAQA,eAAe,KAAK,OAAO,aAAa,SAAS;AAC7C,SAAO,OAAO,OAAO,aAAa,CAAC,cAAc;AAC7C,YAAQ,SAAS;AACjB,cAAU,OAAO,UAAU,EAAE,EAAE,MAAM,MAAM;AAAA,IAAE,CAAC;AAAA,EAClD,CAAC;AACL;;;AC3EA;AAAA;AAAA;AAAA,cAAAA;AAAA,EAAA,cAAAC;AAAA,EAAA,YAAAC;AAAA;AAcA,IAAI;AAAA,CACH,SAAUC,aAAY;AACnB,EAAAA,YAAW,gBAAgB,IAAI;AAC/B,EAAAA,YAAW,cAAc,IAAI;AAC7B,EAAAA,YAAW,wBAAwB,IAAI;AACvC,EAAAA,YAAW,gBAAgB,IAAI;AAC/B,EAAAA,YAAW,kBAAkB,IAAI;AACjC,EAAAA,YAAW,cAAc,IAAI;AAC7B,EAAAA,YAAW,aAAa,IAAI;AAC5B,EAAAA,YAAW,6BAA6B,IAAI;AAC5C,EAAAA,YAAW,sBAAsB,IAAI;AACrC,EAAAA,YAAW,kBAAkB,IAAI;AACjC,EAAAA,YAAW,wBAAwB,IAAI;AACvC,EAAAA,YAAW,4BAA4B,IAAI;AAC3C,EAAAA,YAAW,MAAM,IAAI;AACrB,EAAAA,YAAW,cAAc,IAAI;AAC7B,EAAAA,YAAW,kBAAkB,IAAI;AACjC,EAAAA,YAAW,gBAAgB,IAAI;AAC/B,EAAAA,YAAW,eAAe,IAAI;AAC9B,EAAAA,YAAW,mBAAmB,IAAI;AACtC,GAAG,eAAe,aAAa,CAAC,EAAE;AAuBlC,eAAeC,QAAO,OAAO,SAAS;AAClC,SAAO,OAAS,OAAO,MAAM,OAAO;AACxC;AAyBA,eAAeC,MAAK,OAAO,SAAS;AAChC,SAAO,KAAO,OAAO,MAAM,OAAO;AACtC;AAaA,eAAeC,MAAK,OAAO,SAAS;AAChC,SAAO,KAAO,OAAO,QAAW,OAAO;AAC3C;;;AH3BA,IAAM,cAAN,MAAkB;AAAA,EACd,YAAY,OAAO,QAAQ;AACvB,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,SAAS;AAAA,EAClB;AACJ;AAMA,IAAM,eAAN,MAAmB;AAAA,EACf,YAAY,OAAO,QAAQ;AACvB,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,UAAU,aAAa;AACnB,WAAO,IAAI,YAAY,KAAK,QAAQ,aAAa,KAAK,SAAS,WAAW;AAAA,EAC9E;AACJ;AAMA,IAAM,kBAAN,MAAsB;AAAA,EAClB,YAAY,GAAG,GAAG;AACd,SAAK,OAAO;AACZ,SAAK,IAAI;AACT,SAAK,IAAI;AAAA,EACb;AACJ;AAMA,IAAM,mBAAN,MAAuB;AAAA,EACnB,YAAY,GAAG,GAAG;AACd,SAAK,OAAO;AACZ,SAAK,IAAI;AACT,SAAK,IAAI;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,UAAU,aAAa;AACnB,WAAO,IAAI,gBAAgB,KAAK,IAAI,aAAa,KAAK,IAAI,WAAW;AAAA,EACzE;AACJ;AAMA,IAAI;AAAA,CACH,SAAUC,oBAAmB;AAM1B,EAAAA,mBAAkBA,mBAAkB,UAAU,IAAI,CAAC,IAAI;AAMvD,EAAAA,mBAAkBA,mBAAkB,eAAe,IAAI,CAAC,IAAI;AAChE,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAMhD,SAAS,aAAa;AAClB,SAAO,IAAI,cAAc,OAAO,mBAAmB,gBAAgB,OAAO;AAAA;AAAA,IAEtE,MAAM;AAAA,EACV,CAAC;AACL;AAMA,SAAS,SAAS;AACd,SAAO,OAAO,mBAAmB,UAAU,IAAI,CAAC,MAAM,IAAI,cAAc,EAAE,OAAO;AAAA;AAAA,IAE7E,MAAM;AAAA,EACV,CAAC,CAAC;AACN;AAGA,IAAM,mBAAmB,CAAC,mBAAmB,eAAe;AAO5D,IAAM,sBAAN,MAA0B;AAAA,EACtB,YAAY,OAAO;AACf,SAAK,QAAQ;AAEb,SAAK,YAAY,uBAAO,OAAO,IAAI;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwBA,MAAM,OAAO,OAAO,SAAS;AACzB,QAAI,KAAK,kBAAkB,OAAO,OAAO,GAAG;AACxC,aAAO,QAAQ,QAAQ,MAAM;AAEzB,cAAM,YAAY,KAAK,UAAU,KAAK;AACtC,kBAAU,OAAO,UAAU,QAAQ,OAAO,GAAG,CAAC;AAAA,MAClD,CAAC;AAAA,IACL;AACA,WAAO,OAAO,OAAO,KAAK,OAAO,OAAO;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBA,MAAM,KAAK,OAAO,SAAS;AACvB,QAAI,KAAK,kBAAkB,OAAO,OAAO,GAAG;AACxC,aAAO,QAAQ,QAAQ,MAAM;AAEzB,cAAM,YAAY,KAAK,UAAU,KAAK;AACtC,kBAAU,OAAO,UAAU,QAAQ,OAAO,GAAG,CAAC;AAAA,MAClD,CAAC;AAAA,IACL;AACA,WAAO,KAAK,OAAO,KAAK,OAAO,OAAO;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkCA,MAAM,KAAK,OAAO,SAAS;AACvB,QAAI,iBAAiB,SAAS,KAAK,GAAG;AAElC,iBAAW,WAAW,KAAK,UAAU,KAAK,KAAK,CAAC,GAAG;AAC/C,gBAAQ,EAAE,OAAO,IAAI,IAAI,aAAa,KAAK,OAAO,QAAQ,CAAC;AAAA,MAC/D;AACA,aAAO,QAAQ,QAAQ;AAAA,IAC3B;AACA,WAAO,KAAK,OAAO,KAAK,OAAO,OAAO;AAAA,EAC1C;AAAA;AAAA,EAEA,kBAAkB,OAAO,SAAS;AAC9B,QAAI,iBAAiB,SAAS,KAAK,GAAG;AAClC,UAAI,EAAE,SAAS,KAAK,YAAY;AAE5B,aAAK,UAAU,KAAK,IAAI,CAAC,OAAO;AAAA,MACpC,OACK;AAED,aAAK,UAAU,KAAK,EAAE,KAAK,OAAO;AAAA,MACtC;AACA,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACJ;AAOA,IAAM,gBAAN,cAA4B,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAY5C,MAAM,cAAc;AAChB,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,gBAAgB;AAClB,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,MAAM,IAAI,iBAAiB,GAAG,CAAC,CAAC;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,gBAAgB;AAClB,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,MAAM,IAAI,iBAAiB,GAAG,CAAC,CAAC;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,YAAY;AACd,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC,EAAE,KAAK,CAAC,EAAE,OAAO,OAAO,MAAM,IAAI,aAAa,OAAO,MAAM,CAAC;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,YAAY;AACd,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC,EAAE,KAAK,CAAC,EAAE,OAAO,OAAO,MAAM,IAAI,aAAa,OAAO,MAAM,CAAC;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,eAAe;AACjB,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,cAAc;AAChB,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,cAAc;AAChB,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,YAAY;AACd,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,cAAc;AAChB,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,cAAc;AAChB,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,gBAAgB;AAClB,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,gBAAgB;AAClB,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,aAAa;AACf,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,YAAY;AACd,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,QAAQ;AACV,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,QAAQ;AACV,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,SAAS;AACX,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBA,MAAM,qBAAqB,aAAa;AACpC,QAAI,eAAe;AACnB,QAAI,aAAa;AACb,UAAI,gBAAgB,kBAAkB,UAAU;AAC5C,uBAAe,EAAE,MAAM,WAAW;AAAA,MACtC,OACK;AACD,uBAAe,EAAE,MAAM,gBAAgB;AAAA,MAC3C;AAAA,IACJ;AACA,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,YACN,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,aAAa,WAAW;AAC1B,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,YACN,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,MAAM,eAAe,aAAa;AAC9B,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,YACN,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,MAAM,eAAe,aAAa;AAC9B,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,YACN,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,MAAM,YAAY,UAAU;AACxB,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,YACN,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,SAAS,OAAO;AAClB,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,YACN,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,WAAW;AACb,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,aAAa;AACf,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,iBAAiB;AACnB,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,WAAW;AACb,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,aAAa;AACf,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,OAAO;AACT,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,OAAO;AACT,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,QAAQ;AACV,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,eAAe,aAAa;AAC9B,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,YACN,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,eAAe,aAAa;AAC9B,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,YACN,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,oBAAoB,YAAY;AAClC,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,YACN,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,QAAQ,MAAM;AAChB,QAAI,CAAC,QAAS,KAAK,SAAS,aAAa,KAAK,SAAS,YAAa;AAChE,YAAM,IAAI,MAAM,6EAA6E;AAAA,IACjG;AACA,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,YACN,SAAS;AAAA,cACL,MAAM,KAAK;AAAA,cACX,MAAM;AAAA,gBACF,OAAO,KAAK;AAAA,gBACZ,QAAQ,KAAK;AAAA,cACjB;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,WAAW,MAAM;AACnB,QAAI,QAAQ,KAAK,SAAS,aAAa,KAAK,SAAS,YAAY;AAC7D,YAAM,IAAI,MAAM,6EAA6E;AAAA,IACjG;AACA,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,YACN,SAAS,OACH;AAAA,cACE,MAAM,KAAK;AAAA,cACX,MAAM;AAAA,gBACF,OAAO,KAAK;AAAA,gBACZ,QAAQ,KAAK;AAAA,cACjB;AAAA,YACJ,IACE;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,WAAW,MAAM;AACnB,QAAI,QAAQ,KAAK,SAAS,aAAa,KAAK,SAAS,YAAY;AAC7D,YAAM,IAAI,MAAM,6EAA6E;AAAA,IACjG;AACA,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,YACN,SAAS,OACH;AAAA,cACE,MAAM,KAAK;AAAA,cACX,MAAM;AAAA,gBACF,OAAO,KAAK;AAAA,gBACZ,QAAQ,KAAK;AAAA,cACjB;AAAA,YACJ,IACE;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,YAAY,UAAU;AACxB,QAAI,CAAC,YACA,SAAS,SAAS,aAAa,SAAS,SAAS,YAAa;AAC/D,YAAM,IAAI,MAAM,yFAAyF;AAAA,IAC7G;AACA,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,YACN,SAAS;AAAA,cACL,MAAM,SAAS;AAAA,cACf,MAAM;AAAA,gBACF,GAAG,SAAS;AAAA,gBACZ,GAAG,SAAS;AAAA,cAChB;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,cAAc,YAAY;AAC5B,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,YACN,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,WAAW;AACb,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,MAAM,QAAQ,MAAM;AAChB,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,YACN,SAAS;AAAA;AAAA,cAEL,MAAM,OAAO,SAAS,WAAW,OAAO,MAAM,KAAK,IAAI;AAAA,YAC3D;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,eAAe,MAAM;AACvB,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,YACN,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,MAAM,cAAc,MAAM;AACtB,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,YACN,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,MAAM,iBAAiB,SAAS;AAC5B,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,YACN,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,cAAc,MAAM;AACtB,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,YACN,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,kBAAkB,UAAU;AAC9B,QAAI,CAAC,YACA,SAAS,SAAS,aAAa,SAAS,SAAS,YAAa;AAC/D,YAAM,IAAI,MAAM,yFAAyF;AAAA,IAC7G;AACA,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,YACN,SAAS;AAAA,cACL,MAAM,SAAS;AAAA,cACf,MAAM;AAAA,gBACF,GAAG,SAAS;AAAA,gBACZ,GAAG,SAAS;AAAA,cAChB;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,sBAAsB,QAAQ;AAChC,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,YACN,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,gBAAgB;AAClB,WAAO,mBAAmB;AAAA,MACtB,eAAe;AAAA,MACf,SAAS;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,UACF,OAAO,KAAK;AAAA,UACZ,KAAK;AAAA,YACD,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,MAAM,UAAU,SAAS;AACrB,WAAO,KAAK,OAAO,WAAW,gBAAgB,CAAC,MAAM;AACjD,QAAE,UAAU,gBAAgB,EAAE,OAAO;AACrC,cAAQ,CAAC;AAAA,IACb,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,MAAM,QAAQ,SAAS;AACnB,WAAO,KAAK,OAAO,WAAW,cAAc,CAAC,MAAM;AAC/C,QAAE,UAAU,oBAAoB,EAAE,OAAO;AACzC,cAAQ,CAAC;AAAA,IACb,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0BA,MAAM,iBAAiB,SAAS;AAC5B,WAAO,KAAK,OAAO,WAAW,wBAAwB,CAAC,UAAU;AAC7D,YAAM,MAAM,IAAI,oBAAoB,KAAK;AACzC,WAAK,QAAQ,QAAQ,QAAQ,GAAG,CAAC,EAAE,KAAK,MAAM;AAC1C,YAAI,CAAC,IAAI,iBAAiB,GAAG;AACzB,iBAAO,KAAK,MAAM;AAAA,QACtB;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,MAAM,eAAe,SAAS;AAC1B,UAAM,gBAAgB,MAAM,KAAK,OAAO,WAAW,cAAc,CAAC,UAAU;AACxE,cAAQ,EAAE,GAAG,OAAO,SAAS,KAAK,CAAC;AAAA,IACvC,CAAC;AACD,UAAM,eAAe,MAAM,KAAK,OAAO,WAAW,aAAa,CAAC,UAAU;AACtE,cAAQ,EAAE,GAAG,OAAO,SAAS,MAAM,CAAC;AAAA,IACxC,CAAC;AACD,WAAO,MAAM;AACT,oBAAc;AACd,mBAAa;AAAA,IACjB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwBA,MAAM,eAAe,SAAS;AAC1B,WAAO,KAAK,OAAO,WAAW,6BAA6B,OAAO;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,MAAM,cAAc,SAAS;AACzB,WAAO,KAAK,OAAO,WAAW,MAAM,OAAO;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4BA,MAAM,gBAAgB,SAAS;AAC3B,UAAM,mBAAmB,MAAM,KAAK,OAAO,WAAW,kBAAkB,CAAC,UAAU;AAC/E,cAAQ,EAAE,GAAG,OAAO,SAAS,EAAE,MAAM,QAAQ,OAAO,MAAM,QAAQ,EAAE,CAAC;AAAA,IACzE,CAAC;AACD,UAAM,oBAAoB,MAAM,KAAK,OAAO,WAAW,wBAAwB,CAAC,UAAU;AACtF,cAAQ,EAAE,GAAG,OAAO,SAAS,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,EAAE,CAAC;AAAA,IAC1E,CAAC;AACD,UAAM,iBAAiB,MAAM,KAAK,OAAO,WAAW,4BAA4B,CAAC,UAAU;AACvF,cAAQ,EAAE,GAAG,OAAO,SAAS,EAAE,MAAM,SAAS,EAAE,CAAC;AAAA,IACrD,CAAC;AACD,WAAO,MAAM;AACT,uBAAiB;AACjB,wBAAkB;AAClB,qBAAe;AAAA,IACnB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,MAAM,eAAe,SAAS;AAC1B,WAAO,KAAK,OAAO,WAAW,sBAAsB,OAAO;AAAA,EAC/D;AACJ;AAIA,IAAM,sBAAN,MAA0B;AAAA,EACtB,YAAY,OAAO;AACf,SAAK,kBAAkB;AACvB,SAAK,QAAQ,MAAM;AACnB,SAAK,cAAc,MAAM;AACzB,SAAK,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,iBAAiB;AACb,SAAK,kBAAkB;AAAA,EAC3B;AAAA,EACA,mBAAmB;AACf,WAAO,KAAK;AAAA,EAChB;AACJ;AAkCA,IAAM,gBAAN,MAAM,uBAAsB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBtC,YAAY,OAAO,UAAU,CAAC,GAAG;AAC7B,UAAM,KAAK;AAEX,QAAI,EAAE,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,OAAO;AACnE,yBAAmB;AAAA,QACf,eAAe;AAAA,QACf,SAAS;AAAA,UACL,KAAK;AAAA,UACL,MAAM;AAAA,YACF,SAAS;AAAA,cACL;AAAA,cACA,GAAG;AAAA,YACP;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,CAAC,EACI,KAAK,YAAY,KAAK,KAAK,iBAAiB,CAAC,EAC7C,MAAM,OAAO,MAAM,KAAK,KAAK,iBAAiB,CAAC,CAAC;AAAA,IACzD;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,OAAO,WAAW,OAAO;AACrB,QAAI,OAAO,EAAE,KAAK,CAAC,MAAM,EAAE,UAAU,KAAK,GAAG;AAEzC,aAAO,IAAI,eAAc,OAAO,EAAE,MAAM,KAAK,CAAC;AAAA,IAClD;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,aAAa,mBAAmB;AAC5B,eAAW,KAAK,OAAO,GAAG;AACtB,UAAI,MAAM,EAAE,UAAU,GAAG;AACrB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AAEA,IAAI;AACJ,IAAI,wBAAwB,QAAQ;AAChC,cAAY,IAAI,cAAc,OAAO,mBAAmB,gBAAgB,OAAO;AAAA;AAAA,IAE3E,MAAM;AAAA,EACV,CAAC;AACL,OACK;AACD,UAAQ,KAAK;AAAA,gGAA4M;AACzN,cAAY,IAAI,cAAc,QAAQ;AAAA;AAAA,IAElC,MAAM;AAAA,EACV,CAAC;AACL;AACA,SAAS,WAAW,GAAG;AACnB,SAAO,MAAM,OACP,OACA;AAAA,IACE,MAAM,EAAE;AAAA,IACR,aAAa,EAAE;AAAA,IACf,UAAU,oBAAoB,EAAE,QAAQ;AAAA,IACxC,MAAM,gBAAgB,EAAE,IAAI;AAAA,EAChC;AACR;AACA,SAAS,oBAAoB,GAAG;AAC5B,SAAO,IAAI,iBAAiB,EAAE,GAAG,EAAE,CAAC;AACxC;AACA,SAAS,gBAAgB,GAAG;AACxB,SAAO,IAAI,aAAa,EAAE,OAAO,EAAE,MAAM;AAC7C;AAYA,eAAe,iBAAiB;AAC5B,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,QACF,KAAK;AAAA,UACD,MAAM;AAAA,QACV;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC,EAAE,KAAK,UAAU;AACtB;AAYA,eAAe,iBAAiB;AAC5B,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,QACF,KAAK;AAAA,UACD,MAAM;AAAA,QACV;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC,EAAE,KAAK,UAAU;AACtB;AAWA,eAAe,oBAAoB;AAC/B,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,QACF,KAAK;AAAA,UACD,MAAM;AAAA,QACV;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC,EAAE,KAAK,CAAC,OAAO,GAAG,IAAI,UAAU,CAAC;AACtC;", "names": ["emit", "listen", "once", "TauriEvent", "listen", "once", "emit", "UserAttentionType"]}
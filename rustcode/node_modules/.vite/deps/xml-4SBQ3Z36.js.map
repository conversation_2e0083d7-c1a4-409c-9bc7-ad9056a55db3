{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/xml/xml.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/xml/xml.ts\nvar conf = {\n  comments: {\n    blockComment: [\"<!--\", \"-->\"]\n  },\n  brackets: [[\"<\", \">\"]],\n  autoClosingPairs: [\n    { open: \"<\", close: \">\" },\n    { open: \"'\", close: \"'\" },\n    { open: '\"', close: '\"' }\n  ],\n  surroundingPairs: [\n    { open: \"<\", close: \">\" },\n    { open: \"'\", close: \"'\" },\n    { open: '\"', close: '\"' }\n  ],\n  onEnterRules: [\n    {\n      beforeText: new RegExp(`<([_:\\\\w][_:\\\\w-.\\\\d]*)([^/>]*(?!/)>)[^<]*$`, \"i\"),\n      afterText: /^<\\/([_:\\w][_:\\w-.\\d]*)\\s*>$/i,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent\n      }\n    },\n    {\n      beforeText: new RegExp(`<(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$`, \"i\"),\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".xml\",\n  ignoreCase: true,\n  qualifiedName: /(?:[\\w\\.\\-]+:)?[\\w\\.\\-]+/,\n  tokenizer: {\n    root: [\n      [/[^<&]+/, \"\"],\n      { include: \"@whitespace\" },\n      [/(<)(@qualifiedName)/, [{ token: \"delimiter\" }, { token: \"tag\", next: \"@tag\" }]],\n      [\n        /(<\\/)(@qualifiedName)(\\s*)(>)/,\n        [{ token: \"delimiter\" }, { token: \"tag\" }, \"\", { token: \"delimiter\" }]\n      ],\n      [/(<\\?)(@qualifiedName)/, [{ token: \"delimiter\" }, { token: \"metatag\", next: \"@tag\" }]],\n      [/(<\\!)(@qualifiedName)/, [{ token: \"delimiter\" }, { token: \"metatag\", next: \"@tag\" }]],\n      [/<\\!\\[CDATA\\[/, { token: \"delimiter.cdata\", next: \"@cdata\" }],\n      [/&\\w+;/, \"string.escape\"]\n    ],\n    cdata: [\n      [/[^\\]]+/, \"\"],\n      [/\\]\\]>/, { token: \"delimiter.cdata\", next: \"@pop\" }],\n      [/\\]/, \"\"]\n    ],\n    tag: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/(@qualifiedName)(\\s*=\\s*)(\"[^\"]*\"|'[^']*')/, [\"attribute.name\", \"\", \"attribute.value\"]],\n      [\n        /(@qualifiedName)(\\s*=\\s*)(\"[^\">?\\/]*|'[^'>?\\/]*)(?=[\\?\\/]\\>)/,\n        [\"attribute.name\", \"\", \"attribute.value\"]\n      ],\n      [/(@qualifiedName)(\\s*=\\s*)(\"[^\">]*|'[^'>]*)/, [\"attribute.name\", \"\", \"attribute.value\"]],\n      [/@qualifiedName/, \"attribute.name\"],\n      [/\\?>/, { token: \"delimiter\", next: \"@pop\" }],\n      [/(\\/)(>)/, [{ token: \"tag\" }, { token: \"delimiter\", next: \"@pop\" }]],\n      [/>/, { token: \"delimiter\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/<!--/, { token: \"comment\", next: \"@comment\" }]\n    ],\n    comment: [\n      [/[^<\\-]+/, \"comment.content\"],\n      [/-->/, { token: \"comment\", next: \"@pop\" }],\n      [/<!--/, \"comment.content.invalid\"],\n      [/[<\\-]/, \"comment.content\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;;AAOA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,MAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,aAAS,OAAO,kBAAkB,IAAI;AACpC,UAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,kBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,EACvH;AACA,SAAO;AACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;AAG9I,IAAI,6BAA6B,CAAC;AAClC,WAAW,4BAA4B,kBAAuB;AAI9D,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,cAAc,CAAC,QAAQ,KAAK;AAAA,EAC9B;AAAA,EACA,UAAU,CAAC,CAAC,KAAK,GAAG,CAAC;AAAA,EACrB,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,cAAc;AAAA,IACZ;AAAA,MACE,YAAY,IAAI,OAAO,+CAA+C,GAAG;AAAA,MACzE,WAAW;AAAA,MACX,QAAQ;AAAA,QACN,cAAc,2BAA2B,UAAU,aAAa;AAAA,MAClE;AAAA,IACF;AAAA,IACA;AAAA,MACE,YAAY,IAAI,OAAO,uCAAuC,GAAG;AAAA,MACjE,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,OAAO;AAAA,IACnF;AAAA,EACF;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,CAAC,UAAU,EAAE;AAAA,MACb,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,uBAAuB,CAAC,EAAE,OAAO,YAAY,GAAG,EAAE,OAAO,OAAO,MAAM,OAAO,CAAC,CAAC;AAAA,MAChF;AAAA,QACE;AAAA,QACA,CAAC,EAAE,OAAO,YAAY,GAAG,EAAE,OAAO,MAAM,GAAG,IAAI,EAAE,OAAO,YAAY,CAAC;AAAA,MACvE;AAAA,MACA,CAAC,yBAAyB,CAAC,EAAE,OAAO,YAAY,GAAG,EAAE,OAAO,WAAW,MAAM,OAAO,CAAC,CAAC;AAAA,MACtF,CAAC,yBAAyB,CAAC,EAAE,OAAO,YAAY,GAAG,EAAE,OAAO,WAAW,MAAM,OAAO,CAAC,CAAC;AAAA,MACtF,CAAC,gBAAgB,EAAE,OAAO,mBAAmB,MAAM,SAAS,CAAC;AAAA,MAC7D,CAAC,SAAS,eAAe;AAAA,IAC3B;AAAA,IACA,OAAO;AAAA,MACL,CAAC,UAAU,EAAE;AAAA,MACb,CAAC,SAAS,EAAE,OAAO,mBAAmB,MAAM,OAAO,CAAC;AAAA,MACpD,CAAC,MAAM,EAAE;AAAA,IACX;AAAA,IACA,KAAK;AAAA,MACH,CAAC,cAAc,EAAE;AAAA,MACjB,CAAC,8CAA8C,CAAC,kBAAkB,IAAI,iBAAiB,CAAC;AAAA,MACxF;AAAA,QACE;AAAA,QACA,CAAC,kBAAkB,IAAI,iBAAiB;AAAA,MAC1C;AAAA,MACA,CAAC,8CAA8C,CAAC,kBAAkB,IAAI,iBAAiB,CAAC;AAAA,MACxF,CAAC,kBAAkB,gBAAgB;AAAA,MACnC,CAAC,OAAO,EAAE,OAAO,aAAa,MAAM,OAAO,CAAC;AAAA,MAC5C,CAAC,WAAW,CAAC,EAAE,OAAO,MAAM,GAAG,EAAE,OAAO,aAAa,MAAM,OAAO,CAAC,CAAC;AAAA,MACpE,CAAC,KAAK,EAAE,OAAO,aAAa,MAAM,OAAO,CAAC;AAAA,IAC5C;AAAA,IACA,YAAY;AAAA,MACV,CAAC,cAAc,EAAE;AAAA,MACjB,CAAC,QAAQ,EAAE,OAAO,WAAW,MAAM,WAAW,CAAC;AAAA,IACjD;AAAA,IACA,SAAS;AAAA,MACP,CAAC,WAAW,iBAAiB;AAAA,MAC7B,CAAC,OAAO,EAAE,OAAO,WAAW,MAAM,OAAO,CAAC;AAAA,MAC1C,CAAC,QAAQ,yBAAyB;AAAA,MAClC,CAAC,SAAS,iBAAiB;AAAA,IAC7B;AAAA,EACF;AACF;", "names": []}
{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/graphql/graphql.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/graphql/graphql.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"\"\"', close: '\"\"\"', notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"\"\"', close: '\"\"\"' },\n    { open: '\"', close: '\"' }\n  ],\n  folding: {\n    offSide: true\n  }\n};\nvar language = {\n  defaultToken: \"invalid\",\n  tokenPostfix: \".gql\",\n  keywords: [\n    \"null\",\n    \"true\",\n    \"false\",\n    \"query\",\n    \"mutation\",\n    \"subscription\",\n    \"extend\",\n    \"schema\",\n    \"directive\",\n    \"scalar\",\n    \"type\",\n    \"interface\",\n    \"union\",\n    \"enum\",\n    \"input\",\n    \"implements\",\n    \"fragment\",\n    \"on\"\n  ],\n  typeKeywords: [\"Int\", \"Float\", \"String\", \"Boolean\", \"ID\"],\n  directiveLocations: [\n    \"SCHEMA\",\n    \"SCALAR\",\n    \"OBJECT\",\n    \"FIELD_DEFINITION\",\n    \"ARGUMENT_DEFINITION\",\n    \"INTERFACE\",\n    \"UNION\",\n    \"ENUM\",\n    \"ENUM_VALUE\",\n    \"INPUT_OBJECT\",\n    \"INPUT_FIELD_DEFINITION\",\n    \"QUERY\",\n    \"MUTATION\",\n    \"SUBSCRIPTION\",\n    \"FIELD\",\n    \"FRAGMENT_DEFINITION\",\n    \"FRAGMENT_SPREAD\",\n    \"INLINE_FRAGMENT\",\n    \"VARIABLE_DEFINITION\"\n  ],\n  operators: [\"=\", \"!\", \"?\", \":\", \"&\", \"|\"],\n  symbols: /[=!?:&|]+/,\n  escapes: /\\\\(?:[\"\\\\\\/bfnrt]|u[0-9A-Fa-f]{4})/,\n  tokenizer: {\n    root: [\n      [\n        /[a-z_][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"key.identifier\"\n          }\n        }\n      ],\n      [\n        /[$][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"argument.identifier\"\n          }\n        }\n      ],\n      [\n        /[A-Z][\\w\\$]*/,\n        {\n          cases: {\n            \"@typeKeywords\": \"keyword\",\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      { include: \"@whitespace\" },\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, { cases: { \"@operators\": \"operator\", \"@default\": \"\" } }],\n      [/@\\s*[a-zA-Z_\\$][\\w\\$]*/, { token: \"annotation\", log: \"annotation token: $0\" }],\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F]+/, \"number.hex\"],\n      [/\\d+/, \"number\"],\n      [/[;,.]/, \"delimiter\"],\n      [/\"\"\"/, { token: \"string\", next: \"@mlstring\", nextEmbedded: \"markdown\" }],\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@string\" }]\n    ],\n    mlstring: [\n      [/[^\"]+/, \"string\"],\n      ['\"\"\"', { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/#.*$/, \"comment\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AAQA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,OAAO,OAAO,OAAO,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IAC1D,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EACxD;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,OAAO,OAAO,MAAM;AAAA,IAC5B,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,EACX;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,cAAc,CAAC,OAAO,SAAS,UAAU,WAAW,IAAI;AAAA,EACxD,oBAAoB;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACxC,SAAS;AAAA,EACT,SAAS;AAAA,EACT,WAAW;AAAA,IACT,MAAM;AAAA,MACJ;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa;AAAA,YACb,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa;AAAA,YACb,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,iBAAiB;AAAA,YACjB,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,YAAY,EAAE,OAAO,EAAE,cAAc,YAAY,YAAY,GAAG,EAAE,CAAC;AAAA,MACpE,CAAC,0BAA0B,EAAE,OAAO,cAAc,KAAK,uBAAuB,CAAC;AAAA,MAC/E,CAAC,4BAA4B,cAAc;AAAA,MAC3C,CAAC,qBAAqB,YAAY;AAAA,MAClC,CAAC,OAAO,QAAQ;AAAA,MAChB,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,OAAO,EAAE,OAAO,UAAU,MAAM,aAAa,cAAc,WAAW,CAAC;AAAA,MACxE,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,UAAU,CAAC;AAAA,IACpE;AAAA,IACA,UAAU;AAAA,MACR,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,OAAO,EAAE,OAAO,UAAU,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,IACjE;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAClE;AAAA,IACA,YAAY;AAAA,MACV,CAAC,cAAc,EAAE;AAAA,MACjB,CAAC,QAAQ,SAAS;AAAA,IACpB;AAAA,EACF;AACF;", "names": []}
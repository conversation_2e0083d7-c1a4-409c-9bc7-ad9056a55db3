import {
  TauriEvent,
  emit,
  event_exports,
  invokeTauriCommand,
  listen,
  once,
  window_exports
} from "./chunk-FKTA2JJH.js";
import {
  invoke,
  tauri_exports,
  transformCallback
} from "./chunk-VDMZGQQV.js";
import {
  __export
} from "./chunk-WVUCPEO5.js";

// node_modules/@tauri-apps/api/app.js
var app_exports = {};
__export(app_exports, {
  getName: () => getName,
  getTauriVersion: () => getTauriVersion,
  getVersion: () => getVersion,
  hide: () => hide,
  show: () => show
});
async function getVersion() {
  return invokeTauriCommand({
    __tauriModule: "App",
    message: {
      cmd: "getAppVersion"
    }
  });
}
async function getName() {
  return invokeTauriCommand({
    __tauriModule: "App",
    message: {
      cmd: "getAppName"
    }
  });
}
async function getTauriVersion() {
  return invokeTauriCommand({
    __tauriModule: "App",
    message: {
      cmd: "getTauriVersion"
    }
  });
}
async function show() {
  return invokeTauriCommand({
    __tauriModule: "App",
    message: {
      cmd: "show"
    }
  });
}
async function hide() {
  return invokeTauriCommand({
    __tauriModule: "App",
    message: {
      cmd: "hide"
    }
  });
}

// node_modules/@tauri-apps/api/cli.js
var cli_exports = {};
__export(cli_exports, {
  getMatches: () => getMatches
});
async function getMatches() {
  return invokeTauriCommand({
    __tauriModule: "Cli",
    message: {
      cmd: "cliMatches"
    }
  });
}

// node_modules/@tauri-apps/api/clipboard.js
var clipboard_exports = {};
__export(clipboard_exports, {
  readText: () => readText,
  writeText: () => writeText
});
async function writeText(text) {
  return invokeTauriCommand({
    __tauriModule: "Clipboard",
    message: {
      cmd: "writeText",
      data: text
    }
  });
}
async function readText() {
  return invokeTauriCommand({
    __tauriModule: "Clipboard",
    message: {
      cmd: "readText",
      // if data is not set, `serde` will ignore the custom deserializer
      // that is set when the API is not allowlisted
      data: null
    }
  });
}

// node_modules/@tauri-apps/api/dialog.js
var dialog_exports = {};
__export(dialog_exports, {
  ask: () => ask,
  confirm: () => confirm,
  message: () => message,
  open: () => open,
  save: () => save
});
async function open(options = {}) {
  if (typeof options === "object") {
    Object.freeze(options);
  }
  return invokeTauriCommand({
    __tauriModule: "Dialog",
    message: {
      cmd: "openDialog",
      options
    }
  });
}
async function save(options = {}) {
  if (typeof options === "object") {
    Object.freeze(options);
  }
  return invokeTauriCommand({
    __tauriModule: "Dialog",
    message: {
      cmd: "saveDialog",
      options
    }
  });
}
async function message(message2, options) {
  var _a, _b;
  const opts = typeof options === "string" ? { title: options } : options;
  return invokeTauriCommand({
    __tauriModule: "Dialog",
    message: {
      cmd: "messageDialog",
      message: message2.toString(),
      title: (_a = opts === null || opts === void 0 ? void 0 : opts.title) === null || _a === void 0 ? void 0 : _a.toString(),
      type: opts === null || opts === void 0 ? void 0 : opts.type,
      buttonLabel: (_b = opts === null || opts === void 0 ? void 0 : opts.okLabel) === null || _b === void 0 ? void 0 : _b.toString()
    }
  });
}
async function ask(message2, options) {
  var _a, _b, _c, _d, _e;
  const opts = typeof options === "string" ? { title: options } : options;
  return invokeTauriCommand({
    __tauriModule: "Dialog",
    message: {
      cmd: "askDialog",
      message: message2.toString(),
      title: (_a = opts === null || opts === void 0 ? void 0 : opts.title) === null || _a === void 0 ? void 0 : _a.toString(),
      type: opts === null || opts === void 0 ? void 0 : opts.type,
      buttonLabels: [
        (_c = (_b = opts === null || opts === void 0 ? void 0 : opts.okLabel) === null || _b === void 0 ? void 0 : _b.toString()) !== null && _c !== void 0 ? _c : "Yes",
        (_e = (_d = opts === null || opts === void 0 ? void 0 : opts.cancelLabel) === null || _d === void 0 ? void 0 : _d.toString()) !== null && _e !== void 0 ? _e : "No"
      ]
    }
  });
}
async function confirm(message2, options) {
  var _a, _b, _c, _d, _e;
  const opts = typeof options === "string" ? { title: options } : options;
  return invokeTauriCommand({
    __tauriModule: "Dialog",
    message: {
      cmd: "confirmDialog",
      message: message2.toString(),
      title: (_a = opts === null || opts === void 0 ? void 0 : opts.title) === null || _a === void 0 ? void 0 : _a.toString(),
      type: opts === null || opts === void 0 ? void 0 : opts.type,
      buttonLabels: [
        (_c = (_b = opts === null || opts === void 0 ? void 0 : opts.okLabel) === null || _b === void 0 ? void 0 : _b.toString()) !== null && _c !== void 0 ? _c : "Ok",
        (_e = (_d = opts === null || opts === void 0 ? void 0 : opts.cancelLabel) === null || _d === void 0 ? void 0 : _d.toString()) !== null && _e !== void 0 ? _e : "Cancel"
      ]
    }
  });
}

// node_modules/@tauri-apps/api/fs.js
var fs_exports = {};
__export(fs_exports, {
  BaseDirectory: () => BaseDirectory,
  Dir: () => BaseDirectory,
  copyFile: () => copyFile,
  createDir: () => createDir,
  exists: () => exists,
  readBinaryFile: () => readBinaryFile,
  readDir: () => readDir,
  readTextFile: () => readTextFile,
  removeDir: () => removeDir,
  removeFile: () => removeFile,
  renameFile: () => renameFile,
  writeBinaryFile: () => writeBinaryFile,
  writeFile: () => writeTextFile,
  writeTextFile: () => writeTextFile
});
var BaseDirectory;
(function(BaseDirectory2) {
  BaseDirectory2[BaseDirectory2["Audio"] = 1] = "Audio";
  BaseDirectory2[BaseDirectory2["Cache"] = 2] = "Cache";
  BaseDirectory2[BaseDirectory2["Config"] = 3] = "Config";
  BaseDirectory2[BaseDirectory2["Data"] = 4] = "Data";
  BaseDirectory2[BaseDirectory2["LocalData"] = 5] = "LocalData";
  BaseDirectory2[BaseDirectory2["Desktop"] = 6] = "Desktop";
  BaseDirectory2[BaseDirectory2["Document"] = 7] = "Document";
  BaseDirectory2[BaseDirectory2["Download"] = 8] = "Download";
  BaseDirectory2[BaseDirectory2["Executable"] = 9] = "Executable";
  BaseDirectory2[BaseDirectory2["Font"] = 10] = "Font";
  BaseDirectory2[BaseDirectory2["Home"] = 11] = "Home";
  BaseDirectory2[BaseDirectory2["Picture"] = 12] = "Picture";
  BaseDirectory2[BaseDirectory2["Public"] = 13] = "Public";
  BaseDirectory2[BaseDirectory2["Runtime"] = 14] = "Runtime";
  BaseDirectory2[BaseDirectory2["Template"] = 15] = "Template";
  BaseDirectory2[BaseDirectory2["Video"] = 16] = "Video";
  BaseDirectory2[BaseDirectory2["Resource"] = 17] = "Resource";
  BaseDirectory2[BaseDirectory2["App"] = 18] = "App";
  BaseDirectory2[BaseDirectory2["Log"] = 19] = "Log";
  BaseDirectory2[BaseDirectory2["Temp"] = 20] = "Temp";
  BaseDirectory2[BaseDirectory2["AppConfig"] = 21] = "AppConfig";
  BaseDirectory2[BaseDirectory2["AppData"] = 22] = "AppData";
  BaseDirectory2[BaseDirectory2["AppLocalData"] = 23] = "AppLocalData";
  BaseDirectory2[BaseDirectory2["AppCache"] = 24] = "AppCache";
  BaseDirectory2[BaseDirectory2["AppLog"] = 25] = "AppLog";
})(BaseDirectory || (BaseDirectory = {}));
async function readTextFile(filePath, options = {}) {
  return invokeTauriCommand({
    __tauriModule: "Fs",
    message: {
      cmd: "readTextFile",
      path: filePath,
      options
    }
  });
}
async function readBinaryFile(filePath, options = {}) {
  const arr = await invokeTauriCommand({
    __tauriModule: "Fs",
    message: {
      cmd: "readFile",
      path: filePath,
      options
    }
  });
  return Uint8Array.from(arr);
}
async function writeTextFile(path, contents, options) {
  if (typeof options === "object") {
    Object.freeze(options);
  }
  if (typeof path === "object") {
    Object.freeze(path);
  }
  const file = { path: "", contents: "" };
  let fileOptions = options;
  if (typeof path === "string") {
    file.path = path;
  } else {
    file.path = path.path;
    file.contents = path.contents;
  }
  if (typeof contents === "string") {
    file.contents = contents !== null && contents !== void 0 ? contents : "";
  } else {
    fileOptions = contents;
  }
  return invokeTauriCommand({
    __tauriModule: "Fs",
    message: {
      cmd: "writeFile",
      path: file.path,
      contents: Array.from(new TextEncoder().encode(file.contents)),
      options: fileOptions
    }
  });
}
async function writeBinaryFile(path, contents, options) {
  if (typeof options === "object") {
    Object.freeze(options);
  }
  if (typeof path === "object") {
    Object.freeze(path);
  }
  const file = { path: "", contents: [] };
  let fileOptions = options;
  if (typeof path === "string") {
    file.path = path;
  } else {
    file.path = path.path;
    file.contents = path.contents;
  }
  if (contents && "dir" in contents) {
    fileOptions = contents;
  } else if (typeof path === "string") {
    file.contents = contents !== null && contents !== void 0 ? contents : [];
  }
  return invokeTauriCommand({
    __tauriModule: "Fs",
    message: {
      cmd: "writeFile",
      path: file.path,
      contents: Array.from(file.contents instanceof ArrayBuffer ? new Uint8Array(file.contents) : file.contents),
      options: fileOptions
    }
  });
}
async function readDir(dir, options = {}) {
  return invokeTauriCommand({
    __tauriModule: "Fs",
    message: {
      cmd: "readDir",
      path: dir,
      options
    }
  });
}
async function createDir(dir, options = {}) {
  return invokeTauriCommand({
    __tauriModule: "Fs",
    message: {
      cmd: "createDir",
      path: dir,
      options
    }
  });
}
async function removeDir(dir, options = {}) {
  return invokeTauriCommand({
    __tauriModule: "Fs",
    message: {
      cmd: "removeDir",
      path: dir,
      options
    }
  });
}
async function copyFile(source, destination, options = {}) {
  return invokeTauriCommand({
    __tauriModule: "Fs",
    message: {
      cmd: "copyFile",
      source,
      destination,
      options
    }
  });
}
async function removeFile(file, options = {}) {
  return invokeTauriCommand({
    __tauriModule: "Fs",
    message: {
      cmd: "removeFile",
      path: file,
      options
    }
  });
}
async function renameFile(oldPath, newPath, options = {}) {
  return invokeTauriCommand({
    __tauriModule: "Fs",
    message: {
      cmd: "renameFile",
      oldPath,
      newPath,
      options
    }
  });
}
async function exists(path, options = {}) {
  return invokeTauriCommand({
    __tauriModule: "Fs",
    message: {
      cmd: "exists",
      path,
      options
    }
  });
}

// node_modules/@tauri-apps/api/globalShortcut.js
var globalShortcut_exports = {};
__export(globalShortcut_exports, {
  isRegistered: () => isRegistered,
  register: () => register,
  registerAll: () => registerAll,
  unregister: () => unregister,
  unregisterAll: () => unregisterAll
});
async function register(shortcut, handler) {
  return invokeTauriCommand({
    __tauriModule: "GlobalShortcut",
    message: {
      cmd: "register",
      shortcut,
      handler: transformCallback(handler)
    }
  });
}
async function registerAll(shortcuts, handler) {
  return invokeTauriCommand({
    __tauriModule: "GlobalShortcut",
    message: {
      cmd: "registerAll",
      shortcuts,
      handler: transformCallback(handler)
    }
  });
}
async function isRegistered(shortcut) {
  return invokeTauriCommand({
    __tauriModule: "GlobalShortcut",
    message: {
      cmd: "isRegistered",
      shortcut
    }
  });
}
async function unregister(shortcut) {
  return invokeTauriCommand({
    __tauriModule: "GlobalShortcut",
    message: {
      cmd: "unregister",
      shortcut
    }
  });
}
async function unregisterAll() {
  return invokeTauriCommand({
    __tauriModule: "GlobalShortcut",
    message: {
      cmd: "unregisterAll"
    }
  });
}

// node_modules/@tauri-apps/api/http.js
var http_exports = {};
__export(http_exports, {
  Body: () => Body,
  Client: () => Client,
  Response: () => Response,
  ResponseType: () => ResponseType,
  fetch: () => fetch,
  getClient: () => getClient
});
var ResponseType;
(function(ResponseType2) {
  ResponseType2[ResponseType2["JSON"] = 1] = "JSON";
  ResponseType2[ResponseType2["Text"] = 2] = "Text";
  ResponseType2[ResponseType2["Binary"] = 3] = "Binary";
})(ResponseType || (ResponseType = {}));
async function formBody(data) {
  const form = {};
  const append = async (key, v) => {
    if (v !== null) {
      let r;
      if (typeof v === "string") {
        r = v;
      } else if (v instanceof Uint8Array || Array.isArray(v)) {
        r = Array.from(v);
      } else if (v instanceof File) {
        r = {
          file: Array.from(new Uint8Array(await v.arrayBuffer())),
          mime: v.type,
          fileName: v.name
        };
      } else if (typeof v.file === "string") {
        r = { file: v.file, mime: v.mime, fileName: v.fileName };
      } else {
        r = { file: Array.from(v.file), mime: v.mime, fileName: v.fileName };
      }
      form[String(key)] = r;
    }
  };
  if (data instanceof FormData) {
    for (const [key, value] of data) {
      await append(key, value);
    }
  } else {
    for (const [key, value] of Object.entries(data)) {
      await append(key, value);
    }
  }
  return form;
}
var Body = class _Body {
  /** @ignore */
  constructor(type2, payload) {
    this.type = type2;
    this.payload = payload;
  }
  /**
   * Creates a new form data body. The form data is an object where each key is the entry name,
   * and the value is either a string or a file object.
   *
   * By default it sets the `application/x-www-form-urlencoded` Content-Type header,
   * but you can set it to `multipart/form-data` if the Cargo feature `http-multipart` is enabled.
   *
   * Note that a file path must be allowed in the `fs` allowlist scope.
   *
   * @example
   * ```typescript
   * import { Body } from "@tauri-apps/api/http"
   * const body = Body.form({
   *   key: 'value',
   *   image: {
   *     file: '/path/to/file', // either a path or an array buffer of the file contents
   *     mime: 'image/jpeg', // optional
   *     fileName: 'image.jpg' // optional
   *   }
   * });
   *
   * // alternatively, use a FormData:
   * const form = new FormData();
   * form.append('key', 'value');
   * form.append('image', file, 'image.png');
   * const formBody = Body.form(form);
   * ```
   *
   * @param data The body data.
   *
   * @returns The body object ready to be used on the POST and PUT requests.
   */
  static form(data) {
    return new _Body("Form", data);
  }
  /**
   * Creates a new JSON body.
   * @example
   * ```typescript
   * import { Body } from "@tauri-apps/api/http"
   * Body.json({
   *   registered: true,
   *   name: 'tauri'
   * });
   * ```
   *
   * @param data The body JSON object.
   *
   * @returns The body object ready to be used on the POST and PUT requests.
   */
  static json(data) {
    return new _Body("Json", data);
  }
  /**
   * Creates a new UTF-8 string body.
   * @example
   * ```typescript
   * import { Body } from "@tauri-apps/api/http"
   * Body.text('The body content as a string');
   * ```
   *
   * @param value The body string.
   *
   * @returns The body object ready to be used on the POST and PUT requests.
   */
  static text(value) {
    return new _Body("Text", value);
  }
  /**
   * Creates a new byte array body.
   * @example
   * ```typescript
   * import { Body } from "@tauri-apps/api/http"
   * Body.bytes(new Uint8Array([1, 2, 3]));
   * ```
   *
   * @param bytes The body byte array.
   *
   * @returns The body object ready to be used on the POST and PUT requests.
   */
  static bytes(bytes) {
    return new _Body("Bytes", Array.from(bytes instanceof ArrayBuffer ? new Uint8Array(bytes) : bytes));
  }
};
var Response = class {
  /** @ignore */
  constructor(response) {
    this.url = response.url;
    this.status = response.status;
    this.ok = this.status >= 200 && this.status < 300;
    this.headers = response.headers;
    this.rawHeaders = response.rawHeaders;
    this.data = response.data;
  }
};
var Client = class {
  /** @ignore */
  constructor(id) {
    this.id = id;
  }
  /**
   * Drops the client instance.
   * @example
   * ```typescript
   * import { getClient } from '@tauri-apps/api/http';
   * const client = await getClient();
   * await client.drop();
   * ```
   */
  async drop() {
    return invokeTauriCommand({
      __tauriModule: "Http",
      message: {
        cmd: "dropClient",
        client: this.id
      }
    });
  }
  /**
   * Makes an HTTP request.
   * @example
   * ```typescript
   * import { getClient } from '@tauri-apps/api/http';
   * const client = await getClient();
   * const response = await client.request({
   *   method: 'GET',
   *   url: 'http://localhost:3003/users',
   * });
   * ```
   */
  async request(options) {
    var _a;
    const jsonResponse = !options.responseType || options.responseType === ResponseType.JSON;
    if (jsonResponse) {
      options.responseType = ResponseType.Text;
    }
    if (((_a = options.body) === null || _a === void 0 ? void 0 : _a.type) === "Form") {
      options.body.payload = await formBody(options.body.payload);
    }
    return invokeTauriCommand({
      __tauriModule: "Http",
      message: {
        cmd: "httpRequest",
        client: this.id,
        options
      }
    }).then((res) => {
      const response = new Response(res);
      if (jsonResponse) {
        try {
          response.data = JSON.parse(response.data);
        } catch (e) {
          if (response.ok && response.data === "") {
            response.data = {};
          } else if (response.ok) {
            throw Error(`Failed to parse response \`${response.data}\` as JSON: ${e};
              try setting the \`responseType\` option to \`ResponseType.Text\` or \`ResponseType.Binary\` if the API does not return a JSON response.`);
          }
        }
        return response;
      }
      return response;
    });
  }
  /**
   * Makes a GET request.
   * @example
   * ```typescript
   * import { getClient, ResponseType } from '@tauri-apps/api/http';
   * const client = await getClient();
   * const response = await client.get('http://localhost:3003/users', {
   *   timeout: 30,
   *   // the expected response type
   *   responseType: ResponseType.JSON
   * });
   * ```
   */
  async get(url, options) {
    return this.request({
      method: "GET",
      url,
      ...options
    });
  }
  /**
   * Makes a POST request.
   * @example
   * ```typescript
   * import { getClient, Body, ResponseType } from '@tauri-apps/api/http';
   * const client = await getClient();
   * const response = await client.post('http://localhost:3003/users', {
   *   body: Body.json({
   *     name: 'tauri',
   *     password: 'awesome'
   *   }),
   *   // in this case the server returns a simple string
   *   responseType: ResponseType.Text,
   * });
   * ```
   */
  async post(url, body, options) {
    return this.request({
      method: "POST",
      url,
      body,
      ...options
    });
  }
  /**
   * Makes a PUT request.
   * @example
   * ```typescript
   * import { getClient, Body } from '@tauri-apps/api/http';
   * const client = await getClient();
   * const response = await client.put('http://localhost:3003/users/1', {
   *   body: Body.form({
   *     file: {
   *       file: '/home/<USER>/avatar.png',
   *       mime: 'image/png',
   *       fileName: 'avatar.png'
   *     }
   *   })
   * });
   * ```
   */
  async put(url, body, options) {
    return this.request({
      method: "PUT",
      url,
      body,
      ...options
    });
  }
  /**
   * Makes a PATCH request.
   * @example
   * ```typescript
   * import { getClient, Body } from '@tauri-apps/api/http';
   * const client = await getClient();
   * const response = await client.patch('http://localhost:3003/users/1', {
   *   body: Body.json({ email: '<EMAIL>' })
   * });
   * ```
   */
  async patch(url, options) {
    return this.request({
      method: "PATCH",
      url,
      ...options
    });
  }
  /**
   * Makes a DELETE request.
   * @example
   * ```typescript
   * import { getClient } from '@tauri-apps/api/http';
   * const client = await getClient();
   * const response = await client.delete('http://localhost:3003/users/1');
   * ```
   */
  async delete(url, options) {
    return this.request({
      method: "DELETE",
      url,
      ...options
    });
  }
};
async function getClient(options) {
  return invokeTauriCommand({
    __tauriModule: "Http",
    message: {
      cmd: "createClient",
      options
    }
  }).then((id) => new Client(id));
}
var defaultClient = null;
async function fetch(url, options) {
  var _a;
  if (defaultClient === null) {
    defaultClient = await getClient();
  }
  return defaultClient.request({
    url,
    method: (_a = options === null || options === void 0 ? void 0 : options.method) !== null && _a !== void 0 ? _a : "GET",
    ...options
  });
}

// node_modules/@tauri-apps/api/notification.js
var notification_exports = {};
__export(notification_exports, {
  isPermissionGranted: () => isPermissionGranted,
  requestPermission: () => requestPermission,
  sendNotification: () => sendNotification
});
async function isPermissionGranted() {
  if (window.Notification.permission !== "default") {
    return Promise.resolve(window.Notification.permission === "granted");
  }
  return invokeTauriCommand({
    __tauriModule: "Notification",
    message: {
      cmd: "isNotificationPermissionGranted"
    }
  });
}
async function requestPermission() {
  return window.Notification.requestPermission();
}
function sendNotification(options) {
  if (typeof options === "string") {
    new window.Notification(options);
  } else {
    new window.Notification(options.title, options);
  }
}

// node_modules/@tauri-apps/api/path.js
var path_exports = {};
__export(path_exports, {
  BaseDirectory: () => BaseDirectory,
  appCacheDir: () => appCacheDir,
  appConfigDir: () => appConfigDir,
  appDataDir: () => appDataDir,
  appDir: () => appDir,
  appLocalDataDir: () => appLocalDataDir,
  appLogDir: () => appLogDir,
  audioDir: () => audioDir,
  basename: () => basename,
  cacheDir: () => cacheDir,
  configDir: () => configDir,
  dataDir: () => dataDir,
  delimiter: () => delimiter,
  desktopDir: () => desktopDir,
  dirname: () => dirname,
  documentDir: () => documentDir,
  downloadDir: () => downloadDir,
  executableDir: () => executableDir,
  extname: () => extname,
  fontDir: () => fontDir,
  homeDir: () => homeDir,
  isAbsolute: () => isAbsolute,
  join: () => join,
  localDataDir: () => localDataDir,
  logDir: () => logDir,
  normalize: () => normalize,
  pictureDir: () => pictureDir,
  publicDir: () => publicDir,
  resolve: () => resolve,
  resolveResource: () => resolveResource,
  resourceDir: () => resourceDir,
  runtimeDir: () => runtimeDir,
  sep: () => sep,
  templateDir: () => templateDir,
  videoDir: () => videoDir
});

// node_modules/@tauri-apps/api/helpers/os-check.js
function isWindows() {
  return navigator.appVersion.includes("Win");
}

// node_modules/@tauri-apps/api/path.js
async function appDir() {
  return appConfigDir();
}
async function appConfigDir() {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolvePath",
      path: "",
      directory: BaseDirectory.AppConfig
    }
  });
}
async function appDataDir() {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolvePath",
      path: "",
      directory: BaseDirectory.AppData
    }
  });
}
async function appLocalDataDir() {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolvePath",
      path: "",
      directory: BaseDirectory.AppLocalData
    }
  });
}
async function appCacheDir() {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolvePath",
      path: "",
      directory: BaseDirectory.AppCache
    }
  });
}
async function audioDir() {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolvePath",
      path: "",
      directory: BaseDirectory.Audio
    }
  });
}
async function cacheDir() {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolvePath",
      path: "",
      directory: BaseDirectory.Cache
    }
  });
}
async function configDir() {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolvePath",
      path: "",
      directory: BaseDirectory.Config
    }
  });
}
async function dataDir() {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolvePath",
      path: "",
      directory: BaseDirectory.Data
    }
  });
}
async function desktopDir() {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolvePath",
      path: "",
      directory: BaseDirectory.Desktop
    }
  });
}
async function documentDir() {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolvePath",
      path: "",
      directory: BaseDirectory.Document
    }
  });
}
async function downloadDir() {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolvePath",
      path: "",
      directory: BaseDirectory.Download
    }
  });
}
async function executableDir() {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolvePath",
      path: "",
      directory: BaseDirectory.Executable
    }
  });
}
async function fontDir() {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolvePath",
      path: "",
      directory: BaseDirectory.Font
    }
  });
}
async function homeDir() {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolvePath",
      path: "",
      directory: BaseDirectory.Home
    }
  });
}
async function localDataDir() {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolvePath",
      path: "",
      directory: BaseDirectory.LocalData
    }
  });
}
async function pictureDir() {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolvePath",
      path: "",
      directory: BaseDirectory.Picture
    }
  });
}
async function publicDir() {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolvePath",
      path: "",
      directory: BaseDirectory.Public
    }
  });
}
async function resourceDir() {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolvePath",
      path: "",
      directory: BaseDirectory.Resource
    }
  });
}
async function resolveResource(resourcePath) {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolvePath",
      path: resourcePath,
      directory: BaseDirectory.Resource
    }
  });
}
async function runtimeDir() {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolvePath",
      path: "",
      directory: BaseDirectory.Runtime
    }
  });
}
async function templateDir() {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolvePath",
      path: "",
      directory: BaseDirectory.Template
    }
  });
}
async function videoDir() {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolvePath",
      path: "",
      directory: BaseDirectory.Video
    }
  });
}
async function logDir() {
  return appLogDir();
}
async function appLogDir() {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolvePath",
      path: "",
      directory: BaseDirectory.AppLog
    }
  });
}
var sep = isWindows() ? "\\" : "/";
var delimiter = isWindows() ? ";" : ":";
async function resolve(...paths) {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "resolve",
      paths
    }
  });
}
async function normalize(path) {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "normalize",
      path
    }
  });
}
async function join(...paths) {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "join",
      paths
    }
  });
}
async function dirname(path) {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "dirname",
      path
    }
  });
}
async function extname(path) {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "extname",
      path
    }
  });
}
async function basename(path, ext) {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "basename",
      path,
      ext
    }
  });
}
async function isAbsolute(path) {
  return invokeTauriCommand({
    __tauriModule: "Path",
    message: {
      cmd: "isAbsolute",
      path
    }
  });
}

// node_modules/@tauri-apps/api/process.js
var process_exports = {};
__export(process_exports, {
  exit: () => exit,
  relaunch: () => relaunch
});
async function exit(exitCode = 0) {
  return invokeTauriCommand({
    __tauriModule: "Process",
    message: {
      cmd: "exit",
      exitCode
    }
  });
}
async function relaunch() {
  return invokeTauriCommand({
    __tauriModule: "Process",
    message: {
      cmd: "relaunch"
    }
  });
}

// node_modules/@tauri-apps/api/shell.js
var shell_exports = {};
__export(shell_exports, {
  Child: () => Child,
  Command: () => Command,
  EventEmitter: () => EventEmitter,
  open: () => open2
});
var EventEmitter = class {
  constructor() {
    this.eventListeners = /* @__PURE__ */ Object.create(null);
  }
  /**
   * Alias for `emitter.on(eventName, listener)`.
   *
   * @since 1.1.0
   */
  addListener(eventName, listener) {
    return this.on(eventName, listener);
  }
  /**
   * Alias for `emitter.off(eventName, listener)`.
   *
   * @since 1.1.0
   */
  removeListener(eventName, listener) {
    return this.off(eventName, listener);
  }
  /**
   * Adds the `listener` function to the end of the listeners array for the
   * event named `eventName`. No checks are made to see if the `listener` has
   * already been added. Multiple calls passing the same combination of `eventName`and `listener` will result in the `listener` being added, and called, multiple
   * times.
   *
   * Returns a reference to the `EventEmitter`, so that calls can be chained.
   *
   * @since 1.0.0
   */
  on(eventName, listener) {
    if (eventName in this.eventListeners) {
      this.eventListeners[eventName].push(listener);
    } else {
      this.eventListeners[eventName] = [listener];
    }
    return this;
  }
  /**
   * Adds a **one-time**`listener` function for the event named `eventName`. The
   * next time `eventName` is triggered, this listener is removed and then invoked.
   *
   * Returns a reference to the `EventEmitter`, so that calls can be chained.
   *
   * @since 1.1.0
   */
  once(eventName, listener) {
    const wrapper = (...args) => {
      this.removeListener(eventName, wrapper);
      listener(...args);
    };
    return this.addListener(eventName, wrapper);
  }
  /**
   * Removes the all specified listener from the listener array for the event eventName
   * Returns a reference to the `EventEmitter`, so that calls can be chained.
   *
   * @since 1.1.0
   */
  off(eventName, listener) {
    if (eventName in this.eventListeners) {
      this.eventListeners[eventName] = this.eventListeners[eventName].filter((l) => l !== listener);
    }
    return this;
  }
  /**
   * Removes all listeners, or those of the specified eventName.
   *
   * Returns a reference to the `EventEmitter`, so that calls can be chained.
   *
   * @since 1.1.0
   */
  removeAllListeners(event) {
    if (event) {
      delete this.eventListeners[event];
    } else {
      this.eventListeners = /* @__PURE__ */ Object.create(null);
    }
    return this;
  }
  /**
   * @ignore
   * Synchronously calls each of the listeners registered for the event named`eventName`, in the order they were registered, passing the supplied arguments
   * to each.
   *
   * @returns `true` if the event had listeners, `false` otherwise.
   */
  emit(eventName, ...args) {
    if (eventName in this.eventListeners) {
      const listeners = this.eventListeners[eventName];
      for (const listener of listeners)
        listener(...args);
      return true;
    }
    return false;
  }
  /**
   * Returns the number of listeners listening to the event named `eventName`.
   *
   * @since 1.1.0
   */
  listenerCount(eventName) {
    if (eventName in this.eventListeners)
      return this.eventListeners[eventName].length;
    return 0;
  }
  /**
   * Adds the `listener` function to the _beginning_ of the listeners array for the
   * event named `eventName`. No checks are made to see if the `listener` has
   * already been added. Multiple calls passing the same combination of `eventName`and `listener` will result in the `listener` being added, and called, multiple
   * times.
   *
   * Returns a reference to the `EventEmitter`, so that calls can be chained.
   *
   * @since 1.1.0
   */
  prependListener(eventName, listener) {
    if (eventName in this.eventListeners) {
      this.eventListeners[eventName].unshift(listener);
    } else {
      this.eventListeners[eventName] = [listener];
    }
    return this;
  }
  /**
   * Adds a **one-time**`listener` function for the event named `eventName` to the_beginning_ of the listeners array. The next time `eventName` is triggered, this
   * listener is removed, and then invoked.
   *
   * Returns a reference to the `EventEmitter`, so that calls can be chained.
   *
   * @since 1.1.0
   */
  prependOnceListener(eventName, listener) {
    const wrapper = (...args) => {
      this.removeListener(eventName, wrapper);
      listener(...args);
    };
    return this.prependListener(eventName, wrapper);
  }
};
var Child = class {
  constructor(pid) {
    this.pid = pid;
  }
  /**
   * Writes `data` to the `stdin`.
   *
   * @param data The message to write, either a string or a byte array.
   * @example
   * ```typescript
   * import { Command } from '@tauri-apps/api/shell';
   * const command = new Command('node');
   * const child = await command.spawn();
   * await child.write('message');
   * await child.write([0, 1, 2, 3, 4, 5]);
   * ```
   *
   * @returns A promise indicating the success or failure of the operation.
   */
  async write(data) {
    return invokeTauriCommand({
      __tauriModule: "Shell",
      message: {
        cmd: "stdinWrite",
        pid: this.pid,
        // correctly serialize Uint8Arrays
        buffer: typeof data === "string" ? data : Array.from(data)
      }
    });
  }
  /**
   * Kills the child process.
   *
   * @returns A promise indicating the success or failure of the operation.
   */
  async kill() {
    return invokeTauriCommand({
      __tauriModule: "Shell",
      message: {
        cmd: "killChild",
        pid: this.pid
      }
    });
  }
};
var Command = class _Command extends EventEmitter {
  /**
   * Creates a new `Command` instance.
   *
   * @param program The program name to execute.
   * It must be configured on `tauri.conf.json > tauri > allowlist > shell > scope`.
   * @param args Program arguments.
   * @param options Spawn options.
   */
  constructor(program, args = [], options) {
    super();
    this.stdout = new EventEmitter();
    this.stderr = new EventEmitter();
    this.program = program;
    this.args = typeof args === "string" ? [args] : args;
    this.options = options !== null && options !== void 0 ? options : {};
  }
  /**
   * Creates a command to execute the given sidecar program.
   * @example
   * ```typescript
   * import { Command } from '@tauri-apps/api/shell';
   * const command = Command.sidecar('my-sidecar');
   * const output = await command.execute();
   * ```
   *
   * @param program The program to execute.
   * It must be configured on `tauri.conf.json > tauri > allowlist > shell > scope`.
   */
  static sidecar(program, args = [], options) {
    const instance = new _Command(program, args, options);
    instance.options.sidecar = true;
    return instance;
  }
  /**
   * Executes the command as a child process, returning a handle to it.
   *
   * @returns A promise resolving to the child process handle.
   */
  async spawn() {
    const program = this.program;
    const args = this.args;
    const options = this.options;
    if (typeof args === "object") {
      Object.freeze(args);
    }
    const onEvent = (event) => {
      switch (event.event) {
        case "Error":
          this.emit("error", event.payload);
          break;
        case "Terminated":
          this.emit("close", event.payload);
          break;
        case "Stdout":
          this.stdout.emit("data", event.payload);
          break;
        case "Stderr":
          this.stderr.emit("data", event.payload);
          break;
      }
    };
    return invokeTauriCommand({
      __tauriModule: "Shell",
      message: {
        cmd: "execute",
        program,
        args,
        options,
        onEventFn: transformCallback(onEvent)
      }
    }).then((pid) => new Child(pid));
  }
  /**
   * Executes the command as a child process, waiting for it to finish and collecting all of its output.
   * @example
   * ```typescript
   * import { Command } from '@tauri-apps/api/shell';
   * const output = await new Command('echo', 'message').execute();
   * assert(output.code === 0);
   * assert(output.signal === null);
   * assert(output.stdout === 'message');
   * assert(output.stderr === '');
   * ```
   *
   * @returns A promise resolving to the child process output.
   */
  async execute() {
    const program = this.program;
    const args = this.args;
    const options = this.options;
    if (typeof args === "object") {
      Object.freeze(args);
    }
    return invokeTauriCommand({
      __tauriModule: "Shell",
      message: {
        cmd: "executeAndReturn",
        program,
        args,
        options
      }
    });
  }
};
async function open2(path, openWith) {
  return invokeTauriCommand({
    __tauriModule: "Shell",
    message: {
      cmd: "open",
      path,
      with: openWith
    }
  });
}

// node_modules/@tauri-apps/api/updater.js
var updater_exports = {};
__export(updater_exports, {
  checkUpdate: () => checkUpdate,
  installUpdate: () => installUpdate,
  onUpdaterEvent: () => onUpdaterEvent
});
async function onUpdaterEvent(handler) {
  return listen(TauriEvent.STATUS_UPDATE, (data) => {
    handler(data === null || data === void 0 ? void 0 : data.payload);
  });
}
async function installUpdate() {
  let unlistenerFn;
  function cleanListener() {
    if (unlistenerFn) {
      unlistenerFn();
    }
    unlistenerFn = void 0;
  }
  return new Promise((resolve2, reject) => {
    function onStatusChange(statusResult) {
      if (statusResult.error) {
        cleanListener();
        reject(statusResult.error);
        return;
      }
      if (statusResult.status === "DONE") {
        cleanListener();
        resolve2();
      }
    }
    onUpdaterEvent(onStatusChange).then((fn) => {
      unlistenerFn = fn;
    }).catch((e) => {
      cleanListener();
      throw e;
    });
    emit(TauriEvent.INSTALL_UPDATE).catch((e) => {
      cleanListener();
      throw e;
    });
  });
}
async function checkUpdate() {
  let unlistenerFn;
  function cleanListener() {
    if (unlistenerFn) {
      unlistenerFn();
    }
    unlistenerFn = void 0;
  }
  return new Promise((resolve2, reject) => {
    function onUpdateAvailable(manifest) {
      cleanListener();
      resolve2({
        manifest,
        shouldUpdate: true
      });
    }
    function onStatusChange(statusResult) {
      if (statusResult.error) {
        cleanListener();
        reject(statusResult.error);
        return;
      }
      if (statusResult.status === "UPTODATE") {
        cleanListener();
        resolve2({
          shouldUpdate: false
        });
      }
    }
    once(TauriEvent.UPDATE_AVAILABLE, (data) => {
      onUpdateAvailable(data === null || data === void 0 ? void 0 : data.payload);
    }).catch((e) => {
      cleanListener();
      throw e;
    });
    onUpdaterEvent(onStatusChange).then((fn) => {
      unlistenerFn = fn;
    }).catch((e) => {
      cleanListener();
      throw e;
    });
    emit(TauriEvent.CHECK_UPDATE).catch((e) => {
      cleanListener();
      throw e;
    });
  });
}

// node_modules/@tauri-apps/api/os.js
var os_exports = {};
__export(os_exports, {
  EOL: () => EOL,
  arch: () => arch,
  locale: () => locale,
  platform: () => platform,
  tempdir: () => tempdir,
  type: () => type,
  version: () => version
});
var EOL = isWindows() ? "\r\n" : "\n";
async function platform() {
  return invokeTauriCommand({
    __tauriModule: "Os",
    message: {
      cmd: "platform"
    }
  });
}
async function version() {
  return invokeTauriCommand({
    __tauriModule: "Os",
    message: {
      cmd: "version"
    }
  });
}
async function type() {
  return invokeTauriCommand({
    __tauriModule: "Os",
    message: {
      cmd: "osType"
    }
  });
}
async function arch() {
  return invokeTauriCommand({
    __tauriModule: "Os",
    message: {
      cmd: "arch"
    }
  });
}
async function tempdir() {
  return invokeTauriCommand({
    __tauriModule: "Os",
    message: {
      cmd: "tempdir"
    }
  });
}
async function locale() {
  return invokeTauriCommand({
    __tauriModule: "Os",
    message: {
      cmd: "locale"
    }
  });
}

// node_modules/@tauri-apps/api/index.js
var invoke2 = invoke;
export {
  app_exports as app,
  cli_exports as cli,
  clipboard_exports as clipboard,
  dialog_exports as dialog,
  event_exports as event,
  fs_exports as fs,
  globalShortcut_exports as globalShortcut,
  http_exports as http,
  invoke2 as invoke,
  notification_exports as notification,
  os_exports as os,
  path_exports as path,
  process_exports as process,
  shell_exports as shell,
  tauri_exports as tauri,
  updater_exports as updater,
  window_exports as window
};
//# sourceMappingURL=@tauri-apps_api.js.map

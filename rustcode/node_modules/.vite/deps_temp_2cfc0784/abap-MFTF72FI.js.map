{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/abap/abap.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/abap/abap.ts\nvar conf = {\n  comments: {\n    lineComment: \"*\"\n  },\n  brackets: [\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ]\n};\nvar language = {\n  defaultToken: \"invalid\",\n  ignoreCase: true,\n  tokenPostfix: \".abap\",\n  keywords: [\n    \"abap-source\",\n    \"abbreviated\",\n    \"abstract\",\n    \"accept\",\n    \"accepting\",\n    \"according\",\n    \"activation\",\n    \"actual\",\n    \"add\",\n    \"add-corresponding\",\n    \"adjacent\",\n    \"after\",\n    \"alias\",\n    \"aliases\",\n    \"align\",\n    \"all\",\n    \"allocate\",\n    \"alpha\",\n    \"analysis\",\n    \"analyzer\",\n    \"and\",\n    \"append\",\n    \"appendage\",\n    \"appending\",\n    \"application\",\n    \"archive\",\n    \"area\",\n    \"arithmetic\",\n    \"as\",\n    \"ascending\",\n    \"aspect\",\n    \"assert\",\n    \"assign\",\n    \"assigned\",\n    \"assigning\",\n    \"association\",\n    \"asynchronous\",\n    \"at\",\n    \"attributes\",\n    \"authority\",\n    \"authority-check\",\n    \"avg\",\n    \"back\",\n    \"background\",\n    \"backup\",\n    \"backward\",\n    \"badi\",\n    \"base\",\n    \"before\",\n    \"begin\",\n    \"between\",\n    \"big\",\n    \"binary\",\n    \"bintohex\",\n    \"bit\",\n    \"black\",\n    \"blank\",\n    \"blanks\",\n    \"blob\",\n    \"block\",\n    \"blocks\",\n    \"blue\",\n    \"bound\",\n    \"boundaries\",\n    \"bounds\",\n    \"boxed\",\n    \"break-point\",\n    \"buffer\",\n    \"by\",\n    \"bypassing\",\n    \"byte\",\n    \"byte-order\",\n    \"call\",\n    \"calling\",\n    \"case\",\n    \"cast\",\n    \"casting\",\n    \"catch\",\n    \"center\",\n    \"centered\",\n    \"chain\",\n    \"chain-input\",\n    \"chain-request\",\n    \"change\",\n    \"changing\",\n    \"channels\",\n    \"character\",\n    \"char-to-hex\",\n    \"check\",\n    \"checkbox\",\n    \"ci_\",\n    \"circular\",\n    \"class\",\n    \"class-coding\",\n    \"class-data\",\n    \"class-events\",\n    \"class-methods\",\n    \"class-pool\",\n    \"cleanup\",\n    \"clear\",\n    \"client\",\n    \"clob\",\n    \"clock\",\n    \"close\",\n    \"coalesce\",\n    \"code\",\n    \"coding\",\n    \"col_background\",\n    \"col_group\",\n    \"col_heading\",\n    \"col_key\",\n    \"col_negative\",\n    \"col_normal\",\n    \"col_positive\",\n    \"col_total\",\n    \"collect\",\n    \"color\",\n    \"column\",\n    \"columns\",\n    \"comment\",\n    \"comments\",\n    \"commit\",\n    \"common\",\n    \"communication\",\n    \"comparing\",\n    \"component\",\n    \"components\",\n    \"compression\",\n    \"compute\",\n    \"concat\",\n    \"concat_with_space\",\n    \"concatenate\",\n    \"cond\",\n    \"condense\",\n    \"condition\",\n    \"connect\",\n    \"connection\",\n    \"constants\",\n    \"context\",\n    \"contexts\",\n    \"continue\",\n    \"control\",\n    \"controls\",\n    \"conv\",\n    \"conversion\",\n    \"convert\",\n    \"copies\",\n    \"copy\",\n    \"corresponding\",\n    \"country\",\n    \"cover\",\n    \"cpi\",\n    \"create\",\n    \"creating\",\n    \"critical\",\n    \"currency\",\n    \"currency_conversion\",\n    \"current\",\n    \"cursor\",\n    \"cursor-selection\",\n    \"customer\",\n    \"customer-function\",\n    \"dangerous\",\n    \"data\",\n    \"database\",\n    \"datainfo\",\n    \"dataset\",\n    \"date\",\n    \"dats_add_days\",\n    \"dats_add_months\",\n    \"dats_days_between\",\n    \"dats_is_valid\",\n    \"daylight\",\n    \"dd/mm/yy\",\n    \"dd/mm/yyyy\",\n    \"ddmmyy\",\n    \"deallocate\",\n    \"decimal_shift\",\n    \"decimals\",\n    \"declarations\",\n    \"deep\",\n    \"default\",\n    \"deferred\",\n    \"define\",\n    \"defining\",\n    \"definition\",\n    \"delete\",\n    \"deleting\",\n    \"demand\",\n    \"department\",\n    \"descending\",\n    \"describe\",\n    \"destination\",\n    \"detail\",\n    \"dialog\",\n    \"directory\",\n    \"disconnect\",\n    \"display\",\n    \"display-mode\",\n    \"distinct\",\n    \"divide\",\n    \"divide-corresponding\",\n    \"division\",\n    \"do\",\n    \"dummy\",\n    \"duplicate\",\n    \"duplicates\",\n    \"duration\",\n    \"during\",\n    \"dynamic\",\n    \"dynpro\",\n    \"edit\",\n    \"editor-call\",\n    \"else\",\n    \"elseif\",\n    \"empty\",\n    \"enabled\",\n    \"enabling\",\n    \"encoding\",\n    \"end\",\n    \"endat\",\n    \"endcase\",\n    \"endcatch\",\n    \"endchain\",\n    \"endclass\",\n    \"enddo\",\n    \"endenhancement\",\n    \"end-enhancement-section\",\n    \"endexec\",\n    \"endform\",\n    \"endfunction\",\n    \"endian\",\n    \"endif\",\n    \"ending\",\n    \"endinterface\",\n    \"end-lines\",\n    \"endloop\",\n    \"endmethod\",\n    \"endmodule\",\n    \"end-of-definition\",\n    \"end-of-editing\",\n    \"end-of-file\",\n    \"end-of-page\",\n    \"end-of-selection\",\n    \"endon\",\n    \"endprovide\",\n    \"endselect\",\n    \"end-test-injection\",\n    \"end-test-seam\",\n    \"endtry\",\n    \"endwhile\",\n    \"endwith\",\n    \"engineering\",\n    \"enhancement\",\n    \"enhancement-point\",\n    \"enhancements\",\n    \"enhancement-section\",\n    \"entries\",\n    \"entry\",\n    \"enum\",\n    \"environment\",\n    \"equiv\",\n    \"errormessage\",\n    \"errors\",\n    \"escaping\",\n    \"event\",\n    \"events\",\n    \"exact\",\n    \"except\",\n    \"exception\",\n    \"exceptions\",\n    \"exception-table\",\n    \"exclude\",\n    \"excluding\",\n    \"exec\",\n    \"execute\",\n    \"exists\",\n    \"exit\",\n    \"exit-command\",\n    \"expand\",\n    \"expanding\",\n    \"expiration\",\n    \"explicit\",\n    \"exponent\",\n    \"export\",\n    \"exporting\",\n    \"extend\",\n    \"extended\",\n    \"extension\",\n    \"extract\",\n    \"fail\",\n    \"fetch\",\n    \"field\",\n    \"field-groups\",\n    \"fields\",\n    \"field-symbol\",\n    \"field-symbols\",\n    \"file\",\n    \"filter\",\n    \"filters\",\n    \"filter-table\",\n    \"final\",\n    \"find\",\n    \"first\",\n    \"first-line\",\n    \"fixed-point\",\n    \"fkeq\",\n    \"fkge\",\n    \"flush\",\n    \"font\",\n    \"for\",\n    \"form\",\n    \"format\",\n    \"forward\",\n    \"found\",\n    \"frame\",\n    \"frames\",\n    \"free\",\n    \"friends\",\n    \"from\",\n    \"function\",\n    \"functionality\",\n    \"function-pool\",\n    \"further\",\n    \"gaps\",\n    \"generate\",\n    \"get\",\n    \"giving\",\n    \"gkeq\",\n    \"gkge\",\n    \"global\",\n    \"grant\",\n    \"green\",\n    \"group\",\n    \"groups\",\n    \"handle\",\n    \"handler\",\n    \"harmless\",\n    \"hashed\",\n    \"having\",\n    \"hdb\",\n    \"header\",\n    \"headers\",\n    \"heading\",\n    \"head-lines\",\n    \"help-id\",\n    \"help-request\",\n    \"hextobin\",\n    \"hide\",\n    \"high\",\n    \"hint\",\n    \"hold\",\n    \"hotspot\",\n    \"icon\",\n    \"id\",\n    \"identification\",\n    \"identifier\",\n    \"ids\",\n    \"if\",\n    \"ignore\",\n    \"ignoring\",\n    \"immediately\",\n    \"implementation\",\n    \"implementations\",\n    \"implemented\",\n    \"implicit\",\n    \"import\",\n    \"importing\",\n    \"in\",\n    \"inactive\",\n    \"incl\",\n    \"include\",\n    \"includes\",\n    \"including\",\n    \"increment\",\n    \"index\",\n    \"index-line\",\n    \"infotypes\",\n    \"inheriting\",\n    \"init\",\n    \"initial\",\n    \"initialization\",\n    \"inner\",\n    \"inout\",\n    \"input\",\n    \"insert\",\n    \"instance\",\n    \"instances\",\n    \"instr\",\n    \"intensified\",\n    \"interface\",\n    \"interface-pool\",\n    \"interfaces\",\n    \"internal\",\n    \"intervals\",\n    \"into\",\n    \"inverse\",\n    \"inverted-date\",\n    \"is\",\n    \"iso\",\n    \"job\",\n    \"join\",\n    \"keep\",\n    \"keeping\",\n    \"kernel\",\n    \"key\",\n    \"keys\",\n    \"keywords\",\n    \"kind\",\n    \"language\",\n    \"last\",\n    \"late\",\n    \"layout\",\n    \"leading\",\n    \"leave\",\n    \"left\",\n    \"left-justified\",\n    \"leftplus\",\n    \"leftspace\",\n    \"legacy\",\n    \"length\",\n    \"let\",\n    \"level\",\n    \"levels\",\n    \"like\",\n    \"line\",\n    \"lines\",\n    \"line-count\",\n    \"linefeed\",\n    \"line-selection\",\n    \"line-size\",\n    \"list\",\n    \"listbox\",\n    \"list-processing\",\n    \"little\",\n    \"llang\",\n    \"load\",\n    \"load-of-program\",\n    \"lob\",\n    \"local\",\n    \"locale\",\n    \"locator\",\n    \"logfile\",\n    \"logical\",\n    \"log-point\",\n    \"long\",\n    \"loop\",\n    \"low\",\n    \"lower\",\n    \"lpad\",\n    \"lpi\",\n    \"ltrim\",\n    \"mail\",\n    \"main\",\n    \"major-id\",\n    \"mapping\",\n    \"margin\",\n    \"mark\",\n    \"mask\",\n    \"match\",\n    \"matchcode\",\n    \"max\",\n    \"maximum\",\n    \"medium\",\n    \"members\",\n    \"memory\",\n    \"mesh\",\n    \"message\",\n    \"message-id\",\n    \"messages\",\n    \"messaging\",\n    \"method\",\n    \"methods\",\n    \"min\",\n    \"minimum\",\n    \"minor-id\",\n    \"mm/dd/yy\",\n    \"mm/dd/yyyy\",\n    \"mmddyy\",\n    \"mode\",\n    \"modif\",\n    \"modifier\",\n    \"modify\",\n    \"module\",\n    \"move\",\n    \"move-corresponding\",\n    \"multiply\",\n    \"multiply-corresponding\",\n    \"name\",\n    \"nametab\",\n    \"native\",\n    \"nested\",\n    \"nesting\",\n    \"new\",\n    \"new-line\",\n    \"new-page\",\n    \"new-section\",\n    \"next\",\n    \"no\",\n    \"no-display\",\n    \"no-extension\",\n    \"no-gap\",\n    \"no-gaps\",\n    \"no-grouping\",\n    \"no-heading\",\n    \"no-scrolling\",\n    \"no-sign\",\n    \"no-title\",\n    \"no-topofpage\",\n    \"no-zero\",\n    \"node\",\n    \"nodes\",\n    \"non-unicode\",\n    \"non-unique\",\n    \"not\",\n    \"null\",\n    \"number\",\n    \"object\",\n    \"objects\",\n    \"obligatory\",\n    \"occurrence\",\n    \"occurrences\",\n    \"occurs\",\n    \"of\",\n    \"off\",\n    \"offset\",\n    \"ole\",\n    \"on\",\n    \"only\",\n    \"open\",\n    \"option\",\n    \"optional\",\n    \"options\",\n    \"or\",\n    \"order\",\n    \"other\",\n    \"others\",\n    \"out\",\n    \"outer\",\n    \"output\",\n    \"output-length\",\n    \"overflow\",\n    \"overlay\",\n    \"pack\",\n    \"package\",\n    \"pad\",\n    \"padding\",\n    \"page\",\n    \"pages\",\n    \"parameter\",\n    \"parameters\",\n    \"parameter-table\",\n    \"part\",\n    \"partially\",\n    \"pattern\",\n    \"percentage\",\n    \"perform\",\n    \"performing\",\n    \"person\",\n    \"pf1\",\n    \"pf10\",\n    \"pf11\",\n    \"pf12\",\n    \"pf13\",\n    \"pf14\",\n    \"pf15\",\n    \"pf2\",\n    \"pf3\",\n    \"pf4\",\n    \"pf5\",\n    \"pf6\",\n    \"pf7\",\n    \"pf8\",\n    \"pf9\",\n    \"pf-status\",\n    \"pink\",\n    \"places\",\n    \"pool\",\n    \"pos_high\",\n    \"pos_low\",\n    \"position\",\n    \"pragmas\",\n    \"precompiled\",\n    \"preferred\",\n    \"preserving\",\n    \"primary\",\n    \"print\",\n    \"print-control\",\n    \"priority\",\n    \"private\",\n    \"procedure\",\n    \"process\",\n    \"program\",\n    \"property\",\n    \"protected\",\n    \"provide\",\n    \"public\",\n    \"push\",\n    \"pushbutton\",\n    \"put\",\n    \"queue-only\",\n    \"quickinfo\",\n    \"radiobutton\",\n    \"raise\",\n    \"raising\",\n    \"range\",\n    \"ranges\",\n    \"read\",\n    \"reader\",\n    \"read-only\",\n    \"receive\",\n    \"received\",\n    \"receiver\",\n    \"receiving\",\n    \"red\",\n    \"redefinition\",\n    \"reduce\",\n    \"reduced\",\n    \"ref\",\n    \"reference\",\n    \"refresh\",\n    \"regex\",\n    \"reject\",\n    \"remote\",\n    \"renaming\",\n    \"replace\",\n    \"replacement\",\n    \"replacing\",\n    \"report\",\n    \"request\",\n    \"requested\",\n    \"reserve\",\n    \"reset\",\n    \"resolution\",\n    \"respecting\",\n    \"responsible\",\n    \"result\",\n    \"results\",\n    \"resumable\",\n    \"resume\",\n    \"retry\",\n    \"return\",\n    \"returncode\",\n    \"returning\",\n    \"returns\",\n    \"right\",\n    \"right-justified\",\n    \"rightplus\",\n    \"rightspace\",\n    \"risk\",\n    \"rmc_communication_failure\",\n    \"rmc_invalid_status\",\n    \"rmc_system_failure\",\n    \"role\",\n    \"rollback\",\n    \"rows\",\n    \"rpad\",\n    \"rtrim\",\n    \"run\",\n    \"sap\",\n    \"sap-spool\",\n    \"saving\",\n    \"scale_preserving\",\n    \"scale_preserving_scientific\",\n    \"scan\",\n    \"scientific\",\n    \"scientific_with_leading_zero\",\n    \"scroll\",\n    \"scroll-boundary\",\n    \"scrolling\",\n    \"search\",\n    \"secondary\",\n    \"seconds\",\n    \"section\",\n    \"select\",\n    \"selection\",\n    \"selections\",\n    \"selection-screen\",\n    \"selection-set\",\n    \"selection-sets\",\n    \"selection-table\",\n    \"select-options\",\n    \"send\",\n    \"separate\",\n    \"separated\",\n    \"set\",\n    \"shared\",\n    \"shift\",\n    \"short\",\n    \"shortdump-id\",\n    \"sign_as_postfix\",\n    \"single\",\n    \"size\",\n    \"skip\",\n    \"skipping\",\n    \"smart\",\n    \"some\",\n    \"sort\",\n    \"sortable\",\n    \"sorted\",\n    \"source\",\n    \"specified\",\n    \"split\",\n    \"spool\",\n    \"spots\",\n    \"sql\",\n    \"sqlscript\",\n    \"stable\",\n    \"stamp\",\n    \"standard\",\n    \"starting\",\n    \"start-of-editing\",\n    \"start-of-selection\",\n    \"state\",\n    \"statement\",\n    \"statements\",\n    \"static\",\n    \"statics\",\n    \"statusinfo\",\n    \"step-loop\",\n    \"stop\",\n    \"structure\",\n    \"structures\",\n    \"style\",\n    \"subkey\",\n    \"submatches\",\n    \"submit\",\n    \"subroutine\",\n    \"subscreen\",\n    \"subtract\",\n    \"subtract-corresponding\",\n    \"suffix\",\n    \"sum\",\n    \"summary\",\n    \"summing\",\n    \"supplied\",\n    \"supply\",\n    \"suppress\",\n    \"switch\",\n    \"switchstates\",\n    \"symbol\",\n    \"syncpoints\",\n    \"syntax\",\n    \"syntax-check\",\n    \"syntax-trace\",\n    \"system-call\",\n    \"system-exceptions\",\n    \"system-exit\",\n    \"tab\",\n    \"tabbed\",\n    \"table\",\n    \"tables\",\n    \"tableview\",\n    \"tabstrip\",\n    \"target\",\n    \"task\",\n    \"tasks\",\n    \"test\",\n    \"testing\",\n    \"test-injection\",\n    \"test-seam\",\n    \"text\",\n    \"textpool\",\n    \"then\",\n    \"throw\",\n    \"time\",\n    \"times\",\n    \"timestamp\",\n    \"timezone\",\n    \"tims_is_valid\",\n    \"title\",\n    \"titlebar\",\n    \"title-lines\",\n    \"to\",\n    \"tokenization\",\n    \"tokens\",\n    \"top-lines\",\n    \"top-of-page\",\n    \"trace-file\",\n    \"trace-table\",\n    \"trailing\",\n    \"transaction\",\n    \"transfer\",\n    \"transformation\",\n    \"translate\",\n    \"transporting\",\n    \"trmac\",\n    \"truncate\",\n    \"truncation\",\n    \"try\",\n    \"tstmp_add_seconds\",\n    \"tstmp_current_utctimestamp\",\n    \"tstmp_is_valid\",\n    \"tstmp_seconds_between\",\n    \"type\",\n    \"type-pool\",\n    \"type-pools\",\n    \"types\",\n    \"uline\",\n    \"unassign\",\n    \"under\",\n    \"unicode\",\n    \"union\",\n    \"unique\",\n    \"unit_conversion\",\n    \"unix\",\n    \"unpack\",\n    \"until\",\n    \"unwind\",\n    \"up\",\n    \"update\",\n    \"upper\",\n    \"user\",\n    \"user-command\",\n    \"using\",\n    \"utf-8\",\n    \"valid\",\n    \"value\",\n    \"value-request\",\n    \"values\",\n    \"vary\",\n    \"varying\",\n    \"verification-message\",\n    \"version\",\n    \"via\",\n    \"view\",\n    \"visible\",\n    \"wait\",\n    \"warning\",\n    \"when\",\n    \"whenever\",\n    \"where\",\n    \"while\",\n    \"width\",\n    \"window\",\n    \"windows\",\n    \"with\",\n    \"with-heading\",\n    \"without\",\n    \"with-title\",\n    \"word\",\n    \"work\",\n    \"write\",\n    \"writer\",\n    \"xml\",\n    \"xsd\",\n    \"yellow\",\n    \"yes\",\n    \"yymmdd\",\n    \"zero\",\n    \"zone\",\n    \"abap_system_timezone\",\n    \"abap_user_timezone\",\n    \"access\",\n    \"action\",\n    \"adabas\",\n    \"adjust_numbers\",\n    \"allow_precision_loss\",\n    \"allowed\",\n    \"amdp\",\n    \"applicationuser\",\n    \"as_geo_json\",\n    \"as400\",\n    \"associations\",\n    \"balance\",\n    \"behavior\",\n    \"breakup\",\n    \"bulk\",\n    \"cds\",\n    \"cds_client\",\n    \"check_before_save\",\n    \"child\",\n    \"clients\",\n    \"corr\",\n    \"corr_spearman\",\n    \"cross\",\n    \"cycles\",\n    \"datn_add_days\",\n    \"datn_add_months\",\n    \"datn_days_between\",\n    \"dats_from_datn\",\n    \"dats_tims_to_tstmp\",\n    \"dats_to_datn\",\n    \"db2\",\n    \"db6\",\n    \"ddl\",\n    \"dense_rank\",\n    \"depth\",\n    \"deterministic\",\n    \"discarding\",\n    \"entities\",\n    \"entity\",\n    \"error\",\n    \"failed\",\n    \"finalize\",\n    \"first_value\",\n    \"fltp_to_dec\",\n    \"following\",\n    \"fractional\",\n    \"full\",\n    \"graph\",\n    \"grouping\",\n    \"hierarchy\",\n    \"hierarchy_ancestors\",\n    \"hierarchy_ancestors_aggregate\",\n    \"hierarchy_descendants\",\n    \"hierarchy_descendants_aggregate\",\n    \"hierarchy_siblings\",\n    \"incremental\",\n    \"indicators\",\n    \"lag\",\n    \"last_value\",\n    \"lead\",\n    \"leaves\",\n    \"like_regexpr\",\n    \"link\",\n    \"locale_sap\",\n    \"lock\",\n    \"locks\",\n    \"many\",\n    \"mapped\",\n    \"matched\",\n    \"measures\",\n    \"median\",\n    \"mssqlnt\",\n    \"multiple\",\n    \"nodetype\",\n    \"ntile\",\n    \"nulls\",\n    \"occurrences_regexpr\",\n    \"one\",\n    \"operations\",\n    \"oracle\",\n    \"orphans\",\n    \"over\",\n    \"parent\",\n    \"parents\",\n    \"partition\",\n    \"pcre\",\n    \"period\",\n    \"pfcg_mapping\",\n    \"preceding\",\n    \"privileged\",\n    \"product\",\n    \"projection\",\n    \"rank\",\n    \"redirected\",\n    \"replace_regexpr\",\n    \"reported\",\n    \"response\",\n    \"responses\",\n    \"root\",\n    \"row\",\n    \"row_number\",\n    \"sap_system_date\",\n    \"save\",\n    \"schema\",\n    \"session\",\n    \"sets\",\n    \"shortdump\",\n    \"siblings\",\n    \"spantree\",\n    \"start\",\n    \"stddev\",\n    \"string_agg\",\n    \"subtotal\",\n    \"sybase\",\n    \"tims_from_timn\",\n    \"tims_to_timn\",\n    \"to_blob\",\n    \"to_clob\",\n    \"total\",\n    \"trace-entry\",\n    \"tstmp_to_dats\",\n    \"tstmp_to_dst\",\n    \"tstmp_to_tims\",\n    \"tstmpl_from_utcl\",\n    \"tstmpl_to_utcl\",\n    \"unbounded\",\n    \"utcl_add_seconds\",\n    \"utcl_current\",\n    \"utcl_seconds_between\",\n    \"uuid\",\n    \"var\",\n    \"verbatim\"\n  ],\n  builtinFunctions: [\n    \"abs\",\n    \"acos\",\n    \"asin\",\n    \"atan\",\n    \"bit-set\",\n    \"boolc\",\n    \"boolx\",\n    \"ceil\",\n    \"char_off\",\n    \"charlen\",\n    \"cmax\",\n    \"cmin\",\n    \"concat_lines_of\",\n    \"contains\",\n    \"contains_any_not_of\",\n    \"contains_any_of\",\n    \"cos\",\n    \"cosh\",\n    \"count\",\n    \"count_any_not_of\",\n    \"count_any_of\",\n    \"dbmaxlen\",\n    \"distance\",\n    \"escape\",\n    \"exp\",\n    \"find_any_not_of\",\n    \"find_any_of\",\n    \"find_end\",\n    \"floor\",\n    \"frac\",\n    \"from_mixed\",\n    \"ipow\",\n    \"line_exists\",\n    \"line_index\",\n    \"log\",\n    \"log10\",\n    \"matches\",\n    \"nmax\",\n    \"nmin\",\n    \"numofchar\",\n    \"repeat\",\n    \"rescale\",\n    \"reverse\",\n    \"round\",\n    \"segment\",\n    \"shift_left\",\n    \"shift_right\",\n    \"sign\",\n    \"sin\",\n    \"sinh\",\n    \"sqrt\",\n    \"strlen\",\n    \"substring\",\n    \"substring_after\",\n    \"substring_before\",\n    \"substring_from\",\n    \"substring_to\",\n    \"tan\",\n    \"tanh\",\n    \"to_lower\",\n    \"to_mixed\",\n    \"to_upper\",\n    \"trunc\",\n    \"utclong_add\",\n    \"utclong_current\",\n    \"utclong_diff\",\n    \"xsdbool\",\n    \"xstrlen\"\n  ],\n  typeKeywords: [\n    \"b\",\n    \"c\",\n    \"d\",\n    \"decfloat16\",\n    \"decfloat34\",\n    \"f\",\n    \"i\",\n    \"int8\",\n    \"n\",\n    \"p\",\n    \"s\",\n    \"string\",\n    \"t\",\n    \"utclong\",\n    \"x\",\n    \"xstring\",\n    \"any\",\n    \"clike\",\n    \"csequence\",\n    \"decfloat\",\n    \"numeric\",\n    \"simple\",\n    \"xsequence\",\n    \"accp\",\n    \"char\",\n    \"clnt\",\n    \"cuky\",\n    \"curr\",\n    \"datn\",\n    \"dats\",\n    \"d16d\",\n    \"d16n\",\n    \"d16r\",\n    \"d34d\",\n    \"d34n\",\n    \"d34r\",\n    \"dec\",\n    \"df16_dec\",\n    \"df16_raw\",\n    \"df34_dec\",\n    \"df34_raw\",\n    \"fltp\",\n    \"geom_ewkb\",\n    \"int1\",\n    \"int2\",\n    \"int4\",\n    \"lang\",\n    \"lchr\",\n    \"lraw\",\n    \"numc\",\n    \"quan\",\n    \"raw\",\n    \"rawstring\",\n    \"sstring\",\n    \"timn\",\n    \"tims\",\n    \"unit\",\n    \"utcl\",\n    \"df16_scl\",\n    \"df34_scl\",\n    \"prec\",\n    \"varc\",\n    \"abap_bool\",\n    \"abap_false\",\n    \"abap_true\",\n    \"abap_undefined\",\n    \"me\",\n    \"screen\",\n    \"space\",\n    \"super\",\n    \"sy\",\n    \"syst\",\n    \"table_line\",\n    \"*sys*\"\n  ],\n  builtinMethods: [\"class_constructor\", \"constructor\"],\n  derivedTypes: [\n    \"%CID\",\n    \"%CID_REF\",\n    \"%CONTROL\",\n    \"%DATA\",\n    \"%ELEMENT\",\n    \"%FAIL\",\n    \"%KEY\",\n    \"%MSG\",\n    \"%PARAM\",\n    \"%PID\",\n    \"%PID_ASSOC\",\n    \"%PID_PARENT\",\n    \"%_HINTS\"\n  ],\n  cdsLanguage: [\n    \"@AbapAnnotation\",\n    \"@AbapCatalog\",\n    \"@AccessControl\",\n    \"@API\",\n    \"@ClientDependent\",\n    \"@ClientHandling\",\n    \"@CompatibilityContract\",\n    \"@DataAging\",\n    \"@EndUserText\",\n    \"@Environment\",\n    \"@LanguageDependency\",\n    \"@MappingRole\",\n    \"@Metadata\",\n    \"@MetadataExtension\",\n    \"@ObjectModel\",\n    \"@Scope\",\n    \"@Semantics\",\n    \"$EXTENSION\",\n    \"$SELF\"\n  ],\n  selectors: [\"->\", \"->*\", \"=>\", \"~\", \"~*\"],\n  operators: [\n    \" +\",\n    \" -\",\n    \"/\",\n    \"*\",\n    \"**\",\n    \"div\",\n    \"mod\",\n    \"=\",\n    \"#\",\n    \"@\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"/=\",\n    \"**=\",\n    \"&&=\",\n    \"?=\",\n    \"&\",\n    \"&&\",\n    \"bit-and\",\n    \"bit-not\",\n    \"bit-or\",\n    \"bit-xor\",\n    \"m\",\n    \"o\",\n    \"z\",\n    \"<\",\n    \" >\",\n    \"<=\",\n    \">=\",\n    \"<>\",\n    \"><\",\n    \"=<\",\n    \"=>\",\n    \"bt\",\n    \"byte-ca\",\n    \"byte-cn\",\n    \"byte-co\",\n    \"byte-cs\",\n    \"byte-na\",\n    \"byte-ns\",\n    \"ca\",\n    \"cn\",\n    \"co\",\n    \"cp\",\n    \"cs\",\n    \"eq\",\n    \"ge\",\n    \"gt\",\n    \"le\",\n    \"lt\",\n    \"na\",\n    \"nb\",\n    \"ne\",\n    \"np\",\n    \"ns\",\n    \"*/\",\n    \"*:\",\n    \"--\",\n    \"/*\",\n    \"//\"\n  ],\n  symbols: /[=><!~?&+\\-*\\/\\^%#@]+/,\n  tokenizer: {\n    root: [\n      [\n        /[a-z_\\/$%@]([\\w\\/$%]|-(?!>))*/,\n        {\n          cases: {\n            \"@typeKeywords\": \"type\",\n            \"@keywords\": \"keyword\",\n            \"@cdsLanguage\": \"annotation\",\n            \"@derivedTypes\": \"type\",\n            \"@builtinFunctions\": \"type\",\n            \"@builtinMethods\": \"type\",\n            \"@operators\": \"key\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/<[\\w]+>/, \"identifier\"],\n      [/##[\\w|_]+/, \"comment\"],\n      { include: \"@whitespace\" },\n      [/[:,.]/, \"delimiter\"],\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@selectors\": \"tag\",\n            \"@operators\": \"key\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      [/'/, { token: \"string\", bracket: \"@open\", next: \"@stringquote\" }],\n      [/`/, { token: \"string\", bracket: \"@open\", next: \"@stringping\" }],\n      [/\\|/, { token: \"string\", bracket: \"@open\", next: \"@stringtemplate\" }],\n      [/\\d+/, \"number\"]\n    ],\n    stringtemplate: [\n      [/[^\\\\\\|]+/, \"string\"],\n      [/\\\\\\|/, \"string\"],\n      [/\\|/, { token: \"string\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    stringping: [\n      [/[^\\\\`]+/, \"string\"],\n      [/`/, { token: \"string\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    stringquote: [\n      [/[^\\\\']+/, \"string\"],\n      [/'/, { token: \"string\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/^\\*.*$/, \"comment\"],\n      [/\\\".*$/, \"comment\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AAQA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,kBAAkB;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,cAAc;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,gBAAgB,CAAC,qBAAqB,aAAa;AAAA,EACnD,cAAc;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,aAAa;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW,CAAC,MAAM,OAAO,MAAM,KAAK,IAAI;AAAA,EACxC,WAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,SAAS;AAAA,EACT,WAAW;AAAA,IACT,MAAM;AAAA,MACJ;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,iBAAiB;AAAA,YACjB,aAAa;AAAA,YACb,gBAAgB;AAAA,YAChB,iBAAiB;AAAA,YACjB,qBAAqB;AAAA,YACrB,mBAAmB;AAAA,YACnB,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,WAAW,YAAY;AAAA,MACxB,CAAC,aAAa,SAAS;AAAA,MACvB,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,cAAc,WAAW;AAAA,MAC1B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,cAAc;AAAA,YACd,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,KAAK,EAAE,OAAO,UAAU,SAAS,SAAS,MAAM,eAAe,CAAC;AAAA,MACjE,CAAC,KAAK,EAAE,OAAO,UAAU,SAAS,SAAS,MAAM,cAAc,CAAC;AAAA,MAChE,CAAC,MAAM,EAAE,OAAO,UAAU,SAAS,SAAS,MAAM,kBAAkB,CAAC;AAAA,MACrE,CAAC,OAAO,QAAQ;AAAA,IAClB;AAAA,IACA,gBAAgB;AAAA,MACd,CAAC,YAAY,QAAQ;AAAA,MACrB,CAAC,QAAQ,QAAQ;AAAA,MACjB,CAAC,MAAM,EAAE,OAAO,UAAU,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAC7D;AAAA,IACA,YAAY;AAAA,MACV,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,KAAK,EAAE,OAAO,UAAU,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAC5D;AAAA,IACA,aAAa;AAAA,MACX,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,KAAK,EAAE,OAAO,UAAU,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAC5D;AAAA,IACA,YAAY;AAAA,MACV,CAAC,cAAc,EAAE;AAAA,MACjB,CAAC,UAAU,SAAS;AAAA,MACpB,CAAC,SAAS,SAAS;AAAA,IACrB;AAAA,EACF;AACF;", "names": []}
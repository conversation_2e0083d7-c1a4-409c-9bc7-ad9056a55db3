{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/hcl/hcl.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/hcl/hcl.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"', notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".hcl\",\n  keywords: [\n    \"var\",\n    \"local\",\n    \"path\",\n    \"for_each\",\n    \"any\",\n    \"string\",\n    \"number\",\n    \"bool\",\n    \"true\",\n    \"false\",\n    \"null\",\n    \"if \",\n    \"else \",\n    \"endif \",\n    \"for \",\n    \"in\",\n    \"endfor\"\n  ],\n  operators: [\n    \"=\",\n    \">=\",\n    \"<=\",\n    \"==\",\n    \"!=\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"&&\",\n    \"||\",\n    \"!\",\n    \"<\",\n    \">\",\n    \"?\",\n    \"...\",\n    \":\"\n  ],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  terraformFunctions: /(abs|ceil|floor|log|max|min|pow|signum|chomp|format|formatlist|indent|join|lower|regex|regexall|replace|split|strrev|substr|title|trimspace|upper|chunklist|coalesce|coalescelist|compact|concat|contains|distinct|element|flatten|index|keys|length|list|lookup|map|matchkeys|merge|range|reverse|setintersection|setproduct|setunion|slice|sort|transpose|values|zipmap|base64decode|base64encode|base64gzip|csvdecode|jsondecode|jsonencode|urlencode|yamldecode|yamlencode|abspath|dirname|pathexpand|basename|file|fileexists|fileset|filebase64|templatefile|formatdate|timeadd|timestamp|base64sha256|base64sha512|bcrypt|filebase64sha256|filebase64sha512|filemd5|filemd1|filesha256|filesha512|md5|rsadecrypt|sha1|sha256|sha512|uuid|uuidv5|cidrhost|cidrnetmask|cidrsubnet|tobool|tolist|tomap|tonumber|toset|tostring)/,\n  terraformMainBlocks: /(module|data|terraform|resource|provider|variable|output|locals)/,\n  tokenizer: {\n    root: [\n      [\n        /^@terraformMainBlocks([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)(\\{)/,\n        [\"type\", \"\", \"string\", \"\", \"string\", \"\", \"@brackets\"]\n      ],\n      [\n        /(\\w+[ \\t]+)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)(\\{)/,\n        [\"identifier\", \"\", \"string\", \"\", \"string\", \"\", \"@brackets\"]\n      ],\n      [\n        /(\\w+[ \\t]+)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)(=)(\\{)/,\n        [\"identifier\", \"\", \"string\", \"\", \"operator\", \"\", \"@brackets\"]\n      ],\n      { include: \"@terraform\" }\n    ],\n    terraform: [\n      [/@terraformFunctions(\\()/, [\"type\", \"@brackets\"]],\n      [\n        /[a-zA-Z_]\\w*-*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"variable\"\n          }\n        }\n      ],\n      { include: \"@whitespace\" },\n      { include: \"@heredoc\" },\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"operator\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      [/\\d*\\d+[eE]([\\-+]?\\d+)?/, \"number.float\"],\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/\\d[\\d']*/, \"number\"],\n      [/\\d/, \"number\"],\n      [/[;,.]/, \"delimiter\"],\n      [/\"/, \"string\", \"@string\"],\n      [/'/, \"invalid\"]\n    ],\n    heredoc: [\n      [/<<[-]*\\s*[\"]?([\\w\\-]+)[\"]?/, { token: \"string.heredoc.delimiter\", next: \"@heredocBody.$1\" }]\n    ],\n    heredocBody: [\n      [\n        /([\\w\\-]+)$/,\n        {\n          cases: {\n            \"$1==$S2\": [\n              {\n                token: \"string.heredoc.delimiter\",\n                next: \"@popall\"\n              }\n            ],\n            \"@default\": \"string.heredoc\"\n          }\n        }\n      ],\n      [/./, \"string.heredoc\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"],\n      [/#.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    string: [\n      [/\\$\\{/, { token: \"delimiter\", next: \"@stringExpression\" }],\n      [/[^\\\\\"\\$]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@popall\"]\n    ],\n    stringInsideExpression: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    stringExpression: [\n      [/\\}/, { token: \"delimiter\", next: \"@pop\" }],\n      [/\"/, \"string\", \"@stringInsideExpression\"],\n      { include: \"@terraform\" }\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AAQA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,EAC7C;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,SAAS;AAAA,EACT,SAAS;AAAA,EACT,oBAAoB;AAAA,EACpB,qBAAqB;AAAA,EACrB,WAAW;AAAA,IACT,MAAM;AAAA,MACJ;AAAA,QACE;AAAA,QACA,CAAC,QAAQ,IAAI,UAAU,IAAI,UAAU,IAAI,WAAW;AAAA,MACtD;AAAA,MACA;AAAA,QACE;AAAA,QACA,CAAC,cAAc,IAAI,UAAU,IAAI,UAAU,IAAI,WAAW;AAAA,MAC5D;AAAA,MACA;AAAA,QACE;AAAA,QACA,CAAC,cAAc,IAAI,UAAU,IAAI,YAAY,IAAI,WAAW;AAAA,MAC9D;AAAA,MACA,EAAE,SAAS,aAAa;AAAA,IAC1B;AAAA,IACA,WAAW;AAAA,MACT,CAAC,2BAA2B,CAAC,QAAQ,WAAW,CAAC;AAAA,MACjD;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa,EAAE,OAAO,aAAa;AAAA,YACnC,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA,MACtB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,oBAAoB,WAAW;AAAA,MAChC;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,0BAA0B,cAAc;AAAA,MACzC,CAAC,4BAA4B,cAAc;AAAA,MAC3C,CAAC,YAAY,QAAQ;AAAA,MACrB,CAAC,MAAM,QAAQ;AAAA,MACf,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,KAAK,UAAU,SAAS;AAAA,MACzB,CAAC,KAAK,SAAS;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,8BAA8B,EAAE,OAAO,4BAA4B,MAAM,kBAAkB,CAAC;AAAA,IAC/F;AAAA,IACA,aAAa;AAAA,MACX;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW;AAAA,cACT;AAAA,gBACE,OAAO;AAAA,gBACP,MAAM;AAAA,cACR;AAAA,YACF;AAAA,YACA,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,KAAK,gBAAgB;AAAA,IACxB;AAAA,IACA,YAAY;AAAA,MACV,CAAC,cAAc,EAAE;AAAA,MACjB,CAAC,QAAQ,WAAW,UAAU;AAAA,MAC9B,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,QAAQ,SAAS;AAAA,IACpB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,SAAS,SAAS;AAAA,IACrB;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,QAAQ,EAAE,OAAO,aAAa,MAAM,oBAAoB,CAAC;AAAA,MAC1D,CAAC,aAAa,QAAQ;AAAA,MACtB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,UAAU,SAAS;AAAA,IAC3B;AAAA,IACA,wBAAwB;AAAA,MACtB,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,UAAU,MAAM;AAAA,IACxB;AAAA,IACA,kBAAkB;AAAA,MAChB,CAAC,MAAM,EAAE,OAAO,aAAa,MAAM,OAAO,CAAC;AAAA,MAC3C,CAAC,KAAK,UAAU,yBAAyB;AAAA,MACzC,EAAE,SAAS,aAAa;AAAA,IAC1B;AAAA,EACF;AACF;", "names": []}
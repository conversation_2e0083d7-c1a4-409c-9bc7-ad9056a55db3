{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/freemarker2/freemarker2.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/freemarker2/freemarker2.ts\nvar EMPTY_ELEMENTS = [\n  \"assign\",\n  \"flush\",\n  \"ftl\",\n  \"return\",\n  \"global\",\n  \"import\",\n  \"include\",\n  \"break\",\n  \"continue\",\n  \"local\",\n  \"nested\",\n  \"nt\",\n  \"setting\",\n  \"stop\",\n  \"t\",\n  \"lt\",\n  \"rt\",\n  \"fallback\"\n];\nvar BLOCK_ELEMENTS = [\n  \"attempt\",\n  \"autoesc\",\n  \"autoEsc\",\n  \"compress\",\n  \"comment\",\n  \"escape\",\n  \"noescape\",\n  \"function\",\n  \"if\",\n  \"list\",\n  \"items\",\n  \"sep\",\n  \"macro\",\n  \"noparse\",\n  \"noParse\",\n  \"noautoesc\",\n  \"noAutoEsc\",\n  \"outputformat\",\n  \"switch\",\n  \"visit\",\n  \"recurse\"\n];\nvar TagSyntaxAngle = {\n  close: \">\",\n  id: \"angle\",\n  open: \"<\"\n};\nvar TagSyntaxBracket = {\n  close: \"\\\\]\",\n  id: \"bracket\",\n  open: \"\\\\[\"\n};\nvar TagSyntaxAuto = {\n  close: \"[>\\\\]]\",\n  id: \"auto\",\n  open: \"[<\\\\[]\"\n};\nvar InterpolationSyntaxDollar = {\n  close: \"\\\\}\",\n  id: \"dollar\",\n  open1: \"\\\\$\",\n  open2: \"\\\\{\"\n};\nvar InterpolationSyntaxBracket = {\n  close: \"\\\\]\",\n  id: \"bracket\",\n  open1: \"\\\\[\",\n  open2: \"=\"\n};\nfunction createLangConfiguration(ts) {\n  return {\n    brackets: [\n      [\"<\", \">\"],\n      [\"[\", \"]\"],\n      [\"(\", \")\"],\n      [\"{\", \"}\"]\n    ],\n    comments: {\n      blockComment: [`${ts.open}--`, `--${ts.close}`]\n    },\n    autoCloseBefore: \"\\n\\r\t }]),.:;=\",\n    autoClosingPairs: [\n      { open: \"{\", close: \"}\" },\n      { open: \"[\", close: \"]\" },\n      { open: \"(\", close: \")\" },\n      { open: '\"', close: '\"', notIn: [\"string\"] },\n      { open: \"'\", close: \"'\", notIn: [\"string\"] }\n    ],\n    surroundingPairs: [\n      { open: '\"', close: '\"' },\n      { open: \"'\", close: \"'\" },\n      { open: \"{\", close: \"}\" },\n      { open: \"[\", close: \"]\" },\n      { open: \"(\", close: \")\" },\n      { open: \"<\", close: \">\" }\n    ],\n    folding: {\n      markers: {\n        start: new RegExp(`${ts.open}#(?:${BLOCK_ELEMENTS.join(\"|\")})([^/${ts.close}]*(?!/)${ts.close})[^${ts.open}]*$`),\n        end: new RegExp(`${ts.open}/#(?:${BLOCK_ELEMENTS.join(\"|\")})[\\\\r\\\\n\\\\t ]*>`)\n      }\n    },\n    onEnterRules: [\n      {\n        beforeText: new RegExp(`${ts.open}#(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))([a-zA-Z_]+)([^/${ts.close}]*(?!/)${ts.close})[^${ts.open}]*$`),\n        afterText: new RegExp(`^${ts.open}/#([a-zA-Z_]+)[\\\\r\\\\n\\\\t ]*${ts.close}$`),\n        action: {\n          indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent\n        }\n      },\n      {\n        beforeText: new RegExp(`${ts.open}#(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))([a-zA-Z_]+)([^/${ts.close}]*(?!/)${ts.close})[^${ts.open}]*$`),\n        action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n      }\n    ]\n  };\n}\nfunction createLangConfigurationAuto() {\n  return {\n    brackets: [\n      [\"<\", \">\"],\n      [\"[\", \"]\"],\n      [\"(\", \")\"],\n      [\"{\", \"}\"]\n    ],\n    autoCloseBefore: \"\\n\\r\t }]),.:;=\",\n    autoClosingPairs: [\n      { open: \"{\", close: \"}\" },\n      { open: \"[\", close: \"]\" },\n      { open: \"(\", close: \")\" },\n      { open: '\"', close: '\"', notIn: [\"string\"] },\n      { open: \"'\", close: \"'\", notIn: [\"string\"] }\n    ],\n    surroundingPairs: [\n      { open: '\"', close: '\"' },\n      { open: \"'\", close: \"'\" },\n      { open: \"{\", close: \"}\" },\n      { open: \"[\", close: \"]\" },\n      { open: \"(\", close: \")\" },\n      { open: \"<\", close: \">\" }\n    ],\n    folding: {\n      markers: {\n        start: new RegExp(`[<\\\\[]#(?:${BLOCK_ELEMENTS.join(\"|\")})([^/>\\\\]]*(?!/)[>\\\\]])[^<\\\\[]*$`),\n        end: new RegExp(`[<\\\\[]/#(?:${BLOCK_ELEMENTS.join(\"|\")})[\\\\r\\\\n\\\\t ]*>`)\n      }\n    },\n    onEnterRules: [\n      {\n        beforeText: new RegExp(`[<\\\\[]#(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))([a-zA-Z_]+)([^/>\\\\]]*(?!/)[>\\\\]])[^[<\\\\[]]*$`),\n        afterText: new RegExp(`^[<\\\\[]/#([a-zA-Z_]+)[\\\\r\\\\n\\\\t ]*[>\\\\]]$`),\n        action: {\n          indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent\n        }\n      },\n      {\n        beforeText: new RegExp(`[<\\\\[]#(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))([a-zA-Z_]+)([^/>\\\\]]*(?!/)[>\\\\]])[^[<\\\\[]]*$`),\n        action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n      }\n    ]\n  };\n}\nfunction createMonarchLanguage(ts, is) {\n  const id = `_${ts.id}_${is.id}`;\n  const s = (name) => name.replace(/__id__/g, id);\n  const r = (regexp) => {\n    const source = regexp.source.replace(/__id__/g, id);\n    return new RegExp(source, regexp.flags);\n  };\n  return {\n    unicode: true,\n    includeLF: false,\n    start: s(\"default__id__\"),\n    ignoreCase: false,\n    defaultToken: \"invalid\",\n    tokenPostfix: `.freemarker2`,\n    brackets: [\n      { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n      { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n      { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n      { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n    ],\n    [s(\"open__id__\")]: new RegExp(ts.open),\n    [s(\"close__id__\")]: new RegExp(ts.close),\n    [s(\"iOpen1__id__\")]: new RegExp(is.open1),\n    [s(\"iOpen2__id__\")]: new RegExp(is.open2),\n    [s(\"iClose__id__\")]: new RegExp(is.close),\n    [s(\"startTag__id__\")]: r(/(@open__id__)(#)/),\n    [s(\"endTag__id__\")]: r(/(@open__id__)(\\/#)/),\n    [s(\"startOrEndTag__id__\")]: r(/(@open__id__)(\\/?#)/),\n    [s(\"closeTag1__id__\")]: r(/((?:@blank)*)(@close__id__)/),\n    [s(\"closeTag2__id__\")]: r(/((?:@blank)*\\/?)(@close__id__)/),\n    blank: /[ \\t\\n\\r]/,\n    keywords: [\"false\", \"true\", \"in\", \"as\", \"using\"],\n    directiveStartCloseTag1: /attempt|recover|sep|auto[eE]sc|no(?:autoe|AutoE)sc|compress|default|no[eE]scape|comment|no[pP]arse/,\n    directiveStartCloseTag2: /else|break|continue|return|stop|flush|t|lt|rt|nt|nested|recurse|fallback|ftl/,\n    directiveStartBlank: /if|else[iI]f|list|for[eE]ach|switch|case|assign|global|local|include|import|function|macro|transform|visit|stop|return|call|setting|output[fF]ormat|nested|recurse|escape|ftl|items/,\n    directiveEndCloseTag1: /if|list|items|sep|recover|attempt|for[eE]ach|local|global|assign|function|macro|output[fF]ormat|auto[eE]sc|no(?:autoe|AutoE)sc|compress|transform|switch|escape|no[eE]scape/,\n    escapedChar: /\\\\(?:[ntrfbgla\\\\'\"\\{=]|(?:x[0-9A-Fa-f]{1,4}))/,\n    asciiDigit: /[0-9]/,\n    integer: /[0-9]+/,\n    nonEscapedIdStartChar: /[\\$@-Z_a-z\\u00AA\\u00B5\\u00BA\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u1FFF\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2183-\\u2184\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2-\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005-\\u3006\\u3031-\\u3035\\u303B-\\u303C\\u3040-\\u318F\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3300-\\u337F\\u3400-\\u4DB5\\u4E00-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA62B\\uA640-\\uA66E\\uA67F-\\uA697\\uA6A0-\\uA6E5\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA78E\\uA790-\\uA793\\uA7A0-\\uA7AA\\uA7F8-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8D0-\\uA8D9\\uA8F2-\\uA8F7\\uA8FB\\uA900-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF-\\uA9D9\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA50-\\uAA59\\uAA60-\\uAA76\\uAA7A\\uAA80-\\uAAAF\\uAAB1\\uAAB5-\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uABC0-\\uABE2\\uABF0-\\uABF9\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40-\\uFB41\\uFB43-\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF10-\\uFF19\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]/,\n    escapedIdChar: /\\\\[\\-\\.:#]/,\n    idStartChar: /(?:@nonEscapedIdStartChar)|(?:@escapedIdChar)/,\n    id: /(?:@idStartChar)(?:(?:@idStartChar)|(?:@asciiDigit))*/,\n    specialHashKeys: /\\*\\*|\\*|false|true|in|as|using/,\n    namedSymbols: /&lt;=|&gt;=|\\\\lte|\\\\lt|&lt;|\\\\gte|\\\\gt|&gt;|&amp;&amp;|\\\\and|-&gt;|->|==|!=|\\+=|-=|\\*=|\\/=|%=|\\+\\+|--|<=|&&|\\|\\||:|\\.\\.\\.|\\.\\.\\*|\\.\\.<|\\.\\.!|\\?\\?|=|<|\\+|-|\\*|\\/|%|\\||\\.\\.|\\?|!|&|\\.|,|;/,\n    arrows: [\"->\", \"-&gt;\"],\n    delimiters: [\";\", \":\", \",\", \".\"],\n    stringOperators: [\"lte\", \"lt\", \"gte\", \"gt\"],\n    noParseTags: [\"noparse\", \"noParse\", \"comment\"],\n    tokenizer: {\n      [s(\"default__id__\")]: [\n        { include: s(\"@directive_token__id__\") },\n        { include: s(\"@interpolation_and_text_token__id__\") }\n      ],\n      [s(\"fmExpression__id__.directive\")]: [\n        { include: s(\"@blank_and_expression_comment_token__id__\") },\n        { include: s(\"@directive_end_token__id__\") },\n        { include: s(\"@expression_token__id__\") }\n      ],\n      [s(\"fmExpression__id__.interpolation\")]: [\n        { include: s(\"@blank_and_expression_comment_token__id__\") },\n        { include: s(\"@expression_token__id__\") },\n        { include: s(\"@greater_operators_token__id__\") }\n      ],\n      [s(\"inParen__id__.plain\")]: [\n        { include: s(\"@blank_and_expression_comment_token__id__\") },\n        { include: s(\"@directive_end_token__id__\") },\n        { include: s(\"@expression_token__id__\") }\n      ],\n      [s(\"inParen__id__.gt\")]: [\n        { include: s(\"@blank_and_expression_comment_token__id__\") },\n        { include: s(\"@expression_token__id__\") },\n        { include: s(\"@greater_operators_token__id__\") }\n      ],\n      [s(\"noSpaceExpression__id__\")]: [\n        { include: s(\"@no_space_expression_end_token__id__\") },\n        { include: s(\"@directive_end_token__id__\") },\n        { include: s(\"@expression_token__id__\") }\n      ],\n      [s(\"unifiedCall__id__\")]: [{ include: s(\"@unified_call_token__id__\") }],\n      [s(\"singleString__id__\")]: [{ include: s(\"@string_single_token__id__\") }],\n      [s(\"doubleString__id__\")]: [{ include: s(\"@string_double_token__id__\") }],\n      [s(\"rawSingleString__id__\")]: [{ include: s(\"@string_single_raw_token__id__\") }],\n      [s(\"rawDoubleString__id__\")]: [{ include: s(\"@string_double_raw_token__id__\") }],\n      [s(\"expressionComment__id__\")]: [{ include: s(\"@expression_comment_token__id__\") }],\n      [s(\"noParse__id__\")]: [{ include: s(\"@no_parse_token__id__\") }],\n      [s(\"terseComment__id__\")]: [{ include: s(\"@terse_comment_token__id__\") }],\n      [s(\"directive_token__id__\")]: [\n        [\n          r(/(?:@startTag__id__)(@directiveStartCloseTag1)(?:@closeTag1__id__)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            {\n              cases: {\n                \"@noParseTags\": { token: \"tag\", next: s(\"@noParse__id__.$3\") },\n                \"@default\": { token: \"tag\" }\n              }\n            },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\" }\n          ]\n        ],\n        [\n          r(/(?:@startTag__id__)(@directiveStartCloseTag2)(?:@closeTag2__id__)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag\" },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\" }\n          ]\n        ],\n        [\n          r(/(?:@startTag__id__)(@directiveStartBlank)(@blank)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag\" },\n            { token: \"\", next: s(\"@fmExpression__id__.directive\") }\n          ]\n        ],\n        [\n          r(/(?:@endTag__id__)(@directiveEndCloseTag1)(?:@closeTag1__id__)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag\" },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\" }\n          ]\n        ],\n        [\n          r(/(@open__id__)(@)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\", next: s(\"@unifiedCall__id__\") }\n          ]\n        ],\n        [\n          r(/(@open__id__)(\\/@)((?:(?:@id)(?:\\.(?:@id))*)?)(?:@closeTag1__id__)/),\n          [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag\" },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\" }\n          ]\n        ],\n        [\n          r(/(@open__id__)#--/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : { token: \"comment\", next: s(\"@terseComment__id__\") }\n        ],\n        [\n          r(/(?:@startOrEndTag__id__)([a-zA-Z_]+)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag.invalid\", next: s(\"@fmExpression__id__.directive\") }\n          ]\n        ]\n      ],\n      [s(\"interpolation_and_text_token__id__\")]: [\n        [\n          r(/(@iOpen1__id__)(@iOpen2__id__)/),\n          [\n            { token: is.id === \"bracket\" ? \"@brackets.interpolation\" : \"delimiter.interpolation\" },\n            {\n              token: is.id === \"bracket\" ? \"delimiter.interpolation\" : \"@brackets.interpolation\",\n              next: s(\"@fmExpression__id__.interpolation\")\n            }\n          ]\n        ],\n        [/[\\$#<\\[\\{]|(?:@blank)+|[^\\$<#\\[\\{\\n\\r\\t ]+/, { token: \"source\" }]\n      ],\n      [s(\"string_single_token__id__\")]: [\n        [/[^'\\\\]/, { token: \"string\" }],\n        [/@escapedChar/, { token: \"string.escape\" }],\n        [/'/, { token: \"string\", next: \"@pop\" }]\n      ],\n      [s(\"string_double_token__id__\")]: [\n        [/[^\"\\\\]/, { token: \"string\" }],\n        [/@escapedChar/, { token: \"string.escape\" }],\n        [/\"/, { token: \"string\", next: \"@pop\" }]\n      ],\n      [s(\"string_single_raw_token__id__\")]: [\n        [/[^']+/, { token: \"string.raw\" }],\n        [/'/, { token: \"string.raw\", next: \"@pop\" }]\n      ],\n      [s(\"string_double_raw_token__id__\")]: [\n        [/[^\"]+/, { token: \"string.raw\" }],\n        [/\"/, { token: \"string.raw\", next: \"@pop\" }]\n      ],\n      [s(\"expression_token__id__\")]: [\n        [\n          /(r?)(['\"])/,\n          {\n            cases: {\n              \"r'\": [\n                { token: \"keyword\" },\n                { token: \"string.raw\", next: s(\"@rawSingleString__id__\") }\n              ],\n              'r\"': [\n                { token: \"keyword\" },\n                { token: \"string.raw\", next: s(\"@rawDoubleString__id__\") }\n              ],\n              \"'\": [{ token: \"source\" }, { token: \"string\", next: s(\"@singleString__id__\") }],\n              '\"': [{ token: \"source\" }, { token: \"string\", next: s(\"@doubleString__id__\") }]\n            }\n          }\n        ],\n        [\n          /(?:@integer)(?:\\.(?:@integer))?/,\n          {\n            cases: {\n              \"(?:@integer)\": { token: \"number\" },\n              \"@default\": { token: \"number.float\" }\n            }\n          }\n        ],\n        [\n          /(\\.)(@blank*)(@specialHashKeys)/,\n          [{ token: \"delimiter\" }, { token: \"\" }, { token: \"identifier\" }]\n        ],\n        [\n          /(?:@namedSymbols)/,\n          {\n            cases: {\n              \"@arrows\": { token: \"meta.arrow\" },\n              \"@delimiters\": { token: \"delimiter\" },\n              \"@default\": { token: \"operators\" }\n            }\n          }\n        ],\n        [\n          /@id/,\n          {\n            cases: {\n              \"@keywords\": { token: \"keyword.$0\" },\n              \"@stringOperators\": { token: \"operators\" },\n              \"@default\": { token: \"identifier\" }\n            }\n          }\n        ],\n        [\n          /[\\[\\]\\(\\)\\{\\}]/,\n          {\n            cases: {\n              \"\\\\[\": {\n                cases: {\n                  \"$S2==gt\": { token: \"@brackets\", next: s(\"@inParen__id__.gt\") },\n                  \"@default\": { token: \"@brackets\", next: s(\"@inParen__id__.plain\") }\n                }\n              },\n              \"\\\\]\": {\n                cases: {\n                  ...is.id === \"bracket\" ? {\n                    \"$S2==interpolation\": { token: \"@brackets.interpolation\", next: \"@popall\" }\n                  } : {},\n                  ...ts.id === \"bracket\" ? {\n                    \"$S2==directive\": { token: \"@brackets.directive\", next: \"@popall\" }\n                  } : {},\n                  [s(\"$S1==inParen__id__\")]: { token: \"@brackets\", next: \"@pop\" },\n                  \"@default\": { token: \"@brackets\" }\n                }\n              },\n              \"\\\\(\": { token: \"@brackets\", next: s(\"@inParen__id__.gt\") },\n              \"\\\\)\": {\n                cases: {\n                  [s(\"$S1==inParen__id__\")]: { token: \"@brackets\", next: \"@pop\" },\n                  \"@default\": { token: \"@brackets\" }\n                }\n              },\n              \"\\\\{\": {\n                cases: {\n                  \"$S2==gt\": { token: \"@brackets\", next: s(\"@inParen__id__.gt\") },\n                  \"@default\": { token: \"@brackets\", next: s(\"@inParen__id__.plain\") }\n                }\n              },\n              \"\\\\}\": {\n                cases: {\n                  ...is.id === \"bracket\" ? {} : {\n                    \"$S2==interpolation\": { token: \"@brackets.interpolation\", next: \"@popall\" }\n                  },\n                  [s(\"$S1==inParen__id__\")]: { token: \"@brackets\", next: \"@pop\" },\n                  \"@default\": { token: \"@brackets\" }\n                }\n              }\n            }\n          }\n        ],\n        [/\\$\\{/, { token: \"delimiter.invalid\" }]\n      ],\n      [s(\"blank_and_expression_comment_token__id__\")]: [\n        [/(?:@blank)+/, { token: \"\" }],\n        [/[<\\[][#!]--/, { token: \"comment\", next: s(\"@expressionComment__id__\") }]\n      ],\n      [s(\"directive_end_token__id__\")]: [\n        [\n          />/,\n          ts.id === \"bracket\" ? { token: \"operators\" } : { token: \"@brackets.directive\", next: \"@popall\" }\n        ],\n        [\n          r(/(\\/)(@close__id__)/),\n          [{ token: \"delimiter.directive\" }, { token: \"@brackets.directive\", next: \"@popall\" }]\n        ]\n      ],\n      [s(\"greater_operators_token__id__\")]: [\n        [/>/, { token: \"operators\" }],\n        [/>=/, { token: \"operators\" }]\n      ],\n      [s(\"no_space_expression_end_token__id__\")]: [\n        [/(?:@blank)+/, { token: \"\", switchTo: s(\"@fmExpression__id__.directive\") }]\n      ],\n      [s(\"unified_call_token__id__\")]: [\n        [\n          /(@id)((?:@blank)+)/,\n          [{ token: \"tag\" }, { token: \"\", next: s(\"@fmExpression__id__.directive\") }]\n        ],\n        [\n          r(/(@id)(\\/?)(@close__id__)/),\n          [\n            { token: \"tag\" },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\", next: \"@popall\" }\n          ]\n        ],\n        [/./, { token: \"@rematch\", next: s(\"@noSpaceExpression__id__\") }]\n      ],\n      [s(\"no_parse_token__id__\")]: [\n        [\n          r(/(@open__id__)(\\/#?)([a-zA-Z]+)((?:@blank)*)(@close__id__)/),\n          {\n            cases: {\n              \"$S2==$3\": [\n                { token: \"@brackets.directive\" },\n                { token: \"delimiter.directive\" },\n                { token: \"tag\" },\n                { token: \"\" },\n                { token: \"@brackets.directive\", next: \"@popall\" }\n              ],\n              \"$S2==comment\": [\n                { token: \"comment\" },\n                { token: \"comment\" },\n                { token: \"comment\" },\n                { token: \"comment\" },\n                { token: \"comment\" }\n              ],\n              \"@default\": [\n                { token: \"source\" },\n                { token: \"source\" },\n                { token: \"source\" },\n                { token: \"source\" },\n                { token: \"source\" }\n              ]\n            }\n          }\n        ],\n        [\n          /[^<\\[\\-]+|[<\\[\\-]/,\n          {\n            cases: {\n              \"$S2==comment\": { token: \"comment\" },\n              \"@default\": { token: \"source\" }\n            }\n          }\n        ]\n      ],\n      [s(\"expression_comment_token__id__\")]: [\n        [\n          /--[>\\]]/,\n          {\n            token: \"comment\",\n            next: \"@pop\"\n          }\n        ],\n        [/[^\\->\\]]+|[>\\]\\-]/, { token: \"comment\" }]\n      ],\n      [s(\"terse_comment_token__id__\")]: [\n        [r(/--(?:@close__id__)/), { token: \"comment\", next: \"@popall\" }],\n        [/[^<\\[\\-]+|[<\\[\\-]/, { token: \"comment\" }]\n      ]\n    }\n  };\n}\nfunction createMonarchLanguageAuto(is) {\n  const angle = createMonarchLanguage(TagSyntaxAngle, is);\n  const bracket = createMonarchLanguage(TagSyntaxBracket, is);\n  const auto = createMonarchLanguage(TagSyntaxAuto, is);\n  return {\n    ...angle,\n    ...bracket,\n    ...auto,\n    unicode: true,\n    includeLF: false,\n    start: `default_auto_${is.id}`,\n    ignoreCase: false,\n    defaultToken: \"invalid\",\n    tokenPostfix: `.freemarker2`,\n    brackets: [\n      { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n      { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n      { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n      { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n    ],\n    tokenizer: {\n      ...angle.tokenizer,\n      ...bracket.tokenizer,\n      ...auto.tokenizer\n    }\n  };\n}\nvar TagAngleInterpolationDollar = {\n  conf: createLangConfiguration(TagSyntaxAngle),\n  language: createMonarchLanguage(TagSyntaxAngle, InterpolationSyntaxDollar)\n};\nvar TagBracketInterpolationDollar = {\n  conf: createLangConfiguration(TagSyntaxBracket),\n  language: createMonarchLanguage(TagSyntaxBracket, InterpolationSyntaxDollar)\n};\nvar TagAngleInterpolationBracket = {\n  conf: createLangConfiguration(TagSyntaxAngle),\n  language: createMonarchLanguage(TagSyntaxAngle, InterpolationSyntaxBracket)\n};\nvar TagBracketInterpolationBracket = {\n  conf: createLangConfiguration(TagSyntaxBracket),\n  language: createMonarchLanguage(TagSyntaxBracket, InterpolationSyntaxBracket)\n};\nvar TagAutoInterpolationDollar = {\n  conf: createLangConfigurationAuto(),\n  language: createMonarchLanguageAuto(InterpolationSyntaxDollar)\n};\nvar TagAutoInterpolationBracket = {\n  conf: createLangConfigurationAuto(),\n  language: createMonarchLanguageAuto(InterpolationSyntaxBracket)\n};\nexport {\n  TagAngleInterpolationBracket,\n  TagAngleInterpolationDollar,\n  TagAutoInterpolationBracket,\n  TagAutoInterpolationDollar,\n  TagBracketInterpolationBracket,\n  TagBracketInterpolationDollar\n};\n"], "mappings": ";;;;;;AAOA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,MAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,aAAS,OAAO,kBAAkB,IAAI;AACpC,UAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,kBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,EACvH;AACA,SAAO;AACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;AAG9I,IAAI,6BAA6B,CAAC;AAClC,WAAW,4BAA4B,kBAAuB;AAI9D,IAAI,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,iBAAiB;AAAA,EACnB,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,MAAM;AACR;AACA,IAAI,mBAAmB;AAAA,EACrB,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,MAAM;AACR;AACA,IAAI,gBAAgB;AAAA,EAClB,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,MAAM;AACR;AACA,IAAI,4BAA4B;AAAA,EAC9B,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,OAAO;AACT;AACA,IAAI,6BAA6B;AAAA,EAC/B,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,OAAO;AACT;AACA,SAAS,wBAAwB,IAAI;AACnC,SAAO;AAAA,IACL,UAAU;AAAA,MACR,CAAC,KAAK,GAAG;AAAA,MACT,CAAC,KAAK,GAAG;AAAA,MACT,CAAC,KAAK,GAAG;AAAA,MACT,CAAC,KAAK,GAAG;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,cAAc,CAAC,GAAG,GAAG,IAAI,MAAM,KAAK,GAAG,KAAK,EAAE;AAAA,IAChD;AAAA,IACA,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,MAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,MAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC7C;AAAA,IACA,kBAAkB;AAAA,MAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IAC1B;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,QACP,OAAO,IAAI,OAAO,GAAG,GAAG,IAAI,OAAO,eAAe,KAAK,GAAG,CAAC,QAAQ,GAAG,KAAK,UAAU,GAAG,KAAK,MAAM,GAAG,IAAI,KAAK;AAAA,QAC/G,KAAK,IAAI,OAAO,GAAG,GAAG,IAAI,QAAQ,eAAe,KAAK,GAAG,CAAC,iBAAiB;AAAA,MAC7E;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ;AAAA,QACE,YAAY,IAAI,OAAO,GAAG,GAAG,IAAI,UAAU,eAAe,KAAK,GAAG,CAAC,qBAAqB,GAAG,KAAK,UAAU,GAAG,KAAK,MAAM,GAAG,IAAI,KAAK;AAAA,QACpI,WAAW,IAAI,OAAO,IAAI,GAAG,IAAI,8BAA8B,GAAG,KAAK,GAAG;AAAA,QAC1E,QAAQ;AAAA,UACN,cAAc,2BAA2B,UAAU,aAAa;AAAA,QAClE;AAAA,MACF;AAAA,MACA;AAAA,QACE,YAAY,IAAI,OAAO,GAAG,GAAG,IAAI,UAAU,eAAe,KAAK,GAAG,CAAC,qBAAqB,GAAG,KAAK,UAAU,GAAG,KAAK,MAAM,GAAG,IAAI,KAAK;AAAA,QACpI,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,OAAO;AAAA,MACnF;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,8BAA8B;AACrC,SAAO;AAAA,IACL,UAAU;AAAA,MACR,CAAC,KAAK,GAAG;AAAA,MACT,CAAC,KAAK,GAAG;AAAA,MACT,CAAC,KAAK,GAAG;AAAA,MACT,CAAC,KAAK,GAAG;AAAA,IACX;AAAA,IACA,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,MAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,MAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC7C;AAAA,IACA,kBAAkB;AAAA,MAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IAC1B;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,QACP,OAAO,IAAI,OAAO,aAAa,eAAe,KAAK,GAAG,CAAC,kCAAkC;AAAA,QACzF,KAAK,IAAI,OAAO,cAAc,eAAe,KAAK,GAAG,CAAC,iBAAiB;AAAA,MACzE;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ;AAAA,QACE,YAAY,IAAI,OAAO,gBAAgB,eAAe,KAAK,GAAG,CAAC,iDAAiD;AAAA,QAChH,WAAW,IAAI,OAAO,2CAA2C;AAAA,QACjE,QAAQ;AAAA,UACN,cAAc,2BAA2B,UAAU,aAAa;AAAA,QAClE;AAAA,MACF;AAAA,MACA;AAAA,QACE,YAAY,IAAI,OAAO,gBAAgB,eAAe,KAAK,GAAG,CAAC,iDAAiD;AAAA,QAChH,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,OAAO;AAAA,MACnF;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,sBAAsB,IAAI,IAAI;AACrC,QAAM,KAAK,IAAI,GAAG,EAAE,IAAI,GAAG,EAAE;AAC7B,QAAM,IAAI,CAAC,SAAS,KAAK,QAAQ,WAAW,EAAE;AAC9C,QAAM,IAAI,CAAC,WAAW;AACpB,UAAM,SAAS,OAAO,OAAO,QAAQ,WAAW,EAAE;AAClD,WAAO,IAAI,OAAO,QAAQ,OAAO,KAAK;AAAA,EACxC;AACA,SAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,OAAO,EAAE,eAAe;AAAA,IACxB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,cAAc;AAAA,IACd,UAAU;AAAA,MACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,MAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,MACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,MACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IACpD;AAAA,IACA,CAAC,EAAE,YAAY,CAAC,GAAG,IAAI,OAAO,GAAG,IAAI;AAAA,IACrC,CAAC,EAAE,aAAa,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK;AAAA,IACvC,CAAC,EAAE,cAAc,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK;AAAA,IACxC,CAAC,EAAE,cAAc,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK;AAAA,IACxC,CAAC,EAAE,cAAc,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK;AAAA,IACxC,CAAC,EAAE,gBAAgB,CAAC,GAAG,EAAE,kBAAkB;AAAA,IAC3C,CAAC,EAAE,cAAc,CAAC,GAAG,EAAE,oBAAoB;AAAA,IAC3C,CAAC,EAAE,qBAAqB,CAAC,GAAG,EAAE,qBAAqB;AAAA,IACnD,CAAC,EAAE,iBAAiB,CAAC,GAAG,EAAE,6BAA6B;AAAA,IACvD,CAAC,EAAE,iBAAiB,CAAC,GAAG,EAAE,gCAAgC;AAAA,IAC1D,OAAO;AAAA,IACP,UAAU,CAAC,SAAS,QAAQ,MAAM,MAAM,OAAO;AAAA,IAC/C,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,IACzB,qBAAqB;AAAA,IACrB,uBAAuB;AAAA,IACvB,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,uBAAuB;AAAA,IACvB,eAAe;AAAA,IACf,aAAa;AAAA,IACb,IAAI;AAAA,IACJ,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,QAAQ,CAAC,MAAM,OAAO;AAAA,IACtB,YAAY,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,IAC/B,iBAAiB,CAAC,OAAO,MAAM,OAAO,IAAI;AAAA,IAC1C,aAAa,CAAC,WAAW,WAAW,SAAS;AAAA,IAC7C,WAAW;AAAA,MACT,CAAC,EAAE,eAAe,CAAC,GAAG;AAAA,QACpB,EAAE,SAAS,EAAE,wBAAwB,EAAE;AAAA,QACvC,EAAE,SAAS,EAAE,qCAAqC,EAAE;AAAA,MACtD;AAAA,MACA,CAAC,EAAE,8BAA8B,CAAC,GAAG;AAAA,QACnC,EAAE,SAAS,EAAE,2CAA2C,EAAE;AAAA,QAC1D,EAAE,SAAS,EAAE,4BAA4B,EAAE;AAAA,QAC3C,EAAE,SAAS,EAAE,yBAAyB,EAAE;AAAA,MAC1C;AAAA,MACA,CAAC,EAAE,kCAAkC,CAAC,GAAG;AAAA,QACvC,EAAE,SAAS,EAAE,2CAA2C,EAAE;AAAA,QAC1D,EAAE,SAAS,EAAE,yBAAyB,EAAE;AAAA,QACxC,EAAE,SAAS,EAAE,gCAAgC,EAAE;AAAA,MACjD;AAAA,MACA,CAAC,EAAE,qBAAqB,CAAC,GAAG;AAAA,QAC1B,EAAE,SAAS,EAAE,2CAA2C,EAAE;AAAA,QAC1D,EAAE,SAAS,EAAE,4BAA4B,EAAE;AAAA,QAC3C,EAAE,SAAS,EAAE,yBAAyB,EAAE;AAAA,MAC1C;AAAA,MACA,CAAC,EAAE,kBAAkB,CAAC,GAAG;AAAA,QACvB,EAAE,SAAS,EAAE,2CAA2C,EAAE;AAAA,QAC1D,EAAE,SAAS,EAAE,yBAAyB,EAAE;AAAA,QACxC,EAAE,SAAS,EAAE,gCAAgC,EAAE;AAAA,MACjD;AAAA,MACA,CAAC,EAAE,yBAAyB,CAAC,GAAG;AAAA,QAC9B,EAAE,SAAS,EAAE,sCAAsC,EAAE;AAAA,QACrD,EAAE,SAAS,EAAE,4BAA4B,EAAE;AAAA,QAC3C,EAAE,SAAS,EAAE,yBAAyB,EAAE;AAAA,MAC1C;AAAA,MACA,CAAC,EAAE,mBAAmB,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,2BAA2B,EAAE,CAAC;AAAA,MACtE,CAAC,EAAE,oBAAoB,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,4BAA4B,EAAE,CAAC;AAAA,MACxE,CAAC,EAAE,oBAAoB,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,4BAA4B,EAAE,CAAC;AAAA,MACxE,CAAC,EAAE,uBAAuB,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,gCAAgC,EAAE,CAAC;AAAA,MAC/E,CAAC,EAAE,uBAAuB,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,gCAAgC,EAAE,CAAC;AAAA,MAC/E,CAAC,EAAE,yBAAyB,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,iCAAiC,EAAE,CAAC;AAAA,MAClF,CAAC,EAAE,eAAe,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,uBAAuB,EAAE,CAAC;AAAA,MAC9D,CAAC,EAAE,oBAAoB,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,4BAA4B,EAAE,CAAC;AAAA,MACxE,CAAC,EAAE,uBAAuB,CAAC,GAAG;AAAA,QAC5B;AAAA,UACE,EAAE,mEAAmE;AAAA,UACrE,GAAG,OAAO,SAAS;AAAA,YACjB,OAAO;AAAA,cACL,SAAS,EAAE,OAAO,YAAY,UAAU,kBAAkB,GAAG,EAAE,GAAG;AAAA,cAClE,SAAS,EAAE,OAAO,YAAY,UAAU,oBAAoB,GAAG,EAAE,GAAG;AAAA,YACtE;AAAA,UACF,IAAI;AAAA,YACF,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,sBAAsB;AAAA,YAC/B;AAAA,cACE,OAAO;AAAA,gBACL,gBAAgB,EAAE,OAAO,OAAO,MAAM,EAAE,mBAAmB,EAAE;AAAA,gBAC7D,YAAY,EAAE,OAAO,MAAM;AAAA,cAC7B;AAAA,YACF;AAAA,YACA,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,sBAAsB;AAAA,UACjC;AAAA,QACF;AAAA,QACA;AAAA,UACE,EAAE,mEAAmE;AAAA,UACrE,GAAG,OAAO,SAAS;AAAA,YACjB,OAAO;AAAA,cACL,SAAS,EAAE,OAAO,YAAY,UAAU,kBAAkB,GAAG,EAAE,GAAG;AAAA,cAClE,SAAS,EAAE,OAAO,YAAY,UAAU,oBAAoB,GAAG,EAAE,GAAG;AAAA,YACtE;AAAA,UACF,IAAI;AAAA,YACF,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,MAAM;AAAA,YACf,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,sBAAsB;AAAA,UACjC;AAAA,QACF;AAAA,QACA;AAAA,UACE,EAAE,mDAAmD;AAAA,UACrD,GAAG,OAAO,SAAS;AAAA,YACjB,OAAO;AAAA,cACL,SAAS,EAAE,OAAO,YAAY,UAAU,kBAAkB,GAAG,EAAE,GAAG;AAAA,cAClE,SAAS,EAAE,OAAO,YAAY,UAAU,oBAAoB,GAAG,EAAE,GAAG;AAAA,YACtE;AAAA,UACF,IAAI;AAAA,YACF,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,MAAM;AAAA,YACf,EAAE,OAAO,IAAI,MAAM,EAAE,+BAA+B,EAAE;AAAA,UACxD;AAAA,QACF;AAAA,QACA;AAAA,UACE,EAAE,+DAA+D;AAAA,UACjE,GAAG,OAAO,SAAS;AAAA,YACjB,OAAO;AAAA,cACL,SAAS,EAAE,OAAO,YAAY,UAAU,kBAAkB,GAAG,EAAE,GAAG;AAAA,cAClE,SAAS,EAAE,OAAO,YAAY,UAAU,oBAAoB,GAAG,EAAE,GAAG;AAAA,YACtE;AAAA,UACF,IAAI;AAAA,YACF,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,MAAM;AAAA,YACf,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,sBAAsB;AAAA,UACjC;AAAA,QACF;AAAA,QACA;AAAA,UACE,EAAE,kBAAkB;AAAA,UACpB,GAAG,OAAO,SAAS;AAAA,YACjB,OAAO;AAAA,cACL,SAAS,EAAE,OAAO,YAAY,UAAU,kBAAkB,GAAG,EAAE,GAAG;AAAA,cAClE,SAAS,EAAE,OAAO,YAAY,UAAU,oBAAoB,GAAG,EAAE,GAAG;AAAA,YACtE;AAAA,UACF,IAAI;AAAA,YACF,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,uBAAuB,MAAM,EAAE,oBAAoB,EAAE;AAAA,UAChE;AAAA,QACF;AAAA,QACA;AAAA,UACE,EAAE,oEAAoE;AAAA,UACtE;AAAA,YACE,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,MAAM;AAAA,YACf,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,sBAAsB;AAAA,UACjC;AAAA,QACF;AAAA,QACA;AAAA,UACE,EAAE,kBAAkB;AAAA,UACpB,GAAG,OAAO,SAAS;AAAA,YACjB,OAAO;AAAA,cACL,SAAS,EAAE,OAAO,YAAY,UAAU,kBAAkB,GAAG,EAAE,GAAG;AAAA,cAClE,SAAS,EAAE,OAAO,YAAY,UAAU,oBAAoB,GAAG,EAAE,GAAG;AAAA,YACtE;AAAA,UACF,IAAI,EAAE,OAAO,WAAW,MAAM,EAAE,qBAAqB,EAAE;AAAA,QACzD;AAAA,QACA;AAAA,UACE,EAAE,sCAAsC;AAAA,UACxC,GAAG,OAAO,SAAS;AAAA,YACjB,OAAO;AAAA,cACL,SAAS,EAAE,OAAO,YAAY,UAAU,kBAAkB,GAAG,EAAE,GAAG;AAAA,cAClE,SAAS,EAAE,OAAO,YAAY,UAAU,oBAAoB,GAAG,EAAE,GAAG;AAAA,YACtE;AAAA,UACF,IAAI;AAAA,YACF,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,eAAe,MAAM,EAAE,+BAA+B,EAAE;AAAA,UACnE;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,EAAE,oCAAoC,CAAC,GAAG;AAAA,QACzC;AAAA,UACE,EAAE,gCAAgC;AAAA,UAClC;AAAA,YACE,EAAE,OAAO,GAAG,OAAO,YAAY,4BAA4B,0BAA0B;AAAA,YACrF;AAAA,cACE,OAAO,GAAG,OAAO,YAAY,4BAA4B;AAAA,cACzD,MAAM,EAAE,mCAAmC;AAAA,YAC7C;AAAA,UACF;AAAA,QACF;AAAA,QACA,CAAC,8CAA8C,EAAE,OAAO,SAAS,CAAC;AAAA,MACpE;AAAA,MACA,CAAC,EAAE,2BAA2B,CAAC,GAAG;AAAA,QAChC,CAAC,UAAU,EAAE,OAAO,SAAS,CAAC;AAAA,QAC9B,CAAC,gBAAgB,EAAE,OAAO,gBAAgB,CAAC;AAAA,QAC3C,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,MACzC;AAAA,MACA,CAAC,EAAE,2BAA2B,CAAC,GAAG;AAAA,QAChC,CAAC,UAAU,EAAE,OAAO,SAAS,CAAC;AAAA,QAC9B,CAAC,gBAAgB,EAAE,OAAO,gBAAgB,CAAC;AAAA,QAC3C,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,MACzC;AAAA,MACA,CAAC,EAAE,+BAA+B,CAAC,GAAG;AAAA,QACpC,CAAC,SAAS,EAAE,OAAO,aAAa,CAAC;AAAA,QACjC,CAAC,KAAK,EAAE,OAAO,cAAc,MAAM,OAAO,CAAC;AAAA,MAC7C;AAAA,MACA,CAAC,EAAE,+BAA+B,CAAC,GAAG;AAAA,QACpC,CAAC,SAAS,EAAE,OAAO,aAAa,CAAC;AAAA,QACjC,CAAC,KAAK,EAAE,OAAO,cAAc,MAAM,OAAO,CAAC;AAAA,MAC7C;AAAA,MACA,CAAC,EAAE,wBAAwB,CAAC,GAAG;AAAA,QAC7B;AAAA,UACE;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL,MAAM;AAAA,gBACJ,EAAE,OAAO,UAAU;AAAA,gBACnB,EAAE,OAAO,cAAc,MAAM,EAAE,wBAAwB,EAAE;AAAA,cAC3D;AAAA,cACA,MAAM;AAAA,gBACJ,EAAE,OAAO,UAAU;AAAA,gBACnB,EAAE,OAAO,cAAc,MAAM,EAAE,wBAAwB,EAAE;AAAA,cAC3D;AAAA,cACA,KAAK,CAAC,EAAE,OAAO,SAAS,GAAG,EAAE,OAAO,UAAU,MAAM,EAAE,qBAAqB,EAAE,CAAC;AAAA,cAC9E,KAAK,CAAC,EAAE,OAAO,SAAS,GAAG,EAAE,OAAO,UAAU,MAAM,EAAE,qBAAqB,EAAE,CAAC;AAAA,YAChF;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL,gBAAgB,EAAE,OAAO,SAAS;AAAA,cAClC,YAAY,EAAE,OAAO,eAAe;AAAA,YACtC;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE;AAAA,UACA,CAAC,EAAE,OAAO,YAAY,GAAG,EAAE,OAAO,GAAG,GAAG,EAAE,OAAO,aAAa,CAAC;AAAA,QACjE;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL,WAAW,EAAE,OAAO,aAAa;AAAA,cACjC,eAAe,EAAE,OAAO,YAAY;AAAA,cACpC,YAAY,EAAE,OAAO,YAAY;AAAA,YACnC;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL,aAAa,EAAE,OAAO,aAAa;AAAA,cACnC,oBAAoB,EAAE,OAAO,YAAY;AAAA,cACzC,YAAY,EAAE,OAAO,aAAa;AAAA,YACpC;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL,OAAO;AAAA,gBACL,OAAO;AAAA,kBACL,WAAW,EAAE,OAAO,aAAa,MAAM,EAAE,mBAAmB,EAAE;AAAA,kBAC9D,YAAY,EAAE,OAAO,aAAa,MAAM,EAAE,sBAAsB,EAAE;AAAA,gBACpE;AAAA,cACF;AAAA,cACA,OAAO;AAAA,gBACL,OAAO;AAAA,kBACL,GAAG,GAAG,OAAO,YAAY;AAAA,oBACvB,sBAAsB,EAAE,OAAO,2BAA2B,MAAM,UAAU;AAAA,kBAC5E,IAAI,CAAC;AAAA,kBACL,GAAG,GAAG,OAAO,YAAY;AAAA,oBACvB,kBAAkB,EAAE,OAAO,uBAAuB,MAAM,UAAU;AAAA,kBACpE,IAAI,CAAC;AAAA,kBACL,CAAC,EAAE,oBAAoB,CAAC,GAAG,EAAE,OAAO,aAAa,MAAM,OAAO;AAAA,kBAC9D,YAAY,EAAE,OAAO,YAAY;AAAA,gBACnC;AAAA,cACF;AAAA,cACA,OAAO,EAAE,OAAO,aAAa,MAAM,EAAE,mBAAmB,EAAE;AAAA,cAC1D,OAAO;AAAA,gBACL,OAAO;AAAA,kBACL,CAAC,EAAE,oBAAoB,CAAC,GAAG,EAAE,OAAO,aAAa,MAAM,OAAO;AAAA,kBAC9D,YAAY,EAAE,OAAO,YAAY;AAAA,gBACnC;AAAA,cACF;AAAA,cACA,OAAO;AAAA,gBACL,OAAO;AAAA,kBACL,WAAW,EAAE,OAAO,aAAa,MAAM,EAAE,mBAAmB,EAAE;AAAA,kBAC9D,YAAY,EAAE,OAAO,aAAa,MAAM,EAAE,sBAAsB,EAAE;AAAA,gBACpE;AAAA,cACF;AAAA,cACA,OAAO;AAAA,gBACL,OAAO;AAAA,kBACL,GAAG,GAAG,OAAO,YAAY,CAAC,IAAI;AAAA,oBAC5B,sBAAsB,EAAE,OAAO,2BAA2B,MAAM,UAAU;AAAA,kBAC5E;AAAA,kBACA,CAAC,EAAE,oBAAoB,CAAC,GAAG,EAAE,OAAO,aAAa,MAAM,OAAO;AAAA,kBAC9D,YAAY,EAAE,OAAO,YAAY;AAAA,gBACnC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,CAAC,QAAQ,EAAE,OAAO,oBAAoB,CAAC;AAAA,MACzC;AAAA,MACA,CAAC,EAAE,0CAA0C,CAAC,GAAG;AAAA,QAC/C,CAAC,eAAe,EAAE,OAAO,GAAG,CAAC;AAAA,QAC7B,CAAC,eAAe,EAAE,OAAO,WAAW,MAAM,EAAE,0BAA0B,EAAE,CAAC;AAAA,MAC3E;AAAA,MACA,CAAC,EAAE,2BAA2B,CAAC,GAAG;AAAA,QAChC;AAAA,UACE;AAAA,UACA,GAAG,OAAO,YAAY,EAAE,OAAO,YAAY,IAAI,EAAE,OAAO,uBAAuB,MAAM,UAAU;AAAA,QACjG;AAAA,QACA;AAAA,UACE,EAAE,oBAAoB;AAAA,UACtB,CAAC,EAAE,OAAO,sBAAsB,GAAG,EAAE,OAAO,uBAAuB,MAAM,UAAU,CAAC;AAAA,QACtF;AAAA,MACF;AAAA,MACA,CAAC,EAAE,+BAA+B,CAAC,GAAG;AAAA,QACpC,CAAC,KAAK,EAAE,OAAO,YAAY,CAAC;AAAA,QAC5B,CAAC,MAAM,EAAE,OAAO,YAAY,CAAC;AAAA,MAC/B;AAAA,MACA,CAAC,EAAE,qCAAqC,CAAC,GAAG;AAAA,QAC1C,CAAC,eAAe,EAAE,OAAO,IAAI,UAAU,EAAE,+BAA+B,EAAE,CAAC;AAAA,MAC7E;AAAA,MACA,CAAC,EAAE,0BAA0B,CAAC,GAAG;AAAA,QAC/B;AAAA,UACE;AAAA,UACA,CAAC,EAAE,OAAO,MAAM,GAAG,EAAE,OAAO,IAAI,MAAM,EAAE,+BAA+B,EAAE,CAAC;AAAA,QAC5E;AAAA,QACA;AAAA,UACE,EAAE,0BAA0B;AAAA,UAC5B;AAAA,YACE,EAAE,OAAO,MAAM;AAAA,YACf,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,uBAAuB,MAAM,UAAU;AAAA,UAClD;AAAA,QACF;AAAA,QACA,CAAC,KAAK,EAAE,OAAO,YAAY,MAAM,EAAE,0BAA0B,EAAE,CAAC;AAAA,MAClE;AAAA,MACA,CAAC,EAAE,sBAAsB,CAAC,GAAG;AAAA,QAC3B;AAAA,UACE,EAAE,2DAA2D;AAAA,UAC7D;AAAA,YACE,OAAO;AAAA,cACL,WAAW;AAAA,gBACT,EAAE,OAAO,sBAAsB;AAAA,gBAC/B,EAAE,OAAO,sBAAsB;AAAA,gBAC/B,EAAE,OAAO,MAAM;AAAA,gBACf,EAAE,OAAO,GAAG;AAAA,gBACZ,EAAE,OAAO,uBAAuB,MAAM,UAAU;AAAA,cAClD;AAAA,cACA,gBAAgB;AAAA,gBACd,EAAE,OAAO,UAAU;AAAA,gBACnB,EAAE,OAAO,UAAU;AAAA,gBACnB,EAAE,OAAO,UAAU;AAAA,gBACnB,EAAE,OAAO,UAAU;AAAA,gBACnB,EAAE,OAAO,UAAU;AAAA,cACrB;AAAA,cACA,YAAY;AAAA,gBACV,EAAE,OAAO,SAAS;AAAA,gBAClB,EAAE,OAAO,SAAS;AAAA,gBAClB,EAAE,OAAO,SAAS;AAAA,gBAClB,EAAE,OAAO,SAAS;AAAA,gBAClB,EAAE,OAAO,SAAS;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL,gBAAgB,EAAE,OAAO,UAAU;AAAA,cACnC,YAAY,EAAE,OAAO,SAAS;AAAA,YAChC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,EAAE,gCAAgC,CAAC,GAAG;AAAA,QACrC;AAAA,UACE;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,MAAM;AAAA,UACR;AAAA,QACF;AAAA,QACA,CAAC,qBAAqB,EAAE,OAAO,UAAU,CAAC;AAAA,MAC5C;AAAA,MACA,CAAC,EAAE,2BAA2B,CAAC,GAAG;AAAA,QAChC,CAAC,EAAE,oBAAoB,GAAG,EAAE,OAAO,WAAW,MAAM,UAAU,CAAC;AAAA,QAC/D,CAAC,qBAAqB,EAAE,OAAO,UAAU,CAAC;AAAA,MAC5C;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,0BAA0B,IAAI;AACrC,QAAM,QAAQ,sBAAsB,gBAAgB,EAAE;AACtD,QAAM,UAAU,sBAAsB,kBAAkB,EAAE;AAC1D,QAAM,OAAO,sBAAsB,eAAe,EAAE;AACpD,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,SAAS;AAAA,IACT,WAAW;AAAA,IACX,OAAO,gBAAgB,GAAG,EAAE;AAAA,IAC5B,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,cAAc;AAAA,IACd,UAAU;AAAA,MACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,MAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,MACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,MACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IACpD;AAAA,IACA,WAAW;AAAA,MACT,GAAG,MAAM;AAAA,MACT,GAAG,QAAQ;AAAA,MACX,GAAG,KAAK;AAAA,IACV;AAAA,EACF;AACF;AACA,IAAI,8BAA8B;AAAA,EAChC,MAAM,wBAAwB,cAAc;AAAA,EAC5C,UAAU,sBAAsB,gBAAgB,yBAAyB;AAC3E;AACA,IAAI,gCAAgC;AAAA,EAClC,MAAM,wBAAwB,gBAAgB;AAAA,EAC9C,UAAU,sBAAsB,kBAAkB,yBAAyB;AAC7E;AACA,IAAI,+BAA+B;AAAA,EACjC,MAAM,wBAAwB,cAAc;AAAA,EAC5C,UAAU,sBAAsB,gBAAgB,0BAA0B;AAC5E;AACA,IAAI,iCAAiC;AAAA,EACnC,MAAM,wBAAwB,gBAAgB;AAAA,EAC9C,UAAU,sBAAsB,kBAAkB,0BAA0B;AAC9E;AACA,IAAI,6BAA6B;AAAA,EAC/B,MAAM,4BAA4B;AAAA,EAClC,UAAU,0BAA0B,yBAAyB;AAC/D;AACA,IAAI,8BAA8B;AAAA,EAChC,MAAM,4BAA4B;AAAA,EAClC,UAAU,0BAA0B,0BAA0B;AAChE;", "names": []}
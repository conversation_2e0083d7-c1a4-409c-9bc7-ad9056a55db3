{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/python/python.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/python/python.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\",\n    blockComment: [\"'''\", \"'''\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  onEnterRules: [\n    {\n      beforeText: new RegExp(\"^\\\\s*(?:def|class|for|if|elif|else|while|try|with|finally|except|async|match|case).*?:\\\\s*$\"),\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    }\n  ],\n  folding: {\n    offSide: true,\n    markers: {\n      start: new RegExp(\"^\\\\s*#region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".python\",\n  keywords: [\n    \"False\",\n    \"None\",\n    \"True\",\n    \"_\",\n    \"and\",\n    \"as\",\n    \"assert\",\n    \"async\",\n    \"await\",\n    \"break\",\n    \"case\",\n    \"class\",\n    \"continue\",\n    \"def\",\n    \"del\",\n    \"elif\",\n    \"else\",\n    \"except\",\n    \"exec\",\n    \"finally\",\n    \"for\",\n    \"from\",\n    \"global\",\n    \"if\",\n    \"import\",\n    \"in\",\n    \"is\",\n    \"lambda\",\n    \"match\",\n    \"nonlocal\",\n    \"not\",\n    \"or\",\n    \"pass\",\n    \"print\",\n    \"raise\",\n    \"return\",\n    \"try\",\n    \"while\",\n    \"with\",\n    \"yield\",\n    \"int\",\n    \"float\",\n    \"long\",\n    \"complex\",\n    \"hex\",\n    \"abs\",\n    \"all\",\n    \"any\",\n    \"apply\",\n    \"basestring\",\n    \"bin\",\n    \"bool\",\n    \"buffer\",\n    \"bytearray\",\n    \"callable\",\n    \"chr\",\n    \"classmethod\",\n    \"cmp\",\n    \"coerce\",\n    \"compile\",\n    \"complex\",\n    \"delattr\",\n    \"dict\",\n    \"dir\",\n    \"divmod\",\n    \"enumerate\",\n    \"eval\",\n    \"execfile\",\n    \"file\",\n    \"filter\",\n    \"format\",\n    \"frozenset\",\n    \"getattr\",\n    \"globals\",\n    \"hasattr\",\n    \"hash\",\n    \"help\",\n    \"id\",\n    \"input\",\n    \"intern\",\n    \"isinstance\",\n    \"issubclass\",\n    \"iter\",\n    \"len\",\n    \"locals\",\n    \"list\",\n    \"map\",\n    \"max\",\n    \"memoryview\",\n    \"min\",\n    \"next\",\n    \"object\",\n    \"oct\",\n    \"open\",\n    \"ord\",\n    \"pow\",\n    \"print\",\n    \"property\",\n    \"reversed\",\n    \"range\",\n    \"raw_input\",\n    \"reduce\",\n    \"reload\",\n    \"repr\",\n    \"reversed\",\n    \"round\",\n    \"self\",\n    \"set\",\n    \"setattr\",\n    \"slice\",\n    \"sorted\",\n    \"staticmethod\",\n    \"str\",\n    \"sum\",\n    \"super\",\n    \"tuple\",\n    \"type\",\n    \"unichr\",\n    \"unicode\",\n    \"vars\",\n    \"xrange\",\n    \"zip\",\n    \"__dict__\",\n    \"__methods__\",\n    \"__members__\",\n    \"__class__\",\n    \"__bases__\",\n    \"__name__\",\n    \"__mro__\",\n    \"__subclasses__\",\n    \"__init__\",\n    \"__import__\"\n  ],\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      [/[,:;]/, \"delimiter\"],\n      [/[{}\\[\\]()]/, \"@brackets\"],\n      [/@[a-zA-Z_]\\w*/, \"tag\"],\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    whitespace: [\n      [/\\s+/, \"white\"],\n      [/(^#.*$)/, \"comment\"],\n      [/'''/, \"string\", \"@endDocString\"],\n      [/\"\"\"/, \"string\", \"@endDblDocString\"]\n    ],\n    endDocString: [\n      [/[^']+/, \"string\"],\n      [/\\\\'/, \"string\"],\n      [/'''/, \"string\", \"@popall\"],\n      [/'/, \"string\"]\n    ],\n    endDblDocString: [\n      [/[^\"]+/, \"string\"],\n      [/\\\\\"/, \"string\"],\n      [/\"\"\"/, \"string\", \"@popall\"],\n      [/\"/, \"string\"]\n    ],\n    numbers: [\n      [/-?0x([abcdef]|[ABCDEF]|\\d)+[lL]?/, \"number.hex\"],\n      [/-?(\\d*\\.)?\\d+([eE][+\\-]?\\d+)?[jJ]?[lL]?/, \"number\"]\n    ],\n    strings: [\n      [/'$/, \"string.escape\", \"@popall\"],\n      [/'/, \"string.escape\", \"@stringBody\"],\n      [/\"$/, \"string.escape\", \"@popall\"],\n      [/\"/, \"string.escape\", \"@dblStringBody\"]\n    ],\n    stringBody: [\n      [/[^\\\\']+$/, \"string\", \"@popall\"],\n      [/[^\\\\']+/, \"string\"],\n      [/\\\\./, \"string\"],\n      [/'/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ],\n    dblStringBody: [\n      [/[^\\\\\"]+$/, \"string\", \"@popall\"],\n      [/[^\\\\\"]+/, \"string\"],\n      [/\\\\./, \"string\"],\n      [/\"/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;;AAOA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,MAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,aAAS,OAAO,kBAAkB,IAAI;AACpC,UAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,kBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,EACvH;AACA,SAAO;AACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;AAG9I,IAAI,6BAA6B,CAAC;AAClC,WAAW,4BAA4B,kBAAuB;AAI9D,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,OAAO,KAAK;AAAA,EAC7B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EACxD;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,cAAc;AAAA,IACZ;AAAA,MACE,YAAY,IAAI,OAAO,6FAA6F;AAAA,MACpH,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,OAAO;AAAA,IACnF;AAAA,EACF;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,MACP,OAAO,IAAI,OAAO,iBAAiB;AAAA,MACnC,KAAK,IAAI,OAAO,oBAAoB;AAAA,IACtC;AAAA,EACF;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,oBAAoB;AAAA,IACpD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,EAC1D;AAAA,EACA,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,WAAW;AAAA,MACtB,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,iBAAiB,KAAK;AAAA,MACvB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa;AAAA,YACb,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,YAAY;AAAA,MACV,CAAC,OAAO,OAAO;AAAA,MACf,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,OAAO,UAAU,eAAe;AAAA,MACjC,CAAC,OAAO,UAAU,kBAAkB;AAAA,IACtC;AAAA,IACA,cAAc;AAAA,MACZ,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,OAAO,QAAQ;AAAA,MAChB,CAAC,OAAO,UAAU,SAAS;AAAA,MAC3B,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,IACA,iBAAiB;AAAA,MACf,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,OAAO,QAAQ;AAAA,MAChB,CAAC,OAAO,UAAU,SAAS;AAAA,MAC3B,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,oCAAoC,YAAY;AAAA,MACjD,CAAC,2CAA2C,QAAQ;AAAA,IACtD;AAAA,IACA,SAAS;AAAA,MACP,CAAC,MAAM,iBAAiB,SAAS;AAAA,MACjC,CAAC,KAAK,iBAAiB,aAAa;AAAA,MACpC,CAAC,MAAM,iBAAiB,SAAS;AAAA,MACjC,CAAC,KAAK,iBAAiB,gBAAgB;AAAA,IACzC;AAAA,IACA,YAAY;AAAA,MACV,CAAC,YAAY,UAAU,SAAS;AAAA,MAChC,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,OAAO,QAAQ;AAAA,MAChB,CAAC,KAAK,iBAAiB,SAAS;AAAA,MAChC,CAAC,OAAO,QAAQ;AAAA,IAClB;AAAA,IACA,eAAe;AAAA,MACb,CAAC,YAAY,UAAU,SAAS;AAAA,MAChC,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,OAAO,QAAQ;AAAA,MAChB,CAAC,KAAK,iBAAiB,SAAS;AAAA,MAChC,CAAC,OAAO,QAAQ;AAAA,IAClB;AAAA,EACF;AACF;", "names": []}
#!/bin/bash

# RustCode Build Script

echo "🦀 Building RustCode..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if Rust is installed
if ! command -v cargo &> /dev/null; then
    echo "❌ Rust is not installed. Please install Rust first."
    exit 1
fi

# Check if Tauri CLI is installed
if ! command -v cargo tauri &> /dev/null; then
    echo "📦 Installing Tauri CLI..."
    cargo install tauri-cli
fi

# Install Node.js dependencies
echo "📦 Installing Node.js dependencies..."
npm install

# Build the application
echo "🔨 Building RustCode..."
npm run tauri build

echo "✅ Build complete! Check the src-tauri/target/release/bundle directory for the executable."
